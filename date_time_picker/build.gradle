plugins {
    id 'com.android.library'
    id "kotlin-android"
    id 'kotlin-parcelize'
}


android {
//    compileSdkVersion 33
//    buildToolsVersion "30.0.3"

    defaultConfig {
        compileSdk 34
        minSdkVersion 21
        targetSdkVersion 34

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    namespace 'com.loper7.date_time_picker'

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation "com.google.android.material:material:$material_version"
    implementation 'androidx.appcompat:appcompat:1.6.1'

}
