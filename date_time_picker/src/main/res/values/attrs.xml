<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="numberPickerStyle" format="reference" />

    <declare-styleable name="NumberPicker">
        <attr name="np_width" format="dimension" />
        <attr name="np_height" format="dimension" />
        <attr name="np_accessibilityDescriptionEnabled" format="boolean" />
        <attr name="np_divider" format="reference" />
        <attr name="np_dividerType" format="enum">
            <enum name="side_lines" value="0" />
            <enum name="underline" value="1" />
        </attr>
        <attr name="np_dividerColor" format="color" />
        <attr name="np_dividerDistance" format="dimension" />
        <attr name="np_dividerLength" format="dimension" />
        <attr name="np_dividerThickness" format="dimension" />
        <attr name="np_fadingEdgeEnabled" format="boolean" />
        <attr name="np_fadingEdgeStrength" format="float" />
        <attr name="np_formatter" format="string" />
        <attr name="np_hideWheelUntilFocused" format="boolean" />
        <attr name="np_itemSpacing" format="dimension" />
        <attr name="np_lineSpacingMultiplier" format="float" />
        <attr name="np_max" format="integer" />
        <attr name="np_maxFlingVelocityCoefficient" format="integer" />
        <attr name="np_min" format="integer" />
        <attr name="np_order" format="enum">
            <enum name="ascending" value="0" />
            <enum name="descending" value="1" />
        </attr>
        <attr name="np_orientation" format="enum">
            <enum name="horizontal" value="0" />
            <enum name="vertical" value="1" />
        </attr>
        <attr name="np_scrollerEnabled" format="boolean" />
        <attr name="np_selectedTextAlign" format="enum">
            <enum name="selectedTextAlignRight" value="0" />
            <enum name="selectedTextAlignCenter" value="1" />
            <enum name="selectedTextAlignLeft" value="2" />
        </attr>
        <attr name="np_selectedTextColor" format="color" />
        <attr name="np_selectedTextSize" format="dimension" />
        <attr name="np_selectedTextStrikeThru" format="boolean" />
        <attr name="np_selectedTextUnderline" format="boolean" />
        <attr name="np_selectedTypeface" format="string" />
        <attr name="np_textAlign" format="enum">
            <enum name="textAlignRight" value="0" />
            <enum name="textAlignCenter" value="1" />
            <enum name="textAlignLeft" value="2" />
        </attr>
        <attr name="np_textColor" format="color" />
        <attr name="np_textSize" format="dimension" />
        <attr name="np_textStrikeThru" format="boolean" />
        <attr name="np_textUnderline" format="boolean" />
        <attr name="np_typeface" format="string" />
        <attr name="np_value" format="integer" />
        <attr name="np_wheelItemCount" format="integer" />
        <attr name="np_wrapSelectorWheel" format="boolean" />
        <attr name="np_textBold" format="boolean" />
        <attr name="np_selectedTextBold" format="boolean" />
    </declare-styleable>

    <declare-styleable name="DateTimePicker">
        <attr name="dt_showLabel" format="boolean" />
        <attr name="dt_themeColor" format="color" />
        <attr name="dt_selectTextSize" format="dimension" />
        <attr name="dt_normalTextSize" format="dimension" />
        <attr name="dt_layout" format="reference" />
        <attr name="dt_textBold" format="boolean" />
        <attr name="dt_selectedTextBold" format="boolean" />
    </declare-styleable>
</resources>