<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/linear_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:paddingTop="25dp"
        android:background="@drawable/shape_bg_round_white_5"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:visibility="gone"
            android:layout_marginBottom="20dp"
            android:textStyle="bold"
            android:textColor="@color/colorTextBlack"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/tv_choose_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/colorTextBlack"
            android:textSize="14sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.6dp"
            android:background="#E5E5E5" />

        <com.loper7.date_time_picker.DateTimePicker
            android:id="@+id/dateTimePicker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:id="@+id/linear_now"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_go_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="120dp"
                android:text="回到現在"
                android:textColor="@color/colorTextGrayDark"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/btn_today"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginTop="25dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/shape_bg_oval_accent"
                android:elevation="2dp"
                android:gravity="center"
                android:text="今"
                android:textColor="@color/colorTextWhite"
                android:textSize="26dp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.6dp"
            android:layout_marginTop="10dp"
            android:background="#E5E5E5" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/dialog_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:textStyle="bold"
                android:gravity="center_horizontal"
                android:padding="16dp"
                android:text="取消"
                android:textColor="@color/colorTextGray"
                android:textSize="16sp"
                android:visibility="visible" />

            <View
                android:id="@+id/dialog_select_border"
                android:layout_width="0.6dp"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:background="#E5E5E5" />

            <TextView
                android:id="@+id/dialog_submit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:gravity="center_horizontal"
                android:padding="16dp"
                android:text="確定"
                android:textStyle="bold"
                android:textColor="@color/colorAccent"
                android:textSize="16sp" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>