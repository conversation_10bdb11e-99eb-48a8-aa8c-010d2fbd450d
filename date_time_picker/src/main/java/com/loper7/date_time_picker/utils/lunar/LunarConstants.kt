package com.loper7.date_time_picker.utils.lunar

/**
 *<AUTHOR>
 *@Date 2021/12/2 10:56
 *@Description
 **/
object LunarConstants {
    /** 农历表中最小的年份 **/
    const val MIN_LUNAR_YEAR = 1899

    /** 未找到农历信息 **/
    const val NOT_FOUND_LUNAR = -1

    /** 农历月名称 **/
    val LUNAR_MONTH_NAMES = arrayOf(
        "正月", "二月", "三月", "四月",
        "五月", "六月", "七月", "八月", "九月", "十月", "冬月", "腊月"
    )

    /** 农历日名称 **/
    val LUNAR_DAY_NAMES = arrayOf(
        "初一", "初二", "初三", "初四",
        "初五", "初六", "初七", "初八", "初九", "初十", "十一", "十二", "十三", "十四", "十五",
        "十六", "十七", "十八", "十九", "二十", "廿一", "廿二", "廿三", "廿四", "廿五", "廿六",
        "廿七", "廿八", "廿九", "三十"
    )

    /** 天干**/
    val LUNAR_TG = arrayOf("甲", "乙", "丙", "丁", "戊", "己","庚","辛","壬","癸")
    /** 地支**/
    val LUNAR_DZ = arrayOf("子", "丑", "寅", "卯", "辰", "巳", "午", "未","申", "酉", "戌", "亥")

    /**
     * 农历信息表
     *
     * 1899~2135年 农历信息
     *
     * eg:2012年 -> 0x1754416 -> (0001 0111 0101 0100 0100 0001 0110)2
     * 从后往前读8位 表示 正月初一 距离 公历1月1日 的天数: (0001 0110)2 -> 22 天
     * 继续往前读4位 表示 闰哪个月 (0100)2 -> 4 即 闰四月 （0表示该年没有闰月）
     * 继续往前读13位 表示 每月天数信息 其中前12位表示正月到腊月的天数信息 第13位表示闰月的天数信息 (1 0111 0101 0100)2 -> 正月大、二月小、三月大 。。。腊月小、闰四月小
     *
     * 注:农历月大30天 月小29天
     */
    val LUNAR_TABLE = intArrayOf(
        0x156A028, 0x97A81E, 0x95C031, 0x14AE026, 0xA9A51C, 0x1A4C02E, 0x1B2A022, 0xCAB418, 0xAD402B, 0x135A020,  //1899-1908
        0xABA215, 0x95C028, 0x14B661D, 0x149A030, 0x1A4A024, 0x1A4B519, 0x16A802C, 0x1AD4021, 0x15B4216, 0x12B6029,  //1909-1918
        0x92F71F, 0x92E032, 0x1496026, 0x169651B, 0xD4A02E, 0xDA8023, 0x156B417, 0x56C02B, 0x12AE020, 0xA5E216,  //1919-1928
        0x92E028, 0xCAC61D, 0x1A9402F, 0x1D4A024, 0xD53519, 0xB5A02C, 0x56C022, 0x10DD317, 0x125C029, 0x191B71E,  //1929-1938
        0x192A031, 0x1A94026, 0x1B1561A, 0x16AA02D, 0xAD4023, 0x14B7418, 0x4BA02B, 0x125A020, 0x1A56215, 0x152A028,  //1939-1948
        0x16AA71C, 0xD9402F, 0x16AA024, 0xA6B51A, 0x9B402C, 0x14B6021, 0x8AF317, 0xA5602A, 0x153481E, 0x1D2A030,  //1949-1958
        0xD54026, 0x15D461B, 0x156A02D, 0x96C023, 0x155C418, 0x14AE02B, 0xA4C020, 0x1E4C314, 0x1B2A027, 0xB6A71D,  //1959-1968
        0xAD402F, 0x12DA024, 0x9BA51A, 0x95A02D, 0x149A021, 0x1A9A416, 0x1A4A029, 0x1AAA81E, 0x16A8030, 0x16D4025,  //1969-1978
        0x12B561B, 0x12B602E, 0x936023, 0x152E418, 0x149602B, 0x164EA20, 0xD4A032, 0xDA8027, 0x15E861C, 0x156C02F,  //1979-1988
        0x12AE024, 0x95E51A, 0x92E02D, 0xC96022, 0xE94316, 0x1D4A028, 0xD6A81E, 0xB58031, 0x156C025, 0x12DA51B,  //1989-1998
        0x125C02E, 0x192C023, 0x1B2A417, 0x1A9402A, 0x1B4A01F, 0xEAA215, 0xAD4027, 0x157671C, 0x4BA030, 0x125A025,  //1999-2008
        0x1956519, 0x152A02C, 0x1694021, 0x1754416, 0x15AA028, 0xABA91E, 0x974031, 0x14B6026, 0xA2F61B, 0xA5602E,  //2009-2018
        0x1526023, 0xF2A418, 0xD5402A, 0x15AA01F, 0xB6A215, 0x96C028, 0x14DC61C, 0x149C02F, 0x1A4C024, 0x1D4C519,  //2019-2028
        0x1AA602B, 0xB54021, 0xED4316, 0x12DA029, 0x95EB1E, 0x95A031, 0x149A026, 0x1A1761B, 0x1A4A02D, 0x1AA4022,  //2029-2038
        0x1BA8517, 0x16B402A, 0xADA01F, 0xAB6215, 0x936028, 0x14AE71D, 0x149602F, 0x154A024, 0x164B519, 0xDA402C,  //2039-2048
        0x15B4020, 0x96D316, 0x126E029, 0x93E81F, 0x92E031, 0xC96026, 0xD1561B, 0x1D4A02D, 0xD64022, 0x14D9417,  //2049-2058
        0x155C02A, 0x125C020, 0x1A5C314, 0x192C027, 0x1AAA71C, 0x1A9402F, 0x1B4A023, 0xBAA519, 0xAD402C, 0x14DA021,  //2059-2068
        0xABA416, 0xA5A029, 0x153681E, 0x152A031, 0x1694025, 0x16D461A, 0x15AA02D, 0xAB4023, 0x1574417, 0x14B602A,  //2069-2078
        0xA56020, 0x164E315, 0xD26027, 0xE6671C, 0xD5402F, 0x15AA024, 0x96B519, 0x96C02C, 0x14AE021, 0xA9C417,  //2079-2088
        0x1A4C028, 0x1D2C81D, 0x1AA4030, 0x1B54025, 0xD5561A, 0xADA02D, 0x95C023, 0x153A418, 0x149A02A, 0x1A2A01F,  //2089-2098
        0x1E4A214, 0x1AA4027, 0x1B6471C, 0x16B402F, 0xABA025, 0x9B651B, 0x93602D, 0x1496022, 0x1A96417, 0x154A02A,  //2099-2108
        0x16AA91E, 0xDA4031, 0x15AC026, 0xAEC61C, 0x126E02E, 0x92E024, 0xD2E419, 0xA9602C, 0xD4A020, 0xF4A315,  //2109-2118
        0xD54028, 0x155571D, 0x155A02F, 0xA5C025, 0x195C51A, 0x152C02D, 0x1A94021, 0x1C95416, 0x1B2A029, 0xB5A91F,  //2119-2128
        0xAD4031, 0x14DA026, 0xA3B61C, 0xA5A02F, 0x151A023, 0x1A2B518, 0x165402B //2129-2135
    )
}