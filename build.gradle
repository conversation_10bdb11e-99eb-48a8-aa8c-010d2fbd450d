// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        kotlin_version = "1.8.10" // Jetpack Compose 需要 1.8.10 先不升級
        objectboxVersion = "4.2.0"

        // Dependency 'androidx.navigation:navigation-common:2.7.1' requires libraries and applications that
        // depend on it to compile against version 34 or later of the Android APIs.
        nav_version = "2.7.7"
        permissionsdispatcher_version = "4.9.2"
        compose_version = '1.5.0'
        material_version = '1.11.0'
    }

    repositories {
        google()
        jcenter()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
    dependencies {
//        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath("io.objectbox:objectbox-gradle-plugin:$objectboxVersion")

        classpath 'com.google.gms:google-services:4.4.2'  // Google Services plugin

        // Add the Crashlytics Gradle plugin.
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'

        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

        // Add the dependency for the App Distribution Gradle plugin
        classpath 'com.google.firebase:firebase-appdistribution-gradle:5.1.1'
    }
}

plugins {
    id 'com.android.application' version '8.10.1' apply false
    id 'com.android.library' version '8.10.1' apply false
    id 'org.jetbrains.kotlin.android' version '2.0.0' apply false
    id "org.jlleitschuh.gradle.ktlint" version "11.0.0"
    id 'com.google.dagger.hilt.android' version '2.44' apply false
    id("org.jetbrains.kotlin.plugin.compose") version "2.0.0"
}

allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}