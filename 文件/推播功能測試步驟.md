# 推播功能測試步驟

## 修正完成的問題

✅ **已修正**：推播設定現在正確地在 `SettingFragment` 中實作，而不是在 `OtherSettingsScreen`
✅ **已修正**：添加了詳細的日誌來調試推播沒有收到的問題
✅ **已修正**：確保預設推播設定會被保存到資料庫
✅ **已修正**：當沒有重要事件時會發送測試推播

## 快速測試步驟

### 1. 安裝應用
```bash
./gradlew installDevDebug
```

### 2. 檢查推播設定
1. 開啟應用
2. 進入「設定」頁面
3. 點擊「推播設定」項目
4. 確認推播設定頁面正常顯示

### 3. 設定推播
1. 開啟「每日行運推播」開關
2. 設定推播時間為當前時間後 2-3 分鐘
3. 確認其他事件類型開關都是開啟的

### 4. 檢查日誌
使用 adb 查看日誌：
```bash
adb logcat | grep -E "(DailyTransitWorker|NotificationUtil|NotificationScheduler)"
```

### 5. 立即測試推播
1. 在首頁點擊「立即執行」按鈕
2. 查看日誌輸出
3. 確認收到推播通知

## 調試日誌說明

### 應用啟動時的日誌
```
App: 推播功能初始化完成
NotificationScheduler: 已創建預設推播設定
NotificationScheduler: 已初始化推播排程，推播開關: true, 時間: 10:00
```

### 推播任務執行時的日誌
```
DailyTransitWorker: 開始執行每日行運推播任務
DailyTransitWorker: 推播設定 - 開關: true, 時間: 10:00
DailyTransitWorker: 找到出生資料: [姓名]
DailyTransitWorker: 計算到 X 個行運事件
DailyTransitWorker: 篩選出 X 個重要事件
DailyTransitWorker: 今日無重要行運事件，發送測試推播
DailyTransitWorker: 已發送測試推播
```

### 推播發送時的日誌
```
NotificationUtil: 準備發送推播 - 標題: [標題], 內容: [內容]
NotificationUtil: 通知權限檢查結果: true
NotificationUtil: 已發送行運推播通知，通知ID: 1001
```

## 常見問題排除

### 1. 沒有收到推播
**檢查項目：**
- 通知權限是否開啟
- 推播開關是否啟用
- 是否有選中的出生資料
- 系統電池優化設定

**解決方案：**
```bash
# 檢查通知權限
adb shell dumpsys notification | grep -A5 "com.one.astrology"

# 檢查應用是否被電池優化
adb shell dumpsys deviceidle whitelist | grep astrology
```

### 2. 推播時間不準確
**檢查項目：**
- 系統時間是否正確
- WorkManager 排程是否正常

**解決方案：**
```bash
# 檢查 WorkManager 狀態
adb shell dumpsys jobscheduler | grep -A10 "com.one.astrology"
```

### 3. 推播內容異常
**檢查項目：**
- 出生資料是否完整
- 行運計算是否正常

**解決方案：**
- 檢查應用日誌中的錯誤訊息
- 確認出生資料包含姓名、日期、地點

## 手動觸發推播測試

### 方法1：使用首頁測試按鈕
1. 在首頁找到「推播測試」區域
2. 點擊「立即執行」按鈕
3. 等待幾分鐘後查看推播

### 方法2：使用 adb 命令
```bash
# 立即觸發 WorkManager 任務
adb shell am broadcast -a androidx.work.impl.background.systemjob.RescheduleReceiver
```

### 方法3：修改系統時間
```bash
# 暫時修改系統時間到推播時間
adb shell su -c "date MMDDhhmmYYYY.ss"
# 記得之後恢復正確時間
```

## 成功標準

✅ **推播設定頁面**：能正常開啟和操作
✅ **推播排程**：能正確設定和更新
✅ **推播發送**：在設定時間收到推播
✅ **推播內容**：顯示正確的標題和內容
✅ **推播點擊**：能正常跳轉到應用

## 下一步優化

1. **權限引導**：添加通知權限請求引導
2. **電池優化**：提示用戶將應用加入白名單
3. **推播歷史**：記錄推播發送歷史
4. **個人化內容**：根據用戶偏好調整推播內容
5. **推播統計**：追蹤推播開啟率和用戶反饋

## 注意事項

- 測試時建議使用較短的時間間隔（2-3分鐘）
- 確保設備有網路連接（雖然推播不需要網路）
- 測試完成後記得將推播時間調整回正常時間
- 在不同 Android 版本上測試兼容性
