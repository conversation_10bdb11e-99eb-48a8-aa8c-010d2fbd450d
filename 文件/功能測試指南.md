# 每日行運推播功能測試指南

## 測試前準備

### 1. 確認應用編譯成功
```bash
./gradlew assembleDevDebug
```

### 2. 安裝應用到設備
```bash
./gradlew installDevDebug
```

### 3. 確認權限設定
- 通知權限：Android 13+ 需要手動授予 POST_NOTIFICATIONS 權限
- 電池優化：建議將應用加入電池優化白名單

## 功能測試步驟

### 第一階段：基本設定測試

#### 1. 推播設定頁面測試
1. 開啟應用
2. 進入「設定」→「其他設定」
3. 確認「推播設定」區域顯示正常
4. 測試以下功能：
   - [ ] 開啟/關閉「每日行運推播」
   - [ ] 點擊「推播時間」，確認時間選擇對話框正常顯示
   - [ ] 修改推播時間，確認設定保存成功
   - [ ] 開啟/關閉各種事件類型（相位事件、宮位變化等）

#### 2. 出生資料設定測試
1. 確認至少有一筆出生資料
2. 選擇一筆出生資料作為當前使用者
3. 確認出生資料包含：
   - [ ] 姓名
   - [ ] 出生日期時間
   - [ ] 出生地點座標

### 第二階段：推播功能測試

#### 3. 手動推播測試
1. 在首頁找到「推播測試」區域
2. 點擊「測試推播」按鈕
3. 確認：
   - [ ] 顯示「已發送測試推播」提示
   - [ ] 收到推播通知
   - [ ] 推播標題格式：「測試推播 - [姓名]」
   - [ ] 推播內容：「火星與太陽形成四分相」
   - [ ] 點擊推播能正常跳轉到應用

#### 4. 立即執行測試
1. 點擊「立即執行」按鈕
2. 確認：
   - [ ] 顯示「已排程立即執行推播任務」提示
   - [ ] 等待幾分鐘後收到實際計算的行運推播
   - [ ] 推播內容包含真實的行運事件

### 第三階段：排程功能測試

#### 5. 推播排程測試
1. 設定推播時間為當前時間後 2-3 分鐘
2. 確認推播開關為開啟狀態
3. 等待設定時間到達
4. 確認：
   - [ ] 在設定時間收到推播
   - [ ] 推播內容為當日行運事件
   - [ ] 推播標題格式：「今日行運提醒 - [姓名]」

#### 6. 推播設定變更測試
1. 修改推播時間
2. 確認新的排程生效
3. 關閉推播功能
4. 確認不再收到推播
5. 重新開啟推播功能
6. 確認推播恢復正常

### 第四階段：進階功能測試

#### 7. 事件類型篩選測試
1. 關閉「相位事件」推播
2. 確認只收到其他類型事件的推播
3. 重新開啟「相位事件」推播
4. 確認相位事件推播恢復

#### 8. 多出生資料測試
1. 新增另一筆出生資料
2. 切換選中的出生資料
3. 確認推播內容對應正確的出生資料

#### 9. 應用重啟測試
1. 完全關閉應用
2. 重新開啟應用
3. 確認推播設定保持不變
4. 確認推播排程繼續運作

## 錯誤排除

### 常見問題及解決方案

#### 1. 沒有收到推播
- 檢查通知權限是否開啟
- 檢查推播設定是否啟用
- 檢查系統電池優化設定
- 檢查應用是否被系統清理

#### 2. 推播時間不準確
- 確認系統時間正確
- 檢查時區設定
- 重新設定推播時間

#### 3. 推播內容異常
- 確認出生資料完整性
- 檢查應用日誌錯誤訊息
- 重新計算行運事件

#### 4. 設定無法保存
- 檢查應用存儲權限
- 清除應用快取重試
- 檢查資料庫完整性

## 測試記錄表

### 基本功能測試
- [ ] 推播設定頁面正常顯示
- [ ] 時間選擇功能正常
- [ ] 設定保存功能正常
- [ ] 事件類型開關正常

### 推播功能測試
- [ ] 測試推播正常發送
- [ ] 立即執行功能正常
- [ ] 定時推播正常運作
- [ ] 推播內容格式正確

### 進階功能測試
- [ ] 事件篩選功能正常
- [ ] 多出生資料支援正常
- [ ] 應用重啟後功能正常
- [ ] 排程管理功能正常

## 性能測試

### 1. 記憶體使用
- 監控應用記憶體使用情況
- 確認無記憶體洩漏

### 2. 電池消耗
- 監控後台任務電池消耗
- 確認在合理範圍內

### 3. 計算效率
- 測試行運計算時間
- 確認響應速度合理

## 測試完成確認

完成所有測試項目後，確認：
- [ ] 所有基本功能正常運作
- [ ] 推播功能穩定可靠
- [ ] 用戶體驗良好
- [ ] 無明顯錯誤或異常
- [ ] 性能表現符合預期

## 後續維護

### 定期檢查項目
1. 推播功能是否正常運作
2. 行運計算準確性
3. 用戶反饋和問題
4. 系統兼容性

### 更新注意事項
1. 測試新版本兼容性
2. 確認資料庫遷移正常
3. 驗證推播排程不受影響
4. 檢查新功能與現有功能的整合
