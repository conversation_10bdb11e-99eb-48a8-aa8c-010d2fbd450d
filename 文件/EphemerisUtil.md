# EphemerisUtil 說明文件

## 概述
`EphemerisUtil` 類提供了與天文曆相關的計算功能，包括時間偏移、黃道日計算、行星位置計算等。此類使用 Swiss Ephemeris 庫來進行天文計算。

## 主要功能
- **時間偏移計算**：根據給定的時間和地理位置計算當地的時間偏移。
- **黃道日計算**：計算給定時間的黃道日。
- **行星位置計算**：計算行星在特定時間的角度和位置。
- **相位計算**：計算行星之間的相位關係。
- **宮位計算**：根據黃道日和地理位置計算宮位。

## 使用方法
### 初始化
在使用 `EphemerisUtil` 的方法之前，必須先調用 `initData(context: Context)` 方法來初始化數據。

### 方法列表
1. **getOffset(time: Long, latLng: LatLng): Pair<Double, Boolean>**
   - 根據給定的時間和地理位置計算當地的時間偏移和是否為日光節約時間。
   - **參數**：
     - `time`: 時間的 Unix 時間戳。
     - `latLng`: 地理位置的緯度和經度。
   - **返回**：一個包含偏移量和是否為日光節約時間的 Pair。

2. **getJulDay(time: Long, latLng: LatLng): Double**
   - 計算給定時間的黃道日。
   - **參數**：
     - `time`: 時間的 Unix 時間戳。
     - `latLng`: 地理位置的緯度和經度。
   - **返回**：計算出的黃道日。

3. **calculate(context: Context, chart: Chart, name: String, time: Long, latLng: LatLng): Horoscope**
   - 根據給定的時間和地理位置計算星盤。
   - **參數**：
     - `context`: 應用上下文。
     - `chart`: 星盤類型。
     - `name`: 星盤名稱。
     - `time`: 時間的 Unix 時間戳。
     - `latLng`: 地理位置的緯度和經度。
   - **返回**：計算出的 Horoscope 對象。

4. **calculateComposite(context: Context, chart: Chart, horoscopeA: Horoscope, horoscopeB: Horoscope): Horoscope**
   - 計算兩個星盤的合成。
   - **參數**：
     - `context`: 應用上下文。
     - `chart`: 星盤類型。
     - `horoscopeA`: 第一個星盤。
     - `horoscopeB`: 第二個星盤。
   - **返回**：合成後的 Horoscope 對象。

## 注意事項
- 確保在使用任何計算方法之前，已經正確初始化數據。
- 使用 Swiss Ephemeris 庫進行計算，請確保相關依賴已正確添加到項目中。

## 參考
- [Swiss Ephemeris Documentation](http://th-mack.de/download/swisseph-doc/swisseph/SwissEph.html) 