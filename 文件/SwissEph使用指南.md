# SwissEph 星曆表庫使用方法整理

SwissEph 是一個用於天文計算的庫，特別適用於占星學計算。以下是對 SwissEph 主要功能和用法的整理：

## 基本初始化

```kotlin
// 初始化 SwissEph 物件
val swissEph = SwissEph(context.filesDir.toString() + File.separator + "ephe")
```

## 宮位計算 (House Systems)

```kotlin
/**
 * 計算宮位
 * @param julDay Julian Day 儒略日
 * @param flags 計算標誌，通常為 0 或 SEFLG_SIDEREAL 和/或 SEFLG_RADIANS
 * @param latitude 地理緯度
 * @param longitude 地理經度
 * @param houseSystem 宮制字符代碼
 * @param cusps 宮頭數組，大小為 13，返回 1 至 12 宮的宮頭
 * @param ascmc 特殊點數組，大小為 10，返回上升點等特殊點
 */
val result = swissEph.swe_houses(
    julDay,
    flags,
    latitude,
    longitude,
    houseSystem.code, // 'P', 'K', 'C', 'E' 等
    cusps,  // 返回 1 至 12 宮的宮頭 cusps[1...12]
    ascmc   // 返回上升點等特殊點
)
```

### 宮制代碼 (House Systems)

- `'P'` - Placidus (胎次法)
- `'K'` - Koch (科赫法)
- `'O'` - Porphyry (波菲里法)
- `'R'` - Regiomontanus (蒙塔努斯法)
- `'C'` - Campanus (坎帕努斯法)
- `'A'` 或 `'E'` - Equal (等宮法)
- `'W'` - Whole Sign (整宮法)
- `'B'` - Alcabitius (阿卡比修斯法)
- `'M'` - Morinus (莫里努斯法)
- `'T'` - Topocentric (地心法)
- `'V'` - Equal Vehlow (韋洛等宮法)

### ascmc 數組內容

- `ascmc[0]` - 上升點 (Ascendant)
- `ascmc[1]` - 中天點 (MC, Medium Coeli)
- `ascmc[2]` - ARMC (= 恆星時)
- `ascmc[3]` - 宿命點 (Vertex)
- `ascmc[4]` - 赤道上升點 (Equatorial Ascendant)
- `ascmc[5]` - 輔助上升點 (Walter Koch)
- `ascmc[6]` - 輔助上升點 (Michael Munkasey)
- `ascmc[7]` - 極上升點 (Michael Munkasey)
- `ascmc[8]` - 保留
- `ascmc[9]` - 保留

## 行星位置計算

```kotlin
/**
 * 計算行星位置
 * @param julDay Julian Day 儒略日 (UT)
 * @param planetId 行星 ID，見 SweConst
 * @param flags 計算標誌
 * @param xx 結果數組，大小為 6
 * @param message 錯誤信息緩衝區
 */
val result = swissEph.swe_calc_ut(
    julDay,
    planetId,
    flags,
    xx,
    message
)
```

### xx 數組內容

- `xx[0]` - 黃經 (longitude)
- `xx[1]` - 黃緯 (latitude)
- `xx[2]` - 距離 (AU)
- `xx[3]` - 黃經速度 (度/天)，若小於 0 則代表行星逆行
- `xx[4]` - 黃緯速度 (度/天)
- `xx[5]` - 距離變化速度 (AU/天)

### 常用行星 ID

- `SweConst.SE_SUN` - 太陽 (0)
- `SweConst.SE_MOON` - 月亮 (1)
- `SweConst.SE_MERCURY` - 水星 (2)
- `SweConst.SE_VENUS` - 金星 (3)
- `SweConst.SE_MARS` - 火星 (4)
- `SweConst.SE_JUPITER` - 木星 (5)
- `SweConst.SE_SATURN` - 土星 (6)
- `SweConst.SE_URANUS` - 天王星 (7)
- `SweConst.SE_NEPTUNE` - 海王星 (8)
- `SweConst.SE_PLUTO` - 冥王星 (9)
- `SweConst.SE_MEAN_NODE` - 北交點 (10)
- `SweConst.SE_MEAN_APOG` - 莉莉斯 (12)
- `SweConst.SE_CHIRON` - 凱龍星 (15)

### 常用計算標誌 (flags)

- `SweConst.SEFLG_MOSEPH` - 使用 Moshier 星曆表
- `SweConst.SEFLG_SWIEPH` - 使用 Swiss Ephemeris 星曆表
- `SweConst.SEFLG_JPLEPH` - 使用 JPL 星曆表
- `SweConst.SEFLG_SPEED` - 計算速度
- `SweConst.SEFLG_SIDEREAL` - 使用恆星黃道
- `SweConst.SEFLG_RADIANS` - 使用弧度而非角度
- `SweConst.SEFLG_TOPOCTR` - 地心坐標
- `SweConst.SEFLG_EQUATORIAL` - 赤道坐標

## 儒略日計算

```kotlin
/**
 * 計算儒略日
 * @param year 年
 * @param month 月
 * @param day 日
 * @param hour 小時 (包含分鐘，如 12.5 表示 12:30)
 */
val julDay = SweDate(year, month, day, hour - timeZoneOffset).julDay
```

## 恆星計算

```kotlin
/**
 * 計算恆星位置
 * @param starName 恆星名稱
 * @param julDay 儒略日
 * @param flags 計算標誌
 * @param xx 結果數組，大小為 6
 * @param message 錯誤信息緩衝區
 */
val result = swissEph.swe_fixstar_ut(
    starName,
    julDay,
    flags,
    xx,
    message
)
```

## 相位計算

相位計算不是 SwissEph 的直接功能，但可以通過計算兩個天體的角度差來實現：

```kotlin
// 計算兩個天體之間的相位
val angle = Math.abs(planet1Angle - planet2Angle)
val normalizedAngle = if (angle > 180) 360 - angle else angle

// 判斷相位類型
val orb = 5.0 // 容許度
when {
    Math.abs(normalizedAngle - 0) <= orb -> "合相"
    Math.abs(normalizedAngle - 60) <= orb -> "六分相"
    Math.abs(normalizedAngle - 90) <= orb -> "四分相"
    Math.abs(normalizedAngle - 120) <= orb -> "三分相"
    Math.abs(normalizedAngle - 180) <= orb -> "對分相"
}
```

## 過境計算

```kotlin
/**
 * 計算行星過境
 * @param tc 過境計算器
 * @param jdET 開始時間 (ET)
 * @param backwards 是否向後搜索
 */
val transitTime = swissEph.getTransitET(
    tc,
    jdET,
    backwards
)
```

## 實用功能

### 獲取行星名稱

```kotlin
val planetName = swissEph.swe_get_planet_name(planetId)
```

### 獲取恆星亮度

```kotlin
val magnitude = swissEph.getFixstarMagnitude(starName)
```

### 計算本地視時與平時的差異

```kotlin
val E = DoubleArray(1)
swissEph.swe_time_equ(julDay, E, message)
// E[0] 包含本地視時與平時的差異 (LAT - LMT)
```

## 優化建議

1. **使用緩存**：計算結果可以緩存，避免重複計算
2. **批量計算**：一次性計算多個行星位置，減少方法調用
3. **使用適當的星曆表**：根據需要選擇 Moshier、Swiss Ephemeris 或 JPL 星曆表
4. **錯誤處理**：始終檢查返回值和錯誤信息
5. **資源管理**：在不需要時釋放 SwissEph 資源

## 參考資料

更多詳細信息可參考 SwissEph 官方文檔：[http://th-mack.de/download/swisseph-doc/swisseph/SwissEph.html](http://th-mack.de/download/swisseph-doc/swisseph/SwissEph.html) 