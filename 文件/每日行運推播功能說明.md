# 每日行運推播功能說明

## 功能概述

每日行運推播功能為用戶提供基於其出生資料的個人化行運事件通知。系統會自動計算當前行運行星與本命行星的相位關係、宮位變化、星座切換等重要天文事件，並在設定的時間發送推播通知。

## 主要功能

### 1. 行運事件計算
- **相位分析**：計算行運行星與本命行星之間的入相位關係
- **宮位變化**：檢測行運行星進入不同宮位的時機
- **星座切換**：監控行運行星從一個星座進入另一個星座
- **逆行事件**：追蹤行星開始或結束逆行的時間點

### 2. 推播設定管理
- **時間設定**：用戶可自訂每日推播時間（預設早上10:00）
- **事件類型篩選**：可選擇接收哪些類型的行運事件
- **重要性等級**：設定最低推播重要性等級
- **容許度設定**：調整相位計算的容許度範圍

### 3. 智能推播排程
- **每日定時執行**：使用 WorkManager 確保推播準時發送
- **事件優先級**：根據重要性排序，優先推播關鍵事件
- **避免重複**：已推播的事件不會重複發送

## 技術架構

### 資料模型

#### TransitEvent（行運事件）
```kotlin
data class TransitEvent(
    var eventType: TransitEventType,        // 事件類型
    var eventTime: Long,                    // 事件時間
    var transitPlanetName: String,          // 行運行星名稱
    var natalPlanetName: String?,           // 本命行星名稱
    var aspectType: Int?,                   // 相位類型
    var aspectDirection: AspectDirection?,   // 相位方向
    var orb: Double,                        // 容許度
    var importance: TransitImportance,      // 重要性等級
    var description: String,                // 事件描述
    // ... 其他屬性
)
```

#### NotificationSettings（推播設定）
```kotlin
data class NotificationSettings(
    var isDailyTransitEnabled: Boolean,     // 是否啟用推播
    var notificationHour: Int,              // 推播小時
    var notificationMinute: Int,            // 推播分鐘
    var isAspectEventsEnabled: Boolean,     // 相位事件開關
    var isHouseChangeEnabled: Boolean,      // 宮位變化開關
    var isSignChangeEnabled: Boolean,       // 星座切換開關
    var minimumImportance: TransitImportance, // 最小重要性
    // ... 其他設定
)
```

### 核心類別

#### TransitCalculator（行運計算器）
- 負責計算指定日期範圍內的行運事件
- 分析相位關係、宮位變化、星座切換
- 評估事件重要性等級

#### DailyTransitWorker（每日推播工作器）
- 使用 WorkManager 實現的後台任務
- 每日在設定時間自動執行
- 計算當日重要行運事件並發送推播

#### NotificationUtil（通知工具）
- 處理推播通知的顯示邏輯
- 支援聲音、震動等通知效果
- 提供測試推播功能

#### NotificationScheduler（推播排程管理器）
- 管理每日推播的排程設定
- 支援立即執行和取消排程
- 監控排程狀態

## 使用方式

### 1. 設定推播偏好
1. 進入「設定」→「其他設定」
2. 在「推播設定」區域調整以下選項：
   - 開啟/關閉每日行運推播
   - 設定推播時間
   - 選擇要接收的事件類型
   - 調整重要性等級

### 2. 測試推播功能
1. 在首頁選擇出生資料後
2. 點擊「推播測試」區域的按鈕：
   - 「測試推播」：發送測試通知
   - 「立即執行」：立即計算並推播今日行運

### 3. 查看推播內容
- 推播標題：顯示出生資料名稱
- 推播內容：列出重要行運事件
- 點擊推播：跳轉到應用主頁面

## 事件重要性評級

### 評級標準
- **關鍵（Critical）**：涉及太陽/月亮的精確相位，慢行星重要相位
- **重要（High）**：主要行星間的緊密相位，宮位重要變化
- **中等（Medium）**：一般相位事件，星座切換
- **輕微（Low）**：容許度較大的相位，次要事件

### 推播策略
- 每日最多推播3個最重要的事件
- 優先推播關鍵和重要等級的事件
- 避免推播過於頻繁的輕微事件

## 相位類型說明

### 主要相位
- **合相（0°）**：能量融合，新開始
- **六分相（60°）**：和諧機會，輕鬆發展
- **四分相（90°）**：挑戰衝突，需要行動
- **三分相（120°）**：順利流動，天賦展現
- **對分相（180°）**：對立平衡，關係議題

### 容許度設定
- 合相、對分相：±8°
- 四分相、三分相：±6°
- 六分相：±4°

## 注意事項

### 權限要求
- **通知權限**：Android 13+ 需要 POST_NOTIFICATIONS 權限
- **後台執行**：確保應用不被系統清理，以保證推播正常運作

### 電池優化
- 建議將應用加入電池優化白名單
- 避免系統休眠時停止推播服務

### 資料準確性
- 推播內容基於瑞士星曆表（Swiss Ephemeris）計算
- 時間精度可達分鐘級別
- 地理位置影響宮位計算結果

## 故障排除

### 推播未收到
1. 檢查通知權限是否開啟
2. 確認推播設定是否啟用
3. 檢查系統電池優化設定
4. 重新設定推播時間

### 計算結果異常
1. 確認出生資料的準確性
2. 檢查時區設定是否正確
3. 驗證地理座標是否精確

### 應用崩潰
1. 檢查 ObjectBox 資料庫完整性
2. 清除應用快取重新啟動
3. 確認 WorkManager 服務正常運行

## 未來擴展

### 計劃功能
- **週期性推播**：月返、年返等特殊時間點提醒
- **個人化解讀**：基於用戶偏好的事件解釋
- **推播歷史**：查看過往推播記錄
- **多語言支援**：支援更多語言的推播內容

### 技術優化
- **機器學習**：根據用戶反饋優化推播內容
- **雲端同步**：跨設備同步推播設定
- **即時計算**：提高計算效率和準確性
