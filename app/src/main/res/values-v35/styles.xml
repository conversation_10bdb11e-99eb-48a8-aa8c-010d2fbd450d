<?xml version="1.0" encoding="utf-8"?>
<resources>

        <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
                <item name="colorPrimary">@color/colorPrimary</item>
                <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
                <item name="colorAccent">@color/colorAccent</item>

                <item name="android:itemTextAppearance">@style/AppTheme.ItemTextStyle</item>

                <item name="materialTimePickerTheme">@style/AppTheme.MaterialTimePickerTheme</item>

                <item name="navigationViewStyle">@style/Widget.App.NavigationView</item>

                <item name="bottomSheetDialogTheme">@style/ThemeOverlay.App.BottomSheetDialog</item>
                <item name="android:spinnerDropDownItemStyle">@style/mySpinnerItemStyle</item>

                <item name="android:statusBarColor">@color/colorPrimary</item>
                <item name="android:navigationBarColor">@color/white</item>
                <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
                <!--if status bar color is light then true, if statusbar color is dark then make it false-->
                <item name="android:windowLightStatusBar">false</item>
        </style>
</resources>