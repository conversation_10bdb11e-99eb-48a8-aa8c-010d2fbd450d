<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:bottom="0dp"
        android:drawable="@color/colorPrimary"
        android:left="0dp"
        android:right="0dp"
        android:state_selected="true"
        android:top="0dp">

        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="16dp" />
        </shape>

    </item>

    <item
        android:bottom="0dp"
        android:drawable="@color/white"
        android:left="0dp"
        android:right="0dp"
        android:top="0dp">

        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="16dp" />
        </shape>

    </item>
</layer-list>