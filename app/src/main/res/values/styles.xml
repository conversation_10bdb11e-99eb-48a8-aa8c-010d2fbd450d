<resources>

    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

        <item name="android:itemTextAppearance">@style/AppTheme.ItemTextStyle</item>

        <item name="materialTimePickerTheme">@style/AppTheme.MaterialTimePickerTheme</item>

        <item name="navigationViewStyle">@style/Widget.App.NavigationView</item>

        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.App.BottomSheetDialog</item>
        <item name="android:spinnerDropDownItemStyle">@style/mySpinnerItemStyle</item>
    </style>

    <style name="SplashTheme" parent="AppTheme">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

        <item name="android:itemTextAppearance">@style/AppTheme.ItemTextStyle</item>

        <item name="materialTimePickerTheme">@style/AppTheme.MaterialTimePickerTheme</item>

        <item name="navigationViewStyle">@style/Widget.App.NavigationView</item>

        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.App.BottomSheetDialog</item>
        <item name="android:spinnerDropDownItemStyle">@style/mySpinnerItemStyle</item>
        <item name="android:windowBackground">@color/colorPrimaryDark</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="mySpinnerItemStyle" parent="@android:style/Widget.Holo.DropDownItem.Spinner">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/colorPrimaryDark</item>
    </style>

    <style name="ThemeOverlay.App.BottomSheetDialog" parent="ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/ModalBottomSheetDialog</item>
    </style>

    <style name="ModalBottomSheetDialog_70" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="backgroundTint">@color/white_t70</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.LargeComponent</item>
    </style>

    <style name="ModalBottomSheetDialog" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="backgroundTint">@color/white</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.LargeComponent</item>
    </style>

    <style name="ShapeAppearance.App.LargeComponent" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>

    <style name="AppTheme.ItemTextStyle" parent="@android:style/TextAppearance.Widget.IconMenu.Item">
        <item name="android:textColor">@color/primary_text</item>
    </style>

    <style name="Widget.App.NavigationView" parent="Widget.MaterialComponents.NavigationView">
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.NavigationView</item>
        <item name="itemIconTint">@color/navigation_item_color</item>
        <item name="itemTextColor">@color/navigation_item_color</item>
        <item name="itemShapeFillColor">@color/navigation_item_background_color</item>
    </style>

    <style name="ThemeOverlay.App.NavigationView" parent="">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorSurface">@color/colorPrimary</item>
        <item name="colorOnSurface">@color/colorAccent</item>
    </style>

    <style name="NavigationDrawerStyle">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">#FFFFFF</item>
    </style>

    <style name="AppTheme.MaterialTimePickerTheme" parent="@style/ThemeOverlay.MaterialComponents.TimePicker">
        <item name="colorOnPrimary">@color/colorPrimary</item>
        <item name="colorOnSurface">@color/primary_text</item>
        <item name="colorPrimary">@color/colorPrimary</item>
    </style>

    <style name="CompatDialogTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowActionModeOverlay">true</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <style name="MaterialComponentsDialogTheme" parent="Theme.MaterialComponents.NoActionBar">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowActionModeOverlay">true</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <style name="DefaultNumberPickerTheme" parent="AppTheme">
        <item name="colorControlNormal">@color/dividerColor</item>
    </style>

    <style name="AppTabLayout" parent="Base.Widget.Design.TabLayout">
        <!--        <item name="tabMaxWidth">?attr/actionBarSize</item>-->
        <item name="tabIndicatorColor">@color/colorPrimaryYellow</item>
        <!--        <item name="tabIndicatorHeight">4dp</item>-->
        <!--        <item name="tabPaddingStart">6dp</item>-->
        <!--        <item name="tabPaddingEnd">6dp</item>-->
        <item name="tabBackground">?attr/selectableItemBackground</item>
        <item name="tabTextAppearance">@style/AppTabTextAppearance</item>
        <item name="tabSelectedTextColor">@color/colorPrimaryYellow</item>
    </style>


    <style name="AppTabLayout2" parent="Base.Widget.Design.TabLayout">
        <!--        <item name="tabMaxWidth">?attr/actionBarSize</item>-->
        <item name="tabIndicatorColor">@color/colorPrimaryYellow</item>
        <!--        <item name="tabIndicatorHeight">4dp</item>-->
        <!--        <item name="tabPaddingStart">6dp</item>-->
        <!--        <item name="tabPaddingEnd">6dp</item>-->
        <item name="tabBackground">@color/transparent</item>
        <item name="tabTextAppearance">@style/AppTabTextAppearance2</item>
        <item name="tabSelectedTextColor">@color/colorPrimaryYellow</item>
    </style>

    <style name="RoundedTabLayoutStyle" parent="Widget.Design.TabLayout">
        <item name="tabBackground">@drawable/tabs_selector</item>
        <item name="tabIndicator">@null</item>
        <item name="android:background">@drawable/tab_layout_bg</item>
        <item name="tabSelectedTextColor">@color/white</item>
        <item name="tabTextColor">@color/colorPrimary</item>
    </style>

    <!-- for text -->
    <style name="AppTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="AppTabTextAppearance2" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="AppTabLayout.Light" parent="Base.Widget.Design.TabLayout">
        <!--        <item name="tabMaxWidth">?attr/actionBarSize</item>-->
        <item name="tabIndicatorColor">@color/colorPrimaryDark</item>
        <!--        <item name="tabIndicatorHeight">4dp</item>-->
        <!--        <item name="tabPaddingStart">6dp</item>-->
        <!--        <item name="tabPaddingEnd">6dp</item>-->
        <item name="tabBackground">?attr/selectableItemBackground</item>
        <item name="tabTextAppearance">@style/AppTabTextAppearance</item>
        <item name="tabSelectedTextColor">@color/colorPrimaryDark</item>
    </style>

    <!-- for text -->
    <style name="AppTabTextAppearance.Light" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/greyish_brown_30</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="ItemTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="ItemTextStyleColorPrimary">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="ItemTextStyle_y">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/colorAccent</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="ItemTextStyle_16">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">normal</item>
    </style>


    <style name="ErrorTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">@color/red</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold|italic</item>
    </style>

    <style name="SwitchCompatTheme">
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorPrimary</item>
        <item name="android:colorForeground">@color/grey</item>
    </style>

    <style name="CircleImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

</resources>
