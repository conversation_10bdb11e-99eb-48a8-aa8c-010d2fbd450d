<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--圓角-->
    <style name="roundedCorner" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>

    <!--圓形-->
    <style name="circle" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <!--切角-->
    <style name="cutImageView" parent="">
        <item name="cornerFamily">cut</item>
        <item name="cornerSize">20dp</item>
    </style>

    <!--菱形-->
    <style name="cutImageView2" parent="">
        <item name="cornerFamily">cut</item>
        <item name="cornerSize">50%</item>
    </style>

    <!--右上-->
    <style name="topRightCorner" parent="">
        <item name="cornerFamilyTopRight">rounded</item>
        <item name="cornerSizeTopRight">50dp</item>
    </style>

    <!--右邊-->
    <style name="rightCut" parent="">
        <item name="cornerFamily">cut</item>
        <item name="cornerSizeTopRight">50%</item>
        <item name="cornerSizeBottomRight">50%</item>
    </style>
</resources>