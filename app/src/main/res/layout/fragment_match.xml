<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/coordinator_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/btChoose"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            style="@style/AppTabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabBackground="@drawable/tab_background"
            app:tabContentStart="56dp"
            app:tabIndicatorFullWidth="true"
            app:tabMode="fixed"
            app:tabTextAppearance="@style/AppTabTextAppearance" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </LinearLayout>

    <Button
        android:id="@+id/btChoose"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/btn_primary_dark"
        android:text="選擇"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />


</RelativeLayout>