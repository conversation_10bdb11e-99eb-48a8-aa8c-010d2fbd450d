<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragment.navigation.BirthDataListFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:layout_scrollFlags="scroll|enterAlways">

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabFilter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="0dp"
                android:layout_marginEnd="10dp"
                android:background="?android:selectableItemBackground"
                android:contentDescription="@null"
                android:paddingHorizontal="15dp"
                android:src="@drawable/ic_baseline_filter_list_24"
                android:visibility="visible"
                app:backgroundTint="@android:color/transparent"
                app:elevation="0dp"
                app:fabSize="normal"
                app:layout_constraintBottom_toBottomOf="@+id/tipSearch"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tipSearch"
                app:tint="@color/colorPrimary" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_margin="10dp">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etSearch"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_edit_text"
                    android:drawableStart="@drawable/ic_baseline_search_24"
                    android:drawablePadding="10dp"
                    android:drawableTint="@color/colorPrimary"
                    android:hint="@string/please_enter_a_search_keyword"
                    android:padding="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivFilter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:padding="10dp"
                    android:src="@drawable/ic_baseline_filter_list_24"
                    android:tint="@color/colorPrimary"
                    android:visibility="visible" />
            </RelativeLayout>


        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tipSearch" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:contentDescription="@string/add"
        android:src="@android:drawable/ic_input_add"
        app:backgroundTint="@color/colorAccent"
        app:backgroundTintMode="add"
        app:behavior_autoHide="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:rippleColor="@color/white"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>