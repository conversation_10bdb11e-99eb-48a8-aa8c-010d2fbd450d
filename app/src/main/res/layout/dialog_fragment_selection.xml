<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginHorizontal="20dp"
    android:layout_marginVertical="30dp"
    android:padding="120dp"
    app:cardCornerRadius="@dimen/cardCornerRadius"
    app:cardElevation="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="選擇用戶"
            android:textColor="@color/colorPrimary"
            android:textSize="20sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="400dp" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btOK"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/btn_primary_dark"
            android:text="確定"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>