<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".fragment.report.InterpretationFragment">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:textColor="@color/colorPrimary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="@tools:sample/lorem" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/tab_layout_bg"
                android:padding="10dp"
                app:tabContentStart="0dp"
                app:tabGravity="start"
                app:tabIndicatorColor="@color/colorAccent"
                app:tabMode="scrollable"
                app:tabSelectedTextColor="@color/colorAccent"
                app:tabTextColor="@color/colorPrimary">

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/planet_sign" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/planet_house" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/planet_aspect" />

                <!--                <com.google.android.material.tabs.TabItem-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:visibility="gone"-->
                <!--                    android:text="@string/house_sign_desc" />-->

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/flying_star" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/career" />
            </com.google.android.material.tabs.TabLayout>

            <TextView
                android:id="@+id/tvSignPosition"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="@string/planet_sign"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/baseline_expand_less_24" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewSign"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp" />

            <TextView
                android:id="@+id/tvHouse"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="@string/planet_house"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/baseline_expand_less_24"
                tools:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewHouse"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvAspect"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="@string/planet_aspect"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/baseline_expand_less_24" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewAspect"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvHouseSign"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="@string/house_sign_desc"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/baseline_expand_less_24"
                tools:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewHouseSign"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:visibility="gone"
                tools:visibility="gone" />

            <TextView
                android:id="@+id/tvFly"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="@string/flying_star"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/baseline_expand_less_24" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewFly"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvCareer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="@string/career"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableEndCompat="@drawable/baseline_expand_less_24" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewCareer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:visibility="gone" />

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabUpload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/add"
        android:src="@drawable/baseline_upload_file_24"
        app:backgroundTint="@color/colorAccent"
        app:backgroundTintMode="add"
        app:behavior_autoHide="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:rippleColor="@color/white"
        app:tint="@android:color/white" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>