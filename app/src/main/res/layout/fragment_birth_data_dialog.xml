<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="20dp"
    android:padding="20dp"
    app:cardCornerRadius="@dimen/cardCornerRadius"
    app:cardElevation="20dp"
    tools:context=".ui.fragment.dialog.BirthDataDialogFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        android:maxHeight="600dp"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="20dp"
            android:paddingTop="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/birth_information"
                android:textColor="@color/colorPrimary"
                android:textSize="20sp"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/please_enter_a_name"
                android:minWidth="300dp"
                android:textColorHint="@color/primary_text"
                app:backgroundTint="@color/colorPrimary"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="1dp"
                app:hintTextAppearance="@style/ErrorTextAppearance"
                app:hintTextColor="@color/colorPrimary"
                app:startIconDrawable="@drawable/ic_baseline_person_24"
                app:startIconTint="@color/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:importantForAutofill="no"
                    android:inputType="text"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/please_select_date_of_birth"
                android:textColorHint="@color/primary_text"
                app:backgroundTint="@color/colorPrimary"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="1dp"
                app:endIconTint="@color/colorPrimary"
                app:hintTextAppearance="@style/ErrorTextAppearance"
                app:hintTextColor="@color/colorPrimary"
                app:startIconDrawable="@drawable/ic_baseline_date_range_24"
                app:startIconTint="@color/colorPrimary">

                <AutoCompleteTextView
                    android:id="@+id/tvDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:gravity="center_vertical"
                    android:importantForAutofill="no"
                    android:inputType="text"
                    android:labelFor="@+id/etDegree120"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:ignore="LabelFor" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/please_select_a_date_of_birth"
                android:textColorHint="@color/primary_text"
                android:visibility="gone"
                app:backgroundTint="@color/colorPrimary"
                app:boxBackgroundColor="@color/white"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="1dp"
                app:endIconTint="@color/colorPrimary"
                app:hintTextAppearance="@style/ErrorTextAppearance"
                app:hintTextColor="@color/colorPrimary"
                app:startIconDrawable="@drawable/ic_baseline_access_time_24"
                app:startIconTint="@color/colorPrimary">

                <AutoCompleteTextView
                    android:id="@+id/tvTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:gravity="start"
                    android:importantForAutofill="no"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/layoutBirthplace"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="0dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical">

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:hint="@string/please_select_your_place_of_birth"
                            android:textColorHint="@color/primary_text"
                            app:backgroundTint="@color/colorPrimary"
                            app:boxStrokeColor="@color/colorPrimary"
                            app:boxStrokeWidth="1dp"
                            app:hintTextAppearance="@style/ErrorTextAppearance"
                            app:hintTextColor="@color/colorPrimary"
                            app:startIconDrawable="@drawable/ic_round_location_on_24"
                            app:startIconTint="@color/colorPrimary">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etCityName"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:gravity="center_vertical"
                                android:importantForAutofill="no"
                                android:inputType="text"
                                android:textColor="@color/colorPrimary"
                                android:textSize="16sp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <ProgressBar
                            android:id="@+id/progressBar"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_centerInParent="true"
                            android:visibility="gone" />

                        <RelativeLayout
                            android:id="@+id/rltMap"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_toStartOf="@+id/rltSearch">

                            <ImageButton
                                android:id="@+id/btnMap"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_gps_button"
                                android:contentDescription="地圖"
                                android:src="@drawable/ic_map" />

                            <ProgressBar
                                android:id="@+id/progressBarMap"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_centerInParent="true"
                                android:visibility="gone" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rltSearch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_toStartOf="@+id/rltGPS">

                            <ImageButton
                                android:id="@+id/btnSearch"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_gps_button"
                                android:contentDescription="搜尋"
                                android:src="@drawable/ic_baseline_search_24" />

                            <ProgressBar
                                android:id="@+id/progressBarSearch"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_centerInParent="true"
                                android:visibility="gone" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rltGPS"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="12dp"
                            android:layout_marginEnd="2dp">

                            <ImageButton
                                android:id="@+id/btnGetCurrentLocation"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_gps_button"
                                android:contentDescription="GPS定位"
                                android:src="@drawable/ic_gps" />

                            <ProgressBar
                                android:id="@+id/progressBarGetCurrentLocation"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_centerInParent="true"
                                android:visibility="gone" />

                        </RelativeLayout>

                    </RelativeLayout>
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCitySuggestions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:visibility="gone" />

                <!-- 出生地位置資訊卡片 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cvBirthplaceInf"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:visibility="visible"
                    app:cardBackgroundColor="@color/very_light_grey"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <!-- 位置圖示 -->
                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/baseline_location_on_24" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="地理位置資訊"
                                android:textColor="@color/colorPrimary"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                            <!-- 經緯度顯示 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="經緯度："
                                    android:textColor="@color/primary_text"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/tvBirthplaceCoordinates"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="--"
                                    android:textColor="@color/colorPrimaryDark"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- 時區顯示 -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_edit_text_improved"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginEnd="6dp"
                                android:src="@drawable/ic_baseline_access_time_24" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="時區："
                                android:textColor="@color/primary_text"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tvBirthplaceTimezone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="--"
                                android:textColor="@color/colorPrimaryDark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/layoutResidenceSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:padding="0dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical">

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:hint="@string/please_select_place_of_residence"
                            android:textColorHint="@color/primary_text"
                            app:backgroundTint="@color/colorPrimary"
                            app:boxStrokeColor="@color/colorPrimary"
                            app:boxStrokeWidth="1dp"
                            app:hintTextAppearance="@style/ErrorTextAppearance"
                            app:hintTextColor="@color/colorPrimary"
                            app:startIconDrawable="@drawable/ic_round_location_on_24"
                            app:startIconTint="@color/colorPrimary">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etResidenceCity"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:importantForAutofill="no"
                                android:inputType="text"
                                android:ellipsize="end"
                                android:textColor="@color/colorPrimary"
                                android:textSize="16sp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <ProgressBar
                            android:id="@+id/progressBarResidence"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_centerInParent="true"
                            android:visibility="gone" />

                        <RelativeLayout
                            android:id="@+id/rltMapResidence"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_toStartOf="@+id/rltSearchResidence">

                            <ImageButton
                                android:id="@+id/btnResidenceMap"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_gps_button"
                                android:contentDescription="地圖"
                                android:src="@drawable/ic_map" />

                            <ProgressBar
                                android:id="@+id/progressBarMapResidence"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_centerInParent="true"
                                android:visibility="gone" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rltSearchResidence"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_toStartOf="@+id/rltGPSResidence">

                            <ImageButton
                                android:id="@+id/btnSearchResidence"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_gps_button"
                                android:contentDescription="搜尋"
                                android:src="@drawable/ic_baseline_search_24" />

                            <ProgressBar
                                android:id="@+id/progressBarSearchResidence"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_centerInParent="true"
                                android:visibility="gone" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/rltGPSResidence"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_marginTop="12dp"
                            android:layout_marginEnd="2dp">

                            <ImageButton
                                android:id="@+id/btnResidenceGPS"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_gps_button"
                                android:contentDescription="GPS定位"
                                android:src="@drawable/ic_gps" />

                            <ProgressBar
                                android:id="@+id/progressBarGetCurrentLocationResidence"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_centerInParent="true"
                                android:visibility="gone" />

                        </RelativeLayout>

                    </RelativeLayout>


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvResidenceCitySuggestions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:visibility="gone" />

                <!-- 出生地位置資訊卡片 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cvResidenceInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:visibility="visible"
                    app:cardBackgroundColor="@color/very_light_grey"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <!-- 位置圖示 -->
                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/baseline_location_on_24" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="地理位置資訊"
                                android:textColor="@color/colorPrimary"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                            <!-- 經緯度顯示 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="經緯度："
                                    android:textColor="@color/primary_text"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/tvResidenceCoordinates"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="--"
                                    android:textColor="@color/colorPrimaryDark"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- 時區顯示 -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_edit_text_improved"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="8dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginEnd="6dp"
                                android:src="@drawable/ic_baseline_access_time_24" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="時區："
                                android:textColor="@color/primary_text"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tvResidenceTimezone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="--"
                                android:textColor="@color/colorPrimaryDark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text="@string/classify"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chipGroup"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    app:singleLine="true"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_1"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/self" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_2"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/family" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_3"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/friend" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_4"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/schoolmate" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_5"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/ditto" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_6"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/relatives" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_7"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/celebrity" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_8"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false"
                        android:text="@string/other" />
                </com.google.android.material.chip.ChipGroup>
            </HorizontalScrollView>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/notes"
                android:textColorHint="@color/primary_text"
                app:backgroundTint="@color/colorPrimary"
                app:boxStrokeColor="@color/colorPrimary"
                app:boxStrokeWidth="1dp"
                app:hintTextAppearance="@style/ErrorTextAppearance"
                app:hintTextColor="@color/colorPrimary"
                app:startIconDrawable="@drawable/ic_baseline_edit_24"
                app:startIconTint="@color/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etNotes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top|start"
                    android:importantForAutofill="no"
                    android:inputType="textMultiLine"
                    android:lines="3"
                    android:maxLines="10"
                    android:minLines="3"
                    android:scrollbars="vertical"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btSave"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/btn_primary_dark"
                android:text="@string/save"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</com.google.android.material.card.MaterialCardView>