<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="3dp"
    android:layout_marginVertical="3dp"
    card_view:cardBackgroundColor="@color/white"
    card_view:cardCornerRadius="0dp"
    card_view:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:orientation="horizontal">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="start"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPlanetName"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                tools:text="太陽" />

            <TextView
                android:id="@+id/tvSignName"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                tools:text="水瓶座" />

            <TextView
                android:id="@+id/tvSignDegree"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:gravity="center"
                tools:text="24°29" />
        </androidx.appcompat.widget.LinearLayoutCompat>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>