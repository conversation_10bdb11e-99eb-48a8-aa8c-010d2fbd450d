<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="10dp"
        android:layout_marginVertical="10dp"
        card_view:cardBackgroundColor="@color/colorPrimaryBlue"
        card_view:cardCornerRadius="@dimen/cardCornerRadius"
        card_view:cardElevation="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvName"
                style="@style/ItemTextStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:paddingHorizontal="20dp"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="@string/natal" />

            <TextView
                android:id="@+id/tvDescription"
                style="@style/ItemTextStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:gravity="start"
                android:paddingHorizontal="20dp"
                android:textSize="14sp"
                android:textStyle="normal"
                tools:text="出生時的命盤，從本命盤上可以看到一生的運勢概況和性格特徵，是各個人生領域的基本情況。" />

            <LinearLayout
                android:id="@+id/lltBottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                android:paddingVertical="10dp">

                <TextView
                    android:id="@+id/tvSource"
                    style="@style/ItemTextStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="@string/wikipedia" />

                <TextView
                    android:id="@+id/tvMore"
                    style="@style/ItemTextStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="查看星盤"
                    android:textColor="@color/colorAccent"
                    android:textSize="14sp"
                    android:textStyle="normal" />
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
</LinearLayout>