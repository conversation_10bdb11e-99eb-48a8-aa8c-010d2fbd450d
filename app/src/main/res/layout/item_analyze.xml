<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="2.5dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="10dp"
        android:layout_marginVertical="2.5dp"
        card_view:cardBackgroundColor="@color/colorPrimaryBlue"
        card_view:cardCornerRadius="@dimen/cardCornerRadius"
        card_view:cardElevation="0dp"
        card_view:contentPadding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="9"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="0dp">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/ItemTextStyle_16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                tools:text="行星星座" />

            <TextView
                android:id="@+id/tvCondition"
                style="@style/ItemTextStyle_16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                tools:text="金星牡羊、狮子、天蠍、射手、金牛座" />

            <TextView
                android:id="@+id/tvDescribe"
                style="@style/ItemTextStyle_16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                tools:text="美女金星落在強勢星座" />

            <TextView
                android:id="@+id/tvResult"
                style="@style/ItemTextStyle_16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:textColor="@color/colorAccent"
                tools:text="金星牡羊座" />

            <TextView
                android:id="@+id/tvScore"
                style="@style/ItemTextStyle_16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:textColor="@color/colorAccent"
                tools:text="5分" />
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</RelativeLayout>