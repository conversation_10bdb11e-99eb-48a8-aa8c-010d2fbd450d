<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragment.report.ChartFragment">

    <Spinner
        android:id="@+id/spHouseSystem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp"
        android:backgroundTint="@color/colorPrimary"
        android:dropDownSelector="@color/colorAccent"
        android:entries="@array/houseSystem"
        android:padding="5dp"
        android:textColor="@color/colorPrimary"
        android:textSize="14sp"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/lltChart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/spHouseSystem"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spHouseSystem">

        <TextView
            android:id="@+id/tvTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:textColor="@color/primary_text"
            android:textSize="16sp"
            android:visibility="gone"
            tools:text="2022/08/30" />

        <RelativeLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.one.astrology.ui.view.DrawView
                    android:id="@+id/drawView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@color/white"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.one.astrology.ui.view.DrawView
                    android:id="@+id/drawViewNew"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@color/white"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:id="@+id/drawContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white" />

            <androidx.core.widget.ContentLoadingProgressBar
                android:id="@+id/loadingProgress"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true" />

            <TextView
                android:id="@+id/tvEmptyDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="尚無資料"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />
        </RelativeLayout>

    </LinearLayout>

    <Button
        android:id="@+id/btAuto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/lltChart"
        android:layout_alignParentEnd="true"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="10dp"
        android:text="自動推運"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabTheme"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:contentDescription="@string/add"
        android:src="@drawable/ic_theme"
        android:visibility="visible"
        app:backgroundTint="@color/colorAccent"
        app:backgroundTintMode="add"
        app:behavior_autoHide="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:rippleColor="@color/white"
        app:tint="@android:color/white"
        tools:visibility="visible" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabSave"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:contentDescription="@string/add"
        android:src="@drawable/baseline_save_24"
        android:visibility="gone"
        app:backgroundTint="@color/colorAccent"
        app:backgroundTintMode="add"
        app:behavior_autoHide="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rippleColor="@color/white"
        app:tint="@android:color/white"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/lltA"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/lltB"
        android:layout_marginHorizontal="10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline1">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrowLeft"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.7"
            android:gravity="center"
            android:tint="@color/colorPrimary"
            app:srcCompat="@drawable/baseline_keyboard_arrow_left_24" />

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="2.2"
            android:hint="出生時間"
            android:textColorHint="@color/colorPrimary"
            app:backgroundTint="@color/colorPrimary"
            app:boxCornerRadiusBottomEnd="5dp"
            app:boxCornerRadiusBottomStart="5dp"
            app:boxCornerRadiusTopEnd="5dp"
            app:boxCornerRadiusTopStart="5dp"
            app:boxStrokeColor="@color/colorPrimaryDark"
            app:boxStrokeWidth="0dp"
            app:endIconTint="@color/colorPrimary"
            app:errorEnabled="false"
            app:hintEnabled="false"
            app:startIconTint="@color/colorPrimary">

            <AutoCompleteTextView
                android:id="@+id/tvBirthday"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:focusable="false"
                android:gravity="center"
                android:importantForAutofill="no"
                android:inputType="text"
                android:labelFor="@+id/etDegree120"
                android:paddingVertical="10dp"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                tools:ignore="LabelFor"
                tools:text="2011/03/21 10:55" />
        </com.google.android.material.textfield.TextInputLayout>

        <Spinner
            android:id="@+id/spinner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="5dp"
            android:layout_marginTop="0sp"
            android:layout_weight="1.8"
            android:backgroundTint="@color/colorPrimary"
            android:dropDownSelector="@color/colorAccent"
            android:entries="@array/spinnerValue"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrowRight"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.7"
            android:gravity="center"
            android:tint="@color/colorPrimary"
            app:srcCompat="@drawable/baseline_keyboard_arrow_right_24" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lltB"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lltA">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrowLeftB"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.7"
            android:gravity="center"
            android:tint="@color/colorPrimary"
            app:srcCompat="@drawable/baseline_keyboard_arrow_left_24" />

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="2.2"
            android:hint="出生時間"
            android:textColorHint="@color/colorPrimary"
            app:backgroundTint="@color/colorPrimary"
            app:boxCornerRadiusBottomEnd="5dp"
            app:boxCornerRadiusBottomStart="5dp"
            app:boxCornerRadiusTopEnd="5dp"
            app:boxCornerRadiusTopStart="5dp"
            app:boxStrokeColor="@color/colorPrimaryDark"
            app:boxStrokeWidth="0dp"
            app:endIconTint="@color/colorPrimary"
            app:errorEnabled="false"
            app:hintEnabled="false"
            app:startIconTint="@color/colorPrimary">

            <AutoCompleteTextView
                android:id="@+id/tvBirthdayB"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:focusable="false"
                android:gravity="center"
                android:importantForAutofill="no"
                android:inputType="text"
                android:labelFor="@+id/etDegree120"
                android:paddingVertical="10dp"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                tools:text="1987/09/10 08:30" />
        </com.google.android.material.textfield.TextInputLayout>

        <Spinner
            android:id="@+id/spinnerB"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="5dp"
            android:layout_marginTop="0sp"
            android:layout_weight="1.8"
            android:backgroundTint="@color/colorPrimary"
            android:dropDownSelector="@color/colorAccent"
            android:entries="@array/spinnerValue"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrowRightB"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.7"
            android:gravity="center"
            android:tint="@color/colorPrimary"
            app:srcCompat="@drawable/baseline_keyboard_arrow_right_24" />
    </LinearLayout>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.85" />
</androidx.constraintlayout.widget.ConstraintLayout>