<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:paddingVertical="1dp"
    card_view:cardCornerRadius="@dimen/cardCornerRadius"
    card_view:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="0dp">

        <TextView
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="與"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPlanetSymbol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:fontFamily="@font/astro_one_font"
                android:paddingHorizontal="5dp"
                android:textColor="@color/red"
                android:textSize="14sp"
                tools:text="%" />

            <TextView
                android:id="@+id/tvPlanetName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginHorizontal="2dp"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                tools:text="太陽" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <TextView
            android:id="@+id/tvAspectType"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            tools:text="合" />


        <TextView
            android:id="@+id/tvDirection"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:visibility="visible"
            tools:text="入相" />

        <TextView
            android:id="@+id/tvOrb"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:visibility="visible"
            tools:text="12°21" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>