<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimaryDark"
    tools:context=".ui.activity.SplashActivity">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_centerHorizontal="true"
            android:contentDescription="@null"
            android:src="@drawable/logo" />

        <TextView
            android:id="@+id/tvLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/ivLogo"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="0dp"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="@android:style/Widget.Material.ProgressBar.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvLogo"
            android:layout_centerHorizontal="true"
            android:layout_margin="10dp"
            android:indeterminateTint="@color/white"
            android:indeterminateTintMode="src_atop"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </RelativeLayout>


    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>