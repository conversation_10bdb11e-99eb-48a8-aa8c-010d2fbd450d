<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="20dp"
    android:padding="20dp"
    app:cardCornerRadius="@dimen/cardCornerRadius"
    app:cardElevation="20dp"
    tools:context=".ui.fragment.dialog.BirthDataDialogFragment">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="20dp"
        android:paddingEnd="20dp"
        android:paddingBottom="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="天象盤資訊"
            android:textColor="@color/colorPrimary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:hint="請選擇日期"
            android:textColorHint="@color/primary_text"
            app:backgroundTint="@color/colorPrimary"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="1dp"
            app:endIconTint="@color/colorPrimary"
            app:hintTextAppearance="@style/ErrorTextAppearance"
            app:hintTextColor="@color/colorPrimary"
            app:startIconDrawable="@drawable/ic_baseline_date_range_24"
            app:startIconTint="@color/colorPrimary">

            <AutoCompleteTextView
                android:id="@+id/tvDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:gravity="start"
                android:importantForAutofill="no"
                android:inputType="text"
                android:labelFor="@+id/etDegree120"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- 地點選擇區域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:hint="@string/please_select_your_place_of_birth"
                    android:textColorHint="@color/primary_text"
                    app:backgroundTint="@color/colorPrimary"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:boxStrokeWidth="1dp"
                    app:hintTextAppearance="@style/ErrorTextAppearance"
                    app:hintTextColor="@color/colorPrimary"
                    app:startIconDrawable="@drawable/ic_round_location_on_24"
                    app:startIconTint="@color/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCityName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:importantForAutofill="no"
                        android:inputType="text"
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerInParent="true"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/rltMap"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_toStartOf="@+id/rltSearch">

                    <ImageButton
                        android:id="@+id/btnMap"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="@drawable/bg_gps_button"
                        android:contentDescription="地圖"
                        android:src="@drawable/ic_map" />

                    <ProgressBar
                        android:id="@+id/progressBarMap"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_centerInParent="true"
                        android:visibility="gone" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rltSearch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_toStartOf="@+id/rltGPS">

                    <ImageButton
                        android:id="@+id/btnSearch"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="@drawable/bg_gps_button"
                        android:contentDescription="搜尋"
                        android:src="@drawable/ic_baseline_search_24" />

                    <ProgressBar
                        android:id="@+id/progressBarSearch"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_centerInParent="true"
                        android:visibility="gone" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rltGPS"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="2dp">

                    <ImageButton
                        android:id="@+id/btnGetCurrentLocation"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="@drawable/bg_gps_button"
                        android:contentDescription="GPS定位"
                        android:src="@drawable/ic_gps" />

                    <ProgressBar
                        android:id="@+id/progressBarGetCurrentLocation"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_centerInParent="true"
                        android:visibility="gone" />

                </RelativeLayout>

            </RelativeLayout>
        </LinearLayout>

        <!-- 位置資訊卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cvBirthplaceInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:visibility="visible"
            app:cardBackgroundColor="@color/very_light_grey"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="12dp">

                <!-- 位置圖示 -->
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/baseline_location_on_24"/>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="地理位置資訊"
                        android:textColor="@color/colorPrimary"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <!-- 經緯度顯示 -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="經緯度："
                            android:textColor="@color/primary_text"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvCoordinates"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textColor="@color/colorPrimaryDark"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 時區顯示 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_edit_text_improved"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/ic_baseline_access_time_24"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="時區："
                        android:textColor="@color/primary_text"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tvTimezone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textColor="@color/colorPrimaryDark"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>



        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btSave"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/btn_primary_dark"
            android:text="確定"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>


</com.google.android.material.card.MaterialCardView>