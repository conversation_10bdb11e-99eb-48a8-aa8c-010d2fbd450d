<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nestedScrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.fragment.bank.DataBankFragment">

    <LinearLayout
        android:id="@+id/llt"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:paddingBottom="0dp">

            <com.one.astrology.ui.view.DrawView
                android:id="@+id/drawView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.one.astrology.ui.view.DrawView
                android:id="@+id/drawViewNew"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <FrameLayout
                android:id="@+id/drawContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:background="@color/white" />

            <androidx.core.widget.ContentLoadingProgressBar
                android:id="@+id/loadingProgress"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabChart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="-40dp"
                android:contentDescription="@null"
                android:src="@drawable/baseline_pie_chart_outline_24"
                app:backgroundTint="@color/colorAccent"
                app:backgroundTintMode="add"
                app:behavior_autoHide="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/drawView"
                app:rippleColor="@color/white"
                app:tint="@android:color/white" />

            <LinearLayout
                android:id="@+id/lltTop"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/drawViewNew">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="名稱 : "
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:lines="1"
                        android:paddingHorizontal="5dp"
                        android:textColor="@color/colorTextBlack"
                        android:textSize="16sp"
                        tools:text="張大成" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="性別 : "
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvGender"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:lines="1"
                        android:paddingHorizontal="5dp"
                        android:textColor="@color/colorTextBlack"
                        android:textSize="16sp"
                        android:textStyle="normal"
                        tools:text="男" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="時間 : "
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvBirthday"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingHorizontal="5dp"
                        android:textColor="@color/colorTextBlack"
                        android:textSize="16sp"
                        tools:text="1990/11/23 11:34" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="0dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="出生地 : "
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvPlace"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingHorizontal="5dp"
                        android:textColor="@color/colorTextBlack"
                        android:textSize="16sp"
                        tools:text="Pairs France" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="0dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="緯度 : "
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvLatitude"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingHorizontal="5dp"
                        android:textColor="@color/colorTextBlack"
                        android:textSize="16sp"
                        tools:text="22.12345678" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="經度 : "
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvLongitude"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingHorizontal="5dp"
                        android:textColor="@color/colorTextBlack"
                        android:textSize="16sp"
                        tools:text="121.556568989899" />
                </LinearLayout>
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvBiographyTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="10dp"
            android:text="傳記"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="visible" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvBiography"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="5dp"
                android:textColor="@color/colorTextBlack"
                android:textSize="14sp"
                android:textStyle="normal"
                tools:text="@tools:sample/lorem/random" />

            <ProgressBar
                android:id="@+id/progressBarBiography"
                style="@android:style/Widget.Material.ProgressBar.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/tvBiography"
                android:layout_alignBottom="@id/tvBiography"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center"
                android:indeterminateTintMode="src_atop"
                android:visibility="gone"
                tools:visibility="visible" />
        </RelativeLayout>


        <TextView
            android:id="@+id/tvCategoriesTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="10dp"
            android:text="類別"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="visible" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvCategories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="5dp"
                android:textColor="@color/colorTextBlack"
                android:textSize="14sp"
                android:textStyle="normal"
                android:visibility="visible"
                tools:text="@tools:sample/lorem/random" />

            <ProgressBar
                android:id="@+id/progressBarCategories"
                style="@android:style/Widget.Material.ProgressBar.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/tvCategories"
                android:layout_alignBottom="@id/tvCategories"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center"
                android:indeterminateTintMode="src_atop"
                android:visibility="gone"
                tools:visibility="visible" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tvLink"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:paddingHorizontal="10dp"
            android:paddingVertical="5dp"
            android:text="@string/link"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvWikipedia"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="40dp"
            android:paddingHorizontal="10dp"
            android:paddingVertical="5dp"
            android:text="@string/wikipedia"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>

</androidx.core.widget.NestedScrollView>