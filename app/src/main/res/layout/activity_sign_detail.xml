<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="50dp"
            android:orientation="vertical">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/toolbar"
                    style="@style/Widget.MaterialComponents.Toolbar.Primary"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:menu="@menu/share_menu"
                    app:navigationIcon="@drawable/ic_baseline_menu_24"
                    app:title="本命盤"
                    app:titleTextColor="@color/white" />

            </com.google.android.material.appbar.AppBarLayout>

            <LinearLayout
                android:id="@+id/coordinator_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tab_layout"
                    style="@style/AppTabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/colorPrimaryDark"
                    app:tabBackground="@drawable/tab_background"
                    app:tabContentStart="0dp"
                    app:tabGravity="center"
                    app:tabIndicatorFullWidth="true"
                    app:tabMode="scrollable"
                    app:tabTextAppearance="@style/AppTabTextAppearance" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/viewPager2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior" />
            </LinearLayout>
        </LinearLayout>


        <include
            android:id="@+id/bottomSheet"
            layout="@layout/bottom_sheet_user" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigationView"
        style="@style/Widget.App.NavigationView"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        android:theme="@style/NavigationDrawerStyle"
        app:headerLayout="@layout/nav_header"
        app:itemIconTint="@color/navigation_item_color"
        app:itemTextColor="@color/navigation_item_color"
        app:menu="@menu/menu_drawer"
        app:subheaderColor="@color/white"
        app:subheaderTextAppearance="?attr/textAppearanceTitleSmall" />

</androidx.drawerlayout.widget.DrawerLayout>