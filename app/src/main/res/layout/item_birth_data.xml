<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="5dp"
    android:layout_marginTop="3dp"
    android:layout_marginBottom="3dp"
    android:checkable="true"
    app:cardBackgroundColor="@color/colorPrimary"
    app:cardCornerRadius="@dimen/cardCornerRadius"
    app:cardElevation="3dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center">

        <CheckBox
            android:id="@+id/checkbox"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:background="@color/colorPrimary"
            android:buttonTint="@color/colorAccent"
            android:layoutDirection="rtl"
            android:textColor="@color/colorPrimary" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/ivMore"
            android:layout_toEndOf="@+id/checkbox">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0.5" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline2"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.65" />

            <TextView
                android:id="@+id/tvName"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:lines="1"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/guideline1"
                app:layout_constraintEnd_toEndOf="@+id/guideline2"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="富蘭克林 羅斯福" />

            <TextView
                android:id="@+id/tvTag"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@+id/guideline1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline2"
                tools:text="家人" />


            <TextView
                android:id="@+id/tvTime"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="@+id/guideline2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/guideline1"
                tools:text="1992/07/09 19:00" />

            <TextView
                android:id="@+id/tvBirthplace"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:lines="1"
                android:maxEms="6"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/guideline2"
                app:layout_constraintTop_toTopOf="@+id/guideline1"
                tools:text="台灣高雄市" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/ivMore"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@color/colorPrimary"
            android:contentDescription="@null"
            android:padding="10dp"
            android:src="@drawable/ic_baseline_more_vert_24"
            app:tint="@color/white" />
    </RelativeLayout>

</com.google.android.material.card.MaterialCardView>