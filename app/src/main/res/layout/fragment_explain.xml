<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.fragment.ExplainFragment">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="10dp"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="本命盤 太陽合水星" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tvMore"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="10dp"
            android:gravity="center"
            android:text="@string/more_info"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.core.widget.NestedScrollView>