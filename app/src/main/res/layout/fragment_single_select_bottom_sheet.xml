<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/standard_bottom_sheet"
    style="?attr/bottomSheetStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp"
    app:behavior_hideable="false"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    tools:context=".ui.fragment.bottomSheet.SelectBottomSheetFragment">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/select_users"
        android:textColor="@color/colorPrimary"
        android:textSize="20sp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvTitle"
        android:layout_marginTop="10dp" />


</RelativeLayout>