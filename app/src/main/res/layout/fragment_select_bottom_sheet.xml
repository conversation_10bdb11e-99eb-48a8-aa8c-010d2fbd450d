<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/standard_bottom_sheet"
    style="?attr/bottomSheetStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:behavior_hideable="false"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    tools:context=".ui.fragment.bottomSheet.SelectBottomSheetFragment">

    <RelativeLayout
        android:id="@+id/rltTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:text="@string/select_users"
            android:textColor="@color/colorPrimary"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/tvOK"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/tvTitle"
            android:gravity="end"
            android:paddingHorizontal="10dp"
            android:text="@string/ok"
            android:textColor="@color/colorAccent"
            android:textSize="16sp"
            android:textStyle="normal"
            app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    </RelativeLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/rltTitle"
        android:paddingHorizontal="10dp"
        android:paddingBottom="10dp" />

</RelativeLayout>