<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <fragment
        android:id="@+id/map"
        class="com.google.android.gms.maps.SupportMapFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/bottomSheet"
        layout="@layout/bottom_sheet_map" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/btMyLocation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:layout_marginHorizontal="10dp"
        android:layout_marginTop="80dp"
        android:contentDescription="@null"
        app:backgroundTint="@color/white_t70"
        app:borderWidth="0dp"
        app:elevation="0dp"
        app:pressedTranslationZ="1dp"
        app:rippleColor="@color/transparent"
        app:srcCompat="@drawable/ic_baseline_my_location_24"
        app:tint="@color/colorPrimary" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:hint="請輸入地點名稱"
        android:paddingHorizontal="70dp"
        android:textColorHint="@color/colorPrimary"
        app:backgroundTint="@color/colorPrimary"
        app:boxBackgroundColor="@color/white_t70"
        app:boxCornerRadiusBottomEnd="20dp"
        app:boxCornerRadiusBottomStart="20dp"
        app:boxCornerRadiusTopEnd="20dp"
        app:boxCornerRadiusTopStart="20dp"
        app:boxStrokeColor="@color/white_t70"
        app:boxStrokeWidth="0dp"
        app:endIconTint="@color/colorPrimary"
        app:startIconDrawable="@drawable/ic_baseline_search_24"
        app:startIconTint="@color/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/tvSearchPlace"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp" />
    </com.google.android.material.textfield.TextInputLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>