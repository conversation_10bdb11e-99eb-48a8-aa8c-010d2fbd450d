<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginVertical="5dp"
    card_view:cardBackgroundColor="@color/colorPrimaryBlue"
    card_view:cardCornerRadius="@dimen/cardCornerRadius"
    card_view:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="5dp">

        <TextView
            android:id="@+id/tvSignTitle"
            style="@style/ItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center"
            android:text="行星" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:id="@+id/tvSignName"
                style="@style/ItemTextStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="星座" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:id="@+id/tvHouse"
                style="@style/ItemTextStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="宮位" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvRetrograde"
            style="@style/ItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center"
            android:text="@string/retrograde" />

        <TextView
            android:id="@+id/tvSignTriplicities"
            style="@style/ItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="gone"
            tools:text="水象" />

        <TextView
            android:id="@+id/tvSignQuadruplicities"
            style="@style/ItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="gone"
            tools:text="啟動" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>