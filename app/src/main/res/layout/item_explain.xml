<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="5dp"
        android:layout_marginTop="5dp"
        android:textColor="@color/colorPrimary"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="@tools:sample/full_names"
        tools:visibility="visible" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginVertical="5dp"
        android:layout_marginHorizontal="10dp"
        card_view:cardBackgroundColor="@color/white"
        card_view:cardCornerRadius="@dimen/cardCornerRadius"
        card_view:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                android:textStyle="normal"
                tools:text="@tools:sample/lorem/random"
                tools:visibility="visible" />
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</androidx.appcompat.widget.LinearLayoutCompat>
