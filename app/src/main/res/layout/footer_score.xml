<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginVertical="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:paddingVertical="10dp"
        android:paddingHorizontal="5dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvPositiveScore"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/primary_text"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="正分：50" />


        <TextView
            android:id="@+id/tvNegativeScore"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/primary_text"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="負分：25" />

        <TextView
            android:id="@+id/tvTotalScore"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/primary_text"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="總分：-14" />

    </LinearLayout>

</LinearLayout>