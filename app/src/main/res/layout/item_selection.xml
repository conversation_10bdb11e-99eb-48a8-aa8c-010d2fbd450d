<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:layout_gravity="center_horizontal"
    android:layout_marginVertical="5dp"
    card_view:cardBackgroundColor="@color/colorPrimaryBlue"
    card_view:cardCornerRadius="@dimen/cardCornerRadius">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:paddingVertical="5dp">

        <TextView
            android:id="@+id/tvName"
            style="@style/ItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:textStyle="bold"
            tools:text="One" />

        <TextView
            android:id="@+id/tvBirthday"
            style="@style/ItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center"
            android:visibility="visible"
            tools:text="1982/07/09 18:00" />

        <TextView
            android:id="@+id/tvTag"
            style="@style/ItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center"
            android:visibility="gone"
            tools:text="家人" />

        <CheckBox
            android:id="@+id/checkbox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:buttonTint="@color/colorAccent"
            android:layoutDirection="rtl"
            android:textColor="@color/white"
            android:visibility="visible" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>