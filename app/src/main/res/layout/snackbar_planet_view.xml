<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    card_view:cardCornerRadius="@dimen/cardCornerRadius"
    card_view:cardElevation="5dp"
    card_view:strokeColor="@color/colorPrimary"
    card_view:strokeWidth="1dp">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="10dp"
        android:paddingVertical="10dp">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPlanetSymbol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:fontFamily="@font/astro_one_font"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                tools:text="A" />

            <TextView
                android:id="@+id/tvPlanetName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginHorizontal="2dp"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                tools:text="@tools:sample/full_names" />
        </androidx.appcompat.widget.LinearLayoutCompat>


        <TextView
            android:id="@+id/tvRuler"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            tools:text="@tools:sample/full_names" />

        <TextView
            android:id="@+id/tvSignName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            tools:text="@tools:sample/full_names" />

        <TextView
            android:id="@+id/tvHouse"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            tools:text="@tools:sample/full_names" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:text="相位 : "
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="1dp" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</com.google.android.material.card.MaterialCardView>