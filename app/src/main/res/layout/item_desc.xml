<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="10dp"
    android:layout_marginVertical="5dp"
    card_view:cardBackgroundColor="@color/white"
    card_view:cardCornerRadius="@dimen/cardCornerRadius"
    card_view:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:gravity="start"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="visible"
            tools:text="@tools:sample/lorem"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvSubTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:gravity="start"
            android:textColor="@color/red"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="@tools:sample/lorem"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:textColor="@color/primary_text"
            android:textSize="14sp"
            android:textStyle="normal"
            tools:text="@tools:sample/lorem/random"
            tools:visibility="visible" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>