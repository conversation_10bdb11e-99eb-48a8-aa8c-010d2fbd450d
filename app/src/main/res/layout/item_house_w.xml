<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:paddingVertical="1dp"
    card_view:cardCornerRadius="@dimen/cardCornerRadius"
    card_view:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="0dp">

        <TextView
            android:id="@+id/tvPlanetSymbol"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="0dp"
            android:fontFamily="@font/astro_one_font"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:visibility="visible"
            tools:text="A" />

        <TextView
            android:id="@+id/tvPlanetName"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            tools:text="太陽" />

        <TextView
            android:id="@+id/tvPlanetDegree"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="24°29" />


        <TextView
            android:id="@+id/tvHouseName"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:visibility="visible"
            tools:text="第十宮" />

        <TextView
            android:id="@+id/tvHouseDegree"
            style="@style/ItemTextStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:visibility="visible"
            tools:text="24°29" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>