<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:context=".ui.fragment.bottomSheet.FilterFragment">

    <View
        android:id="@+id/view"
        android:layout_width="50dp"
        android:layout_height="5dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/rectangle_blue" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="@string/filter"
        android:textColor="@color/colorPrimary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@color/divider" />

    <LinearLayout
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:text="日常"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Spinner
            android:id="@+id/spinnerMundane"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="10sp"
            android:layout_weight="1.5"
            android:backgroundTint="@color/colorPrimary"
            android:dropDownSelector="@color/colorAccent"
            android:entries="@array/spinnerValue"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:text="職業"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Spinner
            android:id="@+id/spinnerVocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="10sp"
            android:layout_weight="1.5"
            android:backgroundTint="@color/colorPrimary"
            android:dropDownSelector="@color/colorAccent"
            android:entries="@array/spinnerValue"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:text="值得注意"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Spinner
            android:id="@+id/spinnerNotable"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="10sp"
            android:layout_weight="1.5"
            android:backgroundTint="@color/colorPrimary"
            android:dropDownSelector="@color/colorAccent"
            android:entries="@array/spinnerValue"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lltContain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    </LinearLayout>

    <RadioGroup
        android:id="@+id/radioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:visibility="gone">

        <RadioButton
            android:id="@+id/rbTimeAsc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="false"
            android:text="依照加入時間升序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbTimeDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="false"
            android:text="依照加入時間降序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbBirthdayAsc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照出生時間升序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbBirthdayDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照出生時間降序排序"
            android:textSize="16sp" />


        <RadioButton
            android:id="@+id/rbNameAsc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照名稱升序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbNameDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照名稱降序排序"
            android:textSize="16sp" />
    </RadioGroup>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/lltBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:background="@color/transparent"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:layout_weight="1"
            android:background="@drawable/rectangle_blue"
            android:gravity="center"
            android:padding="5dp"
            android:text="@string/cancel"
            android:textColor="@color/white"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/tvOK"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:layout_weight="1"
            android:background="@drawable/rectangle_blue"
            android:gravity="center"
            android:padding="5dp"
            android:text="@string/ok"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.appcompat.widget.LinearLayoutCompat>