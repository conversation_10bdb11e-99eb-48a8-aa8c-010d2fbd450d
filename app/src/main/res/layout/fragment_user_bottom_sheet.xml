<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bottomSheet"
    style="@style/ModalBottomSheetDialog"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:behavior_hideable="false"
    app:behavior_peekHeight="50dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <View
        android:id="@+id/view"
        android:layout_width="50dp"
        android:layout_height="5dp"
        android:layout_marginTop="5dp"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="星盤基本資訊"
        android:textColor="@color/colorPrimary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@color/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingVertical="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="0dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/ibEdit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:background="@color/transparent"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="5dp"
                    android:src="@drawable/ic_baseline_edit_24"
                    app:tint="@color/colorPrimary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:drawableTint="@color/colorPrimary"
                    android:gravity="center"
                    android:text="主盤"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="名稱 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="張大成12134564654654678798798464546544644546" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="時間 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvBirthday"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="1990/11/23 11:34" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="緯度 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvLatitude"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="22.12345678" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="經度 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvLongitude"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="121.556568989899" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lltRight"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="0dp"
            android:visibility="visible">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/ibEditB"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:background="@color/transparent"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="5dp"
                    android:src="@drawable/ic_baseline_edit_24"
                    app:tint="@color/colorPrimary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:drawableTint="@color/colorPrimary"
                    android:gravity="center"
                    android:text="副盤"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="名稱 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvNameB"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="王曉明121354554454654546544564654654645465445454454" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="時間 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvBirthdayB"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="1990/11/23 11:34" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="緯度 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvLatitudeB"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="22.12345678" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="經度 : "
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvLongitudeB"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    tools:text="121.556568989899" />
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>