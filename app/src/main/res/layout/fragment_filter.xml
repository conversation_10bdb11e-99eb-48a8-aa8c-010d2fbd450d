<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:context=".ui.fragment.bottomSheet.FilterFragment">

    <View
        android:id="@+id/view"
        android:layout_width="50dp"
        android:layout_height="5dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/rectangle_blue" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="@string/sort"
        android:textColor="@color/colorPrimary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@color/divider" />

    <RadioGroup
        android:id="@+id/radioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp">

        <RadioButton
            android:id="@+id/rbTimeAsc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="false"
            android:text="依照加入時間升序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbTimeDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="false"
            android:text="依照加入時間降序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbBirthdayAsc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照出生時間升序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbBirthdayDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照出生時間降序排序"
            android:textSize="16sp" />


        <RadioButton
            android:id="@+id/rbNameAsc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照名稱升序排序"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbNameDesc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="依照名稱降序排序"
            android:textSize="16sp" />
    </RadioGroup>
</androidx.appcompat.widget.LinearLayoutCompat>