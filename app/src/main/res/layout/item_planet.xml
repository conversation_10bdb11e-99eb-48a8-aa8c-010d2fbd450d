<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    android:stateListAnimator="@null"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:rippleColor="@color/colorPrimaryLight"
    app:strokeColor="@color/colorPrimaryLight"
    app:strokeWidth="0.5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivPlanetIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/bg_planet_icon"
            android:contentDescription="@string/planet_icon"
            android:padding="6dp"
            android:src="@drawable/ic_planet"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/tvPlanetName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/checkbox"
            app:layout_constraintStart_toEndOf="@id/ivPlanetIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:buttonTint="@color/colorAccent"
            android:scaleX="1.2"
            android:scaleY="1.2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>