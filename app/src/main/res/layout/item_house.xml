<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="3dp"
    android:layout_marginVertical="3dp"
    card_view:cardBackgroundColor="@color/white"
    card_view:cardCornerRadius="@dimen/cardCornerRadius"
    card_view:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="5dp">

        <TextView
            android:id="@+id/tvIndex"
            style="@style/ItemTextStyleColorPrimary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center"
            tools:text="2宮" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="3"
            android:gravity="start"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvSignName"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                tools:text="水瓶座" />

            <TextView
                android:id="@+id/tvSignDegree"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:gravity="center"
                tools:text="24°29" />
        </androidx.appcompat.widget.LinearLayoutCompat>


        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvRuler"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="冥王星" />

            <TextView
                android:id="@+id/tvRuler2"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:visibility="gone"
                tools:text="火星"
                tools:visibility="visible" />
        </androidx.appcompat.widget.LinearLayoutCompat>


        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFlyInto"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="6"
                android:gravity="center"
                tools:text="射手座6宮" />

            <TextView
                android:id="@+id/tvFlyInto2"
                style="@style/ItemTextStyleColorPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_weight="5"
                android:gravity="center"
                android:visibility="visible"
                tools:text="獅子座1宮"
                tools:visibility="visible" />
        </androidx.appcompat.widget.LinearLayoutCompat>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>