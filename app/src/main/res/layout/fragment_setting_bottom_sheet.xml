<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:paddingHorizontal="20dp"
    android:paddingVertical="20dp"
    app:behavior_hideable="false"
    app:behavior_peekHeight="50dp"
    android:background="@drawable/bottom_sheet_background"
    tools:context=".ui.fragment.bottomSheet.SettingBottomSheetFragment">

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="8dp"
        android:text="選擇操作"
        android:textColor="@color/colorPrimary"
        android:textSize="20sp"
        android:textStyle="bold" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardBilling"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvBilling"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_blue"
            android:gravity="center"
            android:padding="14dp"
            android:text="贊助"
            android:textColor="@color/white"
            android:visibility="gone"
            android:textSize="18sp"
            android:textStyle="bold" />
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardScan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvScan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_blue"
            android:gravity="center"
            android:padding="14dp"
            android:text="掃描星盤"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardParameters"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvParameters"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_blue"
            android:gravity="center"
            android:padding="14dp"
            android:text="@string/astrolabe_parameter_settings"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardShare"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvShare"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_blue"
            android:gravity="center"
            android:padding="14dp"
            android:text="分享"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardAbout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAbout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rectangle_blue"
            android:gravity="center"
            android:padding="14dp"
            android:text="關於"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>