<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".fragment.report.judgment.PlanetSignFragment">

    <TextView
        android:id="@+id/tvSignPosition"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:text="@string/planet_sign"
        android:textColor="@color/colorPrimary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/baseline_expand_less_24" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewSign"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp" />

</LinearLayout>