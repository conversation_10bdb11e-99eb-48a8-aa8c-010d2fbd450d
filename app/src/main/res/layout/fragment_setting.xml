<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.fragment.navigation.SettingFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/lltAccount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivImage"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginVertical="10dp"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/circle"
                tools:srcCompat="@tools:sample/avatars" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/primary_text"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="visible"
                tools:text="名稱" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/primary_text"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="visible"
                tools:text="Email" />

        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="10dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="10dp"
            app:cardElevation="3dp">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingVertical="10dp">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/lltAccountSettings"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvLogin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="5dp"
                        android:drawableStart="@drawable/baseline_account_circle_24"
                        android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                        android:drawablePadding="10dp"
                        android:gravity="start"
                        android:paddingHorizontal="20dp"
                        android:paddingVertical="5dp"
                        android:text="@string/login"
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvLogout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="5dp"
                        android:drawableStart="@drawable/baseline_account_circle_24"
                        android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                        android:drawablePadding="10dp"
                        android:gravity="start"
                        android:paddingHorizontal="40dp"
                        android:paddingVertical="5dp"
                        android:text="@string/logout"
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="5dp"
                        android:background="@color/white_two" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvUpload"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="5dp"
                        android:drawableStart="@drawable/baseline_cloud_upload_24"
                        android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                        android:drawablePadding="10dp"
                        android:gravity="start"
                        android:paddingHorizontal="20dp"
                        android:paddingVertical="5dp"
                        android:text="星盤資料上傳"
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="5dp"
                        android:background="@color/white_two" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/tvDownload"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="5dp"
                        android:drawableStart="@drawable/baseline_cloud_download_24"
                        android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                        android:drawablePadding="10dp"
                        android:gravity="start"
                        android:paddingHorizontal="20dp"
                        android:paddingVertical="5dp"
                        android:text="星盤資料下載"
                        android:textColor="@color/colorPrimary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="5dp"
                        android:background="@color/white_two" />

                </androidx.appcompat.widget.LinearLayoutCompat>


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvBilling"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="5dp"
                    android:drawableStart="@drawable/baseline_monetization_on_24"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="贊助"
                    android:textColor="@color/colorPrimary"
                    android:visibility="gone"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/white_two" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvExportCsv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="5dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:drawableStart="@drawable/ic_export"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="匯出星盤資料"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/white_two" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvImportCsv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="5dp"
                    android:drawableStart="@drawable/ic_import"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="匯入星盤資料"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />


                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/white_two" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvFeedback"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="5dp"
                    android:drawableStart="@drawable/ic_feedback"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="問題回報"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/white_two" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvParameters"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="5dp"
                    android:drawableStart="@drawable/baseline_settings_24"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="@string/astrolabe_parameter_settings"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/white_two" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvScan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="5dp"
                    android:drawableStart="@drawable/baseline_qr_code_scanner_24"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="掃描星盤"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/white_two" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvShare"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableStart="@drawable/baseline_share_24"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="分享"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/white_two" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvAbout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="5dp"
                    android:drawableStart="@drawable/baseline_info_24"
                    android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24_b"
                    android:drawablePadding="10dp"
                    android:gravity="start"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="5dp"
                    android:text="關於"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </androidx.appcompat.widget.LinearLayoutCompat>
        </androidx.cardview.widget.CardView>
    </LinearLayout>

</androidx.core.widget.NestedScrollView>