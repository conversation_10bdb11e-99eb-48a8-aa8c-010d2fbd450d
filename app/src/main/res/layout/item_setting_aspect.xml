<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    android:stateListAnimator="@null"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/colorPrimaryLight"
    app:strokeWidth="0.5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <TextView
            android:id="@+id/tvDegree"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_degree_label"
            android:gravity="center"
            android:padding="8dp"
            android:text="0ﾟ"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvOrbLabel"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvOrbLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="center"
            android:text="容許度"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/etOrb"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/tvDegree"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/etOrb"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:background="@drawable/bg_orb_value"
            android:gravity="center"
            android:padding="10dp"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/cbDegree"
            app:layout_constraintHorizontal_weight="0.8"
            app:layout_constraintStart_toEndOf="@+id/tvOrbLabel"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/cbDegree"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:buttonTint="@color/colorAccent"
            android:layoutDirection="rtl"
            android:scaleX="1.2"
            android:scaleY="1.2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="0.8"
            app:layout_constraintStart_toEndOf="@+id/etOrb"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>