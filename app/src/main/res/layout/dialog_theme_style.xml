<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bottom_sheet_background"
    android:backgroundTint="?android:attr/colorBackgroundFloating"
    android:clipToOutline="true"
    android:elevation="8dp"
    android:orientation="vertical"
    android:padding="12dp">

    <!-- 標題 -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:padding="16dp"
        android:text="星盤外觀設定"
        android:textColor="@color/colorPrimary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 分隔線 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/colorOutline" />

    <!-- 風格選擇 -->
    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/styleGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:singleSelection="true">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/rbText"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="經典" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/rbDegree"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="度數" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

    <!-- 分隔線 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/colorOutline"
        android:visibility="gone" />

    <!-- 主題列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/themeList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:padding="8dp"
        android:visibility="gone" />

</LinearLayout>
