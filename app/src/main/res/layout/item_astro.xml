<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="10dp"
        android:layout_marginVertical="5dp"
        card_view:cardBackgroundColor="@color/colorPrimaryBlue"
        card_view:cardCornerRadius="@dimen/cardCornerRadius"
        card_view:cardElevation="5dp">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/ItemTextStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginVertical="10dp"
                android:layout_toStartOf="@id/ivAdd"
                android:ellipsize="end"
                android:gravity="start"
                android:lines="1"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="@tools:sample/lorem" />

            <ProgressBar
                android:id="@+id/progressBarTitle"
                style="@android:style/Widget.Material.ProgressBar.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/tvTitle"
                android:layout_alignBottom="@id/tvTitle"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center"
                android:indeterminateTintMode="src_atop"
                android:visibility="gone"
                tools:visibility="gone" />

            <TextView
                android:id="@+id/tvContent"
                style="@style/ItemTextStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tvTitle"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="10dp"
                android:ellipsize="end"
                android:gravity="start"
                android:lines="3"
                android:textSize="16sp"
                android:textStyle="normal"
                tools:text="@tools:sample/lorem/random" />

            <ProgressBar
                android:id="@+id/progressBar"
                style="@android:style/Widget.Material.ProgressBar.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_alignTop="@id/tvContent"
                android:layout_alignBottom="@id/tvContent"
                android:layout_gravity="center"
                android:indeterminateTintMode="src_atop"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/lltBottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tvContent"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvSource"
                    style="@style/ItemTextStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2.5"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="3"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="@tools:sample/lorem" />

                <TextView
                    android:id="@+id/tvMore"
                    style="@style/ItemTextStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="end"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:text="@string/more"
                    android:textColor="@color/colorAccent"
                    android:textSize="14sp"
                    android:textStyle="normal" />
            </LinearLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivAdd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:padding="10dp"
                android:src="@drawable/ic_baseline_add_24"
                android:visibility="visible"
                tools:visibility="visible" />
        </RelativeLayout>
    </com.google.android.material.card.MaterialCardView>
</LinearLayout>
