<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="3dp"
    android:layout_marginVertical="3dp"
    card_view:cardBackgroundColor="@color/white"
    card_view:cardCornerRadius="@dimen/cardCornerRadius"
    card_view:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="5dp">

        <TextView
            android:id="@+id/tvName"
            style="@style/ItemTextStyleColorPrimary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            tools:text="@tools:sample/first_names" />

        <TextView
            android:id="@+id/tvHouse"
            style="@style/ItemTextStyleColorPrimary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            tools:text="太陽" />


        <TextView
            android:id="@+id/tvTimeString"
            style="@style/ItemTextStyleColorPrimary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:gravity="center"
            tools:text="水瓶" />


        <TextView
            android:id="@+id/tvCreateTime"
            style="@style/ItemTextStyleColorPrimary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="visible"
            tools:text="4" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>