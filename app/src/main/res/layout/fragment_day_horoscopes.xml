<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.fragment.horoscopes.HoroscopesFragment">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCurrentDate"
        style="@style/TextAppearance.AppCompat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold"
        tools:text="10月23日 2022" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardMe"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvCurrentDate"
        android:layout_marginHorizontal="20dp"
        android:layout_marginVertical="10dp"
        android:padding="30dp"
        app:cardBackgroundColor="@color/colorPrimary"
        app:cardCornerRadius="@dimen/cardCornerRadius"
        app:cardElevation="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="One" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvBirthday"
                    style="@style/TextAppearance.AppCompat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="1988/10/17" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvSunSign"
                    style="@style/TextAppearance.AppCompat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="天秤座" />
            </LinearLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSunSignSymbol"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:fontFamily="@font/astro_one_font"
                android:gravity="center"
                android:textColor="@color/colorAccent"
                android:textSize="50sp"
                android:textStyle="bold"
                android:typeface="sans"
                tools:text="a" />
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/cardMe"
        android:layout_marginHorizontal="20dp"
        android:layout_marginVertical="10dp"
        android:padding="30dp"
        app:cardCornerRadius="@dimen/cardCornerRadius"
        app:cardElevation="20dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/lltContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvDateRange"
                    style="@style/TextAppearance.AppCompat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textStyle="bold"
                    tools:text="10月23日 -  11月22日" />

                <LinearLayout
                    android:id="@+id/lltTop"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvColor"
                        style="@style/TextAppearance.AppCompat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="10dp"
                        android:textStyle="bold"
                        tools:text="@string/luckyColor" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvMood"
                        style="@style/TextAppearance.AppCompat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="10dp"
                        android:textStyle="bold"
                        tools:text="@string/mood" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvLuckyNumber"
                        style="@style/TextAppearance.AppCompat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="10dp"
                        android:textStyle="bold"
                        tools:text="@string/luckyNumber" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvLuckyTime"
                        style="@style/TextAppearance.AppCompat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="10dp"
                        android:textStyle="bold"
                        tools:text="@string/luckyTime" />

                </LinearLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCompatibility"
                    style="@style/TextAppearance.AppCompat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/lltTop"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:textStyle="bold"
                    tools:text="@string/compatibility" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvDescription"
                    style="@style/TextAppearance.AppCompat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvCompatibility"
                    android:layout_marginTop="10dp"
                    android:textStyle="bold"
                    tools:text="你沒有覺得這麼浪漫 - 好吧，很長一段時間。 這是否意味着你應該完全放棄並做他們想要你做的事情？" />
            </LinearLayout>

            <TextView
                android:padding="20dp"
                android:visibility="gone"
                android:id="@+id/tvErrorMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="@string/error" />
        </RelativeLayout>


    </com.google.android.material.card.MaterialCardView>


    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:elevation="2dp"
        android:translationZ="90dp" />
</RelativeLayout>