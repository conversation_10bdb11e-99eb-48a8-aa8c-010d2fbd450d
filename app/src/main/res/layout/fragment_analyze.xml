<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.fragment.AnalyzeFragment">

    <LinearLayout
        android:id="@+id/banner_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginVertical="10dp"
        android:gravity="center"
        android:textColor="@color/colorPrimary"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="visible"
        tools:text="高智商的星盤配置" />

    <TextView
        android:id="@+id/tvScore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignBaseline="@+id/tvTitle"
        android:layout_marginVertical="10dp"
        android:layout_marginHorizontal="10dp"
        android:gravity="center"
        android:textColor="@color/colorAccent"
        android:textSize="14sp"
        android:textStyle="normal"
        android:visibility="visible"
        tools:text="5分" />

    <TextView
        android:id="@+id/tvHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvTitle"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="start"
        android:textColor="@color/primary_text"
        android:textSize="14sp"
        android:textStyle="normal"
        android:visibility="gone"
        tools:text="當談到「靈魂伴侶業力關係」時，這通常是指一個人在某個時候會遇到另一個人，並且他們兩人之間有一種非常深刻的連接，這種連接可能是跨越時間和空間的。在占星學中，星盤的配置可以提供有關兩個人之間是否有靈魂伴侶業力關係的一些線索。
以下是一些可能與靈魂伴侶業力關係有關的星盤配置的更詳細解釋：
"
        tools:visibility="visible" />

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_below="@+id/tvHeader"
        android:visibility="gone" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/banner_container"
        android:layout_below="@+id/webView"
        android:orientation="vertical"
        android:visibility="gone"
        tools:context=".fragment.AnalyzeFragment">


        <com.google.android.material.bottomappbar.BottomAppBar
            android:id="@+id/bottom_app_bar"
            style="@style/Widget.MaterialComponents.BottomAppBar.Colored"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            app:backgroundTint="@color/colorPrimary"
            app:fabAlignmentMode="end"
            app:fabAnimationMode="scale"
            app:fabCradleMargin="6dp"
            app:fabCradleRoundedCornerRadius="20dp"
            app:fabCradleVerticalOffset="0dp"
            app:hideOnScroll="true"
            app:menu="@menu/menu_bottom_app_bar"
            app:navigationIcon="@drawable/ic_baseline_menu_24" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@null"
            android:src="@drawable/ic_baseline_add_24"
            app:backgroundTint="@color/colorAccent"
            app:layout_anchor="@id/bottom_app_bar"
            app:layout_behavior="com.google.android.material.behavior.HideBottomViewOnScrollBehavior"
            app:rippleColor="@color/white"
            app:srcCompat="@drawable/ic_baseline_add_24"
            app:tint="@android:color/white" />


    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.material.circularreveal.CircularRevealFrameLayout
        android:id="@+id/sheet"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/banner_container"
        android:layout_below="@+id/webView"
        android:layout_alignParentEnd="true"
        android:background="@android:color/transparent"
        app:layout_behavior="@string/fab_transformation_sheet_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginVertical="2.5dp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="visible">

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabSignDetail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="16dp"
                android:contentDescription="@null"
                app:backgroundTint="@color/colorAccent"
                app:fabSize="mini"
                app:layout_behavior="com.google.android.material.behavior.HideBottomViewOnScrollBehavior"
                app:rippleColor="@color/white"
                app:srcCompat="@drawable/ic_baseline_stars_24"
                app:tint="@android:color/white" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabUser"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="10dp"
                android:contentDescription="@null"
                app:backgroundTint="@color/colorAccent"
                app:fabSize="mini"
                app:layout_behavior="com.google.android.material.behavior.HideBottomViewOnScrollBehavior"
                app:rippleColor="@color/white"
                app:srcCompat="@drawable/ic_baseline_swap_horiz_24"
                app:tint="@android:color/white" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fabAdd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="16dp"
                android:contentDescription="@null"
                app:backgroundTint="@color/colorAccent"
                app:behavior_autoHide="true"
                app:layout_behavior="com.google.android.material.behavior.HideBottomViewOnScrollBehavior"
                app:rippleColor="@color/white"
                app:srcCompat="@drawable/ic_baseline_add_24"
                app:tint="@android:color/white" />

        </LinearLayout>

    </com.google.android.material.circularreveal.CircularRevealFrameLayout>
</RelativeLayout>