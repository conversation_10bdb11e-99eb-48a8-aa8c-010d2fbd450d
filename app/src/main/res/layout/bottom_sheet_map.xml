<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bottomSheet"
    style="@style/ModalBottomSheetDialog_70"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="0dp"
    app:behavior_hideable="true"
    app:behavior_peekHeight="300dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <View
        android:id="@+id/view"
        android:layout_width="50dp"
        android:layout_height="5dp"
        android:layout_marginTop="5dp"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="地點資訊"
        android:textColor="@color/colorPrimary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view" />

    <TextView
        android:id="@+id/tvOK"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:paddingVertical="10dp"
        android:text="@string/ok"
        android:textColor="@color/colorAccent"
        android:textSize="16sp"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@color/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="20dp"
        android:paddingVertical="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line">

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="請選擇地點"
            android:textColorHint="@color/colorPrimary"
            android:visibility="gone"
            app:backgroundTint="@color/colorPrimary"
            app:boxStrokeColor="@color/colorPrimary"
            app:boxStrokeWidth="1dp"
            app:endIconTint="@color/colorPrimary"
            app:startIconDrawable="@drawable/ic_round_location_on_24"
            app:startIconTint="@color/colorPrimary">

            <AutoCompleteTextView
                android:id="@+id/tvBirthPlace"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:gravity="start"
                android:inputType="none"
                android:maxLines="2"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>


        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="自訂義"
            android:textStyle="bold"
            android:visibility="gone"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="經度 : "
                android:textColor="@color/colorPrimary"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLongitude"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingHorizontal="5dp"
                android:paddingVertical="5dp"
                android:lines="1"
                android:inputType="numberDecimal|numberSigned"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                tools:text="121.55" />

            <Spinner
                android:id="@+id/spLongitude"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:backgroundTint="@color/transparent"
                android:dropDownSelector="@color/colorAccent"
                android:entries="@array/longitude"
                android:paddingHorizontal="20dp"
                android:paddingVertical="10dp"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="緯度 : "
                android:textColor="@color/colorPrimary"
                android:textSize="16sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLatitude"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:lines="1"
                android:inputType="numberDecimal|numberSigned"
                android:paddingHorizontal="5dp"
                android:paddingVertical="5dp"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                tools:text="22.56" />

            <Spinner
                android:id="@+id/spLatitude"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:backgroundTint="@color/transparent"
                android:dropDownSelector="@color/colorAccent"
                android:entries="@array/latitude"
                android:paddingHorizontal="20dp"
                android:paddingVertical="10dp"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:focusable="true"
            android:paddingHorizontal="0dp"
            android:paddingVertical="5dp"
            android:textColor="@color/colorPrimary"
            android:textSize="16sp"
            android:textStyle="normal"
            android:visibility="visible"
            tools:text="台灣新北市板橋區中山路一段152號" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>