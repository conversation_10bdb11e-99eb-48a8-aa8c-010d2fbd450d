<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingVertical="10dp">

    <ImageView
        android:id="@+id/ivRenew"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:padding="10dp"
        android:src="@drawable/baseline_autorenew_24"
        android:visibility="gone"
        app:tint="@color/colorPrimary"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvHeader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:focusable="false"
        android:text="職業類型"
        android:textStyle="bold"
        android:textColor="@color/colorPrimary"
        android:textSize="18sp" />


    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:padding="10dp"
        android:src="@drawable/baseline_read_more_24"
        android:visibility="gone"
        app:tint="@color/colorPrimary"
        tools:visibility="visible" />
</RelativeLayout>
