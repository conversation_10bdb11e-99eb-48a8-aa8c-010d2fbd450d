<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/homeFragment">

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.one.astrology.ui.fragment.navigation.HomeFragment"
        android:label="fragment_home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/birthDataListFragment"
        android:name="com.one.astrology.ui.fragment.navigation.BirthDataListFragment"
        android:label="fragment_birth_data_list"
        tools:layout="@layout/fragment_birth_data_list" />

    <fragment
        android:id="@+id/birthDataDialogFragment"
        android:name="com.one.astrology.ui.fragment.dialog.BirthDataDialogFragment"
        android:label="fragment_birth_data_dialog"
        tools:layout="@layout/fragment_birth_data_dialog" />

<!--    <fragment-->
<!--        android:id="@+id/markdownFragment"-->
<!--        android:name="com.one.astrology.fragment.unnecessary.MarkdownFragment"-->
<!--        android:label="fragment_markdown"-->
<!--        tools:layout="@layout/fragment_markdown" />-->

    <fragment
        android:id="@+id/matchFragment"
        android:name="com.one.astrology.ui.fragment.unnecessary.MatchFragment"
        android:label="fragment_match"
        tools:layout="@layout/fragment_match" />

    <fragment
        android:id="@+id/articleFragment"
        android:name="com.one.astrology.ui.fragment.navigation.ArticleFragment"
        android:label="fragment_article"
        tools:layout="@layout/fragment_article" />

    <fragment
        android:id="@+id/settingFragment"
        android:name="com.one.astrology.ui.fragment.navigation.SettingFragment"
        android:label="fragment_setting"
        tools:layout="@layout/fragment_setting" />

    <fragment
        android:id="@+id/analyzeFragment"
        android:name="com.one.astrology.ui.fragment.AnalyzeFragment"
        android:label="fragment_analyze"
        tools:layout="@layout/fragment_analyze" />

    <fragment
        android:id="@+id/horoscopesFragment"
        android:name="com.one.astrology.ui.fragment.horoscopes.HoroscopesFragment"
        android:label="fragment_horoscopes"
        tools:layout="@layout/fragment_horoscopes" />

    <fragment
        android:id="@+id/socialFragment"
        android:name="com.one.astrology.ui.fragment.unnecessary.SocialFragment"
        android:label="fragment_social"
        tools:layout="@layout/fragment_social" />

    <fragment
        android:id="@+id/fortuneFragment"
        android:name="com.one.astrology.ui.fragment.navigation.FortuneFragment"
        android:label="fragment_fortune"
        tools:layout="@layout/fragment_fortune" />

    <fragment
        android:id="@+id/dataBankFragment"
        android:name="com.one.astrology.ui.fragment.bank.DataBankFragment"
        android:label="fragment_data_bank"
        tools:layout="@layout/fragment_data_bank" />
    <fragment
        android:id="@+id/dataBankListFragment"
        android:name="com.one.astrology.ui.fragment.bank.DataBankListFragment"
        android:label="DataBankListFragment" />

    <fragment
        android:id="@+id/fateFragment"
        android:name="com.one.astrology.ui.fragment.navigation.FateFragment"
        android:label="fragment_fate" />

    <fragment
        android:id="@+id/natalAnalyzeFragment"
        android:name="com.one.astrology.ui.fragment.analysis.NatalAnalyzeFragment"
        android:label="fragment_natal_analyze" />

    <fragment
        android:id="@+id/synastryAnalyzeFragment"
        android:name="com.one.astrology.ui.fragment.analysis.SynastryAnalyzeFragment"
        android:label="fragment_synastry_analyze" />

    <fragment
        android:id="@+id/transitAnalysisFragment"
        android:name="com.one.astrology.ui.fragment.analysis.TransitAnalysisFragment"
        android:label="fragment_fortune_analysis" />

    <fragment
        android:id="@+id/divinationAnalysisFragment"
        android:name="com.one.astrology.ui.fragment.analysis.DivinationAnalysisFragment"
        android:label="DivinationAnalysisFragment" />

    <fragment
        android:id="@+id/secondaryProgressionsFragment"
        android:name="com.one.astrology.ui.fragment.analysis.SecondaryProgressionsFragment"
        android:label="SecondaryProgressionsFragment" />

    <fragment
        android:id="@+id/solarArcFragment"
        android:name="com.one.astrology.ui.fragment.analysis.SolarArcFragment"
        android:label="SolarArcFragment" />

    <fragment
        android:id="@+id/solarReturnFragment"
        android:name="com.one.astrology.ui.fragment.analysis.SolarReturnFragment"
        android:label="SolarReturnFragment" />

    <fragment
        android:id="@+id/markdownFragment"
        android:name="com.one.astrology.ui.fragment.analysis.MarkdownFragment"
        android:label="MarkdownFragment" />

    <fragment
        android:id="@+id/compositeAnalyzeFragment"
        android:name="com.one.astrology.ui.fragment.analysis.CompositeAnalyzeFragment"
        android:label="CompositeAnalyzeFragment" />

    <fragment
        android:id="@+id/davisonAnalyzeFragment"
        android:name="com.one.astrology.ui.fragment.analysis.DavisonAnalyzeFragment"
        android:label="DavisonAnalyzeFragment" />

    <fragment
        android:id="@+id/compositeProgressionFragment"
        android:name="com.one.astrology.ui.fragment.analysis.CompositeProgressionFragment"
        android:label="CompositeSecondaryProgressionFragment" />

    <fragment
        android:id="@+id/marksAnalyzeFragment"
        android:name="com.one.astrology.ui.fragment.analysis.MarksAnalyzeFragment"
        android:label="MarksAnalyzeFragment" />

    <fragment
        android:id="@+id/firdariaAnalyzeFragment"
        android:name="com.one.astrology.ui.fragment.analysis.FirdariaAnalyzeFragment"
        android:label="FirdariaAnalyzeFragment" />

    <fragment
        android:id="@+id/readingDataListFragment"
        android:name="com.one.astrology.ui.fragment.analysis.ReadingDataListFragment"
        android:label="ReadingDataListFragment" />

</navigation>