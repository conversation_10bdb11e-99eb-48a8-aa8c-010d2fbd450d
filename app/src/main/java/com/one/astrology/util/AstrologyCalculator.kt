package com.one.astrology.util

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import swisseph.SweConst
import swisseph.SweDate
import swisseph.SwissEph
import java.io.File
import java.util.Calendar
import java.util.TimeZone
import kotlin.math.abs

/**
 * 占星計算工具類
 * 基於 SwissEph 庫實現行星位置、相位和宮位的計算
 */
class AstrologyCalculator {

    companion object {
        // 相位類型常量
        const val CONJUNCTION = 0      // 合相 (0°)
        const val SEXTILE = 60         // 六分相 (60°)
        const val SQUARE = 90          // 四分相 (90°)
        const val TRINE = 120          // 三分相 (120°)
        const val OPPOSITION = 180     // 對分相 (180°)
        
        // 默認容許度
        const val DEFAULT_ORB = 5.0
        
        // 默認宮制
        const val DEFAULT_HOUSE_SYSTEM = 'P' // Placidus 胎次法
        
        /**
         * 初始化 SwissEph 物件
         * @param context 上下文
         * @return SwissEph 物件
         */
        fun initSwissEph(context: Context): SwissEph {
            val ephePath = context.filesDir.toString() + File.separator + "ephe"
            return SwissEph(ephePath)
        }
        
        /**
         * 計算儒略日
         * @param year 年
         * @param month 月
         * @param day 日
         * @param hour 小時 (包含分鐘，如 12.5 表示 12:30)
         * @param timeZoneOffset 時區偏移量 (小時)
         * @return 儒略日
         */
        fun calculateJulianDay(year: Int, month: Int, day: Int, hour: Double, timeZoneOffset: Double): Double {
            return SweDate(year, month, day, hour - timeZoneOffset).julDay
        }
        
        /**
         * 從時間戳計算儒略日
         * @param timestamp 時間戳 (毫秒)
         * @param latLng 地理坐標
         * @return 儒略日
         */
        fun calculateJulianDay(timestamp: Long, latLng: LatLng): Double {
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = timestamp
            
            val year = calendar[Calendar.YEAR]
            val month = calendar[Calendar.MONTH] + 1
            val day = calendar[Calendar.DAY_OF_MONTH]
            val hour = calendar[Calendar.HOUR_OF_DAY]
            val minute = calendar[Calendar.MINUTE]
            val dHour = hour + minute / 60.0
            
            // 獲取時區偏移量 (小時)
            val timeZone = TimeZone.getDefault()
            val offsetMillis = timeZone.getOffset(timestamp)
            val offsetHours = offsetMillis / (1000.0 * 60 * 60)
            
            return SweDate(year, month, day, dHour - offsetHours).julDay
        }
        
        /**
         * 計算行星位置
         * @param swissEph SwissEph 物件
         * @param julDay 儒略日
         * @param planetId 行星 ID
         * @return 行星位置數據 [經度, 緯度, 距離, 速度...]
         */
        fun calculatePlanetPosition(swissEph: SwissEph, julDay: Double, planetId: Int): DoubleArray {
            val xx = DoubleArray(6)
            val flags = SweConst.SEFLG_MOSEPH or SweConst.SEFLG_SPEED
            val message = StringBuffer()
            
            swissEph.swe_calc_ut(julDay, planetId, flags, xx, message)
            
            return xx
        }
        
        /**
         * 計算多個行星位置
         * @param swissEph SwissEph 物件
         * @param julDay 儒略日
         * @param planetIds 行星 ID 列表
         * @return 行星位置數據映射 (行星ID -> 位置數據)
         */
        fun calculatePlanetPositions(swissEph: SwissEph, julDay: Double, planetIds: List<Int>): Map<Int, DoubleArray> {
            val result = mutableMapOf<Int, DoubleArray>()
            
            for (planetId in planetIds) {
                result[planetId] = calculatePlanetPosition(swissEph, julDay, planetId)
            }
            
            return result
        }
        
        /**
         * 計算宮位
         * @param swissEph SwissEph 物件
         * @param julDay 儒略日
         * @param latitude 緯度
         * @param longitude 經度
         * @param houseSystem 宮制代碼 (默認為 Placidus)
         * @return 宮位數據 [cusps, ascmc]
         */
        fun calculateHouses(
            swissEph: SwissEph, 
            julDay: Double, 
            latitude: Double, 
            longitude: Double, 
            houseSystem: Char = DEFAULT_HOUSE_SYSTEM
        ): Pair<DoubleArray, DoubleArray> {
            val cusps = DoubleArray(13)
            val ascmc = DoubleArray(10)
            val flags = 0
            
            swissEph.swe_houses(
                julDay,
                flags,
                latitude,
                longitude,
                houseSystem.code,
                cusps,
                ascmc
            )
            
            return Pair(cusps, ascmc)
        }
        
        /**
         * 計算行星所在宮位
         * @param planetLongitude 行星黃經
         * @param cusps 宮頭數組
         * @return 宮位索引 (1-12)
         */
        fun calculatePlanetHouse(planetLongitude: Double, cusps: DoubleArray): Int {
            for (i in 1..12) {
                val nextIndex = if (i == 12) 1 else i + 1
                
                val start = cusps[i]
                val end = cusps[nextIndex]
                
                if (end > start) {
                    if (planetLongitude >= start && planetLongitude < end) {
                        return i
                    }
                } else {
                    if (planetLongitude >= start || planetLongitude < end) {
                        return i
                    }
                }
            }
            
            return 1 // 默認返回第一宮
        }
        
        /**
         * 計算行星所在星座
         * @param longitude 黃經
         * @return 星座索引 (0-11)
         */
        fun calculateZodiacSign(longitude: Double): Int {
            return (longitude / 30).toInt() % 12
        }
        
        /**
         * 計算行星在星座中的度數
         * @param longitude 黃經
         * @return 度數 (0-29.999...)
         */
        fun calculateZodiacDegree(longitude: Double): Double {
            return longitude % 30
        }
        
        /**
         * 計算兩個行星之間的相位
         * @param longitude1 第一個行星的黃經
         * @param longitude2 第二個行星的黃經
         * @return 相位角度 (0-180)
         */
        fun calculateAspect(longitude1: Double, longitude2: Double): Double {
            var angle = abs(longitude1 - longitude2) % 360
            if (angle > 180) angle = 360 - angle
            return angle
        }
        
        /**
         * 判斷相位類型
         * @param angle 相位角度
         * @param orb 容許度
         * @return 相位類型 (CONJUNCTION, SEXTILE, SQUARE, TRINE, OPPOSITION) 或 null
         */
        fun getAspectType(angle: Double, orb: Double = DEFAULT_ORB): Int? {
            return when {
                abs(angle - CONJUNCTION) <= orb -> CONJUNCTION
                abs(angle - SEXTILE) <= orb -> SEXTILE
                abs(angle - SQUARE) <= orb -> SQUARE
                abs(angle - TRINE) <= orb -> TRINE
                abs(angle - OPPOSITION) <= orb -> OPPOSITION
                else -> null
            }
        }
        
        /**
         * 判斷行星是否逆行
         * @param xx 行星位置數據
         * @return 是否逆行
         */
        fun isPlanetRetrograde(xx: DoubleArray): Boolean {
            return xx[3] < 0
        }
        
        /**
         * 獲取行星名稱
         * @param swissEph SwissEph 物件
         * @param planetId 行星 ID
         * @return 行星名稱
         */
        fun getPlanetName(swissEph: SwissEph, planetId: Int): String {
            return swissEph.swe_get_planet_name(planetId)
        }
        
        /**
         * 計算所有行星之間的相位
         * @param planetPositions 行星位置映射 (行星ID -> 位置數據)
         * @param orb 容許度
         * @return 相位列表 (三元組: 行星1 ID, 行星2 ID, 相位類型)
         */
        fun calculateAllAspects(
            planetPositions: Map<Int, DoubleArray>, 
            orb: Double = DEFAULT_ORB
        ): List<Triple<Int, Int, Int>> {
            val aspects = mutableListOf<Triple<Int, Int, Int>>()
            
            val planetIds = planetPositions.keys.toList()
            
            for (i in planetIds.indices) {
                for (j in i + 1 until planetIds.size) {
                    val planet1Id = planetIds[i]
                    val planet2Id = planetIds[j]
                    
                    val longitude1 = planetPositions[planet1Id]!![0]
                    val longitude2 = planetPositions[planet2Id]!![0]
                    
                    val angle = calculateAspect(longitude1, longitude2)
                    val aspectType = getAspectType(angle, orb)
                    
                    if (aspectType != null) {
                        aspects.add(Triple(planet1Id, planet2Id, aspectType))
                    }
                }
            }
            
            return aspects
        }
        
        /**
         * 計算完整的占星數據
         * @param context 上下文
         * @param timestamp 時間戳 (毫秒)
         * @param latitude 緯度
         * @param longitude 經度
         * @param houseSystem 宮制代碼
         * @return 占星數據
         */
        fun calculateAstrologyData(
            context: Context,
            timestamp: Long,
            latitude: Double,
            longitude: Double,
            houseSystem: Char = DEFAULT_HOUSE_SYSTEM
        ): AstrologyData {
            val swissEph = initSwissEph(context)
            val julDay = calculateJulianDay(timestamp, LatLng(latitude, longitude))
            
            // 計算宮位
            val (cusps, ascmc) = calculateHouses(swissEph, julDay, latitude, longitude, houseSystem)
            
            // 計算行星位置
            val planetIds = listOf(
                SweConst.SE_SUN,
                SweConst.SE_MOON,
                SweConst.SE_MERCURY,
                SweConst.SE_VENUS,
                SweConst.SE_MARS,
                SweConst.SE_JUPITER,
                SweConst.SE_SATURN,
                SweConst.SE_URANUS,
                SweConst.SE_NEPTUNE,
                SweConst.SE_PLUTO,
                SweConst.SE_MEAN_NODE
            )
            
            val planetPositions = calculatePlanetPositions(swissEph, julDay, planetIds)
            
            // 計算行星宮位和星座
            val planetHouses = mutableMapOf<Int, Int>()
            val planetSigns = mutableMapOf<Int, Int>()
            val planetDegrees = mutableMapOf<Int, Double>()
            val planetRetrograde = mutableMapOf<Int, Boolean>()
            
            for ((planetId, position) in planetPositions) {
                planetHouses[planetId] = calculatePlanetHouse(position[0], cusps)
                planetSigns[planetId] = calculateZodiacSign(position[0])
                planetDegrees[planetId] = calculateZodiacDegree(position[0])
                planetRetrograde[planetId] = isPlanetRetrograde(position)
            }
            
            // 計算相位
            val aspects = calculateAllAspects(planetPositions)
            
            return AstrologyData(
                julDay,
                cusps.toList(),
                ascmc.toList(),
                planetPositions.mapValues { it.value.toList() },
                planetHouses,
                planetSigns,
                planetDegrees,
                planetRetrograde,
                aspects
            )
        }
    }
    
    /**
     * 占星數據類
     */
    data class AstrologyData(
        val julianDay: Double,
        val cusps: List<Double>,
        val ascmc: List<Double>,
        val planetPositions: Map<Int, List<Double>>,
        val planetHouses: Map<Int, Int>,
        val planetSigns: Map<Int, Int>,
        val planetDegrees: Map<Int, Double>,
        val planetRetrograde: Map<Int, Boolean>,
        val aspects: List<Triple<Int, Int, Int>>
    ) {
        /**
         * 獲取上升點黃經
         */
        fun getAscendant(): Double = ascmc[0]
        
        /**
         * 獲取中天點黃經
         */
        fun getMidheaven(): Double = ascmc[1]
        
        /**
         * 獲取宿命點黃經
         */
        fun getVertex(): Double = ascmc[3]
        
        /**
         * 獲取行星黃經
         */
        fun getPlanetLongitude(planetId: Int): Double? = planetPositions[planetId]?.get(0)
        
        /**
         * 獲取行星所在宮位
         */
        fun getPlanetHouse(planetId: Int): Int? = planetHouses[planetId]
        
        /**
         * 獲取行星所在星座
         */
        fun getPlanetSign(planetId: Int): Int? = planetSigns[planetId]
        
        /**
         * 獲取行星在星座中的度數
         */
        fun getPlanetDegree(planetId: Int): Double? = planetDegrees[planetId]
        
        /**
         * 判斷行星是否逆行
         */
        fun isPlanetRetrograde(planetId: Int): Boolean? = planetRetrograde[planetId]
        
        /**
         * 獲取兩個行星之間的相位
         */
        fun getAspect(planet1Id: Int, planet2Id: Int): Int? {
            return aspects.find { 
                (it.first == planet1Id && it.second == planet2Id) || 
                (it.first == planet2Id && it.second == planet1Id) 
            }?.third
        }
        
        /**
         * 獲取與指定行星形成相位的所有行星
         */
        fun getPlanetAspects(planetId: Int): List<Pair<Int, Int>> {
            return aspects.filter { it.first == planetId || it.second == planetId }
                .map { 
                    if (it.first == planetId) {
                        Pair(it.second, it.third)
                    } else {
                        Pair(it.first, it.third)
                    }
                }
        }
    }
} 