package com.one.astrology.util

import android.app.AlertDialog
import android.content.Context
import android.widget.Toast
import com.one.core.util.LogUtil

/**
 * Helper class to assist with resetting preferences when encryption issues occur
 */
object PreferencesResetHelper {
    
    /**
     * Show a dialog to the user offering to reset preferences
     * This should be called when a fatal encryption error is detected
     */
    fun showResetDialog(context: Context) {
        try {
            AlertDialog.Builder(context)
                .setTitle("設定存取錯誤")
                .setMessage("應用程式無法讀取加密的設定資料。這可能是由於系統更新或應用程式重新安裝造成的。\n\n您想要重設設定資料嗎？這將恢復所有設定為預設值。")
                .setPositiveButton("重設設定") { _, _ ->
                    resetPreferences(context)
                }
                .setNegativeButton("取消") { dialog, _ ->
                    dialog.dismiss()
                    Toast.makeText(context, "應用程式可能無法正常運作，直到問題解決", Toast.LENGTH_LONG).show()
                }
                .setCancelable(false)
                .show()
        } catch (e: Exception) {
            LogUtil.e("Error showing reset dialog: ${e.message}")
        }
    }
    
    /**
     * Reset all preferences and show a confirmation to the user
     */
    private fun resetPreferences(context: Context) {
        try {
            // Reset all preferences
            EncryptedSPUtil.resetPreferences(context)
            
            // Show confirmation
            Toast.makeText(context, "設定已重設，應用程式將正常運作", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            LogUtil.e("Error during preferences reset: ${e.message}")
            Toast.makeText(context, "重設設定時發生錯誤", Toast.LENGTH_LONG).show()
        }
    }
}
