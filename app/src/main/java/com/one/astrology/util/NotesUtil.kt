package com.one.astrology.util

import android.util.Log
import com.one.astrology.data.entity.BirthData
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 備注工具類
 * 提供備注相關的通用功能，可在多個模組中重複使用
 */
object NotesUtil {
    
    /**
     * 驗證備注內容
     * @param notes 備注內容
     * @return 是否有效
     */
    fun isValidNotes(notes: String?): Boolean {
        return !notes.isNullOrBlank() && notes.trim().isNotEmpty()
    }
    
    /**
     * 格式化備注內容
     * 移除多餘的空白字符，統一換行符
     * @param notes 原始備注內容
     * @return 格式化後的備注內容
     */
    fun formatNotes(notes: String?): String {
        if (notes.isNullOrBlank()) return ""
        
        return notes.trim()
            .replace("\r\n", "\n")  // 統一換行符
            .replace("\r", "\n")
            .replace(Regex("\\n{3,}"), "\n\n")  // 最多保留兩個連續換行
    }
    
    /**
     * 獲取備注摘要
     * 用於在列表中顯示備注的簡短版本
     * @param notes 完整備注內容
     * @param maxLength 最大長度，默認50字符
     * @return 備注摘要
     */
    fun getNotesPreview(notes: String?, maxLength: Int = 50): String {
        if (notes.isNullOrBlank()) return ""
        
        val formatted = formatNotes(notes)
        val firstLine = formatted.split("\n").firstOrNull() ?: ""
        
        return if (firstLine.length > maxLength) {
            firstLine.substring(0, maxLength) + "..."
        } else {
            firstLine
        }
    }
    
    /**
     * 為出生資料添加時間戳備注
     * @param birthData 出生資料
     * @param additionalNotes 額外的備注內容
     * @return 包含時間戳的完整備注
     */
    fun addTimestampedNotes(birthData: BirthData, additionalNotes: String): String {
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            .format(Date())
        
        val existingNotes = if (birthData.notes.isNotBlank()) {
            "${birthData.notes}\n\n"
        } else {
            ""
        }
        
        return "${existingNotes}[$timestamp] $additionalNotes"
    }
    
    /**
     * 檢查備注是否包含關鍵字
     * @param notes 備注內容
     * @param keywords 關鍵字列表
     * @param ignoreCase 是否忽略大小寫，默認true
     * @return 是否包含任一關鍵字
     */
    fun containsKeywords(notes: String?, keywords: List<String>, ignoreCase: Boolean = true): Boolean {
        if (notes.isNullOrBlank() || keywords.isEmpty()) return false
        
        val searchText = if (ignoreCase) notes.lowercase() else notes
        return keywords.any { keyword ->
            val searchKeyword = if (ignoreCase) keyword.lowercase() else keyword
            searchText.contains(searchKeyword)
        }
    }
    
    /**
     * 獲取備注統計信息
     * @param notes 備注內容
     * @return 統計信息對象
     */
    fun getNotesStats(notes: String?): NotesStats {
        if (notes.isNullOrBlank()) {
            return NotesStats(0, 0, 0)
        }
        
        val formatted = formatNotes(notes)
        val characterCount = formatted.length
        val wordCount = formatted.split(Regex("\\s+")).filter { it.isNotBlank() }.size
        val lineCount = formatted.split("\n").size
        
        return NotesStats(characterCount, wordCount, lineCount)
    }
    
    /**
     * 備注統計信息數據類
     */
    data class NotesStats(
        val characterCount: Int,
        val wordCount: Int,
        val lineCount: Int
    )
    
    /**
     * 導出備注為純文本格式
     * @param birthData 出生資料
     * @return 格式化的文本內容
     */
    fun exportNotesToText(birthData: BirthData): String {
        val sb = StringBuilder()
        sb.append("=== 出生資料備注 ===\n")
        sb.append("姓名: ${birthData.name}\n")
        sb.append("出生日期: ${birthData.birthdayString}\n")
        sb.append("出生地點: ${birthData.birthplaceArea}\n")
        sb.append("標籤: ${birthData.tag}\n")
        sb.append("創建時間: ${SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date(birthData.createTime))}\n")
        sb.append("\n備注內容:\n")
        sb.append(if (birthData.notes.isNotBlank()) birthData.notes else "無備注")
        sb.append("\n\n=== 結束 ===")
        
        return sb.toString()
    }
    
    /**
     * 記錄備注操作日誌
     * @param operation 操作類型（新增、修改、刪除等）
     * @param birthDataName 出生資料名稱
     * @param notesPreview 備注預覽
     */
    fun logNotesOperation(operation: String, birthDataName: String, notesPreview: String) {
        Log.d("NotesUtil", "$operation - 姓名: $birthDataName, 備注: ${getNotesPreview(notesPreview, 30)}")
    }
}
