package com.one.astrology.util

import android.text.TextUtils
import com.one.astrology.ObjectBox
import com.one.astrology.data.entity.SignTable
import com.one.astrology.data.entity.SignTable_
import io.objectbox.query.QueryBuilder

object SignTableUtil {
    fun getSignTable(sign: String?): SignTable? {
        val boxFor = ObjectBox.get().boxFor(SignTable::class.java)
        return if (TextUtils.isEmpty(sign)) {
            SignTable()
        } else boxFor.query()
            .equal(SignTable_.name, sign, QueryBuilder.StringOrder.CASE_SENSITIVE)
            .build().findFirst()
    }
}