package com.one.astrology.util

import android.Manifest
import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.TransitEvent
import com.one.astrology.ui.activity.SplashActivity
import com.one.core.util.LogUtil as CoreLogUtil

/**
 * 推播通知工具類
 */
object NotificationUtil {

    private const val CHANNEL_ID_TRANSIT = "transit_notifications"
    private const val CHANNEL_NAME_TRANSIT = "行運推播"
    private const val CHANNEL_DESCRIPTION_TRANSIT = "每日行運事件推播通知"
    
    private const val NOTIFICATION_ID_TRANSIT = 1001
    
    /**
     * 初始化通知頻道
     */
    fun createNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // 創建行運推播頻道
            val transitChannel = NotificationChannel(
                CHANNEL_ID_TRANSIT,
                CHANNEL_NAME_TRANSIT,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = CHANNEL_DESCRIPTION_TRANSIT
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            notificationManager.createNotificationChannel(transitChannel)
            CoreLogUtil.d("NotificationUtil: 已創建通知頻道")
        }
    }
    
    /**
     * 顯示行運推播通知
     */
    fun showTransitNotification(
        context: Context,
        title: String,
        content: String,
        events: List<TransitEvent>,
        birthData: BirthData,
        enableSound: Boolean = true,
        enableVibration: Boolean = true
    ) {
        try {
            CoreLogUtil.d("NotificationUtil: 準備發送推播 - 標題: $title, 內容: $content")

            // 檢查通知權限
            val hasPermission = hasNotificationPermission(context)
            CoreLogUtil.d("NotificationUtil: 通知權限檢查結果: $hasPermission")

            if (!hasPermission) {
                CoreLogUtil.e("NotificationUtil: 沒有通知權限，無法發送推播")
                return
            }
            
            // 創建點擊意圖
            val intent = createTransitNotificationIntent(context, events, birthData)
            val pendingIntent = PendingIntent.getActivity(
                context,
                NOTIFICATION_ID_TRANSIT,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // 構建通知
            val notificationBuilder = NotificationCompat.Builder(context, CHANNEL_ID_TRANSIT)
                .setSmallIcon(R.drawable.ic_baseline_stars_24)
                .setContentTitle(title)
                .setContentText(content)
                .setStyle(createBigTextStyle(events))
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
            
            // 設置聲音
            if (enableSound) {
                notificationBuilder.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
            }
            
            // 設置震動
            if (enableVibration) {
                notificationBuilder.setVibrate(longArrayOf(0, 300, 200, 300))
            }
            
            // 發送通知
            val notificationManager = NotificationManagerCompat.from(context)
            val notification = notificationBuilder.build()
            notificationManager.notify(NOTIFICATION_ID_TRANSIT, notification)

            CoreLogUtil.d("NotificationUtil: 已發送行運推播通知，通知ID: $NOTIFICATION_ID_TRANSIT")

        } catch (e: Exception) {
            CoreLogUtil.e("NotificationUtil: 發送推播通知失敗: ${e.message}")
        }
    }
    
    /**
     * 創建大文本樣式
     */
    @SuppressLint("DefaultLocale")
    private fun createBigTextStyle(events: List<TransitEvent>): NotificationCompat.BigTextStyle {
        val bigText = StringBuilder()
        
        events.take(5).forEachIndexed { index, event ->
            if (index > 0) bigText.append("\n")
            bigText.append("• ${event.getEventTitle()}")
            if (event.isAspectEvent() && event.orb > 0) {
                bigText.append(" (${String.format("%.1f", event.orb)}°, ${event.aspectDirection})")
            }
        }
        
        if (events.size > 5) {
            bigText.append("\n還有 ${events.size - 5} 個事件...")
        }
        
        return NotificationCompat.BigTextStyle()
            .bigText(bigText.toString())
            .setBigContentTitle("今日行運事件")
    }
    
    /**
     * 創建通知點擊意圖
     */
    private fun createTransitNotificationIntent(
        context: Context,
        events: List<TransitEvent>,
        birthData: BirthData
    ): Intent {
        return Intent(context, SplashActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            action = "ACTION_TRANSIT_NOTIFICATION"
            putExtra("birth_data", birthData)
            putExtra("event_count", events.size)
            if (events.isNotEmpty()) {
                putExtra("first_event_title", events[0].getEventTitle())
            }
        }
    }
    
    /**
     * 檢查是否有通知權限
     */
    private fun hasNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
    }
    
    /**
     * 取消行運推播通知
     */
    fun cancelTransitNotification(context: Context) {
        try {
            val notificationManager = NotificationManagerCompat.from(context)
            notificationManager.cancel(NOTIFICATION_ID_TRANSIT)
            CoreLogUtil.d("NotificationUtil: 已取消行運推播通知")
        } catch (e: Exception) {
            CoreLogUtil.e("NotificationUtil: 取消推播通知失敗: ${e.message}")
        }
    }
    
    /**
     * 手動發送測試推播
     */
    fun sendTestNotification(
        context: Context,
        birthData: BirthData
    ) {
        val testEvent = TransitEvent().apply {
            eventType = com.one.astrology.data.model.TransitEventType.ASPECT_FORMING
            transitPlanetName = "火星"
            natalPlanetName = "太陽"
            aspectName = "四分相"
            orb = 2.5
            importance = com.one.astrology.data.model.TransitImportance.HIGH
            description = "這是一個測試推播"
            birthDataName = birthData.name
        }
        
        showTransitNotification(
            context = context,
            title = "測試推播 - ${birthData.name}",
            content = "火星與太陽形成四分相",
            events = listOf(testEvent),
            birthData = birthData
        )
    }
}
