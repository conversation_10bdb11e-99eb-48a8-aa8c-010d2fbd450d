package com.one.astrology.util

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.graphics.pdf.PdfDocument
import android.os.Environment
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.drawToBitmap
import com.itextpdf.text.Document
import com.itextpdf.text.Element
import com.itextpdf.text.Font
import com.itextpdf.text.Paragraph
import com.itextpdf.text.pdf.BaseFont
import com.itextpdf.text.pdf.PdfWriter
import com.one.astrology.R
import com.one.astrology.data.InterpretationType
import com.one.astrology.data.PdfData
import com.one.astrology.data.PdfItemData
import com.one.core.util.LogUtil
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.FileWriter
import java.io.IOException


class FileUtil {
    companion object {
        private var pageHeight = 1120
        private var pageWidth = 792
        private val bf: BaseFont =
            BaseFont.createFont("MHei-Medium", "UniCNS-UCS2-H", BaseFont.NOT_EMBEDDED)
        private val fontBig = Font(bf, 28f, Font.BOLD)
        private val fontMedium = Font(bf, 20f, Font.BOLD)

        fun generateTxt(context: Context, pdfData: PdfData) {
            val path =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)

            try {
                // Create a File object for the specified file name
                val file = File(path, pdfData.name + "星盤.txt")
                // Create a FileWriter to write to the file
                val writer = FileWriter(file)

                // Write content to the file
                writer.write(pdfData.name + "\n\n")
                writer.write(context.getString(R.string.planet_sign) + "\n")
                writer(writer, pdfData.pdfItemData, InterpretationType.sign)

                writer.write(context.getString(R.string.planet_house) + "\n")
                writer(writer, pdfData.pdfItemData, InterpretationType.house)

                writer.write(context.getString(R.string.planet_aspect) + "\n")
                writer(writer, pdfData.pdfItemData, InterpretationType.aspect)

                writer.write(context.getString(R.string.flying_star) + "\n")
                writer(writer, pdfData.pdfItemData, InterpretationType.fly)

                writer.close()
                Toast.makeText(context, "檔案建立成功！", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                println("An error occurred: ${e.message}")
                e.message?.let { LogUtil.e(it) }
            }
        }

        private fun writer(
            writer: FileWriter,
            pdfItemDataList: ArrayList<PdfItemData>,
            type: InterpretationType
        ) {
            val signList = pdfItemDataList.filter { it.type == type }
            for (item in signList) {
                if (item.subTitle != null) {
                    writer.write(item.subTitle!! + "\n")
                }
                if (item.descList.isNotEmpty()) {
                    writer.write(item.descList[0] + "\n\n")
                }
            }
        }

        private fun documentAdd(
            document: Document,
            pdfItemDataList: ArrayList<PdfItemData>,
            type: InterpretationType
        ) {
            val fontTitle = Font(bf, 16f, Font.BOLD)
            val fontDesc = Font(bf, 14f, Font.NORMAL)
            val signList = pdfItemDataList.filter { it.type == type }
            for (item in signList) {
                if (item.subTitle != null) {
                    document.add(Paragraph(item.subTitle!! + "\n", fontTitle))
                }
                if (item.descList.isNotEmpty()) {
                    document.add(Paragraph(item.descList[0] + "\n", fontDesc))
                    document.add(Paragraph("\n", fontDesc))
                }
            }
            document.add(Paragraph("\n", fontDesc))
        }

        fun saveViewToJpg(context: Activity, view: View, fileName: String): File {
            val bitmap = view.drawToBitmap()
            val cacheFile = File(context.cacheDir, fileName)
            bitmap.compress(
                Bitmap.CompressFormat.JPEG,
                95,
                FileOutputStream(cacheFile)
            )
            Toast.makeText(context, "儲存星盤成功！", Toast.LENGTH_SHORT).show()
            return cacheFile
        }

        fun generatePdf(context: Context, pdfData: PdfData): File? {
            try {
                val chartName = pdfData.name + " " + context.getString(pdfData.chart.type)
                val title = context.getString(R.string.pdf_title, chartName)
                val fileName = context.getString(R.string.pdf_title, chartName) + ".pdf"

                val cacheFile = File(context.cacheDir, fileName)
                val document = Document()
                PdfWriter.getInstance(
                    document,
                    FileOutputStream(cacheFile)
                )

                document.open()
                val titleParagraph = Paragraph(title + "\n", fontBig)
                titleParagraph.alignment = Element.ALIGN_CENTER
                document.add(titleParagraph)
                document.add(Paragraph("\n", fontMedium))

                val signList = pdfData.pdfItemData.filter { it.type == InterpretationType.sign }
                if (signList.isNotEmpty()) {
                    document.add(
                        Paragraph(
                            context.getString(R.string.planet_sign) + "\n",
                            fontMedium
                        )
                    )
                    documentAdd(document, pdfData.pdfItemData, InterpretationType.sign)
                }

                val houseList = pdfData.pdfItemData.filter { it.type == InterpretationType.house }
                if (houseList.isNotEmpty()) {
                    document.add(
                        Paragraph(
                            context.getString(R.string.planet_house) + "\n",
                            fontMedium
                        )
                    )
                    documentAdd(document, pdfData.pdfItemData, InterpretationType.house)
                }

                val aspectList = pdfData.pdfItemData.filter { it.type == InterpretationType.aspect }
                if (aspectList.isNotEmpty()) {
                    document.add(
                        Paragraph(
                            context.getString(R.string.planet_aspect) + "\n",
                            fontMedium
                        )
                    )
                    documentAdd(document, pdfData.pdfItemData, InterpretationType.aspect)
                }

                val flyList = pdfData.pdfItemData.filter { it.type == InterpretationType.fly }
                if (flyList.isNotEmpty()) {
                    document.add(
                        Paragraph(
                            context.getString(R.string.flying_star) + "\n",
                            fontMedium
                        )
                    )
                    documentAdd(document, pdfData.pdfItemData, InterpretationType.fly)
                }

                document.close()
                Toast.makeText(context, "PDF 檔案建立成功！", Toast.LENGTH_SHORT).show()

                return cacheFile
            } catch (e: Exception) {
                println("An error occurred: ${e.message}")
                e.message?.let { LogUtil.e(it) }
            }
            return null
        }

        fun generatePDF(context: Context, pdfData: PdfData) {
            // creating an object variable
            // for our PDF document.
            val pdfDocument = PdfDocument()

            // two variables for paint "paint" is used
            // for drawing shapes and we will use "title"
            // for adding text in our PDF file.
            val paint = Paint()
            val title = Paint()

            // we are adding page info to our PDF file
            // in which we will be passing our pageWidth,
            // pageHeight and number of pages and after that
            // we are calling it to create our PDF.
            val myPageInfo: PdfDocument.PageInfo? =
                PdfDocument.PageInfo.Builder(pageWidth, pageHeight, 1).create()

            // below line is used for setting
            // start page for our PDF file.
            val myPage: PdfDocument.Page = pdfDocument.startPage(myPageInfo)

            // creating a variable for canvas
            // from our page of PDF.
            val canvas: Canvas = myPage.canvas

            // below line is used to draw our image on our PDF file.
            // the first parameter of our drawbitmap method is
            // our bitmap
            // second parameter is position from left
            // third parameter is position from top and last
            // one is our variable for paint.
//            canvas.drawBitmap(scaledbmp, 56F, 40F, paint)

            // below line is used for adding typeface for
            // our text which we will be adding in our PDF file.
            title.typeface = Typeface.create(Typeface.DEFAULT, Typeface.NORMAL)

            // below line is used for setting text size
            // which we will be displaying in our PDF file.
            title.textSize = 15F

            // below line is sued for setting color
            // of our text inside our PDF file.
            title.color = ContextCompat.getColor(context, R.color.colorPrimary)

            // below line is used to draw text in our PDF file.
            // the first parameter is our text, second parameter
            // is position from start, third parameter is position from top
            // and then we are passing our variable of paint which is title.
            val x = 100F
            var y = 60F
//            canvas.drawText(pdfData.title, 200F, y, title)
            drawText(context, canvas, pdfData.name)
//            val strings: Array<String> = text.split("\n")
            val fm = paint.fontMetrics
            var offsetY = fm.descent - fm.ascent
            for (item in pdfData.pdfItemData) {
                y += 40F
                if (item.subTitle != null) {
//                    canvas.drawText(item.subTitle!!, x, y, title)
                    drawText(context, canvas, item.subTitle!!)
                }
                y += 40F
                if (item.descList.isNotEmpty()) {
                    drawText(context, canvas, item.descList[0])
                }
            }

            title.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            title.color = ContextCompat.getColor(context, R.color.colorPrimary)
            title.textSize = 15F

            // below line is used for setting
            // our text to center of PDF.
            title.textAlign = Paint.Align.CENTER
//            canvas.drawText("This is sample document which we have created.", 396F, 560F, title)

            // after adding all attributes to our
            // PDF file we will be finishing our page.
            pdfDocument.finishPage(myPage)

            // below line is used to set the name of
            // our PDF file and its path.
            val path =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            val file = File(path, "astrology.pdf")

            try {
                // after creating a file name we will
                // write our PDF file to that location.
                pdfDocument.writeTo(FileOutputStream(file))

                // on below line we are displaying a toast message as PDF file generated..
                Toast.makeText(context, "PDF file generated..", Toast.LENGTH_SHORT)
                    .show()
            } catch (e: Exception) {
                // below line is used
                // to handle error
                e.message?.let { LogUtil.e(it) }
                e.printStackTrace()
                // on below line we are displaying a toast message as fail to generate PDF
                Toast.makeText(
                    context,
                    "Fail to generate PDF file..",
                    Toast.LENGTH_SHORT
                ).show()
            }
            // after storing our pdf to that
            // location we are closing our PDF file.
            pdfDocument.close()
        }

        private fun drawText(context: Context, canvas: Canvas, text: String) {
            val rect = Rect(50, 50, 700, 1800)
            val textPaint = TextPaint()
//            textPaint.setARGB(0xFF, 0, 0, 0)
            textPaint.color = ContextCompat.getColor(context, R.color.colorPrimary)
            textPaint.textSize = 20.0f
            textPaint.isAntiAlias = true
            val layout =
                StaticLayout(
                    text,
                    textPaint,
                    rect.width(),
                    Layout.Alignment.ALIGN_NORMAL,
                    1.0f,
                    0.0f,
                    true
                )
            canvas.save()
            canvas.translate(rect.left.toFloat(), rect.top.toFloat())
            layout.draw(canvas)
            canvas.restore()
        }

        fun createPdf(view1: View, pdfName: String) {
            val document: PdfDocument?
            //这个方法只支持api19及以上的系统（android4.4及以上）
            document = PdfDocument()
            // 初始化，我这里的需求是按照A4纸的尺寸生成pdf
            val pageInfo =
                PdfDocument.PageInfo.Builder(view1.width, view1.width * 297 / 210, 1)
                    .create()
            // 这是第一页pdf
            val page = document.startPage(pageInfo)
            view1.draw(page.canvas)
            document.finishPage(page)
            // 这是第二页pdf
//            val page2 = document.startPage(pageInfo)
//            view2.draw(page2.canvas)
//            document.finishPage(page2)
            // 还可以添加更多页
            val file = File("/storage/emulated/0/$pdfName.pdf")
            var outputStream: FileOutputStream? = null
            try {
                outputStream = FileOutputStream(file)
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
            try {
                document.writeTo(outputStream)
            } catch (e: IOException) {
                e.printStackTrace()
            }
            document.close()
        }
    }
}