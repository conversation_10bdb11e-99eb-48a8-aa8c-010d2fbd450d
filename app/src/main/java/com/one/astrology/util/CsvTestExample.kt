package com.one.astrology.util

import com.one.core.util.LogUtil

/**
 * CSV 功能測試範例
 * 用於驗證 CsvUtil 是否正確處理包含逗號的資料
 */
object CsvTestExample {
    
    /**
     * 測試 CSV 匯出和匯入功能
     */
    fun testCsvFunctionality() {
        LogUtil.d("開始測試 CSV 功能")
        
        // 測試資料，包含各種特殊字符
        val testData = listOf(
            listOf("張三", "台北市, 信義區", "25.0337", "121.5644", "true"),
            listOf("李四", "高雄市\"港都\"", "22.6273", "120.3014", "false"),
            listOf("王五", "台中市\n西區", "24.1477", "120.6736", "true"),
            listOf("趙六", "正常地址", "23.5", "120.5", "false")
        )
        
        // 建立 CSV 內容
        val csvContent = StringBuilder()
        csvContent.append(CsvUtil.createCsvHeader("姓名", "地址", "緯度", "經度", "啟用"))
        
        testData.forEach { row ->
            csvContent.append(CsvUtil.createCsvRow(*row.toTypedArray()) + "\n")
        }
        
        LogUtil.d("匯出的 CSV 內容:")
        LogUtil.d(csvContent.toString())
        
        // 模擬匯入過程
        val lines = csvContent.toString().split("\n").filter { it.isNotBlank() }
        val header = lines[0]
        val dataLines = lines.drop(1)
        
        LogUtil.d("解析標題: $header")
        
        // 解析每一行資料
        dataLines.forEachIndexed { index, line ->
            LogUtil.d("解析第 ${index + 1} 行: $line")
            
            val fields = CsvUtil.parseCsvLine(line)
            LogUtil.d("解析結果: $fields")
            
            // 驗證欄位數量
            if (CsvUtil.validateFieldCount(fields, 5)) {
                val name = CsvUtil.getFieldSafely(fields, 0)
                val address = CsvUtil.getFieldSafely(fields, 1)
                val latitude = CsvUtil.parseDoubleSafely(CsvUtil.getFieldSafely(fields, 2))
                val longitude = CsvUtil.parseDoubleSafely(CsvUtil.getFieldSafely(fields, 3))
                val enabled = CsvUtil.parseBooleanSafely(CsvUtil.getFieldSafely(fields, 4))
                
                LogUtil.d("姓名: $name")
                LogUtil.d("地址: $address")
                LogUtil.d("緯度: $latitude")
                LogUtil.d("經度: $longitude")
                LogUtil.d("啟用: $enabled")
                LogUtil.d("---")
            } else {
                LogUtil.e("欄位數量不正確: ${fields.size}")
            }
        }
        
        LogUtil.d("CSV 功能測試完成")
    }
    
    /**
     * 測試特殊字符處理
     */
    fun testSpecialCharacters() {
        LogUtil.d("開始測試特殊字符處理")
        
        val testCases = listOf(
            "正常文字" to "正常文字",
            "包含,逗號" to "\"包含,逗號\"",
            "包含\"雙引號\"" to "\"包含\"\"雙引號\"\"\"",
            "包含\n換行符" to "\"包含\n換行符\"",
            "包含,逗號和\"雙引號\"" to "\"包含,逗號和\"\"雙引號\"\"\""
        )
        
        testCases.forEach { (input, expected) ->
            val result = CsvUtil.escapeField(input)
            val isCorrect = result == expected
            LogUtil.d("輸入: $input")
            LogUtil.d("期望: $expected")
            LogUtil.d("結果: $result")
            LogUtil.d("正確: $isCorrect")
            LogUtil.d("---")
        }
        
        LogUtil.d("特殊字符處理測試完成")
    }
}
