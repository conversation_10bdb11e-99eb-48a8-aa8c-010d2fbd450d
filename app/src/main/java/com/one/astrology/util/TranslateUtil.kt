package com.one.astrology.util

import com.google.mlkit.common.model.DownloadConditions
import com.google.mlkit.nl.languageid.LanguageIdentification
import com.google.mlkit.nl.translate.TranslateLanguage
import com.google.mlkit.nl.translate.Translation
import com.google.mlkit.nl.translate.TranslatorOptions
import com.one.astrology.callback.TranslateCallback
import com.one.core.util.LogUtil
import taobe.tec.jcc.JChineseConvertor

class TranslateUtil {
    companion object {

        fun identifyLanguage(text: String, translateCallback: TranslateCallback) {
            val languageIdentifier = LanguageIdentification.getClient()
            languageIdentifier.identifyLanguage(text)
                .addOnSuccessListener { languageCode ->
                    if (languageCode == "und") {
                        LogUtil.e("Can't identify language.")
                        translate(TranslateLanguage.ENGLISH, text, translateCallback)
                    } else {
//                        LogUtil.i("Language: $languageCode")
                        translate(languageCode, text, translateCallback)
                    }
                }
                .addOnFailureListener {
                    // Model couldn’t be loaded or other internal error.
                    it.message?.let { it1 -> LogUtil.e(it1) }
                    translate(TranslateLanguage.ENGLISH, text, translateCallback)
                }
        }

        private fun translate(
            languageCode: String,
            text: String,
            translateCallback: TranslateCallback
        ) {
            LogUtil.d("languageCode [$languageCode] text [\n$text]")
            val options = TranslatorOptions.Builder()
                .setSourceLanguage(languageCode)// TranslateLanguage.ENGLISH
                .setTargetLanguage(TranslateLanguage.CHINESE)
                .build()
            val translator = Translation.getClient(options)

            val conditions = DownloadConditions.Builder()
//                .requireWifi()
                .build()
            translator.downloadModelIfNeeded(conditions)
                .addOnSuccessListener {
                    translator.translate(text)
                        .addOnSuccessListener { translatedText ->
                            LogUtil.d("translatedText $translatedText")
                            val jChineseConvertor = JChineseConvertor.getInstance()
                            val traditional = jChineseConvertor.s2t(translatedText)
                            translateCallback.onSuccess(traditional)
                        }
                        .addOnFailureListener { exception ->
                            translateCallback.onFailure(exception.message)
                        }
                }
                .addOnFailureListener { exception ->
                    if (exception.message != null) {
                        LogUtil.e(exception.message!!)
                    }
                    translateCallback.onFailure(exception.message)
                }
        }

        fun translate(text: String, translateCallback: TranslateCallback) {
            val options = TranslatorOptions.Builder()
                .setSourceLanguage(TranslateLanguage.ENGLISH)
                .setTargetLanguage(TranslateLanguage.CHINESE)
                .build()
            val translator = Translation.getClient(options)

            val conditions = DownloadConditions.Builder()
//                .requireWifi()
                .build()
            translator.downloadModelIfNeeded(conditions)
                .addOnSuccessListener {
                    translator.translate(text)
                        .addOnSuccessListener { translatedText ->
                            LogUtil.d("translatedText $translatedText")
                            val jChineseConvertor = JChineseConvertor.getInstance()
                            val traditional = jChineseConvertor.s2t(translatedText)
                            translateCallback.onSuccess(traditional)
                        }
                        .addOnFailureListener { exception ->
                            translateCallback.onFailure(exception.message)
                        }
                }
                .addOnFailureListener { exception ->
                    if (exception.message != null) {
                        LogUtil.e(exception.message!!)
                    }
                    translateCallback.onFailure(exception.message)
                }
        }
    }
}