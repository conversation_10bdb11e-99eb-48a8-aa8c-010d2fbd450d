package com.one.astrology.util

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.constant.PrefKey.KEY_AI_MODULE
import com.one.astrology.constant.PrefKey.KEY_CHART_ANIMATION
import com.one.astrology.constant.PrefKey.KEY_FORCED_UPDATE
import com.one.astrology.constant.PrefKey.KEY_HOUSE_SYSTEM
import com.one.astrology.constant.PrefKey.KEY_PLANET_RULER
import com.one.core.util.LogUtil

class EncryptedSPUtil {
    companion object {
        private const val KEY_THEME = "theme"
        private const val KEY_SYMBOL_MODE = "symbol_mode"
        private const val KEY_DAILY_ASTROLOGY_LATITUDE = "daily_astrology_latitude"
        private const val KEY_DAILY_ASTROLOGY_LONGITUDE = "daily_astrology_longitude"
        private const val KEY_DAILY_ASTROLOGY_ADDRESS = "daily_astrology_address"
        private const val KEY_DAILY_ASTROLOGY_LAST_UPDATE = "daily_astrology_last_update"
        private const val PREF_NAME = "secret_shared_prefs"
        private const val FALLBACK_PREF_NAME = "fallback_prefs"
        private var useEncryption = true

        private fun getSecretSharedPref(context: Context): SharedPreferences {
            // If we've already determined encryption isn't working, use fallback immediately
            if (!useEncryption) {
                return getFallbackPreferences(context)
            }

            try {
                val masterKey = MasterKey.Builder(context)
                    .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                    .build()

                return EncryptedSharedPreferences.create(
                    context,
                    PREF_NAME,
                    masterKey,
                    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
                )
            } catch (e: Exception) {
                LogUtil.e("Error creating EncryptedSharedPreferences: ${e.message}")
                // Mark that encryption isn't working so we don't try again
                useEncryption = false
                return getFallbackPreferences(context)
            }
        }

        /**
         * Get fallback preferences when encryption fails
         */
        private fun getFallbackPreferences(context: Context): SharedPreferences {
            val prefs = context.getSharedPreferences(FALLBACK_PREF_NAME, Context.MODE_PRIVATE)

            // Try to migrate data from encrypted preferences if possible
            tryMigrateFromEncrypted(context, prefs)

            return prefs
        }

        /**
         * Attempt to migrate data from encrypted preferences to fallback preferences
         */
        private fun tryMigrateFromEncrypted(context: Context, fallbackPrefs: SharedPreferences) {
            try {
                // Only try migration if fallback is empty
                if (fallbackPrefs.all.isEmpty()) {
                    val masterKey = MasterKey.Builder(context)
                        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                        .build()

                    val encryptedPrefs = EncryptedSharedPreferences.create(
                        context,
                        PREF_NAME,
                        masterKey,
                        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
                    )

                    // Copy all values from encrypted to fallback
                    val editor = fallbackPrefs.edit()

                    for ((key, value) in encryptedPrefs.all) {
                        when (value) {
                            is String -> editor.putString(key, value)
                            is Boolean -> editor.putBoolean(key, value)
                            is Int -> editor.putInt(key, value)
                            is Long -> editor.putLong(key, value)
                            is Float -> editor.putFloat(key, value)
                        }
                    }

                    editor.apply()
                    LogUtil.d("Successfully migrated preferences from encrypted to fallback storage")
                }
            } catch (e: Exception) {
                LogUtil.e("Failed to migrate from encrypted preferences: ${e.message}")
                // Migration failed, but that's okay - we'll use defaults
            }
        }

        fun setChartAnimation(context: Context, data: Boolean) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit().putBoolean(KEY_CHART_ANIMATION, data).apply()
            } catch (e: Exception) {
                LogUtil.e("Error setting chart animation: ${e.message}")
            }
        }

        fun getChartAnimation(context: Context): Boolean {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                return sharedPreferences.getBoolean(KEY_CHART_ANIMATION, false)
            } catch (e: Exception) {
                LogUtil.e("Error getting chart animation: ${e.message}")
                return false // Return default value on error
            }
        }

        fun setPlanetRuler(context: Context, data: Boolean) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit().putBoolean(KEY_PLANET_RULER, data).apply()
            } catch (e: Exception) {
                LogUtil.e("Error setting planet ruler: ${e.message}")
            }
        }

        fun getPlanetRuler(context: Context): Boolean {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                return sharedPreferences.getBoolean(KEY_PLANET_RULER, true)
            } catch (e: Exception) {
                LogUtil.e("Error getting planet ruler: ${e.message}")
                return true // Return default value on error
            }
        }

        fun setHouseSystem(context: Context, data: Char) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit().putString(KEY_HOUSE_SYSTEM, data.toString()).apply()
            } catch (e: Exception) {
                LogUtil.e("Error setting house system: ${e.message}")
            }
        }

        fun getHouseSystem(context: Context): Char {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                return sharedPreferences.getString(KEY_HOUSE_SYSTEM, "P")?.get(0) ?: 'P'
            } catch (e: Exception) {
                LogUtil.e("Error getting house system: ${e.message}")
                return 'P' // Return default value on error
            }
        }

        fun setAIModule(context: Context, data: String) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit().putString(KEY_AI_MODULE, data).apply()
            } catch (e: Exception) {
                LogUtil.e("Error setting AI module: ${e.message}")
            }
        }

        fun getAIModule(context: Context): String {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                return sharedPreferences.getString(KEY_AI_MODULE, "deepseek-r1-distill-llama-70b")
                    ?: "deepseek-r1-distill-llama-70b"
            } catch (e: Exception) {
                LogUtil.e("Error getting AI module: ${e.message}")
                return "deepseek-r1-distill-llama-70b" // Return default value on error
            }
        }
        fun setForcedUpdate(context: Context, data: Boolean) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit().putBoolean(KEY_FORCED_UPDATE, data).apply()
            } catch (e: Exception) {
                LogUtil.e("Error setting forced update: ${e.message}")
            }
        }

        fun getForcedUpdate(context: Context): Boolean {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                return sharedPreferences.getBoolean(KEY_FORCED_UPDATE, false)
            } catch (e: Exception) {
                LogUtil.e("Error getting forced update: ${e.message}")
                return false // Return default value on error
            }
        }

        fun setTheme(context: Context, theme: String) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit().putString(KEY_THEME, theme).apply()
            } catch (e: Exception) {
                LogUtil.e("Error setting theme: ${e.message}")
            }
        }

        fun getTheme(context: Context): String {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                return sharedPreferences.getString(KEY_THEME, "classic") ?: "classic"
            } catch (e: Exception) {
                LogUtil.e("Error getting theme: ${e.message}")
                return "classic" // Return default value on error
            }
        }

        fun setSymbolMode(context: Context, isSymbolMode: Boolean) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit().putBoolean(KEY_SYMBOL_MODE, isSymbolMode).apply()
            } catch (e: Exception) {
                LogUtil.e("Error setting symbol mode: ${e.message}")
            }
        }

        fun getSymbolMode(context: Context): Boolean {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                return sharedPreferences.getBoolean(KEY_SYMBOL_MODE, false)
            } catch (e: Exception) {
                LogUtil.e("Error getting symbol mode: ${e.message}")
                return false // Return default value on error
            }
        }

        /**
         * Reset all preferences and switch to fallback mode
         * This can be called when the app encounters encryption issues
         */
        fun resetPreferences(context: Context) {
            try {
                // Clear fallback preferences
                val fallbackPrefs = context.getSharedPreferences(FALLBACK_PREF_NAME, Context.MODE_PRIVATE)
                fallbackPrefs.edit().clear().apply()

                // Try to clear encrypted preferences if possible
                try {
                    val masterKey = MasterKey.Builder(context)
                        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                        .build()

                    val encryptedPrefs = EncryptedSharedPreferences.create(
                        context,
                        PREF_NAME,
                        masterKey,
                        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
                    )

                    encryptedPrefs.edit().clear().apply()
                } catch (e: Exception) {
                    LogUtil.e("Could not clear encrypted preferences: ${e.message}")
                }

                // Force using fallback preferences
                useEncryption = false

                LogUtil.d("All preferences have been reset")
            } catch (e: Exception) {
                LogUtil.e("Error resetting preferences: ${e.message}")
            }
        }

        /**
         * 儲存今日星象地點
         */
        fun saveDailyAstrologyLocation(context: Context, latLng: LatLng, address: String) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit()
                    .putFloat(KEY_DAILY_ASTROLOGY_LATITUDE, latLng.latitude.toFloat())
                    .putFloat(KEY_DAILY_ASTROLOGY_LONGITUDE, latLng.longitude.toFloat())
                    .putString(KEY_DAILY_ASTROLOGY_ADDRESS, address)
                    .apply()
                LogUtil.d("已儲存今日星象地點: $address (${latLng.latitude}, ${latLng.longitude})")
            } catch (e: Exception) {
                LogUtil.e("儲存今日星象地點時發生錯誤: ${e.message}")
            }
        }

        /**
         * 儲存今日星象最後更新日期
         */
        fun saveDailyAstrologyLastUpdate(context: Context, date: String) {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                sharedPreferences.edit()
                    .putString(KEY_DAILY_ASTROLOGY_LAST_UPDATE, date)
                    .apply()
                LogUtil.d("已儲存今日星象最後更新日期: $date")
            } catch (e: Exception) {
                LogUtil.e("儲存今日星象最後更新日期時發生錯誤: ${e.message}")
            }
        }

        /**
         * 獲取儲存的今日星象地點
         * @return 如果有儲存的地點則返回，否則返回null
         */
        fun getSavedDailyAstrologyLocation(context: Context): Pair<LatLng, String>? {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                val latitude = sharedPreferences.getFloat(KEY_DAILY_ASTROLOGY_LATITUDE, Float.MIN_VALUE)
                val longitude = sharedPreferences.getFloat(KEY_DAILY_ASTROLOGY_LONGITUDE, Float.MIN_VALUE)
                val address = sharedPreferences.getString(KEY_DAILY_ASTROLOGY_ADDRESS, "") ?: ""

                // 檢查是否有有效的儲存值
                if (latitude != Float.MIN_VALUE && longitude != Float.MIN_VALUE && address.isNotEmpty()) {
                    val latLng = LatLng(latitude.toDouble(), longitude.toDouble())
                    LogUtil.d("已讀取儲存的今日星象地點: $address (${latLng.latitude}, ${latLng.longitude})")
                    return Pair(latLng, address)
                }
                return null
            } catch (e: Exception) {
                LogUtil.e("讀取今日星象地點時發生錯誤: ${e.message}")
                return null
            }
        }

        /**
         * 獲取今日星象最後更新日期
         * @return 如果有儲存的日期則返回，否則返回空字符串
         */
        fun getDailyAstrologyLastUpdate(context: Context): String {
            try {
                val sharedPreferences = getSecretSharedPref(context)
                val date = sharedPreferences.getString(KEY_DAILY_ASTROLOGY_LAST_UPDATE, "") ?: ""
                if (date.isNotEmpty()) {
                    LogUtil.d("已讀取今日星象最後更新日期: $date")
                }
                return date
            } catch (e: Exception) {
                LogUtil.e("讀取今日星象最後更新日期時發生錯誤: ${e.message}")
                return ""
            }
        }
    }
}