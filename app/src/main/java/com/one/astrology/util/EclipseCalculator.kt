package com.one.astrology.util

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.model.AstrologyEvent
import com.one.astrology.data.model.AstrologyEventFactory
import com.one.astrology.data.model.EclipseType
import com.one.core.util.LogUtil
import swisseph.SweConst
import swisseph.SwissEph
import java.util.Date
import kotlin.math.abs

/**
 * 專業的日月蝕計算器
 * 基於 Swiss Ephemeris 的蝕相計算功能
 * 參考：https://github.com/krishnact/swisseph/blob/master/src/main/java/de/thmac/Sweclips.java
 * 參考：https://www.astro.com/swisseph/swephprg.htm#_Hlk477326807
 */
object EclipseCalculator {

    private var swissEph: SwissEph? = null

    /**
     * 初始化 Swiss Ephemeris
     */
    private fun initSwissEph(context: Context): SwissEph? {
        if (swissEph == null) {
            try {
                if (EphemerisUtil.initData(context)) {
                    swissEph = SwissEph()
                } else {
                    LogUtil.e("Failed to initialize Swiss Ephemeris")
                    return null
                }
            } catch (e: Exception) {
                LogUtil.e("Error initializing Swiss Ephemeris: ${e.message}")
                return null
            }
        }
        return swissEph
    }

    /**
     * 尋找指定日期附近的月蝕
     */
    private fun findLunarEclipseNear(
        sw: SwissEph,
        julDay: Double,
        location: LatLng
    ): AstrologyEvent? {
        try {
            val serr = StringBuffer()
            val tret = DoubleArray(20)
            val attr = DoubleArray(20)

            // 使用 Swiss Ephemeris 的月蝕計算函數
            // 搜索指定日期前後1天內的月蝕
            val searchFlag = SweConst.SE_ECL_TOTAL or SweConst.SE_ECL_PARTIAL or SweConst.SE_ECL_PENUMBRAL
            val whicheph = SweConst.SEFLG_SWIEPH

            val eclFlag = sw.swe_lun_eclipse_when(julDay - 1.0, whicheph, searchFlag, tret, 0, serr)

            if (eclFlag == SweConst.ERR) {
                LogUtil.e("月蝕計算錯誤: $serr")
                return null
            }

            val eclipseJulDay = tret[0]

            // 檢查蝕相是否在指定日期的±1天內
            if (abs(eclipseJulDay - julDay) <= 1.0) {
                // 計算月蝕詳情
                val howFlag = sw.swe_lun_eclipse_how(eclipseJulDay, whicheph, doubleArrayOf(location.longitude, location.latitude, 0.0), attr, serr)

                if (howFlag == SweConst.ERR) {
                    LogUtil.e("月蝕詳情計算錯誤: $serr")
                    return null
                }

                // 確定月蝕類型
                val eclipseType = when {
                    (eclFlag and SweConst.SE_ECL_TOTAL) != 0 -> EclipseType.TOTAL_LUNAR
                    (eclFlag and SweConst.SE_ECL_PARTIAL) != 0 -> EclipseType.PARTIAL_LUNAR
                    (eclFlag and SweConst.SE_ECL_PENUMBRAL) != 0 -> EclipseType.PENUMBRAL_LUNAR
                    else -> EclipseType.LUNAR_ECLIPSE
                }

                // 食分（eclipse magnitude）
                val magnitude = attr[0]

                return AstrologyEventFactory.createEclipseEvent(
                    date = Date(((eclipseJulDay - 2451545.0) * 86400000).toLong()),
                    eclipseType = eclipseType,
                    magnitude = magnitude
                )
            }

        } catch (e: Exception) {
            LogUtil.e("尋找月蝕時發生錯誤: ${e.message}")
        }

        return null
    }

    /**
     * 計算指定日期的蝕相事件
     */
    fun calculateEclipseForDate(
        context: Context,
        date: Date,
        location: LatLng = LatLng(0.0, 0.0)
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()

        try {
            val sw = initSwissEph(context) ?: return emptyList()
            LogUtil.d("calculateEclipseForDate $date $location")
            val julDay = EphemerisUtil.getJulDay(date.time, location)

            // 檢查當天是否有月蝕
            val lunarEclipse = findLunarEclipseNear(sw, julDay, location)
            if (lunarEclipse != null) {
                events.add(lunarEclipse)
            }

            // 檢查當天是否有日蝕
            val solarEclipse = findSolarEclipseNear(sw, julDay, location)
            if (solarEclipse != null) {
                events.add(solarEclipse)
            }

        } catch (e: Exception) {
            LogUtil.e("計算指定日期蝕相時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 尋找指定日期附近的日蝕
     */
    private fun findSolarEclipseNear(
        sw: SwissEph,
        julDay: Double,
        location: LatLng
    ): AstrologyEvent? {
        try {
            val serr = StringBuffer()
            val tret = DoubleArray(20)
            val attr = DoubleArray(20)
            val geopos = doubleArrayOf(location.longitude, location.latitude, 0.0)

            // 使用 Swiss Ephemeris 的日蝕計算函數
            val searchFlag = SweConst.SE_ECL_TOTAL or SweConst.SE_ECL_PARTIAL or SweConst.SE_ECL_ANNULAR
            val whicheph = SweConst.SEFLG_SWIEPH

            // 先搜索全球日蝕
            val eclFlag = sw.swe_sol_eclipse_when_glob(julDay - 1.0, whicheph, searchFlag, tret, 0, serr)

            if (eclFlag == SweConst.ERR) {
                LogUtil.e("日蝕計算錯誤: $serr")
                return null
            }

            val eclipseJulDay = tret[0]

            // 檢查蝕相是否在指定日期的±1天內
            if (abs(eclipseJulDay - julDay) <= 1.0) {
                // 檢查該地點是否能看到日蝕
                val localFlag = sw.swe_sol_eclipse_when_loc(eclipseJulDay - 0.1, whicheph, geopos, tret, attr, 0, serr)

                if (localFlag != SweConst.ERR && abs(tret[0] - eclipseJulDay) <= 0.5) {
                    // 確定日蝕類型
                    val eclipseType = when {
                        (localFlag and SweConst.SE_ECL_TOTAL) != 0 -> EclipseType.TOTAL_SOLAR
                        (localFlag and SweConst.SE_ECL_ANNULAR) != 0 -> EclipseType.ANNULAR_SOLAR
                        (localFlag and SweConst.SE_ECL_PARTIAL) != 0 -> EclipseType.PARTIAL_SOLAR
                        else -> EclipseType.SOLAR_ECLIPSE
                    }

                    // 食分（eclipse magnitude）
                    val magnitude = attr[0]

                    return AstrologyEventFactory.createEclipseEvent(
                        date = Date(((eclipseJulDay - 2451545.0) * 86400000).toLong()),
                        eclipseType = eclipseType,
                        magnitude = magnitude
                    )
                } else {
                    // 該地點看不到日蝕，但可能是全球性事件
                    val eclipseType = when {
                        (eclFlag and SweConst.SE_ECL_TOTAL) != 0 -> EclipseType.TOTAL_SOLAR
                        (eclFlag and SweConst.SE_ECL_ANNULAR) != 0 -> EclipseType.ANNULAR_SOLAR
                        (eclFlag and SweConst.SE_ECL_PARTIAL) != 0 -> EclipseType.PARTIAL_SOLAR
                        else -> EclipseType.SOLAR_ECLIPSE
                    }

                    // 獲取全球日蝕的詳細信息
                    sw.swe_sol_eclipse_where(eclipseJulDay, whicheph, geopos, attr, serr)

                    return AstrologyEventFactory.createEclipseEvent(
                        date = Date(((eclipseJulDay - 2451545.0) * 86400000).toLong()),
                        eclipseType = eclipseType,
                        magnitude = attr[0]
                    )
                }
            }

        } catch (e: Exception) {
            LogUtil.e("尋找日蝕時發生錯誤: ${e.message}")
        }

        return null
    }





}
