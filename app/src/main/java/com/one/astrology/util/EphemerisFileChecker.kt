package com.one.astrology.util

import android.app.AlertDialog
import android.content.Context
import android.widget.Toast
import com.one.core.util.LogUtil
import java.io.File

/**
 * Utility class to check and manage ephemeris files
 */
object EphemerisFileChecker {
    
    /**
     * Check if ephemeris files are properly installed
     * @param context Application context
     * @return true if files are available, false otherwise
     */
    fun checkEphemerisFiles(context: Context): Boolean {
        val epheDir = File(context.filesDir.toString() + File.separator + "ephe")
        
        // Check if directory exists
        if (!epheDir.exists()) {
            LogUtil.e("Ephemeris directory does not exist")
            return false
        }
        
        // Check if directory contains at least one .se1 file
        val files = epheDir.listFiles { file -> file.name.endsWith(".se1") }
        return files != null && files.isNotEmpty()
    }
    
    /**
     * Install ephemeris files from assets
     * @param context Application context
     * @return true if installation was successful, false otherwise
     */
    fun installEphemerisFiles(context: Context): <PERSON><PERSON><PERSON> {
        try {
            // Create ephemeris directory if it doesn't exist
            val epheDir = File(context.filesDir.toString() + File.separator + "ephe")
            if (!epheDir.exists()) {
                epheDir.mkdirs()
            }
            
            // Copy ephemeris files from assets
            CopyAssetFiles(".*\\.se1", context).copy()
            
            // Check if files were copied successfully
            return checkEphemerisFiles(context)
        } catch (e: Exception) {
            LogUtil.e("Error installing ephemeris files: ${e.message}")
            return false
        }
    }
    
    /**
     * Show a dialog to the user offering to install ephemeris files
     * This should be called when ephemeris files are missing
     */
    fun showInstallDialog(context: Context) {
        try {
            AlertDialog.Builder(context)
                .setTitle("缺少星曆檔案")
                .setMessage("應用程式需要安裝星曆檔案才能正常運作。這些檔案用於計算行星位置。\n\n您想要現在安裝這些檔案嗎？")
                .setPositiveButton("安裝") { _, _ ->
                    val success = installEphemerisFiles(context)
                    if (success) {
                        Toast.makeText(context, "星曆檔案安裝成功", Toast.LENGTH_LONG).show()
                    } else {
                        Toast.makeText(context, "星曆檔案安裝失敗，應用程式可能無法正常運作", Toast.LENGTH_LONG).show()
                    }
                }
                .setNegativeButton("取消") { dialog, _ ->
                    dialog.dismiss()
                    Toast.makeText(context, "應用程式可能無法正常運作，直到星曆檔案安裝完成", Toast.LENGTH_LONG).show()
                }
                .setCancelable(false)
                .show()
        } catch (e: Exception) {
            LogUtil.e("Error showing install dialog: ${e.message}")
        }
    }
}
