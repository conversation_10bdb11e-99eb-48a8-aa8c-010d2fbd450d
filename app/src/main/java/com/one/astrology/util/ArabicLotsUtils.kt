package com.one.astrology.util

import com.one.astrology.data.bean.PlanetBean

object ArabicLotsUtils {
    val planets = listOf(
        PlanetBean(0, "A", "Sun", "Sun", "太陽", "FF0000", 0.0, true, false, true),
        PlanetBean(1, "<PERSON>", "<PERSON>", "<PERSON>", "月亮", "0A0AFF", 0.0, true, false, true),
        PlanetBean(2, "C", "Mercury", "Merc", "水星", "127116", 0.0, true, true, true),
        PlanetBean(3, "D", "Venus", "Venu", "金星", "CC9933", 0.0, true, true, true),
        PlanetBean(4, "E", "Mars", "Mars", "火星", "FF0000", 0.0, true, true, true),
        PlanetBean(5, "F", "Jupiter", "Jupiter", "木星", "FF0000", 0.0, true, true, true),
        PlanetBean(6, "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "土星", "CC9933", 0.0, true, true, true),
        PlanetBean(7, "H", "Uranus", "Uran", "天王星", "0c4b0e", 0.0, true, true),
        Planet<PERSON><PERSON>(8, "I", "<PERSON>", "Nept", "海王星", "0A0AFF", 0.0, true, true),
        PlanetBean(9, "J", "Pluto", "P lut", "冥王星", "0A0AFF", 0.0, true, true),
        PlanetBean(10, "L", "NorthNode", "NorthNode", "北交點", "00B8B8", 0.0, false, false),
        PlanetBean(12, "Q", "Lilith", "Lili", "莉莉斯", "00B8B8", 0.0, false, false),
        PlanetBean(15, "R", "Chir", "Chiron", "凱龍星", "FF33FF", 0.0, false, false),
        PlanetBean(16, "X", "Pholus", "Pholus", "人龍星", "FF33FF", 0.0, false, false),
        PlanetBean(17, "W", "Ceres", "Ceres", "穀神星", "FF33FF", 0.0, false, false),
        PlanetBean(18, "S", "Pallas", "Pall", "智神星", "FF33FF", 0.0, false, false),
        PlanetBean(19, "N", "Juno", "Juno", "婚神星", "FF33FF", 0.0, false, false),
        PlanetBean(20, "T", "Vesta", "Vesta", "灶神星", "FF33FF", 0.0, false, false),
        PlanetBean(60, "M", "S.Node", "S.No", "南交點", "00B8B8", 0.0, false, false),
        PlanetBean(61, "V", "Midpoint", "Midpoint", "日月中點", "00B8B8", 0.0, false, false),
        PlanetBean(62, "Y", "Vesta", "Vesta", "宿命點", "00B8B8", 0.0, false, false),
        PlanetBean(63, "O", "Part of Fortune", "Fortune", "福點", "00B8B8", 0.0, false, false),
        PlanetBean(100, "!", "Asc", "Asc", "上升", "F23837", 0.0, true, false),
        PlanetBean(101, "$", "MC", "MC", "中天", "006633", 0.0, true, false),
        PlanetBean(102, "\"", "Des", "Des", "下降", "CC9933", 0.0, true, false),
        PlanetBean(103, "#", "IC", "IC", "天底", "0A0AFF", 0.0, true, false),
        PlanetBean(10433, "P", "Eros", "Eros", "愛神", "FF33FF", 0.0, false, false)
    )

    val specialPoints = listOf(
        PlanetBean(200, "✦", "Happiness Point", "special_happiness", "幸運點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(201, "☽☉", "Spirit Point", "special_spirit", "精神點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(202, "❤", "Desire Point", "special_desire", "情慾點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(203, "☉♄", "Father Point", "special_father", "父親點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(204, "♀☽", "Mother Point", "special_mother", "母親點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(205, "⚷", "Foot Point", "special_foot", "手足點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(206, "⚳", "Sibling Count Point", "special_sibling", "手足數目點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(207, "♁", "Wealth Point", "special_wealth", "資產點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(208, "☊", "Birth Point", "special_birth", "生計點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(209, "♂♀", "Marriage Point (Male)", "special_marriage_male", "婚姻點 (男性)", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(210, "♀♂", "Marriage Point (Female)", "special_marriage_female", "婚姻點 (女性)", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(211, "⚭", "Marriage Point", "special_marriage_unknown", "婚姻點 (未定)", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(212, "♃♎", "Joy & Marriage Point", "special_joy_marriage", "娛樂和婚姻點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(213, "☉☽♀", "Marriage Day/Male", "special_marriage_day_male", "結婚點 日間／男性", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(214, "☉☽♂", "Marriage Night/Female", "special_marriage_night_female", "結婚點 夜間／女性", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(215, "♃↑", "Daughter Point", "special_daughter", "子女點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(216, "♂☿", "Conception Point", "special_conception", "生產點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(217, "♂☉", "Son Point", "special_son", "兒子點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(218, "♀☽", "Daughter Point", "special_daughter2", "女兒點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(219, "☽♀", "Friendship Point", "special_friendship", "友誼點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(220, "♄↑", "Distant Point", "special_distance", "遠征點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(221, "☿♄", "Servant Point", "special_servant", "奴僕點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(222, "♄☿", "Health Disease Point", "special_disease", "健康疾病點", "B29DD9", 0.0, false, false, false, true),
        PlanetBean(223, "☽♇", "Death Point", "special_death", "死亡點", "B29DD9", 0.0, false, false, false, true)
    )

    /**
     * 計算任一 Arabic Lot，使用公式：Asc + B - A（並做 360 度模運算）
     * @param ascendant 上升點（0°~360°）
     * @param a 行星 A（依 lot 而定）
     * @param b 行星 B（依 lot 而定）
     * @return 計算出來的希臘點位置（0°~360°）
     */
    fun calculateLot(ascendant: Double, a: Double, b: Double): Double {
        val lot = (ascendant + b - a) % 360.0
        return if (lot >= 0) lot else lot + 360.0
    }

    /**
     * 計算福點（Lot of Fortune）
     */
    fun lotOfFortune(ascendant: Double, sun: Double, moon: Double, isDayChart: Boolean): Double {
        return if (isDayChart) {
            calculateLot(ascendant, sun, moon)
        } else {
            calculateLot(ascendant, moon, sun)
        }
    }

    /**
     * 計算精神點（Lot of Spirit）
     */
    fun lotOfSpirit(ascendant: Double, sun: Double, moon: Double, isDayChart: Boolean): Double {
        return if (isDayChart) {
            calculateLot(ascendant, moon, sun)
        } else {
            calculateLot(ascendant, sun, moon)
        }
    }

    /**
     * 計算旺點（Lot of Exaltation）：Asc + Moon - exaltation of Moon (Taurus 3° = 33°)
     */
    fun lotOfExaltation(ascendant: Double, moon: Double): Double {
        val exaltationOfMoon = 33.0 // 金牛3度
        return calculateLot(ascendant, exaltationOfMoon, moon)
    }

    /**
     * 計算愛情點（Lot of Eros）：Asc + Fortune - Venus（簡易版）
     */
    fun lotOfEros(ascendant: Double, venus: Double, lotOfFortune: Double): Double {
        return calculateLot(ascendant, venus, lotOfFortune)
    }

    /**
     * 計算幸運點（Happiness Point）
     */
    fun lotOfHappiness(ascendant: Double, sun: Double, moon: Double): Double {
        return calculateLot(ascendant, sun, moon)
    }

    /**
     * 計算精神點（Spirit Point）
     */
    fun lotOfSpirit(ascendant: Double, sun: Double, moon: Double): Double {
        return calculateLot(ascendant, moon, sun)
    }

    /**
     * 計算情慾點（Desire Point）
     */
    fun lotOfDesire(ascendant: Double, mars: Double, venus: Double): Double {
        return calculateLot(ascendant, mars, venus)
    }

    /**
     * 計算父親點（Father Point）
     */
    fun lotOfFather(ascendant: Double, sun: Double, saturn: Double): Double {
        return calculateLot(ascendant, sun, saturn)
    }

    /**
     * 計算母親點（Mother Point）
     */
    fun lotOfMother(ascendant: Double, venus: Double, moon: Double): Double {
        return calculateLot(ascendant, venus, moon)
    }

    /**
     * 計算手足點（Foot Point）
     */
    fun lotOfFoot(ascendant: Double, mercury: Double, mars: Double): Double {
        return calculateLot(ascendant, mercury, mars)
    }

    /**
     * 計算手足數目點（Sibling Count Point）
     */
    fun lotOfSiblingCount(ascendant: Double, saturn: Double, jupiter: Double): Double {
        return calculateLot(ascendant, saturn, jupiter)
    }

    /**
     * 計算資產點（Wealth Point）
     */
    fun lotOfWealth(ascendant: Double, venus: Double, jupiter: Double): Double {
        return calculateLot(ascendant, venus, jupiter)
    }

    /**
     * 計算生計點（Birth Point）
     */
    fun lotOfBirth(ascendant: Double, jupiter: Double, moon: Double): Double {
        return calculateLot(ascendant, jupiter, moon)
    }

    /**
     * 計算婚姻點（Marriage Point）
     * 根據性別和日夜計算
     */
    fun lotOfMarriage(
        ascendant: Double,
        sun: Double,
        moon: Double,
        venus: Double,
        mars: Double,
        isDayChart: Boolean,
        isMale: Boolean
    ): Double {
        return when {
            isDayChart && isMale -> calculateLot(ascendant, sun, venus)
            isDayChart && !isMale -> calculateLot(ascendant, moon, mars)
            !isDayChart && isMale -> calculateLot(ascendant, moon, mars)
            else -> calculateLot(ascendant, sun, venus)
        }
    }

    /**
     * 計算子女點（Daughter Point）
     */
    fun lotOfDaughter(ascendant: Double, moon: Double, jupiter: Double): Double {
        return calculateLot(ascendant, moon, jupiter)
    }

    /**
     * 計算生產點（Conception Point）
     */
    fun lotOfConception(ascendant: Double, mercury: Double, mars: Double): Double {
        return calculateLot(ascendant, mercury, mars)
    }

    /**
     * 計算兒子點（Son Point）
     */
    fun lotOfSon(ascendant: Double, sun: Double, mars: Double): Double {
        return calculateLot(ascendant, sun, mars)
    }

    /**
     * 計算友誼點（Friendship Point）
     */
    fun lotOfFriendship(ascendant: Double, venus: Double, moon: Double): Double {
        return calculateLot(ascendant, venus, moon)
    }

    /**
     * 計算遠征點（Distant Point）
     */
    fun lotOfDistance(ascendant: Double, jupiter: Double, saturn: Double): Double {
        return calculateLot(ascendant, jupiter, saturn)
    }

    /**
     * 計算奴僕點（Servant Point）
     */
    fun lotOfServant(ascendant: Double, saturn: Double, mercury: Double): Double {
        return calculateLot(ascendant, saturn, mercury)
    }

    /**
     * 計算健康疾病點（Health Disease Point）
     */
    fun lotOfDisease(ascendant: Double, mercury: Double, saturn: Double): Double {
        return calculateLot(ascendant, mercury, saturn)
    }

    /**
     * 計算死亡點（Death Point）
     */
    fun lotOfDeath(ascendant: Double, pluto: Double, moon: Double): Double {
        return calculateLot(ascendant, pluto, moon)
    }

    // 你也可以繼續擴充其他 Arabic Lots...
}
