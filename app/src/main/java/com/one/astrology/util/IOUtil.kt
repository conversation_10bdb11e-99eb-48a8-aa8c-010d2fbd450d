package com.one.astrology.util

import java.io.BufferedOutputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.FileWriter
import java.io.IOException
import java.io.InputStream

/**
 * IO流工具
 *
 */
object IOUtil {
    /**
     * 根据输入流获得字节数组
     *
     * @return
     */
    fun getBytes(`is`: InputStream?): ByteArray {
        var `is` = `is`
        val bos = ByteArrayOutputStream()
        var len = -1
        val bytes = ByteArray(1024 * 8)
        try {
            while (`is`!!.read(bytes).also { len = it } != -1) {
                bos.write(bytes, 0, len)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            if (`is` != null) {
                try {
                    `is`.close() //关闭流
                    `is` = null
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return bos.toByteArray()
    }

    fun writeBytes(file: File?, bytes: ByteArray?) {
        try {
            var bufos: BufferedOutputStream? = BufferedOutputStream(FileOutputStream(file))
            try {
                bufos!!.write(bytes)
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                if (bufos != null) {
                    try {
                        bufos.close() //关闭流
                        bufos = null
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
    }

    fun appened(file: File?, info: String?) {
        var fw: FileWriter? = null
        try {
            fw = FileWriter(file, true)
            fw.write(info)
            fw.write("\r\n")
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            if (fw != null) {
                try {
                    fw.close()
                    fw = null
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
    }
}