package com.one.astrology.util.astro


import com.one.astrology.constant.Planet
import com.one.astrology.data.bean.PlanetBean

data class PlanetCondition(
    val planet: PlanetBean,
    val dignities: Dignity = Dignity.NONE,
    val aspects: List<Aspect> = emptyList(),
    val isRetrograde: Boolean = false,
    val isCombust: Boolean = false,
    val isSectInFavor: <PERSON>ole<PERSON> = true,
    val isHighInSky: Boolean = false,
    val isRulerOfHouse: Boolean = false,
    val lastAspect: Aspect? = null
)

/**
- 入廟 Domicile：力量最強
- 得勢 Exaltation：榮耀、有發揮空間
- 境遇佳 Triplicity, Term, Face：具基本支持力
- 落陷 Detriment：違背其性質
- 失勢 Fall：不適應、行動受限
 */

enum class Dignity {
    DOMICILE, EXALTATION, DETRIMENT, FALL, NONE
}

enum class AspectType {
    CONJUNCTION, SEXTILE, SQUARE, TRINE, OPPOSITION
}

data class Aspect(val with: PlanetBean, val type: AspectType, val isApplying: Boolean)

object AstrologyTool {

    enum class SectType {
        DAY, NIGHT
    }

    fun determineSect(sunLon: Double, ascLon: Double): SectType {
        // Desc 是 Asc 的對宮（+180°，取模 360）
        val descendantLon = (ascLon + 180.0) % 360.0

        val isSunAboveHorizon = if (ascLon < descendantLon) {
            sunLon in ascLon..descendantLon
        } else {
            sunLon in ascLon..360.0 || sunLon in 0.0..descendantLon
        }

        return if (isSunAboveHorizon) SectType.DAY else SectType.NIGHT
    }

    enum class PlanetNature {
        BENEFIC, MALEFIC, NEUTRAL, UNKNOWN
    }

    val planetNatureMap: Map<Int, PlanetNature> = mapOf(
        Planet.SUN to PlanetNature.BENEFIC,  // 可視為中偏吉
        Planet.MOON to PlanetNature.NEUTRAL,
        Planet.MERCURY to PlanetNature.NEUTRAL,
        Planet.VENUS to PlanetNature.BENEFIC,
        Planet.MARS to PlanetNature.MALEFIC,
        Planet.JUPITER to PlanetNature.BENEFIC,
        Planet.SATURN to PlanetNature.MALEFIC,
        Planet.URANUS to PlanetNature.NEUTRAL,
        Planet.NEPTUNE to PlanetNature.NEUTRAL,
        Planet.PLUTO to PlanetNature.MALEFIC,
//                Planet.Chiron to PlanetNature.NEUTRAL,
//        "North Node" to PlanetNature.BENEFIC,
//        "South Node" to PlanetNature.MALEFIC
    )

    fun getPlanetNature(planetName: Int): PlanetNature {
        return planetNatureMap[planetName] ?: PlanetNature.UNKNOWN
    }

    fun PlanetNature.toLocalizedString(): String {
        return when (this) {
            PlanetNature.BENEFIC -> "吉星"
            PlanetNature.MALEFIC -> "凶星"
            PlanetNature.NEUTRAL -> "中性"
            PlanetNature.UNKNOWN -> "未知"
        }
    }

    fun judgeBeneficMalefic(planet: Int): String {
        return when (planet) {
            Planet.VENUS, Planet.JUPITER -> "吉星"
            Planet.MARS, Planet.SATURN -> "凶星"
            Planet.MERCURY -> "中性星"
            Planet.SUN -> "中偏吉"
            Planet.MOON -> "中性星"
            else -> {
                ""
            }
        }
    }


    enum class Zodiac {
        ARIES, TAURUS, GEMINI, CANCER, LEO, VIRGO,
        LIBRA, SCORPIO, SAGITTARIUS, CAPRICORN, AQUARIUS, PISCES
    }



    fun getPlanetStrength(planet: Int, sign: String): Dignity {
        return when (planet) {
            Planet.SUN -> when (sign) {
                "獅子座" -> Dignity.DOMICILE
                "水瓶座" -> Dignity.DETRIMENT
                "牡羊座" -> Dignity.EXALTATION
                "天秤座" -> Dignity.FALL
                else -> Dignity.NONE
            }
            Planet.MOON -> when (sign) {
                "巨蟹座" -> Dignity.DOMICILE
                "魔羯座" -> Dignity.DETRIMENT
                "金牛座" -> Dignity.EXALTATION
                "天蠍座" -> Dignity.FALL
                else -> Dignity.NONE
            }
            Planet.MERCURY -> when (sign) {
                "雙子座", "處女座" -> Dignity.DOMICILE
                "射手座", "雙魚座" -> Dignity.DETRIMENT
                "處女座" -> Dignity.EXALTATION
                "雙魚座" -> Dignity.FALL
                else -> Dignity.NONE
            }
            Planet.VENUS -> when (sign) {
                "金牛座", "天秤座" -> Dignity.DOMICILE
                "天蠍座", "牡羊座" -> Dignity.DETRIMENT
                "雙魚座" -> Dignity.EXALTATION
                "處女座" -> Dignity.FALL
                else -> Dignity.NONE
            }
            Planet.MARS -> when (sign) {
                "牡羊座", "天蠍座" -> Dignity.DOMICILE
                "天秤座", "金牛座" -> Dignity.DETRIMENT
                "摩羯座" -> Dignity.EXALTATION
                "巨蟹座" -> Dignity.FALL
                else -> Dignity.NONE
            }
            Planet.JUPITER -> when (sign) {
                "射手座", "雙魚座" -> Dignity.DOMICILE
                "雙子座", "處女座" -> Dignity.DETRIMENT
                "巨蟹座" -> Dignity.EXALTATION
                "摩羯座" -> Dignity.FALL
                else -> Dignity.NONE
            }
            Planet.SATURN -> when (sign) {
                "摩羯座", "水瓶座" -> Dignity.DOMICILE
                "巨蟹座", "獅子座" -> Dignity.DETRIMENT
                "天秤座" -> Dignity.EXALTATION
                "牡羊座" -> Dignity.FALL
                else -> Dignity.NONE
            }

            else -> {
                Dignity.NONE
            }
        }
    }

    fun calculatePlanetStrength(condition: PlanetCondition): Int {
        // TODO
        var score = 0

        // 廟旺陷落
//        score += when (condition.dignities) {
//            Dignity.DOMICILE -> 4
//            Dignity.EXALTATION -> 3
//            Dignity.FALL -> -4
//            Dignity.DETRIMENT -> -3
//            Dignity.NONE -> 0
//        }

        // 逆行
        if (condition.isRetrograde) score -= 2

        // 燃燒
        if (condition.isCombust) score -= 3

        // 是否是日夜圖主宰（Sect）
        if (condition.isSectInFavor) score += 2 else score -= 1

        // 接近中天
        if (condition.isHighInSky) score += 1

        // 是否為宮主
        if (condition.isRulerOfHouse) score += 1

        // 相位
        condition.aspects.forEach { aspect ->
            score += when (aspect.type) {
                AspectType.SEXTILE, AspectType.TRINE -> 2
                AspectType.CONJUNCTION -> 1
                AspectType.SQUARE, AspectType.OPPOSITION -> -2
            }
        }

        return score
    }

    fun getJudgmentLabel(score: Int): String {
        return when {
            score >= 6 -> "非常強勢"
            score in 3..5 -> "強勢"
            score in 0..2 -> "普通"
            score in -2..-1 -> "弱勢"
            else -> "非常弱勢"
        }
    }
}
