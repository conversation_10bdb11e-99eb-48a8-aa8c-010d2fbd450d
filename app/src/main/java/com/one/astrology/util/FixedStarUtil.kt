package com.one.astrology.util

import com.one.astrology.constant.FixedStar
import com.one.astrology.data.Houses
import com.one.astrology.data.bean.FixedStarBean
import com.one.astrology.data.bean.PlanetBean

/**
 * 固定星計算工具類
 * 用於計算固定星位置和相位
 */
object FixedStarUtil {

    /**
     * 獲取固定星數據列表
     * 包含常用的固定星及其位置和特性
     * @param date 日期（用於計算歲差）
     * @return 固定星數據列表
     */
    fun getFixedStars(date: Long): List<FixedStarBean> {
        // 計算歲差
        val precession = calculatePrecession(date)
        
        // 創建固定星列表
        val fixedStars = mutableListOf<FixedStarBean>()
        
        // 添加常用固定星
        for (starId in FixedStar.FIXED_STARS) {
            val star = createFixedStar(starId, precession)
            fixedStars.add(star)
        }
        
        return fixedStars
    }

    /**
     * 創建固定星數據
     * @param starId 固定星ID
     * @param precession 歲差值
     * @return 固定星數據
     */
    private fun createFixedStar(starId: Int, precession: Double): FixedStarBean {
        // 獲取固定星基本信息
        val chineseName = FixedStar.getChineseName(starId)
        val englishName = FixedStar.getEnglishName(starId)
        val constellation = FixedStar.getConstellation(starId)
        val nature = FixedStar.getNature(starId)
        val symbolism = FixedStar.getSymbolism(starId)
        val keywords = FixedStar.getKeywords(starId)
        
        // 獲取固定星J2000位置
        val j2000Position = getJ2000Position(starId)
        val longitude = j2000Position.first + precession
        val latitude = j2000Position.second
        
        // 獲取固定星視星等
        val magnitude = getMagnitude(starId)
        
        // 創建固定星數據
        return FixedStarBean(
            id = starId,
            chineseName = chineseName,
            englishName = englishName,
            constellation = constellation,
            longitude = longitude,
            latitude = latitude,
            magnitude = magnitude,
            nature = nature,
            symbolism = symbolism,
            keywords = keywords,
            description = getDescription(starId)
        )
    }

    /**
     * 計算歲差
     * 歲差是由於地球自轉軸的進動導致的天球坐標系統的緩慢變化
     * @param date 日期（毫秒時間戳）
     * @return 歲差值（度）
     */
    private fun calculatePrecession(date: Long): Double {
        // J2000.0 參考日期（2000年1月1日12時）
        val j2000 = 946728000000L
        
        // 計算自J2000.0以來的年數
        val years = (date - j2000) / (365.25 * 24 * 60 * 60 * 1000)
        
        // 歲差速率約為每年50.3秒（角秒）
        return years * 50.3 / 3600.0
    }

    /**
     * 獲取固定星J2000參考系中的位置
     * @param starId 固定星ID
     * @return 經度和緯度（黃道坐標系）
     */
    private fun getJ2000Position(starId: Int): Pair<Double, Double> {
        // 這裡應該使用精確的星表數據
        // 以下是簡化的示例數據，實際應用中應該使用更精確的數據
        return when (starId) {
            FixedStar.SIRIUS -> Pair(104.0, -39.6)
            FixedStar.CANOPUS -> Pair(96.0, -52.7)
            FixedStar.ARCTURUS -> Pair(213.9, 19.2)
            FixedStar.VEGA -> Pair(279.2, 38.8)
            FixedStar.CAPELLA -> Pair(79.2, 46.0)
            FixedStar.RIGEL -> Pair(78.6, -8.2)
            FixedStar.PROCYON -> Pair(114.8, 5.2)
            FixedStar.BETELGEUSE -> Pair(88.8, 7.4)
            FixedStar.ACHERNAR -> Pair(335.4, -57.2)
            FixedStar.HADAR -> Pair(210.9, -60.4)
            FixedStar.ALTAIR -> Pair(297.7, 8.9)
            FixedStar.ACRUX -> Pair(173.1, -63.1)
            FixedStar.ALDEBARAN -> Pair(69.0, 5.5)
            FixedStar.ANTARES -> Pair(247.4, -4.7)
            FixedStar.SPICA -> Pair(203.5, -11.2)
            FixedStar.POLLUX -> Pair(116.3, 28.0)
            FixedStar.FOMALHAUT -> Pair(335.9, -29.6)
            FixedStar.DENEB -> Pair(310.4, 45.3)
            FixedStar.MIMOSA -> Pair(191.9, -59.7)
            FixedStar.REGULUS -> Pair(149.2, 0.5)
            FixedStar.CASTOR -> Pair(113.6, 31.9)
            FixedStar.SHAULA -> Pair(262.7, -37.1)
            FixedStar.BELLATRIX -> Pair(81.3, 6.3)
            FixedStar.ELNATH -> Pair(78.6, 28.6)
            FixedStar.MIAPLACIDUS -> Pair(135.2, -69.7)
            FixedStar.ALNILAM -> Pair(84.1, -1.9)
            FixedStar.ALNAIR -> Pair(332.1, -47.0)
            FixedStar.ALNITAK -> Pair(85.2, -1.9)
            FixedStar.ALIOTH -> Pair(166.2, 55.9)
            FixedStar.DUBHE -> Pair(165.9, 61.8)
            else -> Pair(0.0, 0.0)
        }
    }

    /**
     * 獲取固定星視星等
     * @param starId 固定星ID
     * @return 視星等
     */
    private fun getMagnitude(starId: Int): Double {
        return when (starId) {
            FixedStar.SIRIUS -> -1.46
            FixedStar.CANOPUS -> -0.72
            FixedStar.ARCTURUS -> -0.05
            FixedStar.VEGA -> 0.03
            FixedStar.CAPELLA -> 0.08
            FixedStar.RIGEL -> 0.13
            FixedStar.PROCYON -> 0.4
            FixedStar.BETELGEUSE -> 0.45
            FixedStar.ACHERNAR -> 0.46
            FixedStar.HADAR -> 0.61
            FixedStar.ALTAIR -> 0.76
            FixedStar.ACRUX -> 0.77
            FixedStar.ALDEBARAN -> 0.87
            FixedStar.ANTARES -> 1.06
            FixedStar.SPICA -> 1.04
            FixedStar.POLLUX -> 1.16
            FixedStar.FOMALHAUT -> 1.17
            FixedStar.DENEB -> 1.25
            FixedStar.MIMOSA -> 1.25
            FixedStar.REGULUS -> 1.36
            FixedStar.CASTOR -> 1.58
            FixedStar.SHAULA -> 1.62
            FixedStar.BELLATRIX -> 1.64
            FixedStar.ELNATH -> 1.65
            FixedStar.MIAPLACIDUS -> 1.69
            FixedStar.ALNILAM -> 1.69
            FixedStar.ALNAIR -> 1.74
            FixedStar.ALNITAK -> 1.77
            FixedStar.ALIOTH -> 1.77
            FixedStar.DUBHE -> 1.81
            else -> 3.0
        }
    }

    /**
     * 獲取固定星詳細描述
     * @param starId 固定星ID
     * @return 詳細描述
     */
    private fun getDescription(starId: Int): String {
        return when (starId) {
            FixedStar.SIRIUS -> "天狼星是全天最亮的恆星，位於大犬座。在占星學中，天狼星被認為具有木星和火星的性質，帶來榮譽、財富和名聲。與個人星盤中的行星形成合相時，可能帶來突然的成功和認可。"
            FixedStar.CANOPUS -> "老人星是全天第二亮的恆星，位於船底座。在占星學中，老人星被認為具有土星和木星的性質，與航海、旅行和探索有關。它象徵著遠見和指引，特別是在人生的旅程中。"
            FixedStar.ARCTURUS -> "大角星是北天球最亮的恆星，位於牧夫座。在占星學中，大角星被認為具有火星和木星的性質，象徵著守護和保護。它與財富和收穫有關，可能帶來物質上的成功。"
            FixedStar.VEGA -> "織女星是天琴座最亮的恆星，也是北天球第五亮的恆星。在占星學中，織女星被認為具有金星和水星的性質，與藝術、音樂和創造力有關。它象徵著靈感和美麗。"
            FixedStar.REGULUS -> "軒轅十四是獅子座最亮的恆星，被稱為「獅子的心臟」。它是四大皇家星之一，在占星學中被認為具有火星和木星的性質，象徵著權力、領導和成功。與個人星盤中的行星形成合相時，可能帶來權力和地位的提升，但也需要謹慎使用這種力量。"
            FixedStar.ALDEBARAN -> "畢宿五是金牛座最亮的恆星，被稱為「金牛的眼睛」。它是四大皇家星之一，在占星學中被認為具有火星的性質，象徵著誠實、勇氣和正直。與個人星盤中的行星形成合相時，可能帶來勇氣和決心，但也可能引發衝動和魯莽。"
            FixedStar.ANTARES -> "心宿二是天蠍座最亮的恆星，被稱為「天蠍的心臟」。它是四大皇家星之一，在占星學中被認為具有火星和木星的性質，象徵著熱情、激情和競爭。與個人星盤中的行星形成合相時，可能帶來強烈的情感和挑戰，需要學會控制這種能量。"
            FixedStar.FOMALHAUT -> "北落師門是南魚座最亮的恆星，被稱為「南魚的嘴」。它是四大皇家星之一，在占星學中被認為具有金星和水星的性質，象徵著靈性、神秘和藝術。與個人星盤中的行星形成合相時，可能帶來靈感和直覺，但也可能引發幻想和不切實際的想法。"
            else -> "這顆固定星在占星學中有其獨特的意義和象徵。它的位置和性質可能對個人星盤產生特定的影響，特別是當它與重要的行星或點位形成合相時。"
        }
    }

    /**
     * 檢查固定星是否與行星形成合相
     * @param star 固定星
     * @param planet 行星
     * @param orb 容許度（允許的誤差範圍）
     * @return 是否形成合相
     */
    fun isConjunct(star: FixedStarBean, planet: PlanetBean, orb: Double = getDefaultOrb(star.magnitude)): Boolean {
        return star.isConjunct(planet.longitude, orb)
    }

    /**
     * 檢查固定星是否與行星形成對分相
     * @param star 固定星
     * @param planet 行星
     * @param orb 容許度（允許的誤差範圍）
     * @return 是否形成對分相
     */
    fun isOpposite(star: FixedStarBean, planet: PlanetBean, orb: Double = getDefaultOrb(star.magnitude)): Boolean {
        return star.isOpposite(planet.longitude, orb)
    }

    /**
     * 獲取默認的容許度
     * 根據固定星的亮度（星等）決定容許度
     * 亮星（低星等）有更大的容許度
     */
    private fun getDefaultOrb(magnitude: Double): Double {
        return when {
            magnitude <= 1.0 -> 2.0  // 一等星及更亮的星
            magnitude <= 2.0 -> 1.5  // 二等星
            magnitude <= 3.0 -> 1.0  // 三等星
            else -> 0.5             // 四等星及更暗的星
        }
    }

    /**
     * 獲取與行星形成合相的固定星列表
     * @param planet 行星
     * @param stars 固定星列表
     * @return 形成合相的固定星列表
     */
    fun getConjunctStars(planet: PlanetBean, stars: List<FixedStarBean>): List<FixedStarBean> {
        return stars.filter { star ->
            isConjunct(star, planet)
        }
    }

    /**
     * 獲取與行星形成對分相的固定星列表
     * @param planet 行星
     * @param stars 固定星列表
     * @return 形成對分相的固定星列表
     */
    fun getOppositeStars(planet: PlanetBean, stars: List<FixedStarBean>): List<FixedStarBean> {
        return stars.filter { star ->
            isOpposite(star, planet)
        }
    }

    /**
     * 獲取固定星所在的宮位
     * @param star 固定星
     * @param houses 宮位數據
     * @return 宮位編號 (1-12)
     */
    fun getHouse(star: FixedStarBean, houses: Houses): Int {
        return ArabicPartUtil.getHouse(star.longitude, houses)
    }

    /**
     * 獲取固定星解釋
     * @param star 固定星
     * @param planet 與之形成合相的行星（如果有）
     * @param houseId 所在宮位
     * @return 解釋文本
     */
    fun getInterpretation(star: FixedStarBean, planet: PlanetBean? = null, houseId: Int? = null): String {
        val baseInterpretation = star.description
        
        val planetPart = if (planet != null) {
            "\n\n${star.chineseName}與${getPlanetName(planet.id)}形成合相，" +
            "這表示${getPlanetStarMeaning(star.id, planet.id)}。"
        } else {
            ""
        }
        
        val housePart = if (houseId != null) {
            "\n\n${star.chineseName}位於第${houseId}宮，" +
            "這表示其影響主要體現在${getHouseMeaning(houseId)}領域。"
        } else {
            ""
        }
        
        return baseInterpretation + planetPart + housePart
    }

    /**
     * 獲取行星名稱
     * @param planetId 行星ID
     * @return 行星名稱
     */
    private fun getPlanetName(planetId: Int): String {
        // 這裡應該使用行星常量類中的方法
        // 簡化示例
        return when (planetId) {
            0 -> "太陽"
            1 -> "月亮"
            2 -> "水星"
            3 -> "金星"
            4 -> "火星"
            5 -> "木星"
            6 -> "土星"
            7 -> "天王星"
            8 -> "海王星"
            9 -> "冥王星"
            else -> "未知行星"
        }
    }

    /**
     * 獲取固定星與行星合相的意義
     * @param starId 固定星ID
     * @param planetId 行星ID
     * @return 意義描述
     */
    private fun getPlanetStarMeaning(starId: Int, planetId: Int): String {
        // 這裡可以實現更詳細的解釋邏輯
        // 簡化示例
        val starName = FixedStar.getChineseName(starId)
        val planetName = getPlanetName(planetId)
        
        return when (starId) {
            FixedStar.SIRIUS -> when (planetId) {
                0 -> "增強個人的成功潛力和領導能力，可能帶來名聲和認可"
                1 -> "增強情感敏感度和直覺，可能帶來情感上的滿足和安全感"
                3 -> "增強藝術才能和魅力，可能帶來愛情和財富"
                4 -> "增強勇氣和競爭力，可能帶來勝利和成就"
                5 -> "增強幸運和擴張能力，可能帶來重大的成功和機會"
                else -> "${starName}與${planetName}的結合增強了${planetName}的能量，可能在相關領域帶來顯著的影響"
            }
            FixedStar.REGULUS -> when (planetId) {
                0 -> "增強權力和領導能力，可能帶來重要的地位和成就，但需要謹慎使用權力"
                1 -> "增強情感力量和保護能力，可能在家庭或公共領域獲得重要地位"
                4 -> "增強勇氣和競爭力，可能帶來軍事或體育方面的成就，但也可能引發衝突"
                5 -> "增強幸運和擴張能力，可能帶來重大的成功和社會地位"
                6 -> "增強責任感和組織能力，可能帶來穩定的權力結構，但也可能引發限制"
                else -> "${starName}與${planetName}的結合增強了權力和領導的主題，可能在相關領域帶來重要的影響"
            }
            FixedStar.ALDEBARAN -> when (planetId) {
                0 -> "增強意志力和決心，可能帶來重要的成就，但也可能引發固執"
                1 -> "增強情感強度和保護能力，可能在家庭或公共領域表現出強烈的保護欲"
                4 -> "增強勇氣和競爭力，可能帶來軍事或體育方面的成就，但也可能引發暴力傾向"
                else -> "${starName}與${planetName}的結合增強了誠實和勇氣的主題，可能在相關領域帶來重要的影響"
            }
            else -> "${starName}與${planetName}的結合增強了${starName}的特質，可能在相關領域帶來顯著的影響"
        }
    }

    /**
     * 獲取宮位意義
     * @param houseId 宮位ID
     * @return 宮位意義
     */
    private fun getHouseMeaning(houseId: Int): String {
        return when (houseId) {
            1 -> "個人形象和自我表達"
            2 -> "個人價值和物質資源"
            3 -> "溝通和學習"
            4 -> "家庭和情感基礎"
            5 -> "創造力和娛樂"
            6 -> "工作和健康"
            7 -> "關係和合作"
            8 -> "共享資源和轉化"
            9 -> "高等教育和信仰"
            10 -> "職業和社會地位"
            11 -> "友誼和社交圈"
            12 -> "潛意識和精神世界"
            else -> "未知領域"
        }
    }
} 