package com.one.astrology.util

import android.content.Context
import com.google.gson.reflect.TypeToken
import com.one.astrology.constant.AssetsPath
import com.one.astrology.data.News
import com.one.astrology.data.RemoteConfigData
import com.one.astrology.data.Topics
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.type.ChartType
import com.one.core.data.Permissions

class AssetsToObjectUtil {

    companion object {

        fun getSignList(context: Context): ArrayList<SignBean> {
            val type = object : TypeToken<ArrayList<SignBean?>?>() {}.type
            val json = Util.loadStringFromAssets(context, AssetsPath.SIGN)
            return JsonTool.parseJson(json, type)!!
        }

        fun getPlanetList(context: Context): ArrayList<PlanetBean> {
            val type = object : TypeToken<ArrayList<PlanetBean?>?>() {}.type
            val planetJson = Util.loadStringFromAssets(context, AssetsPath.PLANET)
            return JsonTool.parseJson(planetJson, type)!!
        }

        fun getNewsList(context: Context): News {
            val type = object : TypeToken<News>() {}.type
            val planetJson = Util.loadStringFromAssets(context, AssetsPath.NEWS)
            return JsonTool.parseJson(planetJson, type)!!
        }

        fun getNewsList(context: String): News {
            val type = object : TypeToken<News>() {}.type
            return JsonTool.parseJson(context, type)!!
        }

//        fun getTopicList(context: Context): Topics {
//            val type = object : TypeToken<Topics>() {}.type
//            val planetJson = Util.loadStringFromAssets(context, AssetsPath.TOPIC)
//            return JsonTool.parseJson(planetJson, type)!!
//        }

        fun getTopicSataSynastryList(context: Context): Topics {
            val type = object : TypeToken<Topics>() {}.type
            val planetJson = Util.loadStringFromAssets(context, AssetsPath.TOPIC_SATA_SYNASTRY)
            return JsonTool.parseJson(planetJson, type)!!
        }

        fun getTopicList(context: String): Topics {
            val type = object : TypeToken<Topics>() {}.type
            return JsonTool.parseJson(context, type)!!
        }

        fun getChartType(context: Context): ChartType {
            val type = object : TypeToken<ChartType>() {}.type
            val planetJson = Util.loadStringFromAssets(context, AssetsPath.CHART)
            return JsonTool.parseJson(planetJson, type)!!
        }

        fun getRemoteConfigData(json: String): RemoteConfigData {
            val type = object : TypeToken<RemoteConfigData>() {}.type
            return JsonTool.parseJson(json, type)!!
        }

        fun getPermissions(json: String): Permissions {
            val type = object : TypeToken<Permissions>() {}.type
            return JsonTool.parseJson(json, type)!!
        }
    }
}