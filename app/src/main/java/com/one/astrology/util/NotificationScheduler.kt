package com.one.astrology.util

import android.content.Context
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequest
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.one.astrology.ObjectBox
import com.one.astrology.data.model.NotificationSettings
import com.one.astrology.worker.DailyTransitWorker
import java.util.Calendar
import java.util.concurrent.TimeUnit
import com.one.core.util.LogUtil as CoreLogUtil

/**
 * 推播排程管理器
 * 負責管理每日行運推播的排程
 */
object NotificationScheduler {

    private const val TAG = "NotificationScheduler"

    /**
     * 設定每日推播排程
     */
    fun scheduleDailyNotification(context: Context, settings: NotificationSettings) {
        try {
            // 取消現有的排程
            cancelDailyNotification(context)

            if (!settings.isDailyTransitEnabled) {
                CoreLogUtil.d("$TAG: 推播功能已關閉，不設定排程")
                return
            }

            // 計算下次執行時間
            val nextExecutionTime = calculateNextExecutionTime(
                settings.notificationHour,
                settings.notificationMinute
            )

            // 創建工作請求
            val workRequest = createDailyWorkRequest(nextExecutionTime)

            // 排程工作
            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    DailyTransitWorker.WORK_NAME,
                    ExistingPeriodicWorkPolicy.REPLACE,
                    workRequest
                )

            CoreLogUtil.d("$TAG: 已設定每日推播排程，下次執行時間: ${formatTime(nextExecutionTime)}")

        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 設定推播排程失敗: ${e.message}")
        }
    }

    /**
     * 取消每日推播排程
     */
    fun cancelDailyNotification(context: Context) {
        try {
            WorkManager.getInstance(context)
                .cancelUniqueWork(DailyTransitWorker.WORK_NAME)
            CoreLogUtil.d("$TAG: 已取消每日推播排程")
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 取消推播排程失敗: ${e.message}")
        }
    }

    /**
     * 立即執行推播任務（用於測試）
     */
    fun executeImmediately(context: Context) {
        try {
            val workRequest = OneTimeWorkRequestBuilder<DailyTransitWorker>()
                .addTag("immediate_transit")
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                        .build()
                )
                .build()

            WorkManager.getInstance(context)
                .enqueueUniqueWork(
                    "immediate_transit_${System.currentTimeMillis()}",
                    ExistingWorkPolicy.REPLACE,
                    workRequest
                )

            CoreLogUtil.d("$TAG: 已排程立即執行推播任務")

        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 立即執行推播任務失敗: ${e.message}")
        }
    }

    /**
     * 檢查推播排程狀態
     */
    fun checkScheduleStatus(context: Context, callback: (Boolean, String) -> Unit) {
        try {
            WorkManager.getInstance(context)
                .getWorkInfosForUniqueWorkLiveData(DailyTransitWorker.WORK_NAME)
                .observeForever { workInfos ->
                    if (workInfos.isNullOrEmpty()) {
                        callback(false, "未設定排程")
                    } else {
                        val workInfo = workInfos[0]
                        val isScheduled = workInfo.state == WorkInfo.State.ENQUEUED
                        val status = when (workInfo.state) {
                            WorkInfo.State.ENQUEUED -> "已排程"
                            WorkInfo.State.RUNNING -> "執行中"
                            WorkInfo.State.SUCCEEDED -> "執行成功"
                            WorkInfo.State.FAILED -> "執行失敗"
                            WorkInfo.State.BLOCKED -> "被阻塞"
                            WorkInfo.State.CANCELLED -> "已取消"
                        }
                        callback(isScheduled, status)
                    }
                }
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 檢查排程狀態失敗: ${e.message}")
            callback(false, "檢查失敗")
        }
    }

    /**
     * 創建每日工作請求
     */
    private fun createDailyWorkRequest(initialDelay: Long): PeriodicWorkRequest {
        return PeriodicWorkRequestBuilder<DailyTransitWorker>(1, TimeUnit.DAYS)
            .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
            .setConstraints(
                Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                    .setRequiresBatteryNotLow(false)
                    .setRequiresCharging(false)
                    .setRequiresDeviceIdle(false)
                    .setRequiresStorageNotLow(false)
                    .build()
            )
            .addTag(DailyTransitWorker.TAG)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                15,
                TimeUnit.MINUTES
            )
            .build()
    }

    /**
     * 計算下次執行時間
     */
    private fun calculateNextExecutionTime(hour: Int, minute: Int): Long {
        val calendar = Calendar.getInstance()
        val now = calendar.timeInMillis

        // 設定目標時間
        calendar.set(Calendar.HOUR_OF_DAY, hour)
        calendar.set(Calendar.MINUTE, minute)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        var targetTime = calendar.timeInMillis

        // 如果目標時間已過，設定為明天
        if (targetTime <= now) {
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            targetTime = calendar.timeInMillis
        }

        return targetTime - now
    }

    /**
     * 格式化時間顯示
     */
    private fun formatTime(timeInMillis: Long): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = System.currentTimeMillis() + timeInMillis
        return String.format(
            "%04d-%02d-%02d %02d:%02d",
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH),
            calendar.get(Calendar.HOUR_OF_DAY),
            calendar.get(Calendar.MINUTE)
        )
    }

    /**
     * 更新推播排程（當設定變更時調用）
     */
    fun updateSchedule(context: Context, settings: NotificationSettings) {
        CoreLogUtil.d("$TAG: 更新推播排程設定")
        scheduleDailyNotification(context, settings)
    }

    /**
     * 初始化推播排程（應用啟動時調用）
     */
    fun initializeSchedule(context: Context) {
        try {
            // 從資料庫載入設定
            val box = ObjectBox.get().boxFor(NotificationSettings::class.java)
            var settings = box.query().build().findFirst()

            // 如果沒有設定，創建並保存預設設定
            if (settings == null) {
                settings = NotificationSettings.getDefault()
                box.put(settings)
                CoreLogUtil.d("$TAG: 已創建預設推播設定")
            }

            // 設定排程
            scheduleDailyNotification(context, settings)

            CoreLogUtil.d("$TAG: 已初始化推播排程，推播開關: ${settings.isDailyTransitEnabled}, 時間: ${settings.getNotificationTimeString()}")
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 初始化推播排程失敗: ${e.message}")
        }
    }
}
