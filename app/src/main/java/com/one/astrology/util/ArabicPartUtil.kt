package com.one.astrology.util

import com.one.astrology.constant.ArabicPart
import com.one.astrology.constant.ArabicPart.arabicParts
import com.one.astrology.constant.Planet
import com.one.astrology.data.Houses
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.util.EphemerisUtil.Companion.house
import com.one.astrology.util.EphemerisUtil.Companion.isDay
import com.one.astrology.util.EphemerisUtil.Companion.signList
import kotlin.math.abs

/**
 * 阿拉伯點計算工具類
 * 用於計算各種阿拉伯點的位置
 */
object ArabicPartUtil {

    // 將度數限制在 0 ~ 360 範圍內
    private fun mod360(degree: Double): Double {
        val result = degree % 360.0
        return if (result >= 0) result else result + 360.0
    }

    // 計算任一特殊點的基本公式：ASC + B - A
    private fun calculateLot(ascendant: Double, a: Double, b: Double): Double {
        return mod360(ascendant + b - a)
    }

    // 旺點（Lot of Exaltation）：
    // 日生：ASC + 牡羊19° - 太陽（19°）；夜生：ASC + 金牛3° - 月亮（33°）
    private fun lotOfExaltation(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDayChart: Boolean
    ): Double {
        val sunAngle = getPlanetAngle(planets, Planet.SUN)
        val moonAngle = getPlanetAngle(planets, Planet.MOON)

        return if (isDayChart) calculateLot(ascendant, sunAngle, 19.0)
        else calculateLot(ascendant, 33.0, moonAngle)
    }

    /**
     * 計算指定阿拉伯點的角度
     * @param arabicPartId 阿拉伯點ID
     * @param planets 行星列表
     * @param houses 宮位數據
     * @return 阿拉伯點的角度（0-360度）
     */
    fun calculateArabicPartAngle(
        arabicPartId: Int,
        planets: ArrayList<PlanetBean>,
        houses: Houses
    ): PlanetBean {
        // 獲取上升點角度
        val ascendant = houses.getAsc()
        val des = houses.getDes()
        val sun = getPlanetAngle(planets, Planet.SUN)
        val isDay = isDay(ascendant, des, sun)
        // 根據不同的阿拉伯點ID計算角度
        val angle = when (arabicPartId) {
            ArabicPart.FORTUNE -> calculateFortune(ascendant, planets, isDay)
            ArabicPart.SPIRIT -> calculateSpirit(ascendant, planets, isDay)
            ArabicPart.BASIS -> calculateSpirit(ascendant, planets, isDay)
            ArabicPart.VENUS_LOT -> calculateVenusLot(ascendant, planets, isDay)
            ArabicPart.MERCURY_LOT -> calculateMercuryLot(ascendant, planets, isDay)
            ArabicPart.COURAGE -> calculateCourage(ascendant, planets, isDay)
            ArabicPart.VICTORY -> calculateVictory(ascendant, planets, isDay)
            ArabicPart.NEMESIS -> calculateNemesis(ascendant, planets, isDay)
            ArabicPart.EXALTATION -> lotOfExaltation(ascendant, planets, isDay)

            ArabicPart.EROS -> calculateEros(ascendant, planets, isDay)
//            ArabicPart.MARRIAGE -> calculateMarriage(ascendant, planets, houses)
            ArabicPart.MARRIAGE_MEN -> calculateMarriageMen(ascendant, planets, isDay)
            ArabicPart.MARRIAGE_WOMEN -> calculateMarriageWomen(ascendant, planets, isDay)

            ArabicPart.CHILDREN -> calculateChildren(ascendant, planets, isDay)
            ArabicPart.FATHER -> calculateFather(ascendant, planets, isDay)
            ArabicPart.MOTHER -> calculateMother(ascendant, planets, isDay)
            ArabicPart.BROTHERS -> calculateBrothers(ascendant, planets, isDay)
            ArabicPart.DEATH -> calculateDeath(houses.cusps[9], planets, isDay)
            ArabicPart.ILLNESS -> calculateIllness(ascendant, planets, isDay)
            ArabicPart.CAREER -> calculateCareer(ascendant, planets, isDay)
            ArabicPart.COMMERCE -> calculateCommerce(ascendant, planets, isDay)
            ArabicPart.TRAVEL -> calculateTravel(ascendant, planets, isDay)
            ArabicPart.NECESSITY -> calculateNecessity(ascendant, planets, isDay)
            ArabicPart.FAITH -> calculateFaith(ascendant, planets, isDay)
            ArabicPart.DESTINY -> calculateDestiny(ascendant, planets, isDay)
            ArabicPart.INHERITANCE -> calculateInheritance(ascendant, planets, isDay)
            else -> 0.0
        }
        val item = arabicParts.find { it.id == arabicPartId }
        val strings: ArrayList<String> = EphemerisUtil.szZodiac(angle)
        val planet = PlanetBean()
        planet.id = arabicPartId
        if (item != null) {
            planet.chName = item.chName
            planet.enName = item.enName
            planet.symbol = item.symbol
            planet.description = item.description
        }
        planet.longitude = angle
        planet.signBean = SignBean(signList[strings[0].toInt()])
        planet.signBean.houseData = house(planet.longitude, houses.cusps)
        planet.signBean.degree = strings[1] + "°"
        planet.signBean.minute = strings[2]
        planet.houseData = house(planet.longitude, houses.cusps)

        return planet
    }

    /**
     * 計算福點 (Part of Fortune)
     * 白天：ASC + Moon - Sun
     * 夜晚：ASC + Sun - Moon
     */
    private fun calculateFortune(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val sunAngle = getPlanetAngle(planets, Planet.SUN)
        val moonAngle = getPlanetAngle(planets, Planet.MOON)

        return if (isDay) {
            normalizeAngle(ascendant + moonAngle - sunAngle)
        } else {
            normalizeAngle(ascendant + sunAngle - moonAngle)
        }
    }

    /**
     * 計算精神點 (Part of Spirit)
     * 白天：ASC + Sun - Moon
     * 夜晚：ASC + Moon - Sun
     */
    private fun calculateSpirit(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val sunAngle = getPlanetAngle(planets, Planet.SUN)
        val moonAngle = getPlanetAngle(planets, Planet.MOON)

        return if (isDay) {
            normalizeAngle(ascendant + sunAngle - moonAngle)
        } else {
            normalizeAngle(ascendant + moonAngle - sunAngle)
        }
    }

    // 計算基礎點 (Part of Basis)
    fun calculateBasis(ascendant: Double, planets: ArrayList<PlanetBean>, isDay: Boolean): Double {
        val mercury = getPlanetAngle(planets, Planet.MERCURY)
        val jupiter = getPlanetAngle(planets, Planet.JUPITER)
        return if (isDay) {
            normalizeAngle(ascendant + mercury - jupiter)
        } else {
            normalizeAngle(ascendant + jupiter - mercury)
        }
    }

    // 計算金星點 (Lot of Venus)
    private fun calculateVenusLot(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val spirit = calculateSpirit(ascendant, planets, isDay)
        val fortune = calculateFortune(ascendant, planets, isDay)
        return if (isDay) {
            normalizeAngle(ascendant + spirit - fortune)
        } else {
            normalizeAngle(ascendant + fortune - spirit)
        }
    }

    // 計算水星點 (Lot of Mercury)
    private fun calculateMercuryLot(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val spirit = calculateSpirit(ascendant, planets, isDay)
        val fortune = calculateFortune(ascendant, planets, isDay)
        return if (isDay) {
            normalizeAngle(ascendant + fortune - spirit)
        } else {
            normalizeAngle(ascendant + spirit - fortune)
        }
    }

    // 計算勇氣點 (Part of Courage)
    private fun calculateCourage(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val mars = getPlanetAngle(planets, Planet.MARS)
        val fortune = calculateFortune(ascendant, planets, isDay)
        return if (isDay) {
            normalizeAngle(ascendant + fortune - mars)
        } else {
            normalizeAngle(ascendant + mars - fortune)
        }
    }

    // 計算勝利點 (Part of Victory)
    private fun calculateVictory(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val spirit = calculateSpirit(ascendant, planets, isDay)
        val jupiter = getPlanetAngle(planets, Planet.JUPITER)
        return if (isDay) {
            normalizeAngle(ascendant + jupiter - spirit)
        } else {
            normalizeAngle(ascendant + spirit - jupiter)
        }
    }

    // 計算報應點 (Part of Nemesis)
    private fun calculateNemesis(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val fortune = calculateFortune(ascendant, planets, isDay)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + fortune - saturn)
        } else {
            normalizeAngle(ascendant + saturn - fortune)
        }
    }

    // 計算旺點 (Lot of Exaltation)
    fun calculateExaltation(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val sun = getPlanetAngle(planets, Planet.SUN)
        val moon = getPlanetAngle(planets, Planet.MOON)
        return if (isDay) {
            normalizeAngle(ascendant + 19 - sun)
        } else {
            normalizeAngle(ascendant + 33 - moon)
        }
    }

    // 計算父親點 (Part of Father)
    fun calculateFather(ascendant: Double, planets: ArrayList<PlanetBean>, isDay: Boolean): Double {
        val sun = getPlanetAngle(planets, Planet.SUN)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + saturn - sun)
        } else {
            normalizeAngle(ascendant + sun - saturn)
        }
    }

    // 計算母親點 (Part of Mother)
    private fun calculateMother(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val moon = getPlanetAngle(planets, Planet.MOON)
        val venus = getPlanetAngle(planets, Planet.VENUS)
        return if (isDay) {
            normalizeAngle(ascendant + moon - venus)
        } else {
            normalizeAngle(ascendant + venus - moon)
        }
    }

    // 計算婚姻點 (Part of Marriage)
    private fun calculateMarriageMen(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val venus = getPlanetAngle(planets, Planet.VENUS)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + venus - saturn)
        } else {
            normalizeAngle(ascendant + saturn - venus)
        }
    }

    // 計算婚姻點 (Part of Marriage)
    fun calculateMarriageWomen(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val venus = getPlanetAngle(planets, Planet.VENUS)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + saturn - venus)
        } else {
            normalizeAngle(ascendant + venus - saturn)
        }
    }

    // 計算子女點 (Part of Children)
    private fun calculateChildren(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        val jupiter = getPlanetAngle(planets, Planet.JUPITER)
        return if (isDay) {
            normalizeAngle(ascendant + saturn - jupiter)
        } else {
            normalizeAngle(ascendant + jupiter - saturn)
        }
    }

    // 計算愛情點 (Part of Eros)
    private fun calculateEros(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val venus = getPlanetAngle(planets, Planet.VENUS)
        val mars = getPlanetAngle(planets, Planet.MARS)
        return if (isDay) {
            normalizeAngle(ascendant + venus - mars)
        } else {
            normalizeAngle(ascendant + mars - venus)
        }
    }

    // 計算兄弟點 (Part of Brothers)
    private fun calculateBrothers(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val mercury = getPlanetAngle(planets, Planet.MERCURY)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + mercury - saturn)
        } else {
            normalizeAngle(ascendant + saturn - mercury)
        }
    }

    // 計算死亡點 (Part of Death)
    private fun calculateDeath(
        house8: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val moon = getPlanetAngle(planets, Planet.MOON)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(saturn + house8 - moon)
        } else {
            normalizeAngle(saturn + moon - house8)
        }
    }

    // 計算疾病點 (Part of Illness)
    private fun calculateIllness(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val mercury = getPlanetAngle(planets, Planet.MERCURY)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + mercury - saturn)
        } else {
            normalizeAngle(ascendant + saturn - mercury)
        }
    }

    // 計算事業點 (Part of Career)
    private fun calculateCareer(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val sun = getPlanetAngle(planets, Planet.SUN)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + sun - saturn)
        } else {
            normalizeAngle(ascendant + saturn - sun)
        }
    }

    // 計算商業點 (Part of Commerce)
    private fun calculateCommerce(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val mercury = getPlanetAngle(planets, Planet.MERCURY)
        val venus = getPlanetAngle(planets, Planet.VENUS)
        return if (isDay) {
            normalizeAngle(ascendant + mercury - venus)
        } else {
            normalizeAngle(ascendant + venus - mercury)
        }
    }

    // 計算旅行點 (Part of Travel)
    private fun calculateTravel(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val jupiter = getPlanetAngle(planets, Planet.JUPITER)
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        return if (isDay) {
            normalizeAngle(ascendant + jupiter - saturn)
        } else {
            normalizeAngle(ascendant + saturn - jupiter)
        }
    }

    // 計算必然點 (Part of Necessity)
    private fun calculateNecessity(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        val jupiter = getPlanetAngle(planets, Planet.JUPITER)
        return if (isDay) {
            normalizeAngle(ascendant + saturn - jupiter)
        } else {
            normalizeAngle(ascendant + jupiter - saturn)
        }
    }

    // 計算信仰點 (Part of Faith)
    private fun calculateFaith(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val jupiter = getPlanetAngle(planets, Planet.JUPITER)
        val venus = getPlanetAngle(planets, Planet.VENUS)
        return if (isDay) {
            normalizeAngle(ascendant + jupiter - venus)
        } else {
            normalizeAngle(ascendant + venus - jupiter)
        }
    }

    // 計算命運點 (Part of Destiny)
    fun calculateDestiny(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        val jupiter = getPlanetAngle(planets, Planet.JUPITER)
        return if (isDay) {
            normalizeAngle(ascendant + saturn - jupiter)
        } else {
            normalizeAngle(ascendant + jupiter - saturn)
        }
    }

    // 計算遺產點 (Part of Inheritance)
    fun calculateInheritance(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        isDay: Boolean
    ): Double {
        val saturn = getPlanetAngle(planets, Planet.SATURN)
        val moon = getPlanetAngle(planets, Planet.MOON)
        return if (isDay) {
            normalizeAngle(ascendant + saturn - moon)
        } else {
            normalizeAngle(ascendant + moon - saturn)
        }
    }


    /**
     * 計算疾病點 (Part of Illness)
     * ASC + Mars - Saturn
     */
    private fun calculateIllness(
        ascendant: Double,
        planets: ArrayList<PlanetBean>
    ): Double {
        val marsAngle = getPlanetAngle(planets, Planet.MARS)
        val saturnAngle = getPlanetAngle(planets, Planet.SATURN)

        return normalizeAngle(ascendant + marsAngle - saturnAngle)
    }

    /**
     * 計算商業點 (Part of Commerce)
     * ASC + Mercury - Sun
     */
    private fun calculateCommerce(
        ascendant: Double,
        planets: ArrayList<PlanetBean>
    ): Double {
        val mercuryAngle = getPlanetAngle(planets, Planet.MERCURY)
        val sunAngle = getPlanetAngle(planets, Planet.SUN)

        return normalizeAngle(ascendant + mercuryAngle - sunAngle)
    }

    /**
     * 計算旅行點 (Part of Travel)
     * ASC + 9th House Cusp - Moon
     */
    private fun calculateTravel(
        ascendant: Double,
        planets: ArrayList<PlanetBean>,
        houses: Houses
    ): Double {
        val ninthHouseCusp = houses.cusps[8] // 第9宮的宮頭，索引從0開始
        val moonAngle = getPlanetAngle(planets, Planet.MOON)

        return normalizeAngle(ascendant + ninthHouseCusp - moonAngle)
    }

    /**
     * 計算勝利點 (Part of Victory)
     * ASC + Jupiter - Sun
     */
    private fun calculateVictory(
        ascendant: Double,
        planets: ArrayList<PlanetBean>
    ): Double {
        val jupiterAngle = getPlanetAngle(planets, Planet.JUPITER)
        val sunAngle = getPlanetAngle(planets, Planet.SUN)

        return normalizeAngle(ascendant + jupiterAngle - sunAngle)
    }

    /**
     * 計算復仇點 (Part of Nemesis)
     * ASC + Saturn - Mars
     */
    private fun calculateNemesis(
        ascendant: Double,
        planets: ArrayList<PlanetBean>
    ): Double {
        val saturnAngle = getPlanetAngle(planets, Planet.SATURN)
        val marsAngle = getPlanetAngle(planets, Planet.MARS)

        return normalizeAngle(ascendant + saturnAngle - marsAngle)
    }

    /**
     * 計算必然點 (Part of Necessity)
     * ASC + Saturn - Sun
     */
    private fun calculateNecessity(
        ascendant: Double,
        planets: ArrayList<PlanetBean>
    ): Double {
        val saturnAngle = getPlanetAngle(planets, Planet.SATURN)
        val sunAngle = getPlanetAngle(planets, Planet.SUN)

        return normalizeAngle(ascendant + saturnAngle - sunAngle)
    }


    /**
     * 獲取行星角度
     * @param planets 行星列表
     * @param planetId 行星ID
     * @return 行星角度
     */
    private fun getPlanetAngle(planets: ArrayList<PlanetBean>, planetId: Int): Double {
        for (planet in planets) {
            if (planet.id == planetId) {
                return planet.longitude
            }
        }
        return 0.0
    }

    /**
     * 標準化角度到0-360度範圍
     * @param angle 角度
     * @return 標準化後的角度
     */
    private fun normalizeAngle(angle: Double): Double {
        var result = angle % 360.0
        if (result < 0) {
            result += 360.0
        }
        return result
    }

    /**
     * 計算兩個角度之間的最小距離
     * @param angle1 角度1
     * @param angle2 角度2
     * @return 最小距離（0-180度）
     */
    fun calculateAngleDistance(angle1: Double, angle2: Double): Double {
        val diff = abs(angle1 - angle2) % 360.0
        return if (diff > 180.0) 360.0 - diff else diff
    }

    /**
     * 判斷兩個角度是否形成特定相位
     * @param angle1 角度1
     * @param angle2 角度2
     * @param aspectAngle 相位角度（如：0, 60, 90, 120, 180等）
     * @param orb 容許度（允許的誤差範圍）
     * @return 是否形成相位
     */
    fun isAspect(angle1: Double, angle2: Double, aspectAngle: Double, orb: Double): Boolean {
        val distance = calculateAngleDistance(angle1, angle2)
        return abs(distance - aspectAngle) <= orb
    }

    /**
     * 獲取阿拉伯點所在星座
     * @param angle 阿拉伯點角度
     * @return 星座ID (1-12)
     */
    fun getSign(angle: Double): Int {
        return (angle / 30.0).toInt() + 1
    }

    /**
     * 獲取阿拉伯點所在宮位
     * @param angle 阿拉伯點角度
     * @param houses 宮位數據
     * @return 宮位編號 (1-12)
     */
    fun getHouse(angle: Double, houses: Houses): Int {
        val cusps = houses.cusps

        // 檢查角度是否在第12宮和第1宮之間
        if (isAngleBetween(angle, cusps[11], cusps[0])) {
            return 12
        }

        // 檢查其他宮位
        for (i in 0 until 11) {
            if (isAngleBetween(angle, cusps[i], cusps[i + 1])) {
                return i + 1
            }
        }

        return 1 // 默認返回第1宮
    }

    /**
     * 判斷角度是否在兩個邊界之間
     * 考慮到宮位可能跨越0度的情況
     */
    private fun isAngleBetween(angle: Double, start: Double, end: Double): Boolean {
        val normalizedAngle = normalizeAngle(angle)
        val normalizedStart = normalizeAngle(start)
        val normalizedEnd = normalizeAngle(end)

        return if (normalizedStart < normalizedEnd) {
            normalizedAngle >= normalizedStart && normalizedAngle < normalizedEnd
        } else {
            // 處理跨越0度的情況
            normalizedAngle >= normalizedStart || normalizedAngle < normalizedEnd
        }
    }
} 