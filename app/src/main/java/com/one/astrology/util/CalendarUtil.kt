package com.one.astrology.util

import java.util.*
import kotlin.math.abs

class CalendarUtil {
    companion object {

        /**
         * 獲取兩個日期相差的月數
         */
        fun getMonthDiff(d1: Long, d2: Long): Int {
            val c1: Calendar = Calendar.getInstance()
            val c2: Calendar = Calendar.getInstance()
            c1.timeInMillis = d1
            c2.timeInMillis = d2
            val year1: Int = c1.get(Calendar.YEAR)
            val year2: Int = c2.get(Calendar.YEAR)
            val month1: Int = c1.get(Calendar.MONTH)
            val month2: Int = c2.get(Calendar.MONTH)
            val day1: Int = c1.get(Calendar.DAY_OF_MONTH)
            val day2: Int = c2.get(Calendar.DAY_OF_MONTH)
            // 獲取年的差值
            var yearInterval = year1 - year2
            // 如果 d1的 月-日 小於 d2的 月-日 那麼 yearInterval-- 這樣就得到了相差的年數
            if (month1 < month2 || month1 == month2 && day1 < day2) {
                yearInterval--
            }
            // 獲取月數差值
            var monthInterval = month1 + 12 - month2
            if (day1 < day2) {
                monthInterval--
            }
            monthInterval %= 12
            return abs(yearInterval * 12 + monthInterval)
        }

        fun getDayDiff(firstDate: Long, secondDate: Long): Int {
            val firstDay: Long = (firstDate / (86400.0 * 1000.0)).toLong()
            val secondDay: Long = (secondDate / (86400.0 * 1000.0)).toLong()
            return (firstDay - secondDay).toInt()
        }
    }
}