package com.one.astrology.util

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.Horoscope
import com.one.astrology.data.bean.AspectType
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.TransitEvent
import com.one.astrology.data.model.TransitEventType
import com.one.astrology.data.model.TransitImportance
import com.one.astrology.data.type.Chart
import com.one.core.util.LogUtil
import swisseph.SweConst
import kotlin.math.abs

/**
 * 行運計算器
 * 計算行運行星與本命行星的相位、宮位變化、星座切換等事件
 */
class TransitCalculator {

    companion object {
        // 主要行星ID列表
        private val MAIN_PLANETS = listOf(
            SweConst.SE_SUN,      // 太陽
            SweConst.SE_MOON,     // 月亮
            SweConst.SE_MERCURY,  // 水星
            SweConst.SE_VENUS,    // 金星
            SweConst.SE_MARS,     // 火星
            SweConst.SE_JUPITER,  // 木星
            SweConst.SE_SATURN,   // 土星
            SweConst.SE_URANUS,   // 天王星
            SweConst.SE_NEPTUNE,  // 海王星
            SweConst.SE_PLUTO     // 冥王星
        )

        // 快速行星（用於短期預測）
        private val FAST_PLANETS = listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_MARS
        )

        // 慢速行星（用於長期預測）
        private val SLOW_PLANETS = listOf(
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
            SweConst.SE_URANUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_PLUTO
        )

        // 行星名稱映射
        private val PLANET_NAMES = mapOf(
            SweConst.SE_SUN to "太陽",
            SweConst.SE_MOON to "月亮",
            SweConst.SE_MERCURY to "水星",
            SweConst.SE_VENUS to "金星",
            SweConst.SE_MARS to "火星",
            SweConst.SE_JUPITER to "木星",
            SweConst.SE_SATURN to "土星",
            SweConst.SE_URANUS to "天王星",
            SweConst.SE_NEPTUNE to "海王星",
            SweConst.SE_PLUTO to "冥王星"
        )

        // 星座名稱
        private val ZODIAC_SIGNS = listOf(
            "牡羊座", "金牛座", "雙子座", "巨蟹座", "獅子座", "處女座",
            "天秤座", "天蠍座", "射手座", "摩羯座", "水瓶座", "雙魚座"
        )

        // 預設容許度設定
        private val DEFAULT_ORBS = mapOf(
            0 to 8.0,    // 合相
            60 to 4.0,   // 六分相
            90 to 6.0,   // 四分相
            120 to 6.0,  // 三分相
            180 to 8.0   // 對分相
        )
    }

    fun getTransit(
        context: Context,
        chart: Chart,
        signRecordA: BirthData,
        signRecordB: BirthData
    ): Horoscope {
        val horoscopeA = EphemerisUtil.calculate(
            context,
            chart,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val horoscopeB = EphemerisUtil.calculate(
            context,
            chart,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude)
        )
        horoscopeA.aspectList =
            EphemerisUtil.aspects(
                context,
                chart,
                horoscopeB.planetList,
                horoscopeA.planetList,
                true, true
            )
        horoscopeB.aspectList = horoscopeA.aspectList

        return horoscopeB
    }


    /**
     * 計算指定日期的行運事件
     * @param context 上下文
     * @param birthData 出生資料
     * @param targetDate 目標日期
     * @param daysRange 計算範圍（天數）
     * @return 行運事件列表
     */
    fun calculateTransitEvents(
        context: Context,
        birthData: BirthData,
        targetDate: Long = System.currentTimeMillis(),
        daysRange: Int = 7
    ): List<TransitEvent> {
        val events = mutableListOf<TransitEvent>()

        try {
            // 計算本命盤
            val transitChart = EphemerisUtil.calculate(
                context,
                Chart.Natal,
                birthData.name,
                birthData.birthday,
                LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
            )

            val horoscopeB = EphemerisUtil.calculate(
                context,
                Chart.Transit,
                birthData.name + " 行運盤",
                System.currentTimeMillis(),
                LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
            )

            transitChart.aspectList = EphemerisUtil.aspects(
                context,
                Chart.Transit,
                horoscopeB.planetList,
                transitChart.planetList,
                true,
                true
            )

            // 計算指定日期範圍內的事件
            val startDate = targetDate - (daysRange / 2) * 24 * 60 * 60 * 1000L
            val endDate = targetDate + (daysRange / 2) * 24 * 60 * 60 * 1000L

            // 每天檢查一次
            var currentDate = startDate
            while (currentDate <= endDate) {
                val dailyEvents = calculateDailyTransitEvents(
                    birthData,
                    transitChart,
                    currentDate
                )
                events.addAll(dailyEvents)
                currentDate += 24 * 60 * 60 * 1000L // 增加一天
            }

            // 按時間排序
            events.sortBy { it.eventTime }

        } catch (e: Exception) {
            LogUtil.e("計算行運事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 計算單日的行運事件
     */
    private fun calculateDailyTransitEvents(
        birthData: BirthData,
        transitChart: Horoscope,
        date: Long
    ): List<TransitEvent> {
        val events = mutableListOf<TransitEvent>()

        try {
            // 檢查相位事件
            events.addAll(checkAspectEvents(birthData, transitChart, date))

            // 檢查宮位變化事件
            events.addAll(checkHouseChangeEvents(birthData, transitChart, date))

            // 檢查星座切換事件
            events.addAll(checkSignChangeEvents(birthData, transitChart, date))

            // 檢查逆行事件
            events.addAll(checkRetrogradeEvents(birthData, transitChart, date))

        } catch (e: Exception) {
            LogUtil.e("計算單日行運事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 檢查相位事件
     */
    private fun checkAspectEvents(
        birthData: BirthData,
        transitChart: Horoscope,
        date: Long
    ): List<TransitEvent> {
        val events = mutableListOf<TransitEvent>()

        // 實際實現需要計算行運行星與本命行星的相位
        try {
            transitChart.aspectList.forEach { aspect ->
                val event = TransitEvent().apply {
                    eventType = TransitEventType.ASPECT_FORMING
                    eventTime = date
                    transitPlanetName = aspect.planetA
                    natalPlanetName = aspect.planetB
                    aspectName = aspect.type
                    aspectDirection = aspect.direction
                    orb = aspect.orb!!
                    exactness = 0.0
                    importance = TransitImportance.MEDIUM
                    description = "${aspect.planetA} ${aspect.type} ${aspect.planetB}"
                    birthDataName = birthData.name
                }
                events.add(event)
            }
        } catch (e: Exception) {
            LogUtil.e("創建相位事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 檢查宮位變化事件
     */
    private fun checkHouseChangeEvents(
        birthData: BirthData,
        transitChart: Horoscope,
        date: Long
    ): List<TransitEvent> {
        val events = mutableListOf<TransitEvent>()

        // 這裡需要比較前一天的宮位來檢測變化
        // 為簡化實現，暫時跳過詳細的宮位變化檢測
        // 實際實現時需要儲存前一天的行星宮位資訊進行比較

        return events
    }

    /**
     * 檢查星座切換事件
     */
    private fun checkSignChangeEvents(
        birthData: BirthData,
        transitChart: Horoscope,
        date: Long
    ): List<TransitEvent> {
        val events = mutableListOf<TransitEvent>()

        // 這裡需要比較前一天的星座來檢測切換
        // 為簡化實現，暫時跳過詳細的星座切換檢測
        // 實際實現時需要儲存前一天的行星星座資訊進行比較

        return events
    }

    /**
     * 檢查逆行事件
     */
    private fun checkRetrogradeEvents(
        birthData: BirthData,
        transitChart: Horoscope,
        date: Long
    ): List<TransitEvent> {
        val events = mutableListOf<TransitEvent>()

        // 簡化實現：暫時跳過逆行檢測
        // 實際實現需要比較前一天的逆行狀態來檢測變化
        try {
            // 這裡可以添加逆行檢測邏輯
            LogUtil.d("檢查逆行事件：${birthData.name}")
        } catch (e: Exception) {
            LogUtil.e("檢查逆行事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 計算相位資訊
     * @return Triple<AspectType, orb, direction> 或 null
     */
    private fun calculateAspect(
        transitLongitude: Double,
        natalLongitude: Double
    ): Triple<AspectType, Double, String>? {
        val angle = calculateAngleDifference(transitLongitude, natalLongitude)

        // 檢查各種相位
        for (aspectType in AspectType.entries) {
            val targetAngle = aspectType.value.toDouble()
            val orb = abs(angle - targetAngle)

            if (orb <= (DEFAULT_ORBS[aspectType.value] ?: 3.0)) {
                // 判斷相位方向（簡化實現）
                val direction = if (orb < 0.5) {
                    "精確相位"
                } else {
                    "入相位" // 簡化為入相位
                }

                return Triple(aspectType, orb, direction)
            }
        }

        return null
    }

    /**
     * 計算角度差異
     */
    private fun calculateAngleDifference(angle1: Double, angle2: Double): Double {
        var diff = abs(angle1 - angle2)
        if (diff > 180) {
            diff = 360 - diff
        }
        return diff
    }

    /**
     * 計算事件重要性
     */
    private fun calculateImportance(
        transitPlanetId: Int,
        natalPlanetId: Int,
        aspectType: Int,
        orb: Double
    ): TransitImportance {
        var score = 0

        // 根據行星重要性評分
        if (transitPlanetId == SweConst.SE_SUN || natalPlanetId == SweConst.SE_SUN) score += 3
        if (transitPlanetId == SweConst.SE_MOON || natalPlanetId == SweConst.SE_MOON) score += 2
        if (SLOW_PLANETS.contains(transitPlanetId)) score += 2

        // 根據相位類型評分
        when (aspectType) {
            0, 180 -> score += 3  // 合相、對分相
            90 -> score += 2      // 四分相
            120, 60 -> score += 1 // 三分相、六分相
        }

        // 根據精確度評分
        if (orb < 1.0) score += 2
        else if (orb < 2.0) score += 1

        return when {
            score >= 7 -> TransitImportance.CRITICAL
            score >= 5 -> TransitImportance.HIGH
            score >= 3 -> TransitImportance.MEDIUM
            else -> TransitImportance.LOW
        }
    }

    /**
     * 生成相位描述
     */
    private fun generateAspectDescription(
        transitPlanet: PlanetBean,
        natalPlanet: PlanetBean,
        aspectType: AspectType,
        orb: Double
    ): String {
        val transitName = PLANET_NAMES[transitPlanet.id] ?: "未知行星"
        val natalName = PLANET_NAMES[natalPlanet.id] ?: "未知行星"
        val aspectName = aspectType.nameCh

        return "$transitName 與本命 $natalName 形成 $aspectName，容許度 ${
            String.format(
                "%.1f",
                orb
            )
        }°"
    }
}
