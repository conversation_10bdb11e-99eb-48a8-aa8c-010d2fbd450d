package com.one.astrology.util

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.model.AspectType
import com.one.astrology.data.model.AstrologyEvent
import com.one.astrology.data.model.AstrologyEventFactory
import com.one.astrology.data.model.MoonPhase
import com.one.astrology.data.model.SolarTerm
import com.one.core.util.LogUtil
import swisseph.SweConst
import java.util.Calendar
import java.util.Date
import kotlin.math.abs

/**
 * 星象事件計算器
 */
object AstrologyEventCalculator {

    // 主要行星ID列表
    private val mainPlanets = listOf(
        SweConst.SE_SUN, SweConst.SE_MOON, SweConst.SE_MERCURY,
        SweConst.SE_VENUS, SweConst.SE_MARS, SweConst.SE_JUPITER,
        SweConst.SE_SATURN, SweConst.SE_URANUS, SweConst.SE_NEPTUNE,
        SweConst.SE_PLUTO
    )

    // 行星名稱映射
    private val planetNames = mapOf(
        SweConst.SE_SUN to "太陽",
        SweConst.SE_MOON to "月亮",
        SweConst.SE_MERCURY to "水星",
        SweConst.SE_VENUS to "金星",
        SweConst.SE_MARS to "火星",
        SweConst.SE_JUPITER to "木星",
        SweConst.SE_SATURN to "土星",
        SweConst.SE_URANUS to "天王星",
        SweConst.SE_NEPTUNE to "海王星",
        SweConst.SE_PLUTO to "冥王星"
    )

    // 星座名稱映射
    private val signNames = listOf(
        "牡羊座", "金牛座", "雙子座", "巨蟹座", "獅子座", "處女座",
        "天秤座", "天蠍座", "射手座", "摩羯座", "水瓶座", "雙魚座"
    )

    /**
     * 計算指定日期的所有星象事件
     */
    fun calculateDailyEvents(
        context: Context,
        date: Date,
        location: LatLng = LatLng(25.0330, 121.5654) // 預設台北
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()

        try {
            // 初始化 Swiss Ephemeris
            if (!EphemerisUtil.initData(context)) {
                LogUtil.e("Failed to initialize Swiss Ephemeris")
                return emptyList()
            }

            // 計算行星相位事件
            events.addAll(calculateAspectEvents(context, date, location))

            // 計算行星換座事件
            events.addAll(calculateSignChangeEvents(context, date, location))

            // 計算月相事件
            events.addAll(calculateMoonPhaseEvents(context, date))

            // 計算行星逆行事件
            events.addAll(calculateRetrogradeEvents(context, date, location))

            // 計算節氣事件
            events.addAll(calculateSolarTermEvents(context, date))

            // 計算日月蝕事件（使用專業蝕相計算器）
//            events.addAll(EclipseCalculator.calculateEclipseForDate(context, date, location))

        } catch (e: Exception) {
            LogUtil.e("計算星象事件時發生錯誤: ${e.message}")
        }

        return events.sortedBy { it.importance.level }.reversed()
    }

    /**
     * 計算行星相位事件
     */
    private fun calculateAspectEvents(
        context: Context,
        date: Date,
        location: LatLng
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()

        try {
            val julDay = EphemerisUtil.getJulDay(date.time, location)
            val planetPositions = mutableMapOf<Int, Double>()

            // 獲取所有行星位置
            mainPlanets.forEach { planetId ->
                val angle = EphemerisUtil.getPlanetAngle(context, date.time, location, planetId, false)
                planetPositions[planetId] = angle
            }

            // 檢查行星間的相位
            for (i in mainPlanets.indices) {
                for (j in i + 1 until mainPlanets.size) {
                    val planet1Id = mainPlanets[i]
                    val planet2Id = mainPlanets[j]
                    val angle1 = planetPositions[planet1Id] ?: continue
                    val angle2 = planetPositions[planet2Id] ?: continue

                    val aspect = findAspect(angle1, angle2)
                    if (aspect != null) {
                        val event = AstrologyEventFactory.createAspectEvent(
                            date = date,
                            planet1Id = planet1Id,
                            planet1Name = planetNames[planet1Id] ?: "未知",
                            planet2Id = planet2Id,
                            planet2Name = planetNames[planet2Id] ?: "未知",
                            aspectType = aspect.first,
                            orb = aspect.second,
                            isExact = aspect.second <= 0.5
                        )
                        events.add(event)
                    }
                }
            }

        } catch (e: Exception) {
            LogUtil.e("計算相位事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 計算行星換座事件
     */
    private fun calculateSignChangeEvents(
        context: Context,
        date: Date,
        location: LatLng
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()

        try {
            val calendar = Calendar.getInstance()
            calendar.time = date

            // 檢查前一天和當天的行星位置
            calendar.add(Calendar.DAY_OF_MONTH, -1)
            val yesterdayTime = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val todayTime = calendar.timeInMillis

            mainPlanets.forEach { planetId ->
                val yesterdayAngle = EphemerisUtil.getPlanetAngle(context, yesterdayTime, location, planetId, false)
                val todayAngle = EphemerisUtil.getPlanetAngle(context, todayTime, location, planetId, false)

                val yesterdaySign = (yesterdayAngle / 30).toInt()
                val todaySign = (todayAngle / 30).toInt()

                if (yesterdaySign != todaySign) {
                    val event = AstrologyEventFactory.createSignChangeEvent(
                        date = date,
                        planetId = planetId,
                        planetName = planetNames[planetId] ?: "未知",
                        fromSign = yesterdaySign,
                        toSign = todaySign,
                        fromSignName = signNames[yesterdaySign % 12],
                        toSignName = signNames[todaySign % 12]
                    )
                    events.add(event)
                }
            }

        } catch (e: Exception) {
            LogUtil.e("計算換座事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 計算月相事件
     */
    private fun calculateMoonPhaseEvents(
        context: Context,
        date: Date
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()

        try {
            val julDay = EphemerisUtil.getJulDay(date.time, LatLng(0.0, 0.0))
            val moonPhase = calculateMoonPhase(context, julDay)

            if (moonPhase != null) {
                val event = AstrologyEventFactory.createMoonPhaseEvent(date, moonPhase)
                events.add(event)
            }

        } catch (e: Exception) {
            LogUtil.e("計算月相事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 計算行星逆行事件
     */
    private fun calculateRetrogradeEvents(
        context: Context,
        date: Date,
        location: LatLng
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()

        try {
            val calendar = Calendar.getInstance()
            calendar.time = date

            // 檢查前一天和當天的逆行狀態
            calendar.add(Calendar.DAY_OF_MONTH, -1)
            val yesterdayTime = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val todayTime = calendar.timeInMillis

            // 只檢查內行星的逆行
            val innerPlanets = listOf(SweConst.SE_MERCURY, SweConst.SE_VENUS, SweConst.SE_MARS)

            innerPlanets.forEach { planetId ->
                val wasRetrograde = EphemerisUtil.isPlanetRetrograde(context, yesterdayTime, planetId)
                val isRetrograde = EphemerisUtil.isPlanetRetrograde(context, todayTime, planetId)

                if (wasRetrograde != isRetrograde) {
                    val event = AstrologyEventFactory.createRetrogradeEvent(
                        date = date,
                        planetId = planetId,
                        planetName = planetNames[planetId] ?: "未知",
                        isStarting = isRetrograde
                    )
                    events.add(event)
                }
            }

        } catch (e: Exception) {
            LogUtil.e("計算逆行事件時發生錯誤: ${e.message}")
        }

        return events
    }

    /**
     * 計算節氣事件
     */
    private fun calculateSolarTermEvents(
        context: Context,
        date: Date
    ): List<AstrologyEvent> {
        val events = mutableListOf<AstrologyEvent>()

        try {
            val sunAngle = EphemerisUtil.getPlanetAngle(context, date.time, LatLng(0.0, 0.0), SweConst.SE_SUN, false)

            // 檢查是否接近節氣點（容許度±0.5度）
            SolarTerm.values().forEach { solarTerm ->
                val angleDiff = abs(normalizeAngle(sunAngle - solarTerm.longitude))
                if (angleDiff <= 0.5) {
                    val event = AstrologyEventFactory.createSolarTermEvent(date, solarTerm)
                    events.add(event)
                }
            }

        } catch (e: Exception) {
            LogUtil.e("計算節氣事件時發生錯誤: ${e.message}")
        }

        return events
    }



    /**
     * 尋找兩個角度間的相位
     */
    private fun findAspect(angle1: Double, angle2: Double): Pair<AspectType, Double>? {
        val diff = normalizeAngle(abs(angle1 - angle2))

        AspectType.values().forEach { aspectType ->
            val orb = abs(diff - aspectType.angle)
            val allowedOrb = when (aspectType) {
                AspectType.CONJUNCTION, AspectType.OPPOSITION -> 8.0
                AspectType.TRINE, AspectType.SQUARE -> 6.0
                AspectType.SEXTILE -> 4.0
                else -> 2.0
            }

            if (orb <= allowedOrb) {
                return Pair(aspectType, orb)
            }
        }

        return null
    }

    /**
     * 計算月相
     */
    private fun calculateMoonPhase(context: Context, julDay: Double): MoonPhase? {
        try {
            val timeMillis = ((julDay - 2451545.0) * 86400000).toLong()
            val sunAngle = EphemerisUtil.getPlanetAngle(context, timeMillis, LatLng(0.0, 0.0), SweConst.SE_SUN, false)
            val moonAngle = EphemerisUtil.getPlanetAngle(context, timeMillis, LatLng(0.0, 0.0), SweConst.SE_MOON, false)

            val phase = normalizeAngle(moonAngle - sunAngle)

            return when {
                phase <= 15.0 || phase >= 345.0 -> MoonPhase.NEW_MOON
                phase in 75.0..105.0 -> MoonPhase.FIRST_QUARTER
                phase in 165.0..195.0 -> MoonPhase.FULL_MOON
                phase in 255.0..285.0 -> MoonPhase.LAST_QUARTER
                else -> null
            }
        } catch (e: Exception) {
            LogUtil.e("計算月相時發生錯誤: ${e.message}")
            return null
        }
    }

    /**
     * 標準化角度到0-360度範圍
     */
    private fun normalizeAngle(angle: Double): Double {
        var normalized = angle % 360.0
        if (normalized < 0) normalized += 360.0
        return normalized
    }
}
