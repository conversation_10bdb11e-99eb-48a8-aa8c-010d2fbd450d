package com.one.astrology.util

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.Planet
import com.one.astrology.data.Horoscope
import com.one.astrology.data.HouseData
import com.one.astrology.data.Houses
import com.one.astrology.data.bean.Aspect
import com.one.astrology.data.bean.AspectType
import com.one.astrology.data.bean.Dignities
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.ReceptionData
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.bean.SignDescBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.db.BasicDBHelper
import com.one.astrology.util.astro.AstrologyTool
import com.one.astrology.util.astro.BurnChecker
import com.one.core.util.LogUtil
import swisseph.SweConst
import swisseph.SweDate
import swisseph.SwissEph
import java.io.File
import java.text.SimpleDateFormat
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import kotlin.math.abs
import kotlin.math.floor
import kotlin.math.roundToInt

/**
 * 星曆表
 * 參考
 * http://th-mack.de/download/swisseph-doc/swisseph/SwissEph.html#swe_houses-double-int-double-double-int-double:A-double:A-
 */
class EphemerisUtil {

    companion object {
        var houseSystem = 'P'
        lateinit var signList: ArrayList<SignBean>
        private lateinit var planetList: ArrayList<PlanetBean>
        private lateinit var swissEph: SwissEph

        /**
         * Initialize Swiss Ephemeris data and other required data
         * @param context Application context
         * @return true if initialization was successful, false otherwise
         */
        fun initData(context: Context): Boolean {
            try {
                // Check if ephemeris directory exists
                val epheDir = File(context.filesDir.toString() + File.separator + "ephe")
                if (!epheDir.exists()) {
                    // Create directory if it doesn't exist
                    epheDir.mkdirs()

                    // Copy ephemeris files from assets
                    CopyAssetFiles(".*\\.se1", context).copy()
                }

                // Check if at least one ephemeris file exists
                if (!areEphemerisFilesAvailable(epheDir)) {
                    LogUtil.e("No ephemeris files found in $epheDir")
                    // Try to copy files again
                    CopyAssetFiles(".*\\.se1", context).copy()

                    // Check again after copying
                    if (!areEphemerisFilesAvailable(epheDir)) {
                        LogUtil.e("Failed to copy ephemeris files")
                        return false
                    }
                }

                // Initialize Swiss Ephemeris
                swissEph = SwissEph(epheDir.absolutePath)

                // Set ephemeris path explicitly
                swissEph.swe_set_ephe_path(epheDir.absolutePath)

                // Load sign and planet data
                signList = AssetsToObjectUtil.getSignList(context)
                planetList = AssetsToObjectUtil.getPlanetList(context)

                val box = ObjectBox.get().boxFor(PlanetBean::class.java)
                val planetArrayList = ArrayList(box.all)
                for (i in 0 until planetList.size) {
                    val item = planetArrayList.find { it.id == planetList[i].id }
                    if (item != null) {
                        planetList[i].isChecked = item.isChecked
                    }
                }
                box.removeAll()
                box.put(planetList)

                return true
            } catch (e: Exception) {
                LogUtil.e("Error initializing ephemeris data: ${e.message}")
                return false
            }
        }

        /**
         * Check if ephemeris files are available in the specified directory
         * @param epheDir Directory containing ephemeris files
         * @return true if at least one ephemeris file exists, false otherwise
         */
        private fun areEphemerisFilesAvailable(epheDir: File): Boolean {
            if (!epheDir.exists() || !epheDir.isDirectory) {
                return false
            }

            // Check if directory contains at least one .se1 file
            val files = epheDir.listFiles { file -> file.name.endsWith(".se1") }
            return files != null && files.isNotEmpty()
        }

        fun getOffset(
            year: Int,
            month: Int,
            day: Int,
            hour: Int,
            minute: Int,
            latLng: LatLng,
        ): Pair<Double, Boolean> {
            val dHour = hour + minute / 60.0
            // 獲取當地時區
            val resultTimeZone =
                TimezoneMapper.latLngToTimezoneString(latLng.latitude, latLng.longitude)
            val zoneId = ZoneId.of(resultTimeZone)

            // 創建 ZonedDateTime，這裡會自動考慮 DST
            val dateTime = ZonedDateTime.of(year, month, day, hour, minute, 0, 0, zoneId)

            // 獲取當前時區的偏移量（包括 DST）
            val offset = dateTime.offset.totalSeconds / 3600.0 // 以小時為單位的偏移量

            // 判斷是否在 DST
            val isDaylightSavingTime = dateTime.zone.rules.isDaylightSavings(dateTime.toInstant())

            val timeZone: TimeZone = TimeZone.getTimeZone(resultTimeZone)
            val rawOffset = timeZone.rawOffset / 3600000.0

            LogUtil.i("==========================================================================================")
            LogUtil.i("出生資訊 : $year/$month/$day $hour:$minute dHour $dHour offset $offset $resultTimeZone")
            LogUtil.i("經緯度: $latLng")
            LogUtil.i("時間: $dateTime  offset ${dateTime.offset}")
            LogUtil.i("是否為日光節約時間: $isDaylightSavingTime")
            LogUtil.i("獲取當前時區的偏移量（包括 DST）: $offset  rawOffset $rawOffset")
            LogUtil.i("==========================================================================================")

            return Pair(offset, isDaylightSavingTime)
        }

        // 儒略日 (Julian day)
        fun getJulDay(
            time: Long,
            latLng: LatLng
        ): Double {
            val calendar = Calendar.getInstance()
            calendar.time = Date(time)
            val year = calendar[Calendar.YEAR]
            val month = calendar[Calendar.MONTH] + 1
            val day = calendar[Calendar.DAY_OF_MONTH]
            val hour = calendar[Calendar.HOUR_OF_DAY]
            val minute = calendar[Calendar.MINUTE]

            LogUtil.d("time: $time $year/$month/$day $hour:$minute")

            val offsetPair = getOffset(year, month, day, hour, minute, latLng)
            val offset = offsetPair.first
            val dHour = hour + minute / 60.0
            // 計算 Julian Day，這裡 dHour - offset 即使是考慮了 DST 偏移
            return SweDate(year, month, day, dHour - offset).julDay
        }

        private fun julianDayToDate(julDay: Double): Date {
            val sd = SweDate(julDay)
            val cal = Calendar.getInstance().apply {
                set(Calendar.YEAR, sd.year)
                set(Calendar.MONTH, sd.month)
                set(Calendar.DAY_OF_MONTH, sd.day)

                val hour = sd.hour
                val hr = hour.toInt()
//                val min = sd.getMinute(hour).toInt()
//                val sec = ((((hour - hr) * 60) - min) * 60).toInt()

                set(Calendar.HOUR_OF_DAY, hr)
//                set(Calendar.MINUTE, min)
//                set(Calendar.SECOND, sec)
                set(Calendar.MILLISECOND, 0)
            }
            return cal.time
        }

        fun calculate(
            context: Context,
            chart: Chart,
            name: String,
            time: Long,
            latLng: LatLng,
        ): Horoscope {
            val houseSystem = EncryptedSPUtil.getHouseSystem(context)
            val horoscope = calculate(
                context,
                chart,
                name,
                time,
                latLng,
                houseSystem
            )
            return horoscope
        }

        fun getHoroscope(
            context: Context,
            chart: Chart,
            name: String,
            time: Long,
            latLng: LatLng,
            houses: Houses
        ): Horoscope {
            val now = System.currentTimeMillis()
            val horoscope = Horoscope(name, time, latLng)

            try {
                // Initialize Swiss Ephemeris data
                val initSuccess = initData(context)
                if (!initSuccess) {
                    LogUtil.e("Failed to initialize Swiss Ephemeris in calculate")
                    return horoscope // Return empty horoscope as fallback
                }

                val julDay = getJulDay(time, latLng)
                LogUtil.i("julDay $julDay")


                // 處理行星度數
                val planetList = getPlanetListAngle(context, julDay, houses)

                // 處理四軸
                initAxis(context, planetList, houses)

                // 處理星位
                horoscope.planetList.addAll(planetList)

                // 處理相位
                val aspectList: List<AspectData> = aspects(
                    context,
                    chart,
                    horoscope.getPlanetBeanList(),
                    horoscope.getPlanetBeanList()
                )
                horoscope.aspectList.addAll(aspectList)
                LogUtil.d("耗時：${System.currentTimeMillis() - now}")
            } catch (e: Exception) {
                LogUtil.e("Exception in calculate: ${e.message}")
                // Return the basic horoscope even if calculation fails
            }

            return horoscope
        }

        fun calculate(
            context: Context,
            chart: Chart,
            name: String,
            time: Long,
            latLng: LatLng,
            houseSystem: Char
        ): Horoscope {
            val now = System.currentTimeMillis()
            val horoscope = Horoscope(name, time, latLng)

            try {
                // Initialize Swiss Ephemeris data
                val initSuccess = initData(context)
                if (!initSuccess) {
                    LogUtil.e("Failed to initialize Swiss Ephemeris in calculate")
                    return horoscope // Return empty horoscope as fallback
                }

                val julDay = getJulDay(time, latLng)
                LogUtil.i("julDay $julDay")

                // 處理宮位
                horoscope.houses = getHouseCusps(
                    julDay,
                    LatLng(latLng.latitude, latLng.longitude),
                    houseSystem
                )

                // 處理行星度數
                val planetList = getPlanetListAngle(context, julDay, horoscope.houses)

                // 處理四軸
                initAxis(context, planetList, horoscope.houses)

                // 處理星位
                horoscope.planetList.addAll(planetList)

                // 處理相位
                val aspectList: List<AspectData> = aspects(
                    context,
                    chart,
                    horoscope.getPlanetBeanList(),
                    horoscope.getPlanetBeanList()
                )
                horoscope.aspectList.addAll(aspectList)
                LogUtil.d("耗時：${System.currentTimeMillis() - now}")
//                horoscope.featureList = getFeature(planetList, aspectList)
//                horoscope.featureList.forEach {
//                    LogUtil.d("特徵 : $it")
//                }
            } catch (e: Exception) {
                LogUtil.e("Exception in calculate: ${e.message}")
                // Return the basic horoscope even if calculation fails
            }

            return horoscope
        }

        fun calculateComposite(
            context: Context, chart: Chart, horoscopeA: Horoscope, horoscopeB: Horoscope
        ): Horoscope {
            try {
                val initSuccess = initData(context)
                if (!initSuccess) {
                    LogUtil.e("Failed to initialize Swiss Ephemeris in calculateComposite")
                    return horoscopeA // Return input horoscope as fallback
                }

                LogUtil.d("calculate composite start -------------------")
                for (i in 0 until horoscopeA.getPlanetBeanList().size) {
                    var angleA = horoscopeA.planetList[i].longitude
                    var angleB = horoscopeB.planetList[i].longitude
                    LogUtil.d(horoscopeA.planetList[i].chName + " A : $angleA, B : $angleB")
                    if (angleA < angleB && horoscopeA.getPlanetBeanList()[i].id >= Planet.Asc) {
                        angleA += 360
                    }
                    if (angleA - angleB > 180) {
                        angleB += 360
                    }
                    var angle = (angleA + angleB) / 2.0
                    if (angle > 360) {
                        angle -= 360
                    }

                    LogUtil.d("Composite angle : $angle")
                    horoscopeA.planetList[i].longitude = angle
                }

                LogUtil.d("\n\ncusps start -------------------")
                for (i in 1 until 13) {
                    var angleA = horoscopeA.houses.cusps[i]
                    val angleB = horoscopeB.houses.cusps[i]
                    val absAngle = abs(angleA - angleB)
                    if (absAngle > 180) {
                        angleA += 360
                    }
                    LogUtil.d(" A : $angleA, B : $angleB")
                    var angle = (angleA + angleB) / 2.0
                    if (angle > 360) {
                        angle -= 360
                    }
                    horoscopeA.houses.cusps[i] = angle
                    LogUtil.d("cusps : $i -> $angle")
                }
                horoscopeA.houses.initSign()

                LogUtil.d("\n\nascmc start -------------------")
                for (i in 1 until horoscopeA.houses.ascmc.size) {
                    var angleA = horoscopeA.houses.ascmc[i]
                    val angleB = horoscopeB.houses.ascmc[i]
                    if (angleA < angleB) {
                        angleA += 360
                    }
                    LogUtil.d(" A : $angleA, B : $angleB")
                    var angle = (angleA + angleB) / 2.0
                    if (angle > 360) {
                        angle -= 360
                    }
                    horoscopeA.houses.ascmc[i] = angle
                    LogUtil.d("ascmc : $i -> $angle")
                }

                horoscopeA.planetList = getPlanetListSign(
                    context,
                    horoscopeA.planetList,
                    horoscopeA.houses.cusps
                )

                // 處理相位
                horoscopeA.aspectList.clear()
                val aspectList: List<AspectData> = aspects(
                    context,
                    chart,
                    horoscopeA.planetList,
                    horoscopeA.planetList
                )
                horoscopeA.aspectList.addAll(aspectList)
            } catch (e: Exception) {
                LogUtil.e("Exception in calculateComposite: ${e.message}")
                // Return the input horoscope even if calculation fails
            }

            return horoscopeA
        }

        fun julianToDate(jd: Double): String {
            val sweDate = SweDate(jd)
            return "${sweDate.year}-${sweDate.month}-${sweDate.day} ${sweDate.hour} UTC"
        }

        fun julianToUnixTimestamp(jd: Double): Long {
            return ((jd - 2440587.5) * 86400).toLong()
        }

        fun calculateVoidOfCourseMoon(context: Context, jdUt: Double): Pair<Double, Double>? {
            initData(context)
            val moonPos = DoubleArray(6)
            val planetPos = DoubleArray(6)
            val serr = StringBuffer()

            // 計算月亮位置
            swissEph.swe_calc_ut(jdUt, SweConst.SE_MOON, SweConst.SEFLG_SWIEPH, moonPos, serr)
            val moonSign = (moonPos[0] / 30).toInt() // 獲取月亮所在星座

            var lastAspectTime: Double? = null
            var nextIngressTime: Double? = null

            // 找出月亮的最後一個主要相位
            for (planet in listOf(
                SweConst.SE_SUN, SweConst.SE_MERCURY, SweConst.SE_VENUS,
                SweConst.SE_MARS, SweConst.SE_JUPITER, SweConst.SE_SATURN,
                SweConst.SE_URANUS, SweConst.SE_NEPTUNE, SweConst.SE_PLUTO
            )) {
                swissEph.swe_calc_ut(jdUt, planet, SweConst.SEFLG_SWIEPH, planetPos, serr)
                val aspectAngle = moonPos[0] - planetPos[0]
                if (isMajorAspect(aspectAngle)) {
                    lastAspectTime = jdUt  // 記錄最後一個相位時間
                }
            }

            // 計算月亮何時進入下一個星座
            var checkTime = jdUt
            while (nextIngressTime == null) {
                checkTime += 0.1  // 每 0.1 天檢查一次
                swissEph.swe_calc_ut(
                    checkTime,
                    SweConst.SE_MOON,
                    SweConst.SEFLG_SWIEPH,
                    moonPos,
                    serr
                )
                val newSign = (moonPos[0] / 30).toInt()
                if (newSign != moonSign) {
                    nextIngressTime = checkTime
                }
            }

            return lastAspectTime?.let { Pair(it, nextIngressTime) }
        }

        private fun isMajorAspect(angle: Double): Boolean {
            val majorAspects = listOf(0.0, 60.0, 90.0, 120.0, 180.0)
            return majorAspects.any { abs(angle % 360) in it - 1.0..it + 1.0 }
        }

        fun calculateVoidOfCourseMoon2(context: Context, julianDay: Double): Pair<String, Double> {
            initData(context)
            val moonPosition = DoubleArray(6)
            val otherPlanetPositions = DoubleArray(6)
            val flags = SweConst.SEFLG_SWIEPH or SweConst.SEFLG_SPEED

            // 計算月亮的位置
            swissEph.swe_calc_ut(julianDay, SweConst.SE_MOON, flags, moonPosition, null)

            // 計算其他行星的位置，並找出月亮的最後相位
            val planets = arrayOf(
                SweConst.SE_SUN, SweConst.SE_MERCURY, SweConst.SE_VENUS,
                SweConst.SE_MARS, SweConst.SE_JUPITER, SweConst.SE_SATURN,
                SweConst.SE_URANUS, SweConst.SE_NEPTUNE, SweConst.SE_PLUTO
            )
            var lastAspect = ""
            for (planet in planets) {
                swissEph.swe_calc_ut(julianDay, planet, flags, otherPlanetPositions, null)
                val aspect = calculateAspect(moonPosition[0], otherPlanetPositions[0])
                if (aspect.isNotEmpty()) {
                    lastAspect = aspect
                }
            }

            // 計算月亮進入下一個星座的時間
            val nextSignChange = calculateNextSignChange(swissEph, julianDay, moonPosition)

            LogUtil.d("lastAspect $lastAspect nextSignChange $nextSignChange")
            return Pair(lastAspect, nextSignChange)
        }

        private fun calculateAspect(moonLongitude: Double, planetLongitude: Double): String {
            val angle = abs(moonLongitude - planetLongitude)
            return when {
                angle < 8.0 -> "Conjunction"
                abs(angle - 180.0) < 8.0 -> "Opposition"
                abs(angle - 120.0) < 8.0 -> "Trine"
                abs(angle - 90.0) < 8.0 -> "Square"
                abs(angle - 60.0) < 8.0 -> "Sextile"
                else -> ""
            }
        }

        private fun calculateNextSignChange(
            swissEph: SwissEph,
            julianDay: Double,
            moonPosition: DoubleArray
        ): Double {
            val flags = SweConst.SEFLG_SWIEPH or SweConst.SEFLG_SPEED
            val moonPos = DoubleArray(6)
            var jd = julianDay

            // 獲取當前星座的起始經度
            val currentSignStart = (moonPosition[0] / 30).toInt() * 30

            // 迭代計算，直到月亮的經度超過下一個星座的起始經度
            while (true) {
                jd += 0.1 // 每次增加一小段時間，這裡以0.1天為例
                swissEph.swe_calc_ut(jd, SweConst.SE_MOON, flags, moonPos, null)

                // 檢查月亮是否進入下一個星座
                if (moonPos[0] >= currentSignStart + 30) {
                    break
                }
            }

            return jd
        }

        private fun initAxis(
            context: Context,
            planetList: MutableList<PlanetBean>,
            houses: Houses
        ) {
            if (planetList.size == 0) {
                return
            }
            val asc = planetList.find { it.enName == "Asc" }
            if (asc != null) {
                asc.longitude = houses.ascmc[SweConst.SE_ASC]
                asc.signBean = getPlanetSign(context, asc, houses.cusps)
                asc.houseData = house(houses.cusps[1], houses.cusps)
            }
            val des = planetList.find { it.enName == "Des" }
            if (des != null) {
                if (houses.ascmc[SweConst.SE_ASC] + 180 > 360) {
                    des.longitude = houses.ascmc[SweConst.SE_ASC] - 180
                } else {
                    des.longitude = houses.ascmc[SweConst.SE_ASC] + 180
                }
                des.signBean = getPlanetSign(context, des, houses.cusps)
                des.houseData = house(houses.cusps[7], houses.cusps)
            }

            val mc = planetList.find { it.enName == "MC" }
            if (mc != null) {
                mc.longitude = houses.ascmc[SweConst.SE_MC]
                mc.signBean = getPlanetSign(context, mc, houses.cusps)
                mc.houseData = house(houses.cusps[10], houses.cusps)
            }
            val ic = planetList.find { it.enName == "IC" }
            if (ic != null) {
                if (houses.ascmc[SweConst.SE_MC] + 180 > 360) {
                    ic.longitude = houses.ascmc[SweConst.SE_MC] - 180
                } else {
                    ic.longitude = houses.ascmc[SweConst.SE_MC] + 180
                }
                ic.signBean = getPlanetSign(context, ic, houses.cusps)
                ic.houseData = house(houses.cusps[4], houses.cusps)
            }
        }

        private fun getPlanetListSign(
            context: Context,
            planetList: ArrayList<PlanetBean>,
            cusps: MutableList<Double>
        ): ArrayList<PlanetBean> {
            // 計算
            LogUtil.d("===行星-星座===\n")
            for (i in 0 until planetList.size) {
                val arrayOfStrings = szZodiac(planetList[i].longitude)
                val index = arrayOfStrings[0].toInt()
                if (index < 0 || index >= signList.size) {
                    continue
                }
                val signBean = getPlanetSign(context, planetList[i], cusps)
                planetList[i].signBean = signBean
                val houseData = planetList[i].signBean.houseData
                LogUtil.d("${planetList[i].chName} ${signBean.chName} ${signBean.degree} ${houseData.index}宮 ${signBean.houseData.degree}")
            }
            return planetList
        }

        private fun getPlanetSign(
            context: Context,
            planetBean: PlanetBean,
            cusps: MutableList<Double>
        ): SignBean {
            val signBean: SignBean
            val arrayOfStrings = szZodiac(planetBean.longitude)
            val index = arrayOfStrings[0].toInt()
            if (index < 0 || index >= signList.size) {
                return SignBean()
            }
            signBean = SignBean(signList[index])
            val signDescBean = BasicDBHelper.querySignDesc(context, signBean.chName)
            if (signDescBean != null) {
                signBean.exaltation = signDescBean.exalt.toString()
                signBean.rulership = signDescBean.ruler.toString()
                signBean.detriment = signDescBean.detriment.toString()
                signBean.fall = signDescBean.fall.toString()
            }
            val signData = BasicDBHelper.querySignData(context, signBean.chName)
            if (signData != null) {
                signBean.triplicities = signData.triplicities
                signBean.quadruplicities = signData.quadruplicities
                signBean.yinYang = signData.yinYang
            }
            // 入廟 旺相 落陷 失勢
            if (signBean.exaltation.contains(planetBean.chName)) {
                signBean.dignity = context.getString(R.string.exaltation)
            }
            if (signBean.rulership.contains(planetBean.chName)) {
                signBean.dignity = context.getString(R.string.rulership)
            }
            if (signBean.detriment.contains(planetBean.chName)) {
                signBean.dignity = context.getString(R.string.detriment)
            }
            if (signBean.fall.contains(planetBean.chName)) {
                signBean.dignity = context.getString(R.string.fall)
            }
            signBean.houseData = house(planetBean.longitude, cusps)
            signBean.degree = arrayOfStrings[1] + "°"
            signBean.minute = arrayOfStrings[2]
            val houseData = signBean.houseData
            LogUtil.d("${signBean.chName} ${signBean.degree} ${houseData.index}宮 ${signBean.houseData.degree}")
            return signBean
        }

        /**
        ascmc[0] = ascendant
        ascmc[1] = mc
        ascmc[2] = armc (= sidereal time)
        ascmc[3] = vertex
        ascmc[4] = equatorial ascendant
        ascmc[5] = co-ascendant (Walter Koch)
        ascmc[6] = co-ascendant (Michael Munkasey)
        ascmc[7] = polar ascendant (Michael Munkasey)
        ascmc[8] = reserved for future use
        ascmc[9] = reserved for future use

        Parameters:
        tjd_ut - The Julian Day number in UT
        iflag - An additional flag for calculation. It must be 0 or SEFLG_SIDEREAL and / or SEFLG_RADIANS.
        geolat - The latitude on earth, for which the calculation has to be done.
        geolon - The longitude on earth, for which the calculation has to be done. Eastern longitude and northern latitude is given by positive values, western longitude and southern latitude by negative values.
        hsys - The house system as a character given as an integer.
        cusp - (double[13]) The house cusps are returned here in cusp[1...12] for the houses 1 to 12.
        ascmc - (double[10]) The special points like ascendant etc. are returned here. See the list above.

        Returns:
        SweConst.OK (==0) or SweConst.ERR (==-1),
        if calculation was not possible due to nearness to the polar circle in Koch or Placidus house system or
        when requesting Gauquelin sectors. Calculation automatically switched to Porphyry house calculation method in this case,
        so that valid houses will be returned anyway, just in a different house system than requested.
         */
        fun getHouseCusps(
            julDay: Double,
            latLng: LatLng,
            houseSystem: Char
        ): Houses {
            val cusps = DoubleArray(13)
            val ascmc = DoubleArray(10)
            val flags = 0

            /**
            (int)'A'  equal 6
            (int)'E'  equal
            (int)'B'  Alcabitius 11
            (int)'C'  Campanus 5
            (int)'G'  36 Gauquelin sectors 15
            (int)'H'  horizon / azimut
            (int)'K'  Koch 2
            (int)'M'  Morinus 12
            (int)'O'  Porphyry 3
            (int)'P'  Placidus 1
            (int)'R'  Regiomontanus 4
            (int)'T'  Polich/Page ("topocentric") 10
            (int)'U'  Krusinski-Pisa-Goelzer 13
            (int)'V'  equal Vehlow 7
            (int)'W'  equal, whole sign 8
            (int)'X'  axial rotation system/ Meridian houses 9
            (int)'Y'  APC houses

            hsys - House system character house systems are:
            A  equal
            E  equal
            B  Alcabitius
            C  Campanus
            G  36 Gauquelin sectors
            H  horizon / azimut
            K  Koch
            M  Morinus
            O  Porphyry
            P  Placidus
            R  Regiomontanus
            T  Polich/Page ("topocentric")
            U  Krusinski-Pisa-Goelzer
            V  equal Vehlow
            W  equal, whole sign
            X  axial rotation system/ Meridian houses
            Y  APC houses
             */
            val result = swissEph.swe_houses(
                julDay,
                flags,
                latLng.latitude,
                latLng.longitude,
                houseSystem.code, // 'K' 'C' 'E'...
                cusps,  /* array for 13 (or 37 for hsys G) doubles, 返回 1 至 12 宮的宮頭 cusps[1...12] */
                ascmc /* array for 10 doubles, 返回上升點等特殊點 */
            )
            LogUtil.i("swe_houses result : $result")
            val cuspsList = ArrayList<Double>()
            cusps.forEach {
                cuspsList.add(it)
            }
            LogUtil.i("cusps : $cuspsList")
            val ascmcList = ArrayList<Double>()
            ascmc.forEach {
                ascmcList.add(it)
            }
            LogUtil.i("ascmc : $ascmcList")
            return Houses(cuspsList, ascmcList)
        }

        private fun formatDateTime(calendar: Calendar): String {
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.timeZone = calendar.timeZone
            return formatter.format(calendar.time)
        }

        fun formatDateTime(time: Long): String {
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = time
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            formatter.timeZone = calendar.timeZone
            return formatter.format(calendar.time)
        }

        fun solarReturn(
            context: Context,
            name: String,
            time: Long,
            latLng: LatLng,
            progressedTime: BirthData,
            latLng2: LatLng,
            isDaylightSavingTime: Boolean
        ): Horoscope {
            // 獲取出生時太陽的黃道經度
            val birthSunLongitude =
                getPlanetAngle(context, time, latLng, SweConst.SE_SUN, isDaylightSavingTime)

            LogUtil.d("Birth Sun Longitude: $birthSunLongitude")

            // 設定搜尋的目標年份
            val currentCalendar = Calendar.getInstance()
            currentCalendar.timeInMillis = progressedTime.birthday

            // 創建出生日期的 Calendar（用於比較月日時分）
            val birthCalendar = Calendar.getInstance()
            birthCalendar.timeInMillis = time

            // 創建今年的生日日期
            val thisYearBirthday = Calendar.getInstance()
            thisYearBirthday.timeInMillis = time
            thisYearBirthday.set(Calendar.YEAR, currentCalendar.get(Calendar.YEAR))

            // 決定目標年份：若目前時間小於今年度生日，則年份減一年
            val targetYear = if (currentCalendar.timeInMillis < thisYearBirthday.timeInMillis) {
                currentCalendar.get(Calendar.YEAR) - 1
            } else {
                currentCalendar.get(Calendar.YEAR)
            }

            LogUtil.d("Current time: ${formatDateTime(currentCalendar)}")
            LogUtil.d("This year birthday: ${formatDateTime(thisYearBirthday)}")
            LogUtil.d("Target year for Solar Return: $targetYear")

            // 從目標年份的生日開始搜尋
            val searchCalendar = Calendar.getInstance()
            searchCalendar.timeInMillis = time
            searchCalendar.set(Calendar.YEAR, targetYear)


            // 創建搜尋範圍：生日前後 30 天（參考 Dart 版本）
            val searchStart = Calendar.getInstance()
            searchStart.timeInMillis = searchCalendar.timeInMillis
            searchStart.set(Calendar.HOUR, 0)
            searchStart.set(Calendar.MINUTE, 0)

            val searchEnd = Calendar.getInstance()
            searchEnd.timeInMillis = searchCalendar.timeInMillis
            searchEnd.set(Calendar.HOUR, 23)
            searchEnd.set(Calendar.MINUTE, 59)
            searchEnd.set(Calendar.SECOND, 59)

            LogUtil.d("Search range start: ${formatDateTime(searchStart)}")
            LogUtil.d("Search range end: ${formatDateTime(searchEnd)}")

            // 使用二分搜尋法找到精確的太陽返照時間
            val start = searchStart.timeInMillis
            val end = searchEnd.timeInMillis
            val mid: Long  // 初始化為目標生日


            // 智能太陽返照搜尋：根據誤差大小選擇策略
            val targetPrecision = 0.0001 // 目標精度：0.0001 度
            val binarySearchThreshold = 1.0 // 二分法閾值：1 度
            val maxIterations = 500
            var iterations = 0

            // 初始化搜尋時間
            var currentTime = (start + end) / 2

            LogUtil.d("開始智能太陽返照搜尋...")
            LogUtil.d("目標太陽經度: $birthSunLongitude°")
            LogUtil.d("目標精度: $targetPrecision°")
            LogUtil.d("二分法閾值: $binarySearchThreshold°")

            while (iterations < maxIterations) {
                iterations++

                // 計算當前時間的太陽經度
                val currentSunLongitude = getPlanetAngle(
                    context,
                    currentTime,
                    latLng2,
                    SweConst.SE_SUN,
                    isDaylightSavingTime
                )

                // 計算角度差距
                val diff = currentSunLongitude - birthSunLongitude

                val calendar = Calendar.getInstance()
                calendar.timeInMillis = currentTime
                LogUtil.d("迭代 $iterations: ${formatDateTime(calendar)}, 太陽經度=$currentSunLongitude°, 差距=$diff°")

                // 如果達到目標精度，結束搜尋
                if (diff < targetPrecision) {
                    LogUtil.d("達到目標精度，搜尋完成")
                    break
                }

                LogUtil.d("誤差 $diff° ≤ $binarySearchThreshold°，使用線性調整")
                // 根據誤差大小選擇搜尋策略
                if (diff < 0) {
                    currentTime += if (diff > -0.05) {
                        60000 * 2
                    } else if (diff > -0.1) {
                        60000 * 5
                    } else if (diff > -0.5) {
                        60000 * 50
                    } else if (diff > -1.0) {
                        60000 * 100
                    } else if (diff > -5.0) {
                        60000 * 200
                    } else if (diff > -10.0) {
                        60000 * 500
                    } else if (diff > -50.0) {
                        60000 * 1000
                    }else {
                        60000 * 2000
                    }
                } else {
                    currentTime -= if (diff < 0.05) {
                        60000 * 2
                    } else if (diff < 0.1) {
                        60000 * 5
                    } else if (diff < 0.5) {
                        60000 * 10
                    } else if (diff < 1.0) {
                        60000 * 100
                    } else if (diff < 5.0) {
                        60000 * 200
                    } else if (diff < 10.0) {
                        60000 * 500
                    } else if (diff < 50.0) {
                        60000 * 1000
                    } else {
                        60000 * 2000
                    }
                }
            }

            // 記錄最終結果
            if (iterations >= maxIterations) {
                LogUtil.d("達到最大迭代次數，使用當前最佳結果")
            }

            mid = currentTime

            val finalCalendar = Calendar.getInstance()
            finalCalendar.timeInMillis = mid

            val finalSunLongitude = getPlanetAngle(
                context,
                mid,
                latLng2,
                SweConst.SE_SUN,
                isDaylightSavingTime
            )

            val finalDiff = abs(normalizeDegree(finalSunLongitude - birthSunLongitude))

            LogUtil.d("Final Solar Return: ${formatDateTime(finalCalendar)}")
            LogUtil.d("Birth Sun: $birthSunLongitude°, Return Sun: $finalSunLongitude°, Diff: $finalDiff°")

            return calculate(
                context,
                Chart.SolarReturn,
                name,
                mid,
                latLng2
            )
        }

        /**
         * 將角度標準化到 0-360 度範圍
         */
        private fun normalizeDegree(degree: Double): Double {
            var normalized = degree % 360.0
            if (normalized < 0) {
                normalized += 360.0
            }
            return normalized
        }

        /**
         * 二分法搜尋步驟
         * 當誤差大於閾值時使用
         */
        private fun binarySearchStep(
            context: Context,
            planetId: Int,
            start: Long,
            end: Long,
            currentTime: Long,
            targetSunLongitude: Double,
            latLng: LatLng,
            isDaylightSavingTime: Boolean
        ): Long {
            val currentSunLongitude = getPlanetAngle(
                context, currentTime, latLng, planetId, isDaylightSavingTime
            )

            // 計算角度差方向（考慮最短路徑）
            var angleDiff = targetSunLongitude - currentSunLongitude
            if (angleDiff > 180) angleDiff -= 360
            if (angleDiff < -180) angleDiff += 360

            return if (angleDiff > 0) {
                // 目標角度在前方，時間需要增加
                (currentTime + end) / 2
            } else {
                // 目標角度在後方，時間需要減少
                (start + currentTime) / 2
            }
        }

        /**
         * 智能月亮返照搜尋方法
         * 參考 solarReturn 的智能搜尋算法，針對月亮運動特性優化
         */
        private fun intelligentLunarReturnSearch(
            context: Context,
            initialStart: Long,
            initialEnd: Long,
            targetMoonLongitude: Double,
            latLng: LatLng,
            isDaylightSavingTime: Boolean
        ): Long {
            // 月亮運動較快，使用更高的精度和更小的閾值
            val targetPrecision = 0.01 // 目標精度：0.0001 度
            val binarySearchThreshold = 1.0 // 二分法閾值：1 度
            val maxIterations = 50
            var iterations = 0

            // 初始化搜尋時間
            var currentTime = (initialStart + initialEnd) / 2

            LogUtil.d("開始智能月亮返照搜尋...")
            LogUtil.d("目標月亮經度: $targetMoonLongitude°")
            LogUtil.d("目標精度: $targetPrecision°")
            LogUtil.d("二分法閾值: $binarySearchThreshold°")

            while (iterations < maxIterations) {
                iterations++

                // 計算當前時間的月亮經度
                val currentMoonLongitude = getPlanetAngle(
                    context,
                    currentTime,
                    latLng,
                    SweConst.SE_MOON,
                    isDaylightSavingTime
                )

                // 計算角度差距
                // 計算角度差距
                val diff = currentMoonLongitude - targetMoonLongitude

                val calendar = Calendar.getInstance()
                calendar.timeInMillis = currentTime
                LogUtil.d("迭代 $iterations: ${formatDateTime(calendar)}, 月亮經度=$currentMoonLongitude°, 差距=$diff°")

                // 如果達到目標精度，結束搜尋
                if (diff > -targetPrecision && diff < targetPrecision) {
                    LogUtil.d("達到目標精度，搜尋完成")
                    break
                }
                LogUtil.d("誤差 $diff° ≤ $binarySearchThreshold°，使用線性調整")
                // 根據誤差大小選擇搜尋策略
                if (diff < 0) {
                    currentTime += if (diff > -0.05) {
                        60000 * 2
                    } else if (diff > -0.1) {
                        60000 * 5
                    } else if (diff > -0.5) {
                        60000 * 50
                    } else if (diff > -1.0) {
                        60000 * 100
                    } else if (diff > -5.0) {
                        60000 * 200
                    } else if (diff > -10.0) {
                        60000 * 500
                    } else if (diff > -50.0) {
                        60000 * 1000
                    }else {
                        60000 * 2000
                    }
                } else {
                    currentTime -= if (diff < 0.05) {
                        60000 * 2
                    } else if (diff < 0.1) {
                        60000 * 5
                    } else if (diff < 0.5) {
                        60000 * 10
                    } else if (diff < 1.0) {
                        60000 * 100
                    } else if (diff < 5.0) {
                        60000 * 200
                    } else if (diff < 10.0) {
                        60000 * 500
                    } else if (diff < 50.0) {
                        60000 * 1000
                    } else {
                        60000 * 2000
                    }
                }
            }

            // 記錄最終結果
            if (iterations >= maxIterations) {
                LogUtil.d("達到最大迭代次數，使用當前最佳結果")
            }

            return currentTime
        }

        /**
         * 月亮返照二分法搜尋步驟
         * 當誤差大於閾值時使用
         */
        private fun lunarBinarySearchStep(
            context: Context,
            start: Long,
            end: Long,
            currentTime: Long,
            targetMoonLongitude: Double,
            latLng: LatLng,
            isDaylightSavingTime: Boolean
        ): Long {
            val currentMoonLongitude = getPlanetAngle(
                context, currentTime, latLng, SweConst.SE_MOON, isDaylightSavingTime
            )

            // 計算角度差方向（考慮最短路徑）
            var angleDiff = targetMoonLongitude - currentMoonLongitude
            if (angleDiff > 180) angleDiff -= 360
            if (angleDiff < -180) angleDiff += 360

            return if (angleDiff > 0) {
                // 目標角度在前方，時間需要增加
                (currentTime + end) / 2
            } else {
                // 目標角度在後方，時間需要減少
                (start + currentTime) / 2
            }
        }

        /**
         * 月亮返照線性時間調整
         * 當誤差小於閾值時使用，根據月亮運動速度精確調整時間
         * 月亮運動速度約為每天 13.2 度，比太陽快約 13 倍
         */
        private fun lunarLinearTimeAdjustment(
            context: Context,
            currentTime: Long,
            targetMoonLongitude: Double,
            latLng: LatLng,
            isDaylightSavingTime: Boolean
        ): Long {
            val currentMoonLongitude = getPlanetAngle(
                context, currentTime, latLng, SweConst.SE_MOON, isDaylightSavingTime
            )

            // 計算角度差（考慮最短路徑）
            var angleDiff = targetMoonLongitude - currentMoonLongitude
            if (angleDiff > 180) angleDiff -= 360
            if (angleDiff < -180) angleDiff += 360

            // 計算月亮運動速度（度/毫秒）
            // 月亮運動較快，使用較短的時間步長
            val timeStep = 2 * 60 * 60 * 1000L // 2 小時
            val futureTime = currentTime + timeStep
            val futureMoonLongitude = getPlanetAngle(
                context, futureTime, latLng, SweConst.SE_MOON, isDaylightSavingTime
            )

            var moonSpeed = futureMoonLongitude - currentMoonLongitude
            if (moonSpeed > 180) moonSpeed -= 360
            if (moonSpeed < -180) moonSpeed += 360
            moonSpeed /= timeStep // 度/毫秒

            // 避免除以零
            if (abs(moonSpeed) < 1e-10) {
                LogUtil.d("月亮運動速度過小，使用固定調整")
                return currentTime + if (angleDiff > 0) 2 * 60 * 60 * 1000L else -2 * 60 * 60 * 1000L
            }

            // 計算需要調整的時間
            val timeAdjustment = (angleDiff / moonSpeed).toLong()

            // 限制調整幅度（月亮運動較快，最大調整 12 小時）
            val maxAdjustment = 12 * 60 * 60 * 1000L
            val clampedAdjustment = timeAdjustment.coerceIn(-maxAdjustment, maxAdjustment)

            LogUtil.d("角度差: $angleDiff°, 月亮速度: $moonSpeed°/ms, 時間調整: ${clampedAdjustment / (60 * 60 * 1000.0)} 小時")

            return currentTime + clampedAdjustment
        }

        /**
         * 判斷從 current 到 target 是否為順時針方向
         * 參考 Dart 版本的 _isClockwise 方法
         */
        private fun isClockwise(current: Double, target: Double): Boolean {
            val normalizedCurrent = normalizeDegree(current)
            val normalizedTarget = normalizeDegree(target)

            val diff = normalizedTarget - normalizedCurrent
            val normalizedDiff = normalizeDegree(diff)

            // 如果標準化後的差值小於 180 度，表示順時針方向較短
            return normalizedDiff < 180.0
        }

        fun lunarReturn(
            context: Context,
            name: String,
            time: Long,
            latLng: LatLng,
            progressedTime: BirthData,
            latLng2: LatLng,
            isDaylightSavingTime: Boolean
        ): Horoscope {
            // 獲取出生時月亮的黃道經度
            val birthMoonLongitude =
                getPlanetAngle(context, time, latLng, SweConst.SE_MOON, isDaylightSavingTime)

            LogUtil.d("Birth Moon Longitude: $birthMoonLongitude")

            // 設定搜尋的目標時間（月亮返照通常在當前時間前後 27.3 天內）
            val currentCalendar = Calendar.getInstance()
            currentCalendar.timeInMillis = progressedTime.birthday

            // 創建搜尋範圍：當前時間前後 30 天（月亮週期約 27.3 天）
            val searchStart = Calendar.getInstance()
            searchStart.timeInMillis = currentCalendar.timeInMillis
            searchStart.add(Calendar.DAY_OF_YEAR, -28)

            val searchEnd = Calendar.getInstance()
            searchEnd.timeInMillis = currentCalendar.timeInMillis
            searchEnd.add(Calendar.DAY_OF_YEAR, 28)

            LogUtil.d("Current time: ${formatDateTime(currentCalendar)}")
            LogUtil.d("Lunar Return search range start: ${formatDateTime(searchStart)}")
            LogUtil.d("Lunar Return search range end: ${formatDateTime(searchEnd)}")

            // 使用智能月亮返照搜尋算法
            val finalTime = intelligentLunarReturnSearch(
                context,
                searchStart.timeInMillis,
                searchEnd.timeInMillis,
                birthMoonLongitude,
                latLng2,
                isDaylightSavingTime
            )

            val finalCalendar = Calendar.getInstance()
            finalCalendar.timeInMillis = finalTime

            val finalMoonLongitude = getPlanetAngle(
                context,
                finalTime,
                latLng2,
                SweConst.SE_MOON,
                isDaylightSavingTime
            )

            val finalDiff = abs(normalizeDegree(finalMoonLongitude - birthMoonLongitude))

            LogUtil.d("Final Lunar Return: ${formatDateTime(finalCalendar)}")
            LogUtil.d("Birth Moon: $birthMoonLongitude°, Return Moon: $finalMoonLongitude°, Diff: $finalDiff°")

            return calculate(
                context,
                Chart.LunarReturn,
                name,
                finalTime,
                latLng2
            )
        }


        fun getPlanetAngle(
            context: Context,
            time: Long,
            latLng: LatLng,
            planetId: Int,
            isDaylightSavingTime: Boolean
        ): Double {
            val julDay = getJulDay(time, latLng)
            val xx = DoubleArray(6)

            // First check if Swiss Ephemeris is initialized
            if (!this::swissEph.isInitialized) {
                val initSuccess = initData(context)
                if (!initSuccess) {
                    LogUtil.e("Failed to initialize Swiss Ephemeris in getPlanetAngle")
                    return 0.0 // Return default value as fallback
                }
            }

            // Use MOSEPH as fallback if ephemeris files are not available
            val flags = SweConst.SEFLG_MOSEPH or SweConst.SEFLG_SPEED

            try {
                // Create a new StringBuffer with initial content to avoid null issues
                val message = StringBuffer("")
                val result = swissEph.swe_calc_ut(julDay, planetId, flags, xx, message)
                if (result < 0 || result != flags) {
                    LogUtil.e("swe_calc_ut error in getPlanetAngle: ${message.toString()}")
                    return 0.0 // Return default value on error
                }
                return xx[0]
            } catch (e: Exception) {
                LogUtil.e("Exception in getPlanetAngle: ${e.message}")
                return 0.0 // Return default value on exception
            }
        }

        private fun getPlanetListAngle(
            context: Context,
            julDay: Double,
            house: Houses,
        ): ArrayList<PlanetBean> {
            // First check if Swiss Ephemeris is initialized
            if (!this::swissEph.isInitialized) {
                val initSuccess = initData(context)
                if (!initSuccess) {
                    LogUtil.e("Failed to initialize Swiss Ephemeris")
                    // Return empty list as fallback
                    return ArrayList()
                }
            }

            // Use MOSEPH as fallback if ephemeris files are not available
            // MOSEPH is the built-in simplified ephemeris that doesn't require external files
            val flags = try {
                SweConst.SEFLG_SWIEPH or SweConst.SEFLG_SPEED
            } catch (e: Exception) {
                LogUtil.e("Error setting ephemeris flags, using MOSEPH as fallback: ${e.message}")
                SweConst.SEFLG_MOSEPH or SweConst.SEFLG_SPEED
            }

            val xx = DoubleArray(6)
            var sunAngle = 0.0
            val box = ObjectBox.get().boxFor(PlanetBean::class.java)
            val allPlanets = ArrayList(box.all)

            // 保留所有行星，但在計算時檢查isChecked屬性
            val planetArrayList = ArrayList(allPlanets)

            for (planet in planetArrayList) {
                if (!planet.isChecked) {
                    continue
                }
                /**
                 * swe_calc_ut
                The parameter xx is used as an output parameter containing the following info:
                array of 6 doubles for longitude, latitude, distance, speed in long., speed in lat., and speed in dist.
                xx[0] = 經度（Longitude）
                xx[1] = 緯度（Latitude）
                xx[2]	距離	AU	行星與地球的距離 distance
                xx[3]	經度速度	度 / 日	< 0 表逆行. speed in long
                xx[4]	緯度速度	度 / 日	南北方向的變化速度  speed in lat
                xx[5]	距離變化速度	AU / 日	每日距離變化量 speed in dist
                The speed infos will be calculated only, if the appropriate SEFLG_* switch is set.

                Parameters:
                tjd_ut - The Julian Day number in UT (Universal Time).
                ipl - The body to be calculated. See SweConst for a list of bodies
                iflag - A flag that contains detailed specification on how the body is to be computed. See SweConst for a list of valid flags (SEFLG_*).
                xx - A double[6] in which the result is returned. See swe_calc() for the description of this parameter
                serr - A StringBuffer containing a warning or error message, if something fails.
                Returns:
                iflag or SweConst.ERR (-1); iflag MAY have changed from input parameter!
                 */
                if (planet.id == Planet.FORTUNE) {
                    planet.longitude =
                        getFortune(planetArrayList, house.ascmc[SweConst.SE_ASC])
                    planet.signBean =
                        getPlanetSign(context, planet, house.cusps)
                    planet.houseData = house(planet.longitude, house.cusps)
                    continue
                }

                try {
                    // Create a new StringBuffer with initial content to avoid null issues
                    val message = StringBuffer("")
                    val result = swissEph.swe_calc_ut(julDay, planet.id, flags, xx, message)
                    if (result < 0 || result != flags) {
                        LogUtil.e("swe_calc_ut error for ${planet.chName}: ${message.toString()}")
                        continue
                    }
                } catch (e: Exception) {
                    LogUtil.e("Exception calculating position for ${planet.chName}: ${e.message}")
                    continue
                }

                val planetBean: PlanetBean? = Util.getPlanetById(planetArrayList, planet.id)
                if (planetBean != null) {
                    if (planet.id == Planet.MOON_S_NODE) {
                        continue
                    }
                    if (planet.id == Planet.SUN_MOON_MIDPOINT) {
                        continue
                    }

                    planetBean.longitude = xx[0]
                    planetBean.speed = xx[3]
                    // get the name of the planet
                    val planetName = swissEph.swe_get_planet_name(planet.id)
                    planetBean.isRetrograde = xx[3] < 0
                    LogUtil.d(planetName + "(" + planetBean.chName + ") angle = " + planetBean.longitude + ", 逆行 retrograde=" + xx[3])
                    if (planet.id == SweConst.SE_SUN) {
                        sunAngle = planetBean.longitude
                    } else {
                        val sun = planetArrayList.find { it.id == SweConst.SE_SUN }
                        if (sun != null) {
                            planetBean.solarProximity =
                                BurnChecker.getSolarProximity(planet, sun.longitude)
                        }
                    }
                    planet.planetNature = AstrologyTool.judgeBeneficMalefic(planet.id)
                    planet.dignity =
                        AstrologyTool.getPlanetStrength(planet.id, planet.signBean.chName)
//                val score = AstrologyJudgmentTool.calculatePlanetStrength(marsCondition)
//                println("Mars 力量分數：$score，判斷：${AstrologyJudgmentTool.getJudgmentLabel(score)}")
//                println("Mars 吉凶性質：${AstrologyJudgmentTool.judgeBeneficMalefic(Planet.MARS)}")
                    if (planet.id == SweConst.SE_MOON) {
                        val moonAngle = planetBean.longitude
                        val midpoint = planetArrayList.find { it.id == Planet.SUN_MOON_MIDPOINT }
                        val index = planetArrayList.indexOf(midpoint)
                        planetArrayList[index].longitude =
                            getSunMoonMidpoint(planetArrayList, sunAngle, moonAngle)
                        planetArrayList[index].signBean =
                            getPlanetSign(context, planetArrayList[index], house.cusps)
                        planetArrayList[index].houseData =
                            house(planetArrayList[index].longitude, house.cusps)
                    }
                    if (planet.id == SweConst.SE_MEAN_NODE) {
                        val sNode = planetArrayList.find { it.id == Planet.MOON_S_NODE }
                        val index = planetArrayList.indexOf(sNode)
                        if (sNode != null && sNode.isChecked) {
                            planetArrayList[index].longitude =
                                if (planetBean.longitude + 180 > 360) {
                                    planetBean.longitude - 180
                                } else {
                                    planetBean.longitude + 180
                                }
                            planetArrayList[index].signBean =
                                getPlanetSign(context, planetArrayList[index], house.cusps)
                            planetArrayList[index].houseData =
                                house(planetArrayList[index].longitude, house.cusps)
                        }
                    }
                    planet.signBean = getPlanetSign(context, planetBean, house.cusps)
                    planet.houseData = house(planetBean.longitude, house.cusps)
                } else {
                    LogUtil.e("planet = $planet")
                }
            }
            val vertex = planetArrayList.find { it.id == Planet.VERTEX }
            val index = planetArrayList.indexOf(vertex)
            if (vertex != null && vertex.isChecked) {
                planetArrayList[index].longitude = house.ascmc[SweConst.SE_VERTEX]
                planetArrayList[index].signBean =
                    getPlanetSign(context, planetArrayList[index], house.cusps)
                planetArrayList[index].houseData =
                    house(planetArrayList[index].longitude, house.cusps)
            }

            return planetArrayList
        }

        fun getSweLongitude(julianDay: Double, planetId: Int): Double {
            val position = DoubleArray(6)
            val serr = StringBuffer()

            swissEph.swe_calc(julianDay, planetId, SweConst.SEFLG_SWIEPH, position, serr)

            return position[0]  // 黃道經度（度數 0~360）
        }

        /**
         * 行星	原始格式	十進制度格式（度/日）
         * 🌙 月亮	13°10′36″	13.17667°
         * ☿ 水星	0°59′08″	0.98556°
         * ♀ 金星	0°59′08″	0.98556°
         * ☉ 太陽	0°59′08″	0.98556°
         * ♂ 火星	0°31′27″	0.52417°
         * ♃ 木星	0°04′59″	0.08306°
         * ♄ 土星	0°02′01″	0.03361°
         */
        fun classifySpeed(planet: Int, speed: Double): String {
            val avgSpeed = when (planet) {
                SweConst.SE_SUN -> 0.9856
                SweConst.SE_MOON -> 13.17667
                SweConst.SE_MERCURY -> 0.98556
                SweConst.SE_VENUS -> 0.98556
                SweConst.SE_MARS -> 0.52417
                SweConst.SE_JUPITER -> 0.083
                SweConst.SE_SATURN -> 0.03361
                SweConst.SE_URANUS -> 0.0119
                SweConst.SE_NEPTUNE -> 0.006
                SweConst.SE_PLUTO -> 0.004
                else -> 0.0
            }

            LogUtil.d("planet : $planet speed : $speed avgSpeed : $avgSpeed")

            // 🌙 月亮特殊處理
            if (planet == SweConst.SE_MOON) {
                return when {
                    speed >= 14.5 -> "極快"
                    speed >= 13.5 -> "快"
                    speed >= 12.5 -> "平均"
                    speed >= 11.5 -> "慢"
                    else -> "極慢"
                }
            }

            // 其他行星通用邏輯
            return when {
                speed < 0 -> "逆行"
                abs(speed - avgSpeed) < 0.05 -> "平均"
                speed > avgSpeed -> "快"
                else -> "慢"
            }
        }


        private fun getSunMoonMidpoint(
            planetArrayList: ArrayList<PlanetBean>,
            sunAngle: Double,
            moonAngle: Double
        ): Double {
            var angle = 0.0
            val midpoint = planetArrayList.find { it.id == Planet.SUN_MOON_MIDPOINT }
            if (midpoint != null && midpoint.isChecked) {
                var m = sunAngle - moonAngle
                var right = 360 - sunAngle
                var left = moonAngle
                if (sunAngle < moonAngle) {
                    m = moonAngle - sunAngle
                    right = 360 - moonAngle
                    left = sunAngle
                }
                angle = if (m < (right + left)) {
                    m / 2 + left
                } else {
                    val sub = (right + left) / 2
                    if (left - sub >= 0) {
                        left - sub
                    } else {
                        left + m + sub
                    }
                }
            }
            return angle
        }

        private fun getFortune(planetArrayList: ArrayList<PlanetBean>, asc: Double): Double {
            // 福點計算公式是：
            // 白天出生：福點=上升點+月亮-太陽 （日生盤的福點計算公式也是傳統上一直使用的公式）
            // 夜晚出生：福點=上升點+太陽-月亮
            val sun = planetArrayList.find { it.id == SweConst.SE_SUN }!!.longitude
            val moon = planetArrayList.find { it.id == SweConst.SE_MOON }!!.longitude

            val des = (asc + 180) % 360

            val day = (asc + moon - sun + 360) % 360
            val night = (asc + sun - moon + 360) % 360

            val isDay = isDay(asc, des, sun)

            return if (isDay) day else night
        }


        fun isDay(asc: Double, des: Double, sun: Double): Boolean {
            return if (des > asc) {
                !(sun >= asc && sun < des)
            } else {
                !(sun + 360 >= asc && sun + 360 <= des + 360)
            }
        }

        // 角度取得第幾個星座
        @JvmStatic
        fun szZodiac(angle: Double): ArrayList<String> {
            // 取得度數部分
            val degrees = angle.toInt()

            // 計算分鐘部分
            val minutes = ((angle - degrees) * 60).toInt()

            val arrayList = ArrayList<String>()
            var i = angle.toInt() / 30
            var j = angle.toInt() - i * 30
            val num = (60.0 * (angle - floor(angle)))
            if (num.isNaN()) {
                return arrayList
            }
            var k = num.roundToInt()
            if (k == 60) {
                j++
                k = 0
            }
            if (i >= 12) {
                i -= 12
            }
            arrayList.add(i.toString())
            arrayList.add(j.toString())
            arrayList.add(minutes.toString())
            return arrayList
        }

        fun Double.rangeUntil(to: Double): ClosedFloatingPointRange<Double> =
            this..to - 0.000001 // 避免包含 `to`

        fun house(planetAngle: Double, cusps: MutableList<Double>): HouseData {
            val houseData = HouseData()

            for (i in 1 until cusps.size) {
                val startAngle = cusps[i]
                val endAngle = cusps.getOrElse(i + 1) { cusps[1] } // 避免越界，最後一宮連回第一宮

                val inHouse = when {
                    endAngle > startAngle -> planetAngle in startAngle.rangeUntil(endAngle)
                    else -> planetAngle in (startAngle - 360).rangeUntil(endAngle) ||
                            planetAngle in startAngle.rangeUntil(endAngle + 360)
                }

                if (inHouse) {
                    val angleDiff = (planetAngle - startAngle).let { if (it < 0) it + 360 else it }
                    val strHouse = szZodiac(angleDiff)
                    val angleInteger = angleDiff.toInt()
                    houseData.index = i
                    houseData.degree = "$angleInteger°${strHouse[2]}"
                    break
                }
            }

            return houseData
        }

        fun aspects(
            context: Context,
            chart: Chart,
            planetListA: List<PlanetBean>,
            planetListB: List<PlanetBean>,
            isMatch: Boolean = false,
            isGetScore: Boolean = false
        ): ArrayList<AspectData> {
//            LogUtil.d("\n===行星-相位===\n")
            val planetAspectList: ArrayList<AspectData> = ArrayList()

            planetListA.forEach {
                it.aspects.clear()
            }
            planetListB.forEach {
                it.aspects.clear()
            }

            for (i in planetListA.indices) {
                if (!planetListA[i].isChecked) {
                    continue
                }
                for (j in planetListB.indices) {
                    if (i == j && !isMatch) {
                        continue
                    }
                    if (!planetListB[j].isChecked) {
                        continue
                    }
                    var angle = planetListA[i].longitude - planetListB[j].longitude
                    if (angle >= 180) {
                        angle = 360 - angle
                    } else if (angle <= -180) {
                        angle += 360.0
                    } else {
                        angle = abs(angle)
                    }
                    var angle2 = (planetListA[i].longitude + 1) - planetListB[j].longitude
                    if (planetListA[i].speed < planetListB[j].speed) {
                        angle2 = planetListA[i].longitude - (planetListB[j].longitude + 1)
                    }
                    if (angle2 >= 180) {
                        angle2 = 360 - angle2
                    } else if (angle2 <= -180) {
                        angle2 += 360.0
                    } else {
                        angle2 = abs(angle2)
                    }
                    val aspectType = AspectType.getType(chart, angle)
                    if (aspectType != null) {
                        if (!aspectType.isOpen) {
                            continue
                        }
                        if (planetListA[i].id == -1 && planetListB[j].id == -1) {
                            continue
                        }
                        val planetAspect = AspectData()
                        val orb = angle - aspectType.value
                        val orb2 = angle2 - aspectType.value
                        if (abs(orb) < abs(orb2)) {
                            planetAspect.isApplying = false
                            planetAspect.direction = "出相"
                        } else {
                            planetAspect.isApplying = true
                            planetAspect.direction = "入相"
                        }
//                        LogUtil.d(planetListA[i].chName + aspectType.nameCh + planetListB[j].chName + " = " + planetAspect.score + " " + planetAspect.direction)
                        val x = calcOrb(orb)
                        var aspect =
                            Aspect(aspectType, planetListB[j], true, planetAspect.direction)
                        aspect.deltaDegree = x[0]
                        aspect.deltaMinute = x[1]
                        aspect.orb = abs(orb)

                        val aspectTemp =
                            planetListA[i].aspects.find { it.planet.chName == aspect.planet.chName }
                        if (aspectTemp == null) {
                            planetListA[i].aspects.add(aspect)
                        }

                        aspect = Aspect(aspectType, planetListA[i], true, planetAspect.direction)
                        aspect.deltaDegree = x[0]
                        aspect.deltaMinute = x[1]
                        aspect.orb = abs(orb)

                        val aspectTempB =
                            planetListB[j].aspects.find { it.planet.chName == aspect.planet.chName }
                        if (aspectTempB == null) {
                            planetListB[j].aspects.add(aspect)
                        }
                        planetAspect.aspectType = aspectType
                        planetAspect.type = aspectType.nameCh
                        planetAspect.degree = aspectType.value
                        planetAspect.planetA = planetListA[i].chName
                        planetAspect.planetB = planetListB[j].chName
                        planetAspect.orb = abs(orb)
                        planetAspect.score = 0
                        if (isGetScore) {
                            planetAspect.score = getScore(context, planetAspect)
                        }
                        planetAspectList.add(planetAspect)
                    }
                }
            }
            return planetAspectList
        }


        private fun getScore(context: Context, synastryAspect: AspectData): Int {
            val synastryAspectData = query(context, synastryAspect) ?: return 0
            LogUtil.d(synastryAspectData.planetA + synastryAspectData.type + synastryAspectData.planetB + " = " + synastryAspectData.score)
            return synastryAspectData.score
        }

        private fun query(context: Context, aspect: AspectData): AspectData? {
            val synastryAspectData = BasicDBHelper.querySynastryAspect(
                context,
                aspect.planetA,
                aspect.degree.toString(),
                aspect.planetB,
            )
            if (synastryAspectData != null) {
                return synastryAspectData
            }
            return null
        }

        private fun calcOrb(d: Double): IntArray {
            val i = d.toInt() / 30
            var j = d.toInt() - i * 30
            var k = (60.0 * (d - floor(d))).roundToInt().toFloat().roundToInt()
            if (k == 60) {
                j++
                k = 0
            }
            return intArrayOf(j, k)
        }


        /**
         * 互容：兩顆行星互相落在對方所掌管的五種本質強旺位置（本垣，耀升，三分主星，界，十度區間），一般僅重視廟旺。
         * 例如太陽在巨蟹，月亮在獅子；月亮在金牛，金星在巨蟹。
         * 相當於合相
         *
         * 接納：當行星與其星座的五種主星形成主要相位時，稱為該五種主星接納該行星。凶相位的接納凶中帶吉。
         *
         * E (本垣+界) 與 T (本垣+曜升) 互容
         * E 被 Q 接納 (曜升+三分)
         * R 被 Q 接納 (曜升+三分+十度)
         *
         * 接納 Reception
         * 互容 Mutual reception
         */
        fun getReception(
            context: Context,
            planetList: ArrayList<PlanetBean>,
            aspectList: List<AspectData>
        ): ArrayList<ReceptionData> {
            val receptionDataList = ArrayList<ReceptionData>()

            // 使用 filter 而不是 for 循環來提高效能
            planetList.filter { it.isClassic }.forEach { planetBeanA ->
                val receptionData = ReceptionData()
                val reception =
                    findReception2(context, receptionData, planetList, planetBeanA, aspectList)

                if (reception != null) {
                    // 檢查是否已經存在互容關係
                    val receptionA = receptionDataList.find {
                        it.planetNameA == reception.planetNameB && it.planetNameB == reception.planetNameA
                    }

                    if (receptionA != null) {
                        receptionA.isMutual = true
                        receptionA.typeB = reception.typeA
                    } else {
                        receptionDataList.add(reception)
                    }
                }
            }

            return receptionDataList
        }

        private fun findReception2(
            context: Context,
            receptionData: ReceptionData,
            planetList: ArrayList<PlanetBean>,
            planetBeanA: PlanetBean,
            aspectList: List<AspectData>
        ): ReceptionData? {
            val signA = planetBeanA.signBean.chName
            val signDescA = BasicDBHelper.querySignDesc(context, signA)

            // 先檢查 Ruler 接納
            findReception3(
                receptionData,
                signDescA,
                planetList,
                planetBeanA,
                Dignities.Ruler,
                aspectList
            )?.let { return it }

            // 再檢查 Exalt 接納
            findReception3(
                receptionData,
                signDescA,
                planetList,
                planetBeanA,
                Dignities.Exalt,
                aspectList
            )?.let { return it }

            // 如果都沒有找到，返回 null
            return null
        }

        private fun findReception3(
            receptionData: ReceptionData,
            signDescBean: SignDescBean?,
            planetList: ArrayList<PlanetBean>,
            planetBeanA: PlanetBean,
            typeA: Dignities,
            aspectList: List<AspectData>
        ): ReceptionData? {
            if (signDescBean == null) return null

            receptionData.typeA = typeA
            val name = when (typeA) {
                Dignities.Ruler -> signDescBean.ruler
                Dignities.Exalt -> signDescBean.exalt
                Dignities.Triplicity -> signDescBean.triplicityDay
                Dignities.Term -> signDescBean.terms1
                Dignities.Face -> signDescBean.face1
                Dignities.Detriment -> signDescBean.detriment
                Dignities.Fall -> signDescBean.fall
                Dignities.NONE -> ""
            }

            // 如果名稱為空，直接返回 null
            if (name != null) {
                if (name.isEmpty()) return null
            }

            // 使用 find 查找對應的行星
            val planet = planetList.find { it.chName == name } ?: return null

            receptionData.planetNameA = planetBeanA.chName
            receptionData.planetNameB = planet.chName
            receptionData.isReception = true
            receptionData.hasAspect = findAspect(planetBeanA, planet, aspectList)

            return if (receptionData.hasAspect) receptionData else null
        }

        private fun findAspect(
            planetBeanA: PlanetBean,
            planetBeanB: PlanetBean,
            aspectList: List<AspectData>
        ): Boolean {
            // 使用 any 函數來簡化代碼
            return aspectList.any {
                (it.planetA == planetBeanA.chName && it.planetB == planetBeanB.chName) ||
                        (it.planetB == planetBeanA.chName && it.planetA == planetBeanB.chName)
            }
        }

        /**
         * 判斷行星是否逆行
         * @param context Context
         * @param time 時間戳
         * @param planetId 行星ID
         * @return 是否逆行
         */
        fun isPlanetRetrograde(context: Context, time: Long, planetId: Int): Boolean {
            val julDay = getJulDay(time, LatLng(0.0, 0.0))
            val xx = DoubleArray(6)

            // First check if Swiss Ephemeris is initialized
            if (!this::swissEph.isInitialized) {
                val initSuccess = initData(context)
                if (!initSuccess) {
                    LogUtil.e("Failed to initialize Swiss Ephemeris in isPlanetRetrograde")
                    return false // Return default value as fallback
                }
            }

            // Use MOSEPH as fallback if ephemeris files are not available
            val flags = try {
                SweConst.SEFLG_SWIEPH or SweConst.SEFLG_SPEED
            } catch (e: Exception) {
                LogUtil.e("Error setting ephemeris flags in isPlanetRetrograde, using MOSEPH as fallback: ${e.message}")
                SweConst.SEFLG_MOSEPH or SweConst.SEFLG_SPEED
            }

            try {
                // Create a new StringBuffer with initial content to avoid null issues
                val message = StringBuffer("")
                val result = swissEph.swe_calc_ut(julDay, planetId, flags, xx, message)
                if (result < 0 || result != flags) {
                    LogUtil.e("swe_calc_ut error in isPlanetRetrograde: ${message.toString()}")
                    return false // Return default value on error
                }

                // xx[3]是行星的黃經速度，負值表示逆行
                return xx[3] < 0
            } catch (e: Exception) {
                LogUtil.e("Exception in isPlanetRetrograde: ${e.message}")
                return false // Return default value on exception
            }
        }

    }
}
