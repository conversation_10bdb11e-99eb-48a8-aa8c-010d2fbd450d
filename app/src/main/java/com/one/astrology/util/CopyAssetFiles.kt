package com.one.astrology.util

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream

class CopyAssetFiles(private var pattern: String, private var ct: Context) {
    fun copy() {
        val assetManager = ct.assets
        var files: Array<String>? = null
        try {
            files = assetManager.list("se")
        } catch (e: IOException) {
            Log.e("tag", "Failed to get asset file list.", e)
        }
        var outdir = ct.filesDir.toString() + File.separator + "ephe"
        File(outdir).mkdirs()
        outdir += File.separator
        for (filename in files!!) {
            if (File(outdir + filename).exists() || !filename.matches(pattern.toRegex())) {
                continue
            }
            var `in`: InputStream?
            var out: OutputStream?
            try {
                `in` = assetManager.open("se/$filename")
                val outFile = File(outdir, filename)
                out = FileOutputStream(outFile)
                copyFile(`in`, out)
                `in`.close()
                `in` = null
                out.flush()
                out.close()
                out = null
            } catch (e: IOException) {
                Log.e("tag", "Failed to copy asset file: $filename", e)
            }
        }
    }

    @Throws(IOException::class)
    private fun copyFile(`in`: InputStream, out: OutputStream) {
        val buffer = ByteArray(1024)
        var read: Int
        while (`in`.read(buffer).also { read = it } != -1) {
            out.write(buffer, 0, read)
        }
    }
}