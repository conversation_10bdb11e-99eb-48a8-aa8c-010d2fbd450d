package com.one.astrology.util


import com.one.astrology.constant.Planet
import com.one.astrology.data.Horoscope
import com.one.astrology.data.bean.Aspect
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.TimeEventBean
import java.util.Calendar
import kotlin.math.abs

/**
 * 時段分析工具類
 * 用於實現年表和時段分析功能
 */
object TimeAnalysisUtil {

    /**
     * 生成指定時間範圍內的年表
     * @param horoscope 基本星盤數據
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @param includeTransits 是否包含行運
     * @param includeProgressions 是否包含推運
     * @param includeSolarReturn 是否包含太陽返照
     * @return 時間事件列表
     */
    fun generateTimeline(
        horoscope: Horoscope,
        startDate: Long,
        endDate: Long,
        includeTransits: Boolean = true,
        includeProgressions: Boolean = true,
        includeSolarReturn: Boolean = true
    ): List<TimeEventBean> {
        val events = mutableListOf<TimeEventBean>()
        
        // 計算行運事件
        if (includeTransits) {
            events.addAll(calculateTransitEvents(horoscope, startDate, endDate))
        }
        
        // 計算推運事件
        if (includeProgressions) {
            events.addAll(calculateProgressionEvents(horoscope, startDate, endDate))
        }
        
        // 計算太陽返照事件
        if (includeSolarReturn) {
            events.addAll(calculateSolarReturnEvents(horoscope, startDate, endDate))
        }
        
        // 按日期排序
        events.sortBy { it.date }
        
        return events
    }

    /**
     * 計算行運事件
     * @param horoscope 基本星盤數據
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @return 行運事件列表
     */
    private fun calculateTransitEvents(
        horoscope: Horoscope,
        startDate: Long,
        endDate: Long
    ): List<TimeEventBean> {
        val events = mutableListOf<TimeEventBean>()
        
        // 獲取本命行星
        val natalPlanets = horoscope.planetList
        
        // 計算時間間隔（以天為單位）
        val days = (endDate - startDate) / (24 * 60 * 60 * 1000)
        
        // 對於每一天
        for (day in 0..days) {
            val currentDate = startDate + day * 24 * 60 * 60 * 1000
            
            // 計算當天的行運行星位置
            val transitPlanets = calculatePlanetPositions(currentDate)
            
            // 檢查行運行星與本命行星的相位
            for (transitPlanet in transitPlanets) {
                // 跳過快速行星（如果時間範圍較長）
                if (days > 365 && isQuickPlanet(transitPlanet.id)) {
                    continue
                }
                
                for (natalPlanet in natalPlanets) {
                    // 檢查是否形成重要相位
                    val aspectType = getAspectType(transitPlanet.longitude, natalPlanet.longitude)
                    if (aspectType != Aspect.NONE && isSignificantAspect(aspectType)) {
                        // 創建行運事件
                        val event = TimeEventBean(
                            date = currentDate,
                            type = TimeEventBean.TYPE_TRANSIT,
                            firstPlanetId = transitPlanet.id,
                            secondPlanetId = natalPlanet.id,
                            aspectType = aspectType,
                            description = getTransitDescription(transitPlanet.id, natalPlanet.id, aspectType)
                        )
                        events.add(event)
                    }
                }
                
                // 檢查行運行星與本命宮頭的相位
                for (houseId in 1..12) {
                    val houseAngle = getHouseAngle(horoscope, houseId)
                    val aspectType = getAspectType(transitPlanet.longitude, houseAngle)
                    if (aspectType != Aspect.NONE && isSignificantAspect(aspectType)) {
                        // 創建行運事件
                        val event = TimeEventBean(
                            date = currentDate,
                            type = TimeEventBean.TYPE_TRANSIT_TO_HOUSE,
                            firstPlanetId = transitPlanet.id,
                            houseId = houseId,
                            aspectType = aspectType,
                            description = getTransitToHouseDescription(transitPlanet.id, houseId, aspectType)
                        )
                        events.add(event)
                    }
                }
            }
        }
        
        return events
    }

    /**
     * 計算推運事件
     * @param horoscope 基本星盤數據
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @return 推運事件列表
     */
    private fun calculateProgressionEvents(
        horoscope: Horoscope,
        startDate: Long,
        endDate: Long
    ): List<TimeEventBean> {
        val events = mutableListOf<TimeEventBean>()
        
        // 獲取本命行星
        val natalPlanets = horoscope.planetList
        
        // 獲取出生日期
        val birthDate = horoscope.birthdayTime
        
        // 計算時間間隔（以天為單位）
        val days = (endDate - startDate) / (24 * 60 * 60 * 1000)
        
        // 對於每一天
        for (day in 0..days) {
            val currentDate = startDate + day * 24 * 60 * 60 * 1000
            
            // 計算從出生到當前日期的年數
            val years = (currentDate - birthDate) / (365.25 * 24 * 60 * 60 * 1000)
            
            // 計算推運日期（出生後每天代表一年的推運）
            val progressionDate = birthDate + (years * 24 * 60 * 60 * 1000).toLong()
            
            // 計算推運行星位置
            val progressedPlanets = calculatePlanetPositions(progressionDate)
            
            // 檢查推運行星與本命行星的相位
            for (progressedPlanet in progressedPlanets) {
                for (natalPlanet in natalPlanets) {
                    // 跳過同一行星的推運與本命的相位
                    if (progressedPlanet.id == natalPlanet.id) {
                        continue
                    }
                    
                    // 檢查是否形成重要相位
                    val aspectType = getAspectType(progressedPlanet.longitude, natalPlanet.longitude)
                    if (aspectType != Aspect.NONE && isSignificantAspect(aspectType)) {
                        // 創建推運事件
                        val event = TimeEventBean(
                            date = currentDate,
                            type = TimeEventBean.TYPE_PROGRESSION,
                            firstPlanetId = progressedPlanet.id,
                            secondPlanetId = natalPlanet.id,
                            aspectType = aspectType,
                            description = getProgressionDescription(progressedPlanet.id, natalPlanet.id, aspectType)
                        )
                        events.add(event)
                    }
                }
                
                // 檢查推運行星與本命宮頭的相位
                for (houseId in 1..12) {
                    val houseAngle = getHouseAngle(horoscope, houseId)
                    val aspectType = getAspectType(progressedPlanet.longitude, houseAngle)
                    if (aspectType != Aspect.NONE && isSignificantAspect(aspectType)) {
                        // 創建推運事件
                        val event = TimeEventBean(
                            date = currentDate,
                            type = TimeEventBean.TYPE_PROGRESSION_TO_HOUSE,
                            firstPlanetId = progressedPlanet.id,
                            houseId = houseId,
                            aspectType = aspectType,
                            description = getProgressionToHouseDescription(progressedPlanet.id, houseId, aspectType)
                        )
                        events.add(event)
                    }
                }
            }
        }
        
        return events
    }

    /**
     * 計算太陽返照事件
     * @param horoscope 基本星盤數據
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @return 太陽返照事件列表
     */
    private fun calculateSolarReturnEvents(
        horoscope: Horoscope,
        startDate: Long,
        endDate: Long
    ): List<TimeEventBean> {
        val events = mutableListOf<TimeEventBean>()
        
        // 獲取本命太陽角度
        val natalSunAngle = getNatalPlanetAngle(horoscope, Planet.SUN)
        
        // 獲取出生日期
        val birthDate = horoscope.birthdayTime
        val birthCalendar = Calendar.getInstance().apply { timeInMillis = birthDate }
        val birthMonth = birthCalendar.get(Calendar.MONTH)
        val birthDay = birthCalendar.get(Calendar.DAY_OF_MONTH)
        
        // 計算時間範圍內的年份
        val startCalendar = Calendar.getInstance().apply { timeInMillis = startDate }
        val endCalendar = Calendar.getInstance().apply { timeInMillis = endDate }
        val startYear = startCalendar.get(Calendar.YEAR)
        val endYear = endCalendar.get(Calendar.YEAR)
        
        // 對於範圍內的每一年
        for (year in startYear..endYear) {
            // 設置當年生日的大致日期
            val birthdayCalendar = Calendar.getInstance()
            birthdayCalendar.set(year, birthMonth, birthDay, 12, 0, 0)
            birthdayCalendar.set(Calendar.MILLISECOND, 0)
            
            // 在生日前後幾天內尋找太陽返照的確切時間
            for (dayOffset in -5..5) {
                val checkDate = birthdayCalendar.timeInMillis + dayOffset * 24 * 60 * 60 * 1000
                
                // 如果日期在指定範圍內
                if (checkDate in startDate..endDate) {
                    // 計算當天太陽位置
                    val sunPosition = calculateSunPosition(checkDate)
                    
                    // 檢查是否接近本命太陽位置
                    if (abs(sunPosition - natalSunAngle) < 1.0) {
                        // 創建太陽返照事件
                        val event = TimeEventBean(
                            date = checkDate,
                            type = TimeEventBean.TYPE_SOLAR_RETURN,
                            description = getSolarReturnDescription(year)
                        )
                        events.add(event)
                        
                        // 找到該年的太陽返照後跳出內層循環
                        break
                    }
                }
            }
        }
        
        return events
    }

    /**
     * 計算指定日期的行星位置
     * @param date 日期
     * @return 行星列表
     */
    private fun calculatePlanetPositions(date: Long): List<PlanetBean> {
        // 這裡應該調用星曆計算庫來獲取準確的行星位置
        // 簡化示例，返回模擬數據
        val planets = mutableListOf<PlanetBean>()
        
        // 模擬太陽位置（大約每天移動1度）
        val baseDate = Calendar.getInstance().apply {
            set(2000, 0, 1, 12, 0, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        val daysSince2000 = (date - baseDate) / (24 * 60 * 60 * 1000)
        val sunAngle = (daysSince2000 % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.SUN; longitude = sunAngle })
        
        // 模擬月亮位置（大約每天移動13度）
        val moonAngle = ((daysSince2000 * 13) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.MOON; longitude = moonAngle })
        
        // 模擬水星位置
        val mercuryAngle = ((sunAngle + 20 + (daysSince2000 % 30) * 2) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.MERCURY; longitude = mercuryAngle })
        
        // 模擬金星位置
        val venusAngle = ((sunAngle + 40 + (daysSince2000 % 40)) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.VENUS; longitude = venusAngle })
        
        // 模擬火星位置
        val marsAngle = ((sunAngle + 60 + (daysSince2000 % 687) / 2) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.MARS; longitude = marsAngle })
        
        // 模擬木星位置
        val jupiterAngle = ((sunAngle + 90 + (daysSince2000 % 4380) / 12) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.JUPITER; longitude = jupiterAngle })
        
        // 模擬土星位置
        val saturnAngle = ((sunAngle + 120 + (daysSince2000 % 10767) / 30) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.SATURN; longitude = saturnAngle })
        
        // 模擬天王星位置
        val uranusAngle = ((sunAngle + 150 + (daysSince2000 % 30660) / 84) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.URANUS; longitude = uranusAngle })
        
        // 模擬海王星位置
        val neptuneAngle = ((sunAngle + 180 + (daysSince2000 % 60225) / 165) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.NEPTUNE; longitude = neptuneAngle })
        
        // 模擬冥王星位置
        val plutoAngle = ((sunAngle + 210 + (daysSince2000 % 90520) / 248) % 360).toDouble()
        planets.add(PlanetBean().apply { id = Planet.PLUTO; longitude = plutoAngle })
        
        return planets
    }

    /**
     * 計算指定日期的太陽位置
     * @param date 日期
     * @return 太陽角度
     */
    private fun calculateSunPosition(date: Long): Double {
        // 簡化計算，與calculatePlanetPositions中的太陽計算保持一致
        val baseDate = Calendar.getInstance().apply {
            set(2000, 0, 1, 12, 0, 0)
            set(Calendar.MILLISECOND, 0)
        }.timeInMillis
        val daysSince2000 = (date - baseDate) / (24 * 60 * 60 * 1000)
        return (daysSince2000 % 360).toDouble()
    }

    /**
     * 獲取本命行星角度
     * @param horoscope 星盤數據
     * @param planetId 行星ID
     * @return 行星角度
     */
    private fun getNatalPlanetAngle(horoscope: Horoscope, planetId: Int): Double {
        for (planet in horoscope.planetList) {
            if (planet.id == planetId) {
                return planet.longitude
            }
        }
        return 0.0
    }

    /**
     * 獲取宮頭角度
     * @param horoscope 星盤數據
     * @param houseId 宮位ID (1-12)
     * @return 宮頭角度
     */
    private fun getHouseAngle(horoscope: Horoscope, houseId: Int): Double {
        // 宮位索引從0開始，宮位ID從1開始
        return horoscope.houses.cusps[houseId - 1]
    }

    /**
     * 判斷是否為快速行星（太陽、月亮、水星、金星、火星）
     * @param planetId 行星ID
     * @return 是否為快速行星
     */
    private fun isQuickPlanet(planetId: Int): Boolean {
        return planetId in listOf(Planet.SUN, Planet.MOON, Planet.MERCURY, Planet.VENUS, Planet.MARS)
    }

    /**
     * 獲取兩個角度之間的相位類型
     * @param angle1 角度1
     * @param angle2 角度2
     * @return 相位類型
     */
    private fun getAspectType(angle1: Double, angle2: Double): Int {
        val distance = calculateAngleDistance(angle1, angle2)
        
        // 檢查各種相位（使用適當的容許度）
        if (isWithinOrb(distance, 0.0, 8.0)) {
            return Aspect.CONJUNCTION // 合相
        }
        if (isWithinOrb(distance, 60.0, 4.0)) {
            return Aspect.SEXTILE // 六分相
        }
        if (isWithinOrb(distance, 90.0, 6.0)) {
            return Aspect.SQUARE // 刑相
        }
        if (isWithinOrb(distance, 120.0, 6.0)) {
            return Aspect.TRINE // 拱相
        }
        if (isWithinOrb(distance, 180.0, 8.0)) {
            return Aspect.OPPOSITION // 沖相
        }
        
        return Aspect.NONE // 無相位
    }

    /**
     * 計算兩個角度之間的最小距離
     * @param angle1 角度1
     * @param angle2 角度2
     * @return 最小距離（0-180度）
     */
    private fun calculateAngleDistance(angle1: Double, angle2: Double): Double {
        val diff = abs(angle1 - angle2) % 360.0
        return if (diff > 180.0) 360.0 - diff else diff
    }

    /**
     * 判斷角度是否在容許度範圍內
     * @param distance 角度距離
     * @param aspectAngle 相位角度
     * @param orb 容許度
     * @return 是否在容許度範圍內
     */
    private fun isWithinOrb(distance: Double, aspectAngle: Double, orb: Double): Boolean {
        return abs(distance - aspectAngle) <= orb
    }

    /**
     * 判斷是否為重要相位（合相、刑相、拱相、沖相）
     * @param aspectType 相位類型
     * @return 是否為重要相位
     */
    private fun isSignificantAspect(aspectType: Int): Boolean {
        return aspectType in listOf(
            Aspect.CONJUNCTION,
            Aspect.SQUARE,
            Aspect.TRINE,
            Aspect.OPPOSITION
        )
    }

    /**
     * 獲取行運描述
     * @param transitPlanetId 行運行星ID
     * @param natalPlanetId 本命行星ID
     * @param aspectType 相位類型
     * @return 描述文本
     */
    private fun getTransitDescription(transitPlanetId: Int, natalPlanetId: Int, aspectType: Int): String {
        val transitPlanetName = getPlanetName(transitPlanetId)
        val natalPlanetName = getPlanetName(natalPlanetId)
        val aspectName = getAspectName(aspectType)
        
        return "行運$transitPlanetName 與本命$natalPlanetName 形成$aspectName"
    }

    /**
     * 獲取行運對宮位的描述
     * @param transitPlanetId 行運行星ID
     * @param houseId 宮位ID
     * @param aspectType 相位類型
     * @return 描述文本
     */
    private fun getTransitToHouseDescription(transitPlanetId: Int, houseId: Int, aspectType: Int): String {
        val transitPlanetName = getPlanetName(transitPlanetId)
        val aspectName = getAspectName(aspectType)
        
        return "行運$transitPlanetName 與本命第$houseId 宮形成$aspectName"
    }

    /**
     * 獲取推運描述
     * @param progressedPlanetId 推運行星ID
     * @param natalPlanetId 本命行星ID
     * @param aspectType 相位類型
     * @return 描述文本
     */
    private fun getProgressionDescription(progressedPlanetId: Int, natalPlanetId: Int, aspectType: Int): String {
        val progressedPlanetName = getPlanetName(progressedPlanetId)
        val natalPlanetName = getPlanetName(natalPlanetId)
        val aspectName = getAspectName(aspectType)
        
        return "推運$progressedPlanetName 與本命$natalPlanetName 形成$aspectName"
    }

    /**
     * 獲取推運對宮位的描述
     * @param progressedPlanetId 推運行星ID
     * @param houseId 宮位ID
     * @param aspectType 相位類型
     * @return 描述文本
     */
    private fun getProgressionToHouseDescription(progressedPlanetId: Int, houseId: Int, aspectType: Int): String {
        val progressedPlanetName = getPlanetName(progressedPlanetId)
        val aspectName = getAspectName(aspectType)
        
        return "推運$progressedPlanetName 與本命第$houseId 宮形成$aspectName"
    }

    /**
     * 獲取太陽返照描述
     * @param year 年份
     * @return 描述文本
     */
    private fun getSolarReturnDescription(year: Int): String {
        return "$year 年太陽返照"
    }

    /**
     * 獲取行星名稱
     * @param planetId 行星ID
     * @return 行星名稱
     */
    private fun getPlanetName(planetId: Int): String {
        return when (planetId) {
            Planet.SUN -> "太陽"
            Planet.MOON -> "月亮"
            Planet.MERCURY -> "水星"
            Planet.VENUS -> "金星"
            Planet.MARS -> "火星"
            Planet.JUPITER -> "木星"
            Planet.SATURN -> "土星"
            Planet.URANUS -> "天王星"
            Planet.NEPTUNE -> "海王星"
            Planet.PLUTO -> "冥王星"
            else -> "未知行星"
        }
    }

    /**
     * 獲取相位名稱
     * @param aspectType 相位類型
     * @return 相位名稱
     */
    private fun getAspectName(aspectType: Int): String {
        return when (aspectType) {
            Aspect.CONJUNCTION -> "合相"
            Aspect.SEXTILE -> "六分相"
            Aspect.SQUARE -> "刑相"
            Aspect.TRINE -> "拱相"
            Aspect.OPPOSITION -> "沖相"
//            Aspect.SEMI_SEXTILE -> "半六分相"
//            Aspect.SEMI_SQUARE -> "半刑相"
//            Aspect.SESQUI_SQUARE -> "倍半刑相"
//            Aspect.QUINCUNX -> "偏相"
            else -> "未知相位"
        }
    }
} 