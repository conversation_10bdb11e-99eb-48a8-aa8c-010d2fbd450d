package com.one.astrology.util

import android.content.Context
import com.one.core.util.LogUtil.d
import com.one.core.util.AssetsUtil.loadJSONFromAsset
import com.one.astrology.ObjectBox
import com.one.core.util.AssetsUtil
import com.google.gson.JsonArray
import com.google.gson.JsonElement
import com.google.gson.Gson
import com.google.gson.JsonParser
import io.objectbox.Box
import java.lang.Exception
import java.util.ArrayList

object JsonUtil {
    @JvmStatic
    fun <T> initData(context: Context?, fileName: String, entityClass: Class<T>?): Box<T> {
        d("start : $fileName")
        val boxFor = ObjectBox.get().boxFor(entityClass)
        boxFor.removeAll()
        if (boxFor.all.size == 0) {
            val list = fromJsonList(loadJSONFromAsset(context, fileName), entityClass)
            d("size : " + list.size)
            boxFor.put(list)
        }
        d("end : $fileName")
        return boxFor
    }

    fun <T> fromJsonList(json: String?, clazz: Class<T>?): List<T> {
        val lst: MutableList<T> = ArrayList()
        try {
            val array = JsonParser.parseString(json).asJsonArray
            for (elem in array) {
                lst.add(Gson().fromJson(elem, clazz))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return lst
    }

    fun toJson(`object`: Any?): String {
        val gson = Gson()
        return gson.toJson(`object`)
    }
}