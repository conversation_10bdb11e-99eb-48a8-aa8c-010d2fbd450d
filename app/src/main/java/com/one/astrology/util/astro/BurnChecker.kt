package com.one.astrology.util.astro

import com.one.astrology.constant.Planet
import com.one.astrology.data.bean.PlanetBean
import kotlin.math.abs

/**
 * 核心內	Cazimi	0.000° ～ 0.2667°
 * 焦傷	Combustion	0.2667° ～ 7.5°
 * 光束下	Under the Beams	7.5° ～ 15°
 */
enum class SolarProximity {
    CAZIMI, COMBUSTION, UNDER_THE_BEAMS, NONE
}

object BurnChecker {

    // 計算星體與太陽的相對距離，考慮 360 度循環
    private fun angularDistance(long1: Double, long2: Double): Double {
        val diff = abs(long1 - long2)
        return if (diff > 180) 360 - diff else diff
    }

    fun getSolarProximity(planetPos: PlanetBean, angleDistance: Double): SolarProximity {
        if (planetPos.id == Planet.SUN) return SolarProximity.NONE // 太陽不會灼傷自己
        val distance = angularDistance(planetPos.longitude, angleDistance)
        return when {
            distance <= 0.2667 -> SolarProximity.CAZIMI // 16 分以內
            distance <= 7.5 -> SolarProximity.COMBUSTION // 16 分～7度30分
            distance <= 15.0 -> SolarProximity.UNDER_THE_BEAMS // 7度30分～15度
            else -> SolarProximity.NONE // 超過15度
        }
    }

//    fun checkCombustStatus(planetPos: PlanetBean, sunPos: PlanetBean): BurnStatus {
//        if (planetPos.id == Planet.SUN) return BurnStatus.NONE // 太陽不會灼傷自己
//        val distance = angularDistance(planetPos.longitude, sunPos.longitude)
//
//        return when {
//            distance < 0.1667 -> BurnStatus.Cazimi
//            distance < 8.5 -> BurnStatus.COMBUST
//            distance < 15 -> BurnStatus.UnderRays
//            else -> BurnStatus.NONE
//        }
//    }
}
