package com.one.astrology.util

import android.util.Base64
import java.security.InvalidParameterException
import java.security.spec.AlgorithmParameterSpec
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * AES encryption and decryption
 * 1. key's length >= 16
 * 2. iv's length > 16
 * 3. "transformation": AES/CBC/PKCS5Padding
 * 4. iv=12(bytes) length=128(bits)
 * 5. iv=24(bytes) length=192(bits)
 * 6. iv=32(bytes) length=256(bits)
 */
object AESUtils {
    // 密碼
    private const val key = "6E327234753778214125442A472D4B61" // JNITool.getInstance().getAK();//

    // iv 偏移量
    private const val iv = "48404D635166546A" //JNITool.getInstance().getAI();//
    fun encrypt(originalString: String?): String {
        var encryptedString = ""
        try {
            val encryptedData = encryptAndBase64Encode(
                originalString?.toByteArray(),
                key.toByteArray(),
                iv.toByteArray(),
                "AES/CBC/PKCS5Padding"
            )
            encryptedString = String(encryptedData)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return encryptedString
    }

    fun decrypt(originalString: String): String {
        var decryptedString = ""
        try {
            val decryptedData = decryptBase64EncodeData(
                originalString.toByteArray(),
                key.toByteArray(),
                iv.toByteArray(),
                "AES/CBC/PKCS5Padding"
            )
            decryptedString = String(decryptedData)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return decryptedString
    }

    /**
     * Base64 decode then AES decrypt
     *
     * @param data           Data to decrypt
     * @param key            Decrypt key
     * @param iv             Decrypt key
     * @param transformation AES/CBC/PKCS5Padding
     * @return Decrypted bytes
     * @throws Exception Decrypt exception
     */
    @Throws(Exception::class)
    private fun decryptBase64EncodeData(
        data: ByteArray?,
        key: ByteArray?,
        iv: ByteArray?,
        transformation: String?
    ): ByteArray {
        if (data == null || data.size == 0 || key == null || key.size < 16 || iv == null || iv.size < 16 || transformation == null || transformation.length == 0) {
            throw InvalidParameterException()
        }
        val textBytes = Base64.decode(data, Base64.DEFAULT)
        val ivSpec: AlgorithmParameterSpec = IvParameterSpec(iv)
        val newKey = SecretKeySpec(key, "AES")
        val cipher = Cipher.getInstance(transformation)
        cipher.init(Cipher.DECRYPT_MODE, newKey, ivSpec)
        return cipher.doFinal(textBytes)
    }

    /**
     * AES encrypt then base64 decode
     *
     * @param data           Data to encrypt
     * @param key            Encrypt key
     * @param iv             Encrypt key
     * @param transformation AES/CBC/PKCS5Padding
     * @return Encrypted bytes
     * @throws Exception Encrypt exception
     */
    @Throws(Exception::class)
    private fun encryptAndBase64Encode(
        data: ByteArray?,
        key: ByteArray?,
        iv: ByteArray?,
        transformation: String?
    ): ByteArray {
        if (data == null || data.size == 0 || key == null || key.size == 0 || iv == null || iv.size == 0 || transformation == null || transformation.length == 0) {
            throw InvalidParameterException()
        }
        val ivSpec: AlgorithmParameterSpec = IvParameterSpec(iv)
        val newKey = SecretKeySpec(key, "AES")
        val cipher = Cipher.getInstance(transformation)
        cipher.init(Cipher.ENCRYPT_MODE, newKey, ivSpec)
        return Base64.encode(cipher.doFinal(data), Base64.DEFAULT)
    }
}