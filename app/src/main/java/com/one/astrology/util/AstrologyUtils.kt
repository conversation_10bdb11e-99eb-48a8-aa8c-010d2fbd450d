package com.one.astrology.util

import android.content.Context


// 星座的尊貴狀態：主宰（廟）與旺（Exaltation）
data class PlanetaryDignities(
    val domicile: String,
    val exaltation: String? = null
)

object AstrologyUtils {

    fun getHouseSystemName(context: Context): String {
        val code = EncryptedSPUtil.getHouseSystem(context)
        return when (code.uppercaseChar()) {
            'P' -> "普拉西德制 (Placidus)"
            'K' -> "柯赫制 (Koch)"
            'O' -> "波菲利制 (Porphyry)"
            'C' -> "坎帕納斯制 (Campanus)"
            'R' -> "雷吉歐蒙塔努斯制 (Regiomontanus)"
            'M' -> "莫里努斯制 (Morinus)"
            'T' -> "地心視角制 (Topocentric)"
            'B' -> "阿卡比提斯制 (Alcabitius)"
            'A' -> "均分宮制 (Equal)"
            'W' -> "整宮制 (Whole Sign)"
            'V' -> "費羅制 (<PERSON><PERSON><PERSON>)"
            else -> "未知宮位系統"
        }
    }

    val rulers = mapOf(
        "牡羊座" to "火星",
        "金牛座" to "金星",
        "雙子座" to "水星",
        "巨蟹座" to "月亮",
        "獅子座" to "太陽",
        "處女座" to "水星",
        "天秤座" to "金星",
        "天蠍座" to "火星",
        "射手座" to "木星",
        "摩羯座" to "土星",
        "水瓶座" to "天王星",
        "雙魚座" to "海王星"
    )

    val dignities = mapOf(
        "火星" to listOf("牡羊座", "天蠍座"),
        "金星" to listOf("金牛座", "天秤座"),
        "水星" to listOf("雙子座", "處女座"),
        "月亮" to listOf("巨蟹座"),
        "太陽" to listOf("獅子座"),
        "木星" to listOf("射手座", "雙魚座"),
        "土星" to listOf("摩羯座", "水瓶座"),
        "天王星" to listOf("水瓶座"),
        "海王星" to listOf("雙魚座")
    )

    fun isPlanetInDignity(planet: String, sign: String): Boolean {
        return dignities[planet]?.contains(sign) == true
    }

    fun calculateReception(planetSign: String, planetRulerSign: String): Boolean {
        return planetSign == planetRulerSign
    }

    fun calculateMutualReception(
        planet1Sign: String,
        planet1RulerSign: String,
        planet2Sign: String,
        planet2RulerSign: String
    ): Boolean {
        return planet1Sign == planet2RulerSign && planet2Sign == planet1RulerSign
    }

//    fun isMutualReception(planet1: PlanetBean, planet2: PlanetBean): Boolean {
//        // 判斷互榮接納的條件
//        return planet1.signBean.ruler == planet2.signBean.ruler && planet2.sign == planet1.ruler
//    }

    val zodiacDignities = mapOf(
        "牡羊座" to PlanetaryDignities("火星", "太陽"),
        "金牛座" to PlanetaryDignities("金星", "月亮"),
        "雙子座" to PlanetaryDignities("水星", null),
        "巨蟹座" to PlanetaryDignities("月亮", "木星"),
        "獅子座" to PlanetaryDignities("太陽", null),
        "處女座" to PlanetaryDignities("水星", "水星"),
        "天秤座" to PlanetaryDignities("金星", "土星"),
        "天蠍座" to PlanetaryDignities("火星", null), // 可改冥王星
        "射手座" to PlanetaryDignities("木星", null),
        "摩羯座" to PlanetaryDignities("土星", "火星"),
        "水瓶座" to PlanetaryDignities("土星", null), // 可改天王星
        "雙魚座" to PlanetaryDignities("木星", "金星") // 可改海王星
    )

    /**
     * 取得某星座的主宰星與旺星（回傳 PlanetaryDignities）
     */
    fun getDignities(sign: String): PlanetaryDignities? {
        return zodiacDignities[sign]
    }

    /**
     * 判斷某行星是否在廟（主宰）
     */
    fun isInDomicile(planet: String, sign: String): Boolean {
        return zodiacDignities[sign]?.domicile == planet
    }

    /**
     * 判斷某行星是否在旺（廟旺）
     */
    fun isExalted(planet: String, sign: String): Boolean {
        return zodiacDignities[sign]?.exaltation == planet
    }

    // 主宰星對照表（可擴充成 exaltation/fall 等）
    private val domicileRulers = mapOf(
        "Aries" to "Mars",
        "Taurus" to "Venus",
        "Gemini" to "Mercury",
        "Cancer" to "Moon",
        "Leo" to "Sun",
        "Virgo" to "Mercury",
        "Libra" to "Venus",
        "Scorpio" to "Mars", // 可替換成 "Pluto" 根據流派
        "Sagittarius" to "Jupiter",
        "Capricorn" to "Saturn",
        "Aquarius" to "Saturn", // 或 "Uranus"
        "Pisces" to "Jupiter"  // 或 "Neptune"
    )

    /**
     * 判斷某行星是否被接納（即落在其他行星主宰的星座中）
     */
    fun isReception(planet: String, sign: String): Boolean {
        val ruler = domicileRulers[sign]
        return ruler != null && ruler != planet
    }

    /**
     * 判斷兩顆行星是否互容（即彼此落在對方主宰的星座）
     */
    fun isMutualReception(
        planetA: String, signA: String,
        planetB: String, signB: String
    ): Boolean {
        val rulerOfSignA = domicileRulers[signA]
        val rulerOfSignB = domicileRulers[signB]
        return rulerOfSignA == planetB && rulerOfSignB == planetA
    }

    /**
     * 取得某星座的主宰星
     */
    fun getRuler(sign: String): String? {
        return domicileRulers[sign]
    }
}
