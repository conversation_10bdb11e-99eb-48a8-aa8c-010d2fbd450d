package com.one.astrology.util

import com.one.astrology.data.Horoscope
import com.one.astrology.data.entity.PlanetYear
import com.one.astrology.data.entity.dayArray
import com.one.astrology.data.entity.nightArray
import com.one.core.util.LogUtil
import com.one.core.util.formatDate
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Calendar

/**
 * 法達盤計算工具類
 */
object FirdariaCalculator {
    // 法達行星週期表（預設為日間盤）
    private val dayChartPeriods = listOf(
        "Sun" to 10, "Venus" to 8, "Mercury" to 13, "Moon" to 9,
        "Saturn" to 11, "Jupiter" to 12, "Mars" to 7, "North Node" to 3, "South Node" to 3
    )

    private val nightChartPeriods = listOf(
        "Moon" to 9, "Saturn" to 11, "Jupiter" to 12, "Mars" to 7,
        "Sun" to 10, "Venus" to 8, "Mercury" to 13, "North Node" to 3, "South Node" to 3
    )

    /**
     * 計算法達盤主要時期
     * @param birthDate 出生日期 (格式: yyyy-MM-dd)
     * @param isDayChart 是否為日間盤 (true = 日間盤, false = 夜間盤)
     * @return 主要時期清單
     */
    fun calculateMajorPeriods(
        birthDate: String,
        isDayChart: Boolean
    ): List<Pair<String, Pair<LocalDate, LocalDate>>> {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        var startDate = LocalDate.parse(birthDate, formatter)
        val periods = if (isDayChart) dayChartPeriods else nightChartPeriods
        val result = mutableListOf<Pair<String, Pair<LocalDate, LocalDate>>>()

        for ((planet, years) in periods) {
            val endDate = startDate.plusYears(years.toLong())
            result.add(planet to (startDate to endDate))
            startDate = endDate
        }
        return result
    }

    /**
     * 計算法達盤次要時期
     * @param startDate 主要時期開始日期
     * @param mainPeriodYears 主要時期持續年數
     * @return 次要時期清單
     */
    fun calculateSubPeriods(
        startDate: LocalDate,
        mainPeriodYears: Int
    ): List<Pair<String, Pair<LocalDate, LocalDate>>> {
        val subPeriods = mutableListOf<Pair<String, Pair<LocalDate, LocalDate>>>()
        val subPeriodDays = (mainPeriodYears * 365.25 / 9).toLong()
        var subStart = startDate

        for ((planet, _) in dayChartPeriods) { // 次要時期順序與日間盤一致
            val subEnd = subStart.plusDays(subPeriodDays)
            subPeriods.add(planet to (subStart to subEnd))
            subStart = subEnd
        }
        return subPeriods
    }


    private fun getTimeStep(startTime: Long, year: Int): Long {
        val cal: Calendar = Calendar.getInstance()
        cal.timeInMillis = startTime
//        val year = yearPlanet[indexDeputy].year
        cal.add(Calendar.YEAR, year)
        LogUtil.d(
            "startTime ${startTime.formatDate("yyyy/MM/dd")}  timeInMillis ${
                cal.timeInMillis.formatDate(
                    "yyyy/MM/dd"
                )
            }"
        )
        val subTime = cal.timeInMillis - startTime
        val timeStep = (subTime / 7)
        LogUtil.d("subTime $subTime timeStep $timeStep")
        return timeStep
    }

    fun initData(horoscope: Horoscope): ArrayList<PlanetYear> {
        val sun = horoscope.getPlanetBeanList().find { it.chName == "太陽" }
            ?: return ArrayList()

        var yearPlanet = ArrayList<PlanetYear>(nightArray)
        if (sun.signBean.houseData.index > 6) {
            yearPlanet = ArrayList(dayArray)
        }
        val allYearPlanet = arrayListOf<PlanetYear>()
        var count = 0

        val primaryPlanetList = getPrimaryPlanetList(horoscope)

        for (i in 0 until primaryPlanetList.size) {
            val item = primaryPlanetList[i]
            val deputy = yearPlanet.find { it.primary == item.primary }
            var indexDeputy = yearPlanet.indexOf(deputy)

            if (item.primary != "北交點" && item.primary != "南交點") {
                var timeStep = 0L
                for (j in 0 until 7) {
                    val planet =
                        PlanetYear(yearPlanet[indexDeputy].primary, yearPlanet[indexDeputy].year)
                    if (j == 0) {
                        planet.second = item.primary
                        timeStep = getTimeStep(item.startTime!!, yearPlanet[indexDeputy].year!!)
                    } else {
                        planet.primary = item.primary
                        planet.second = yearPlanet[indexDeputy].primary
                    }
                    if (count == 0) {
                        planet.second = item.primary
                        planet.startTime = item.startTime
                    } else {
                        if (j == 0) {
                            LogUtil.d()
                            planet.startTime = item.startTime
                        } else {
                            val timeInMillis = allYearPlanet[count - 1].startTime!!
                            planet.startTime = timeInMillis + timeStep
                            LogUtil.d(
                                "timeInMillis ${timeInMillis.formatDate("yyyy/MM/dd")} startTime ${
                                    planet.startTime!!.formatDate(
                                        "yyyy/MM/dd"
                                    )
                                }"
                            )
                        }
                    }
                    indexDeputy++
                    if (indexDeputy > 6) {
                        indexDeputy = 0
                    }
                    allYearPlanet.add(planet)
                    count++
                }
            } else {
                val planet = PlanetYear(item.primary, item.year)
                planet.second = item.primary
                planet.startTime = item.startTime
                allYearPlanet.add(planet)
                count++
            }
        }

        return allYearPlanet
    }

    private fun getPrimaryPlanetList(horoscope: Horoscope): ArrayList<PlanetYear> {
        val sun = horoscope.getPlanetBeanList().find { it.chName == "太陽" }
            ?: return ArrayList()
        var yearPlanet = ArrayList<PlanetYear>(nightArray)
        if (sun.signBean.houseData.index > 6) {
            yearPlanet = ArrayList(dayArray)
        }
        val primaryPlanetList = arrayListOf<PlanetYear>()
        for (i in 0 until yearPlanet.size) {
            val item = yearPlanet[i]
            val primaryPlanet = PlanetYear(item.primary, item.year)
            if (i == 0) {
                primaryPlanet.startTime = horoscope.birthdayTime
            } else {
                val cal: Calendar = Calendar.getInstance()
                cal.timeInMillis = primaryPlanetList[i - 1].startTime!!
                LogUtil.d("timeInMillis ${cal.timeInMillis.formatDate("yyyy/MM/dd")} add ${yearPlanet[i - 1].year}")
                cal.add(Calendar.YEAR, yearPlanet[i - 1].year!!)
                primaryPlanet.startTime = cal.timeInMillis
            }
            LogUtil.d("timeInMillis ${primaryPlanet.startTime!!.formatDate("yyyy/MM/dd")}")
            primaryPlanetList.add(primaryPlanet)
        }
        return primaryPlanetList
    }
}
