package com.one.astrology.util

import com.one.core.util.LogUtil

/**
 * CSV 工具類
 * 處理 CSV 格式的匯出和匯入，正確處理包含逗號、雙引號等特殊字符的資料
 */
object CsvUtil {
    
    /**
     * 將字串轉換為 CSV 格式的欄位
     * 如果字串包含逗號、雙引號或換行符，會用雙引號包圍並進行轉義
     * 
     * @param value 要轉換的字串值
     * @return CSV 格式的欄位字串
     */
    fun escapeField(value: String?): String {
        if (value == null) return ""
        
        // 如果包含逗號、雙引號、換行符或回車符，需要用雙引號包圍
        val needsQuoting = value.contains(",") || 
                          value.contains("\"") || 
                          value.contains("\n") || 
                          value.contains("\r")
        
        return if (needsQuoting) {
            // 將雙引號轉義為兩個雙引號，然後用雙引號包圍整個字串
            "\"${value.replace("\"", "\"\"")}\""
        } else {
            value
        }
    }
    
    /**
     * 將多個欄位組合成一行 CSV 資料
     * 
     * @param fields 欄位陣列
     * @return CSV 格式的一行資料
     */
    fun createCsvRow(vararg fields: String?): String {
        return fields.joinToString(",") { escapeField(it) }
    }
    
    /**
     * 解析 CSV 行資料
     * 正確處理被雙引號包圍的欄位
     * 
     * @param line CSV 行資料
     * @return 解析後的欄位列表
     */
    fun parseCsvLine(line: String): List<String> {
        val fields = mutableListOf<String>()
        var currentField = StringBuilder()
        var inQuotes = false
        var i = 0
        
        while (i < line.length) {
            val char = line[i]
            
            when {
                // 處理雙引號
                char == '"' -> {
                    if (inQuotes) {
                        // 檢查是否為轉義的雙引號
                        if (i + 1 < line.length && line[i + 1] == '"') {
                            currentField.append('"')
                            i++ // 跳過下一個雙引號
                        } else {
                            // 結束引號
                            inQuotes = false
                        }
                    } else {
                        // 開始引號
                        inQuotes = true
                    }
                }
                
                // 處理逗號
                char == ',' -> {
                    if (inQuotes) {
                        currentField.append(char)
                    } else {
                        // 欄位結束
                        fields.add(currentField.toString())
                        currentField.clear()
                    }
                }
                
                // 其他字符
                else -> {
                    currentField.append(char)
                }
            }
            
            i++
        }
        
        // 添加最後一個欄位
        fields.add(currentField.toString())
        
        return fields
    }
    
    /**
     * 建立 CSV 標題行
     * 
     * @param headers 標題陣列
     * @return CSV 格式的標題行
     */
    fun createCsvHeader(vararg headers: String): String {
        return createCsvRow(*headers) + "\n"
    }
    
    /**
     * 驗證 CSV 行是否有足夠的欄位
     * 
     * @param fields 解析後的欄位列表
     * @param expectedCount 期望的欄位數量
     * @return 是否有足夠的欄位
     */
    fun validateFieldCount(fields: List<String>, expectedCount: Int): Boolean {
        return fields.size >= expectedCount
    }
    
    /**
     * 安全地獲取欄位值
     * 
     * @param fields 欄位列表
     * @param index 欄位索引
     * @param defaultValue 預設值
     * @return 欄位值或預設值
     */
    fun getFieldSafely(fields: List<String>, index: Int, defaultValue: String = ""): String {
        return if (index < fields.size) {
            fields[index].trim()
        } else {
            LogUtil.e("CSV 欄位索引 $index 超出範圍，使用預設值: $defaultValue")
            defaultValue
        }
    }
    
    /**
     * 安全地將字串轉換為 Double
     * 
     * @param value 字串值
     * @param defaultValue 預設值
     * @return Double 值或預設值
     */
    fun parseDoubleSafely(value: String, defaultValue: Double = -1.0): Double {
        return try {
            value.toDouble()
        } catch (e: NumberFormatException) {
            LogUtil.e("無法解析 Double 值: $value，使用預設值: $defaultValue")
            defaultValue
        }
    }
    
    /**
     * 安全地將字串轉換為 Boolean
     * 
     * @param value 字串值
     * @param defaultValue 預設值
     * @return Boolean 值或預設值
     */
    fun parseBooleanSafely(value: String, defaultValue: Boolean = false): Boolean {
        return try {
            value.toBooleanStrictOrNull() ?: defaultValue
        } catch (e: Exception) {
            LogUtil.e("無法解析 Boolean 值: $value，使用預設值: $defaultValue")
            defaultValue
        }
    }
}
