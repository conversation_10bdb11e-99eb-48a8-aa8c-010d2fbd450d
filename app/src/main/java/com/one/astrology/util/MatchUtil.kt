package com.one.astrology.util

class MatchUtil { //    public static String match(UserBirthData signRecordA, UserBirthData signRecordB) {
    //        String desc = "";
    //        SignTable sunTableA = SignTableUtil.getSignTable(signRecordA.getSunSign());
    //        SignTable sunTableB = SignTableUtil.getSignTable(signRecordB.getSunSign());
    //        if (sunTableA.getTriplicities() == sunTableB.getTriplicities()) {
    //            desc += "同星象星座，相處融洽。\n";
    //        }
    //        if (sunTableA.getNum() - sunTableB.getNum() == 6 || sunTableB.getNum() - sunTableA.getNum() == 6) {
    //            desc += "個性不同，可互補。\n";
    //        }
    //
    //        SignTable moonTableA = SignTableUtil.getSignTable(signRecordA.getMoonSign());
    //        SignTable moonTableB = SignTableUtil.getSignTable(signRecordB.getMoonSign());
    //        if (sunTableA.getNum() == moonTableB.getNum() || sunTableB.getNum() == moonTableA.getNum()) {
    //            desc += "太陽月亮同星座，天作之合相位，內心共感和諧。\n";
    //        }
    //        if (moonTableA.getNum() == moonTableB.getNum()) {
    //            desc += "月亮同星座，有共鳴，有種只有你懂我的感受，能理解對方淺意識的想法。\n";
    //        }
    //
    //        SignTable ascendantTableA = SignTableUtil.getSignTable(signRecordA.getAscendantSign());
    //        SignTable ascendantTableB = SignTableUtil.getSignTable(signRecordB.getAscendantSign());
    //        if (sunTableA.getNum() == ascendantTableB.getNum()) {
    //            desc += "太陽上升同星座，" + signRecordA.getName() + "會被" + signRecordB.getName() + "吸引。\n";
    //        }
    //        if (sunTableB.getNum() == ascendantTableA.getNum()) {
    //            desc += "太陽上升同星座，" + signRecordB.getName() + "會被" + signRecordA.getName() + "吸引。\n";
    //        }
    //        if (sunTableA.getNum() - ascendantTableB.getNum() == 6 || ascendantTableB.getNum() - sunTableA.getNum() == 6 ||
    //                sunTableB.getNum() - ascendantTableA.getNum() == 6 || ascendantTableA.getNum() - sunTableB.getNum() == 6) {
    //            desc += "太陽上升互補，有強烈吸引力。\n";
    //        }
    //
    //        SignTable venusTableA = SignTableUtil.getSignTable(signRecordA.getVenusSign());
    //        SignTable venusTableB = SignTableUtil.getSignTable(signRecordB.getVenusSign());
    //
    //        SignTable marsTableA = SignTableUtil.getSignTable(signRecordA.getMarsSign());
    //        SignTable marsTableB = SignTableUtil.getSignTable(signRecordB.getMarsSign());
    //
    //        if (venusTableA.getNum() == venusTableB.getNum() || marsTableA.getNum() == marsTableB.getNum() ||
    //                venusTableA.getNum() == marsTableB.getNum() || marsTableA.getNum() == venusTableB.getNum() ||
    //                venusTableA.getTriplicities() == venusTableB.getTriplicities() || marsTableA.getTriplicities() == marsTableB.getTriplicities() ||
    //                venusTableA.getTriplicities() == marsTableB.getTriplicities() || marsTableA.getTriplicities() == venusTableB.getTriplicities()) {
    //            desc += "金星火星落在相同星座或同性質較能愉快相處。\n";
    //        }
    //        return desc;
    //    }
}