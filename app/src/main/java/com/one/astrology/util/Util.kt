package com.one.astrology.util

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.knuddels.jtokkit.Encodings
import com.knuddels.jtokkit.api.Encoding
import com.knuddels.jtokkit.api.ModelType
import com.one.astrology.data.bean.PlanetBean
import kotlinx.coroutines.CoroutineScope
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.nio.charset.StandardCharsets


inline fun AppCompatActivity.launchWhenStarted(crossinline block: suspend CoroutineScope.() -> Unit) {
    lifecycleScope.launchWhenStarted {
        block()
    }
}

inline fun Fragment.launchWhenStarted(crossinline block: suspend CoroutineScope.() -> Unit) {
    lifecycleScope.launchWhenStarted {
        block()
    }
}

inline fun Fragment.launchWhenResumed(crossinline block: suspend CoroutineScope.() -> Unit) {
    lifecycleScope.launchWhenResumed {
        block()
    }
}

//inline fun Fragment.launch(crossinline block: suspend CoroutineScope.() -> Unit) {
//    CoroutineScope.launch {
//        block()
//    }
//}

object Util {
    @JvmStatic
    fun loadStringFromAssets(context: Context, filename: String?): String? {
        var io: InputStream? = null
        try {
            io = context.assets.open(filename!!)
            val bytes = IOUtil.getBytes(io)
            return convertToString(bytes)
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            if (io != null) {
                try {
                    io.close()
                } catch (e1: IOException) {
                    e1.printStackTrace()
                }
            }
        }
        return ""
    }

    fun getFileFromAssets(context: Context, fileName: String): File =
        File(context.cacheDir, fileName)
            .also {
                if (!it.exists()) {
                    it.outputStream().use { cache ->
                        context.assets.open(fileName).use { inputStream ->
                            inputStream.copyTo(cache)
                        }
                    }
                }
            }

    private fun convertToString(bytes: ByteArray?): String? {
        return if (bytes == null) null else String(bytes, StandardCharsets.UTF_8)
    }

    @JvmStatic
    fun getPlanetById(list: List<PlanetBean>, planet: Int): PlanetBean? {
        for (bean in list) {
            if (bean.id == planet) return bean
        }
        return null
    }

    private fun getPlanetByName(list: List<PlanetBean>, name: String): PlanetBean? {
        for (bean in list) {
            if (name.equals(bean.enName, ignoreCase = true)) return bean
        }
        return null
    }

    @JvmStatic
    fun getAscPlanet(list: List<PlanetBean>): PlanetBean {
        return getPlanetByName(list, "Asc")!!
    }

    @JvmStatic
    fun getDesPlanet(list: List<PlanetBean>): PlanetBean {
        return getPlanetByName(list, "Des")!!
    }

    @JvmStatic
    fun getMcPlanet(list: List<PlanetBean>): PlanetBean {
        return getPlanetByName(list, "MC")!!
    }

    @JvmStatic
    fun getIcPlanet(list: List<PlanetBean>): PlanetBean {
        return getPlanetByName(list, "IC")!!
    }

    fun countTokens(text: String): Int {
        val encoding: Encoding = Encodings.newDefaultEncodingRegistry().getEncodingForModel(ModelType.GPT_4)
        return encoding.countTokens(text)
    }

}