package com.one.astrology.util

import com.google.gson.Gson
import java.lang.reflect.Type

object JsonTool {
    fun <T> parseJson(info: String?, clazz: Class<T>?): T? {
        val gson = Gson()
        try {
            return gson.fromJson(info, clazz)
        } catch (e: Exception) {
            // LogUtil.w("json parse Class error：" + clazz);
            e.printStackTrace()
        }
        return null
    }

    fun <T> parseJson(info: String?, type: Type?): T? {
        val gson = Gson()
        try {
            return gson.fromJson(info, type)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }
}