package com.one.astrology.util.astro


import android.content.Context
import com.one.astrology.data.type.Chart


object ChartUtils {

    /** Chart ➜ nameEng（e.g. "Natal"） */
    fun Chart.toStorageValue(): String = this.nameEng

    /** nameEng ➜ Chart（找不到就回傳 fallback） */
    fun fromStorageValue(value: String?, fallback: Chart = Chart.Natal): Chart {
        return Chart.values().firstOrNull { it.nameEng == value } ?: fallback
    }

    /** Chart ➜ 中文名稱（用在 UI 顯示） */
    fun Chart.getDisplayName(context: Context): String {
        return context.getString(this.type)
    }

    /** Chart ➜ Emoji + 中文（可選） */
    fun Chart.getFancyLabel(context: Context): String {
        return when (this) {
            Chart.Natal -> "🧬 ${getDisplayName(context)}"
            Chart.Transit -> "🚀 ${getDisplayName(context)}"
            Chart.SolarReturn -> "☀️ ${getDisplayName(context)}"
            Chart.LunarReturn -> "🌙 ${getDisplayName(context)}"
            Chart.Synastry -> "💞 ${getDisplayName(context)}"
            Chart.Composite -> "🔗 ${getDisplayName(context)}"
            Chart.Davison -> "🕰️ ${getDisplayName(context)}"
            else -> "🪐 ${getDisplayName(context)}"
        }
    }
}
