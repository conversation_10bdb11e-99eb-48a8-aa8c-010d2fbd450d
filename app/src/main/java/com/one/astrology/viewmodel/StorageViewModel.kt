package com.one.astrology.viewmodel

import android.net.Uri
import com.google.firebase.ktx.Firebase
import com.google.firebase.storage.ktx.storage
import com.one.core.util.LogUtil

class StorageViewModel {
    fun upload(fileUri: Uri) {

        val path = "" //destPath + newFileName
        val photoRef = Firebase.storage.reference.child(path)

        val uploadTask = photoRef.putFile(fileUri)

        // Register observers to listen for when the download is done or if it fails
        uploadTask.addOnFailureListener {
            // Handle unsuccessful uploads
            LogUtil.e("unsuccessful upload : " + it.message)
        }.addOnSuccessListener { taskSnapshot ->
            // taskSnapshot.metadata contains file metadata such as size, content-type, etc.
            if (taskSnapshot.metadata != null) {
                LogUtil.d("successful upload : " + taskSnapshot.metadata!!.path + taskSnapshot.metadata!!.name)
            }
        }
    }
}