package com.one.astrology.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.one.astrology.data.model.Announcement
import com.one.astrology.firebase.RemoteConfigManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AnnouncementViewModel @Inject constructor(
    private val remoteConfigManager: RemoteConfigManager
) : ViewModel() {

    private val _isRead = MutableStateFlow(false)
    val isRead: StateFlow<Boolean> = _isRead.asStateFlow()

    private val _announcements = MutableStateFlow<List<Announcement>>(emptyList())
    val announcements: StateFlow<List<Announcement>> = _announcements.asStateFlow()

    init {
        viewModelScope.launch {
            remoteConfigManager.announcement.collect { announcement ->
                announcement.let {
                    _announcements.value = it
                }
            }
        }
    }

    fun markAsRead(announcement: Announcement) {
        viewModelScope.launch {
            // TODO: 實現已讀狀態的本地存儲
            _isRead.value = true

            // 更新公告列表中的已讀狀態
            val updatedAnnouncements = _announcements.value.map {
                if (it.id == announcement.id) it.copy(isVisible = true) else it
            }
            _announcements.value = updatedAnnouncements
        }
    }

    fun refreshAnnouncements() {
        remoteConfigManager.refreshAnnouncement()
    }
} 