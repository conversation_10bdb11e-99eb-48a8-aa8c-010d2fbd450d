package com.one.astrology.viewmodel

import androidx.lifecycle.ViewModel
import com.one.astrology.api.NetworkState
import com.one.astrology.data.request.openai.OpenResponse
import com.one.astrology.repositories.FcmRepository
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

@HiltViewModel
class FcmViewModel @Inject constructor(
    private val fcmRepository: FcmRepository,
) : ViewModel() {

    private val _loading = MutableStateFlow(false)
    val loading = _loading.asSharedFlow()

    fun send(token: String, title: String, message: String): Flow<OpenResponse> {
        _loading.value = true
        return flow {
            val result = fcmRepository.send(token, title, message)
            _loading.value = false
            when (result) {
                is NetworkState.Success -> {
                    LogUtil.i(result.data.toString())
                    emit(result.data)
                }

                is NetworkState.Error -> {
                    LogUtil.e(result.response.code().toString() + " : " + result.response.message())
                }
            }
        }
    }
}