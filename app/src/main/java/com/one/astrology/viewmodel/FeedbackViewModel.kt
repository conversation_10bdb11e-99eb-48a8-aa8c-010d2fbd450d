package com.one.astrology.viewmodel

import android.app.Application
import android.content.Context
import android.net.Uri
import android.os.Build
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.ktx.Firebase
import com.google.firebase.storage.ktx.storage
import com.one.astrology.BuildConfig
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.core.util.LogUtil
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

private val Context.dataStore by preferencesDataStore(name = "feedback")

class FeedbackViewModel(application: Application) : AndroidViewModel(application) {
    private val _feedbackState = MutableStateFlow<FeedbackState>(FeedbackState.Initial)
    val feedbackState: StateFlow<FeedbackState> = _feedbackState.asStateFlow()

    fun submitFeedback(
        title: String,
        content: String,
        category: String,
        mediaUris: List<Uri>,
        chartType: Chart? = null,
        birthDataList: List<BirthData>? = null,
    ) {
        viewModelScope.launch {
            try {
                _feedbackState.value = FeedbackState.Loading

                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                val currentDate = dateFormat.format(Date())
                val userId = FirebaseAuth.getInstance().currentUser?.uid ?: "anonymous"

                // 上傳媒體檔案到 Firebase Storage
                val mediaUrls = mutableListOf<String>()
                if (mediaUris.isNotEmpty()) {
                    val storage = Firebase.storage
                    val storageRef = storage.reference

                    mediaUris.forEachIndexed { index, uri ->
                        try {
                            // 從 URI 中獲取檔案名稱
                            val fileName = uri.lastPathSegment ?: "file_$index"
                            // 使用時間戳和索引來確保檔案名稱唯一
                            val uniqueFileName = "${System.currentTimeMillis()}_${index}_$fileName"
                            val fileRef = storageRef.child("feedback_media/$userId/$uniqueFileName")

                            val uploadTask = fileRef.putFile(uri).await()
                            val downloadUrl = uploadTask.storage.downloadUrl.await()
                            mediaUrls.add(downloadUrl.toString())
                            LogUtil.d("Successfully uploaded file: $fileName")
                        } catch (e: Exception) {
                            LogUtil.e("Error uploading media file: ${e.message}")
                        }
                    }
                }
                val androidVersion = Build.VERSION.RELEASE // 取得 Android 版本
                val deviceModel = Build.MODEL // 取得裝置型號
                val language = Locale.getDefault().language // 取得語系
                val appVersionCode = BuildConfig.VERSION_CODE

                // 儲存反饋資訊到 Firestore
                val db = FirebaseFirestore.getInstance()
                val feedback = mutableMapOf(
                    "title" to title,
                    "content" to content,
                    "category" to category,
                    "date" to currentDate,
                    "userId" to userId,
                    "mediaUrls" to mediaUrls,
                    "androidVersion" to androidVersion,
                    "deviceModel" to deviceModel,
                    "language" to language,
                    "appVersionCode" to appVersionCode
                )

                // 添加星盤類型（如果有）
                chartType?.let {
                    feedback["chartType"] = it.nameEng
                }

                // 添加出生資料列表（如果有）
                birthDataList?.let { birthList ->
                    feedback["birthDataList"] = birthList.map { birthData ->
                        mapOf(
                            "name" to birthData.name,
                            "birthday" to birthData.birthday,
                            "birthdayString" to birthData.birthdayString,
                            "longitude" to birthData.birthplaceLongitude,
                            "latitude" to birthData.birthplaceLatitude,
                            "birthplaceArea" to birthData.birthplaceArea
                        )
                    }
                }

                db.collection("feedback")
                    .add(feedback)
                    .await()

                // 同時儲存到本地 DataStore
                val feedbackString = feedback.toString()
                getApplication<Application>().dataStore.edit { preferences ->
                    val feedbackList = preferences[FEEDBACK_LIST_KEY] ?: emptySet()
                    preferences[FEEDBACK_LIST_KEY] = feedbackList + feedbackString
                }

                _feedbackState.value = FeedbackState.Success
            } catch (e: Exception) {
                LogUtil.e("Error submitting feedback: ${e.message}")
                _feedbackState.value = FeedbackState.Error(e.message ?: "提交失敗")
            }
        }
    }

    companion object {
        private val FEEDBACK_LIST_KEY = stringSetPreferencesKey("feedback_list")
    }
}

sealed class FeedbackState {
    object Initial : FeedbackState()
    object Loading : FeedbackState()
    object Success : FeedbackState()
    data class Error(val message: String) : FeedbackState()
} 