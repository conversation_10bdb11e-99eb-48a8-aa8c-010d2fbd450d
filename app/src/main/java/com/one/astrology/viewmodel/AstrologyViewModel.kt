package com.one.astrology.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.one.astrology.util.AstrologyCalculator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import swisseph.SweConst
import java.util.Calendar

/**
 * 占星 ViewModel
 * 處理占星數據的計算和管理
 */
class AstrologyViewModel(application: Application) : AndroidViewModel(application) {

    // 出生資料
    private val _birthData = MutableLiveData<BirthData>()
    val birthData: LiveData<BirthData> = _birthData

    // 占星數據
    private val _astrologyData = MutableLiveData<AstrologyCalculator.AstrologyData>()
    val astrologyData: LiveData<AstrologyCalculator.AstrologyData> = _astrologyData

    // 計算狀態
    private val _isCalculating = MutableLiveData<Boolean>()
    val isCalculating: LiveData<Boolean> = _isCalculating

    // 錯誤訊息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 行星名稱映射
    private val planetNames = mapOf(
        SweConst.SE_SUN to "太陽",
        SweConst.SE_MOON to "月亮",
        SweConst.SE_MERCURY to "水星",
        SweConst.SE_VENUS to "金星",
        SweConst.SE_MARS to "火星",
        SweConst.SE_JUPITER to "木星",
        SweConst.SE_SATURN to "土星",
        SweConst.SE_URANUS to "天王星",
        SweConst.SE_NEPTUNE to "海王星",
        SweConst.SE_PLUTO to "冥王星",
        SweConst.SE_MEAN_NODE to "北交點"
    )

    // 星座名稱
    private val zodiacSigns = arrayOf(
        "牡羊座", "金牛座", "雙子座", "巨蟹座",
        "獅子座", "處女座", "天秤座", "天蠍座", 
        "射手座", "摩羯座", "水瓶座", "雙魚座"
    )

    // 宮位名稱
    private val houseNames = arrayOf(
        "第一宮", "第二宮", "第三宮", "第四宮", 
        "第五宮", "第六宮", "第七宮", "第八宮", 
        "第九宮", "第十宮", "第十一宮", "第十二宮"
    )

    // 相位名稱
    private val aspectNames = mapOf(
        AstrologyCalculator.CONJUNCTION to "合相",
        AstrologyCalculator.SEXTILE to "六分相",
        AstrologyCalculator.SQUARE to "四分相",
        AstrologyCalculator.TRINE to "三分相",
        AstrologyCalculator.OPPOSITION to "對分相"
    )

    /**
     * 設置出生資料
     * @param name 姓名
     * @param birthTime 出生時間 (毫秒)
     * @param latitude 出生地緯度
     * @param longitude 出生地經度
     * @param isDaylightSavingTime 是否為日光節約時間
     */
    fun setBirthData(
        name: String,
        birthTime: Long,
        latitude: Double,
        longitude: Double,
        isDaylightSavingTime: Boolean = false
    ) {
        val birthData = BirthData(
            name = name,
            birthTime = birthTime,
            latitude = latitude,
            longitude = longitude,
            isDaylightSavingTime = isDaylightSavingTime
        )
        _birthData.value = birthData
        calculateAstrologyData(birthData)
    }

    /**
     * 計算占星數據
     * @param birthData 出生資料
     * @param houseSystem 宮制代碼 (默認為 Placidus)
     */
    private fun calculateAstrologyData(
        birthData: BirthData,
        houseSystem: Char = AstrologyCalculator.DEFAULT_HOUSE_SYSTEM
    ) {
        viewModelScope.launch {
            _isCalculating.value = true
            try {
                val result = withContext(Dispatchers.IO) {
                    AstrologyCalculator.calculateAstrologyData(
                        getApplication(),
                        birthData.birthTime,
                        birthData.latitude,
                        birthData.longitude,
                        houseSystem
                    )
                }
                _astrologyData.value = result
            } catch (e: Exception) {
                _errorMessage.value = "計算占星數據時發生錯誤: ${e.message}"
            } finally {
                _isCalculating.value = false
            }
        }
    }

    /**
     * 獲取行星資料列表
     * @return 行星資料列表
     */
    fun getPlanetDataList(): List<PlanetData> {
        val data = _astrologyData.value ?: return emptyList()
        
        return planetNames.keys.mapNotNull { planetId ->
            val longitude = data.getPlanetLongitude(planetId) ?: return@mapNotNull null
            val house = data.getPlanetHouse(planetId) ?: return@mapNotNull null
            val sign = data.getPlanetSign(planetId) ?: return@mapNotNull null
            val degree = data.getPlanetDegree(planetId) ?: return@mapNotNull null
            val isRetrograde = data.isPlanetRetrograde(planetId) ?: false
            
            PlanetData(
                id = planetId,
                name = planetNames[planetId] ?: "未知行星",
                longitude = longitude,
                house = house,
                houseName = houseNames[house - 1],
                sign = sign,
                signName = zodiacSigns[sign],
                degree = degree,
                isRetrograde = isRetrograde
            )
        }
    }

    /**
     * 獲取相位資料列表
     * @return 相位資料列表
     */
    fun getAspectDataList(): List<AspectData> {
        val data = _astrologyData.value ?: return emptyList()
        
        return data.aspects.map { (planet1Id, planet2Id, aspectType) ->
            AspectData(
                planet1Id = planet1Id,
                planet1Name = planetNames[planet1Id] ?: "未知行星",
                planet2Id = planet2Id,
                planet2Name = planetNames[planet2Id] ?: "未知行星",
                aspectType = aspectType,
                aspectName = aspectNames[aspectType] ?: "未知相位"
            )
        }
    }

    /**
     * 獲取宮位資料列表
     * @return 宮位資料列表
     */
    fun getHouseDataList(): List<HouseData> {
        val data = _astrologyData.value ?: return emptyList()
        
        return (1..12).map { houseIndex ->
            val cusp = data.cusps[houseIndex]
            val sign = (cusp / 30).toInt() % 12
            
            HouseData(
                index = houseIndex,
                name = houseNames[houseIndex - 1],
                cusp = cusp,
                sign = sign,
                signName = zodiacSigns[sign]
            )
        }
    }

    /**
     * 獲取特殊點資料列表 (上升點、中天點等)
     * @return 特殊點資料列表
     */
    fun getSpecialPointDataList(): List<SpecialPointData> {
        val data = _astrologyData.value ?: return emptyList()
        
        val result = mutableListOf<SpecialPointData>()
        
        // 上升點
        val asc = data.getAscendant()
        val ascSign = (asc / 30).toInt() % 12
        result.add(
            SpecialPointData(
                name = "上升點",
                longitude = asc,
                sign = ascSign,
                signName = zodiacSigns[ascSign]
            )
        )
        
        // 中天點
        val mc = data.getMidheaven()
        val mcSign = (mc / 30).toInt() % 12
        result.add(
            SpecialPointData(
                name = "中天點",
                longitude = mc,
                sign = mcSign,
                signName = zodiacSigns[mcSign]
            )
        )
        
        // 宿命點
        val vertex = data.getVertex()
        val vertexSign = (vertex / 30).toInt() % 12
        result.add(
            SpecialPointData(
                name = "宿命點",
                longitude = vertex,
                sign = vertexSign,
                signName = zodiacSigns[vertexSign]
            )
        )
        
        return result
    }

    /**
     * 獲取與指定行星形成相位的所有行星
     * @param planetId 行星 ID
     * @return 相位資料列表
     */
    fun getPlanetAspects(planetId: Int): List<AspectData> {
        val data = _astrologyData.value ?: return emptyList()
        
        return data.getPlanetAspects(planetId).map { (otherPlanetId, aspectType) ->
            AspectData(
                planet1Id = planetId,
                planet1Name = planetNames[planetId] ?: "未知行星",
                planet2Id = otherPlanetId,
                planet2Name = planetNames[otherPlanetId] ?: "未知行星",
                aspectType = aspectType,
                aspectName = aspectNames[aspectType] ?: "未知相位"
            )
        }
    }

    /**
     * 格式化度數
     * @param degree 度數
     * @return 格式化後的度數字符串 (例如: 15°30')
     */
    fun formatDegree(degree: Double): String {
        val deg = degree.toInt()
        val min = ((degree - deg) * 60).toInt()
        return "$deg°$min'"
    }

    /**
     * 行星資料類
     */
    data class PlanetData(
        val id: Int,
        val name: String,
        val longitude: Double,
        val house: Int,
        val houseName: String,
        val sign: Int,
        val signName: String,
        val degree: Double,
        val isRetrograde: Boolean
    ) {
        /**
         * 獲取格式化的度數
         */
        fun getFormattedDegree(): String {
            val deg = degree.toInt()
            val min = ((degree - deg) * 60).toInt()
            return "$deg°$min'"
        }
        
        /**
         * 獲取完整描述
         */
        fun getDescription(): String {
            val retrograde = if (isRetrograde) "逆行" else "順行"
            return "$name 位於 $signName $houseName ${getFormattedDegree()} $retrograde"
        }
    }

    /**
     * 相位資料類
     */
    data class AspectData(
        val planet1Id: Int,
        val planet1Name: String,
        val planet2Id: Int,
        val planet2Name: String,
        val aspectType: Int,
        val aspectName: String
    ) {
        /**
         * 獲取完整描述
         */
        fun getDescription(): String {
            return "$planet1Name 與 $planet2Name 形成 $aspectName"
        }
    }

    /**
     * 宮位資料類
     */
    data class HouseData(
        val index: Int,
        val name: String,
        val cusp: Double,
        val sign: Int,
        val signName: String
    ) {
        /**
         * 獲取格式化的度數
         */
        fun getFormattedDegree(): String {
            val degree = cusp % 30
            val deg = degree.toInt()
            val min = ((degree - deg) * 60).toInt()
            return "$deg°$min'"
        }
        
        /**
         * 獲取完整描述
         */
        fun getDescription(): String {
            return "$name 宮頭位於 $signName ${getFormattedDegree()}"
        }
    }

    /**
     * 特殊點資料類
     */
    data class SpecialPointData(
        val name: String,
        val longitude: Double,
        val sign: Int,
        val signName: String
    ) {
        /**
         * 獲取格式化的度數
         */
        fun getFormattedDegree(): String {
            val degree = longitude % 30
            val deg = degree.toInt()
            val min = ((degree - deg) * 60).toInt()
            return "$deg°$min'"
        }
        
        /**
         * 獲取完整描述
         */
        fun getDescription(): String {
            return "$name 位於 $signName ${getFormattedDegree()}"
        }
    }
}

/**
 * 出生資料類
 */
data class BirthData(
    val name: String,
    val birthTime: Long,
    val latitude: Double,
    val longitude: Double,
    val isDaylightSavingTime: Boolean = false
) {
    /**
     * 獲取格式化的出生時間
     */
    fun getFormattedBirthTime(): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = birthTime
        
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH) + 1
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        
        return "$year/$month/$day $hour:$minute"
    }
    
    /**
     * 獲取格式化的地理位置
     */
    fun getFormattedLocation(): String {
        return "緯度: $latitude, 經度: $longitude"
    }
}

