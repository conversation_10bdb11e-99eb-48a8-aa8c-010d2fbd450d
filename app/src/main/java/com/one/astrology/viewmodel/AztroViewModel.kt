package com.one.astrology.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.one.astrology.api.NetworkState
import com.one.astrology.data.SunSignInfo
import com.one.astrology.repositories.AztroRepository
import com.one.core.util.LogUtil
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class AztroViewModel(private val mainRepository: AztroRepository) : ViewModel() {

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String>
        get() = _errorMessage

    val signInfoMutableLiveData = MutableLiveData<SunSignInfo?>()

    var job: Job? = null


    private val exceptionHandler = CoroutineExceptionHandler { _, throwable ->
        onError("Exception handled: ${throwable.localizedMessage}")
    }
    val loading = MutableLiveData<Boolean>()

    fun getSignInfo(sign: String, day: String) {
        viewModelScope.launch {
            when (val response = mainRepository.getSignInfo(sign, day)) {
                is NetworkState.Success -> {
                    signInfoMutableLiveData.postValue(response.data)
                }
                is NetworkState.Error -> {
                    LogUtil.e(
                        response.response.code().toString() + " " + response.response.message()
                    )
                    if (response.response.code() == 401) {
                        //movieList.postValue(NetworkState.Error())
                    } else {
                        //movieList.postValue(NetworkState.Error)
                    }
                }
            }
        }
    }

    private fun onError(message: String) {
        _errorMessage.value = message
        loading.value = false
    }

    override fun onCleared() {
        super.onCleared()
        job?.cancel()
    }
}