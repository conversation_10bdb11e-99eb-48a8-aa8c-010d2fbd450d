package com.one.astrology.viewmodel

import android.content.Context
import android.text.TextUtils
import androidx.lifecycle.ViewModel
import com.one.astrology.data.db.AstroData
import com.one.astrology.db.OtherDBHelper
import com.one.astrology.repositories.AstroRepository
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.flow
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.select.Elements
import javax.inject.Inject

@HiltViewModel
class AstroViewModel @Inject constructor(
    private val astroRepository: AstroRepository,
) : ViewModel() {

    private val _loading = MutableStateFlow(false)
    val loading = _loading.asSharedFlow()

    fun getAllPages(context: Context): Flow<String> {
        _loading.value = true
        return flow {
            val result = astroRepository.getAllPages()
            _loading.value = false
            if (result.body() != null) {
                val link = getNextLink(context, result.body()!!.string())
                emit(link)
            } else {
                emit("")
            }
        }
    }

    fun getAstroWiki(context: Context, name: String): Flow<String> {
        _loading.value = true
        return flow {
            val result = astroRepository.getAstroWiki(name)
            _loading.value = false
            if (result.body() != null) {
                val link = getNextLink(context, result.body()!!.string())
                emit(link)
            } else {
                emit("")
            }
        }
    }


    fun getDetail(context: Context, data: AstroData): Flow<String?> {
        _loading.value = true
        return flow {
            val result = astroRepository.getData(data.link!!)
            _loading.value = false
            if (result.isSuccessful && result.body() != null) {
                val message = getDetailData(context, result.body()!!.string(), data)
                emit(message)
            } else {
                val resultMessage = result.code().toString() + " " + result.message()
                val message = "$resultMessage [${data.id}] [${data.name}] [${data.link}]"
                data.createTime = -999
                data.biography = resultMessage
                OtherDBHelper.updateData(context, data)
                LogUtil.e(message)
                emit(message)
            }
        }
    }

    private fun getNextLink(context: Context, response: String?): String {
        val astroDataList = ArrayList<AstroData>()
        try {
            if (TextUtils.isEmpty(response)) {
                return ""
            }
            val document = Jsoup.parse(response)
            val page: Elements =
                document.getElementsByAttributeValueContaining("class", "mw-allpages-chunk")
            if (page.isEmpty()) {
                return ""
            }
            val links = page[0].getElementsByTag("a")
            links.forEach {
                val astroData = AstroData()
                astroData.link = "https://www.astro.com/" + it.attr("href")
                astroData.birthName = it.text().replace("'", " ")
                LogUtil.d("astroData ${astroData.birthName} - ${astroData.link}")
                astroDataList.add(astroData)
                OtherDBHelper.writeAstroDataList(context, astroData.birthName!!, astroData.link!!)
            }
            val nav: Elements =
                document.getElementsByAttributeValueContaining("class", "mw-allpages-nav")
            if (nav.isEmpty()) {
                return ""
            }
            val next = nav[0].getElementsByTag("a")
            var link = ""
            next.forEach {
                link = it.attr("href")
                val from = link.split("from=")
                if (from.size <= 1) {
                    return ""
                }
                val text = it.text()
                if (text.contains("Next page")) {
                    link = from[1]
                }
                LogUtil.d("next link $link text $text")

            }
            return link
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    private fun getDetailData(context: Context, response: String?, data: AstroData): String? {
        try {
            val message = "[${data.id}] [${data.name}] [${data.link}]"
            LogUtil.d(message)
            if (TextUtils.isEmpty(response)) {
                return message
            }
            val doc = Jsoup.parse(response)
            if (doc.title().contains("Login required")) {
                LogUtil.e("Login required $message")
                data.createTime = -99
                data.biography = "Login required"
                OtherDBHelper.updateData(context, data)
                return message
            }
            val tableRedirect: Elements =
                doc.getElementsByAttributeValueContaining("class", "redirectText")
            if (tableRedirect.isNotEmpty()) {
                val next = tableRedirect[0].getElementsByTag("a")
                next.forEach {
                    data.link = "https://www.astro.com" + it.attr("href")
                }
                data.name = tableRedirect.text()
                OtherDBHelper.updateData(context, data)
                LogUtil.i("redirect $message")
                return message
            }
            val table: Elements =
                doc.getElementsByAttributeValueContaining("class", "infobox toccolours")
            val tr = table.select("tr")
            if (doc.text().contains("Birthname")) {
                data.name = tr[1].select("td")[0].text().replace("'", "''")
                data.gender = tr[1].select("td")[1].text().split("Gender: ")[1]
                data.birthName = tr[2].select("td")[1].text().replace("'", "''")

                val times = tr[3].select("td")[1].text().split(" (= ")
                if (times.size > 1) {
                    data.time = times[0]
                } else {
                    data.time = tr[3].select("td")[1].text()
                }
                splitTime(data)

                val place = tr[4].select("td")[1].text()
                getPlace(place, data)

                data.roddenRating = tr[8].select("td")[0].select("b").text()
                getBiography(doc, data)
                data.categories = getCategories(doc).replace("'", "''")
                data.createTime = System.currentTimeMillis()

                OtherDBHelper.updateData(context, data)
                return message
            }
            when (tr.size) {
                0 -> {
                    LogUtil.e("發生錯誤 $message")
                    data.createTime = -1
                    OtherDBHelper.updateData(context, data)
                    return message
                }

                22,
                10,
                16 -> {
                    data.name = tr[1].select("td")[0].text().replace("'", "''")
                    data.gender = tr[1].select("td")[1].text().split("Gender: ")[1]

                    val times = tr[2].select("td")[1].text().split(" (= ")
                    if (times.size > 1) {
                        data.time = times[0]
                    } else {
                        data.time = tr[2].select("td")[1].text()
                    }

                    val place = tr[3].select("td")[1].text()
                    getPlace(place, data)

                    data.roddenRating = tr[7].select("td")[0].select("b").text()
                }

                else -> {
                    if (tr.size > 7) {
                        data.name = tr[1].select("td")[0].text().replace("'", "''")
                        data.gender = tr[1].select("td")[1].text().split("Gender: ")[1]

                        val times = tr[2].select("td")[1].text().split(" (= ")
                        if (times.size > 1) {
                            data.time = times[0]
                        } else {
                            data.time = tr[2].select("td")[1].text()
                        }

                        val place = tr[3].select("td")[1].text()
                        getPlace(place, data)

                        data.roddenRating = tr[7].select("td")[0].select("b").text()
                    } else {
                        LogUtil.e("發生錯誤 $message")
                        data.createTime = -2
                        OtherDBHelper.updateData(context, data)
                        return message
                    }
                }
            }
            splitTime(data)
            getBiography(doc, data)
            data.categories = getCategories(doc).replace("'", "''")
            data.createTime = System.currentTimeMillis()

            OtherDBHelper.updateData(context, data)
            return message
        } catch (e: Exception) {
            e.message?.let { LogUtil.e(it) }
            data.createTime = -3
            OtherDBHelper.updateData(context, data)
            return e.message
        }

    }

    private fun splitTime(data: AstroData) {
        // 1955 年 2 月 24 日 19:15   9 November 1914 at 12:15
        val times = data.time!!.split(" ")
        val year = times[2].toInt()
        val month = getMonth(times[1])
        val day = times[0].toInt()

        val at = data.time!!.split("at")
        if (at.size > 1) {
            val hour = at[1].split(":")[0].replace(" ", "").toInt()
            val min = at[1].split(":")[1].toInt()
            var strTime = "$year/${month}/${day} $hour:$min"
            val date = FormatUtils.stringToDate(strTime, "yyyy/MM/dd HH:mm")
            strTime = FormatUtils.dateToString(date, "yyyy/MM/dd HH:mm")
            LogUtil.d("[${data.id}] ${data.time!!} [$strTime]")
            data.time = strTime
            data.timeInMillis = date.time
        } else {
            var strTime = "$year/${month}/${day}"
            val date = FormatUtils.stringToDate(strTime, "yyyy/MM/dd")
            strTime = FormatUtils.dateToString(date, "yyyy/MM/dd")
            data.time = strTime
            LogUtil.e("發生錯誤 ${data.time}")
        }

        data.latitude = data.latitude?.replace("- ", "-")
        data.longitude = data.longitude?.replace("- ", "-")
    }

    private fun getMonth(month: String): Int {
        when (month) {
            "January" -> {
                return 1
            }

            "February" -> {
                return 2
            }

            "March" -> {
                return 3
            }

            "April" -> {
                return 4
            }

            "May" -> {
                return 5
            }

            "June" -> {
                return 6
            }

            "July" -> {
                return 7
            }

            "August" -> {
                return 8
            }

            "September" -> {
                return 9
            }

            "October" -> {
                return 10
            }

            "November" -> {
                return 11
            }

            "December" -> {
                return 12
            }

            else -> {
                return 1
            }
        }
    }

    private fun getPlace(place: String, data: AstroData) {
        val places = place.split(",")
        if (places.size >= 3) {
            data.place = place.split(places[places.size - 2])[0]
            data.latitude = places[places.size - 2]
            if (data.latitude!!.contains("n")) {
                data.latitude = data.latitude!!.replace("n", ".")
            }
            if (data.latitude!!.contains("s")) {
                data.latitude = data.latitude!!.replace("s", ".")
                data.latitude = "-${data.latitude}"
            }
            data.longitude = places[places.size - 1]
            if (data.longitude!!.contains("e")) {
                data.longitude = data.longitude!!.replace("e", ".")
            }
            if (data.longitude!!.contains("w")) {
                data.longitude = data.longitude!!.replace("w", ".")
                data.longitude = "-${data.longitude}"
            }
        }
    }

    private fun getBiography(doc: Document, data: AstroData) {
        val biographyElement =
            doc.allElements.find { it.text() == "Biography" && it.className() == "mw-headline" }
                ?: return
        val index = doc.allElements.indexOf(biographyElement)
        var biography = ""
        for (i in index + 1 until doc.allElements.size) {
            val element = doc.allElements[i]
            if (element.hasAttr("class")) {
                break
            }
            if (element.hasAttr("href")) {
                break
            }
            val text = element.text().replace("'", " ")
            if (text.contains("Link to Wikipedia")) {
                val item = element.getElementsByTag("a")
                item.forEach {
                    data.wikipedia = it.attr("href")
                }
                continue
            }
            if (biography.contains(text)) {
                continue
            }
            biography += text + "\n"
        }
        data.biography = biography.replace("'", "''")
    }

    private fun getCategories(doc: Document): String {
        val categoriesElement =
            doc.allElements.find { it.text() == "Categories" && it.className() == "mw-headline" }
                ?: return ""
        val index = doc.allElements.indexOf(categoriesElement)
        var categories = ""
        for (i in index + 1 until doc.allElements.size) {
            val element = doc.allElements[i]
            if (element.hasAttr("class")) {
                break
            }
            if (element.hasAttr("href")) {
                break
            }
            val text = element.text().replace("'", " ")
            if (categories.contains(text)) {
                continue
            }
            categories += text + "\n"
        }
        return categories.replace("'", "''")
    }
}
