package com.one.astrology.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.one.astrology.data.request.openai.ChatResponse
import com.one.astrology.data.request.openai.OpenResponse
import com.one.astrology.repositories.OpenRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class OpenViewModel @Inject constructor(
    private val openRepository: OpenRepository,
) : ViewModel() {

    val resultLiveData = MutableLiveData<OpenResponse?>()
    val chatResponseLiveData = MutableLiveData<ChatResponse?>()

    fun completions(response: String) {
        viewModelScope.launch {
            when (val result = openRepository.getChatResponse(response)) {

            }
        }
    }

    fun getChatResponse(response: String) {
        viewModelScope.launch {
            when (val result = openRepository.getChatResponse(response)) {
//                is NetworkState.Success -> {
//                    chatResponseLiveData.postValue(result.data)
//                }
//
//                is NetworkState.Error -> {
//                    LogUtil.e("${result.response.code()} ${result.response.message()}")
//                    if (result.response.code() == 401) {
//                        //movieList.postValue(NetworkState.Error())
//                    } else {
//                        //movieList.postValue(NetworkState.Error)
//                    }
//                }
            }
        }
    }
}