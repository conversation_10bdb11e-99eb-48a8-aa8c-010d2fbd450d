package com.one.astrology.repositories

import com.one.astrology.api.OpenApi
import com.one.astrology.api.RetrofitClient
import com.one.astrology.data.request.groq.Message
import com.one.astrology.data.request.openai.ChatRequest
import com.one.astrology.data.request.openai.ChatResponse
import com.one.core.api.ServiceGenerator
import com.one.core.util.LogUtil
import dagger.Reusable
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import javax.inject.Inject

@Reusable
class OpenRepository @Inject constructor(
    private val openApi: OpenApi,
) {

    fun getChatResponse(prompt: String){
        ServiceGenerator.setBaseUrl("https://api.openai.com/")
        val messages = listOf(
            Message(role = "system", content = "你是占星專家，能夠幫助我解答占星相關問題。"),
            Message(role = "user", content = prompt)
        )

        val request = ChatRequest(
            model = "gpt-4o-mini",  // 可以選擇其他模型 gpt-4o-mini gpt-3.5-turbo
            messages = messages
        )

        // model : gpt-3.5-turbo  text-davinci-003 gpt-3.5-turbo-instruct
        val result = openApi.getChatResponse(request).enqueue(object : Callback<ChatResponse> {
            override fun onResponse(call: Call<ChatResponse>, response: Response<ChatResponse>) {
                if (response.isSuccessful) {
                    val chatResponse = response.body()
                    // 處理回應，這裡是取得 ChatGPT 的回應內容
                    val answer = chatResponse?.choices?.get(0)?.message?.content
                    println("ChatGPT 回應: $answer")
                } else {
                    if (response.errorBody() != null) {
                        response.errorBody()?.string()?.let { LogUtil.e(it) }
                    }

                    if (response.code() == 429) {
                        val retryAfter = response.headers()["Retry-After"]?.toIntOrNull()
                        val XRateLimitLimit = response.headers()["X-RateLimit-Limit"]?.toIntOrNull()
                        val XRateLimitRemaining =
                            response.headers()["X-RateLimit-Remaining"]?.toIntOrNull()
                        LogUtil.d("XRateLimitLimit $XRateLimitLimit XRateLimitRemaining $XRateLimitRemaining")
                        println("需要等待 ${retryAfter ?: 0} 秒後再重試")
                    } else {
                        println("API 請求失敗: ${response.message()}")
                    }
                }
            }

            override fun onFailure(call: Call<ChatResponse>, t: Throwable) {
                println("請求錯誤: ${t.message}")
            }
        })
//        val result = openApi.getChatResponse(request)
//        return if (result.isSuccessful) {
//            val responseBody = result.body()
//            if (responseBody != null) {
//                NetworkState.Success(responseBody)
//            } else {
//                NetworkState.Error(result)
//            }
//        } else {
//            NetworkState.Error(result)
//        }
    }
    private val api = RetrofitClient.instance
}