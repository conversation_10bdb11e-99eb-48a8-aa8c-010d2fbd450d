package com.one.astrology.repositories

import com.one.astrology.api.NetworkState
import com.one.astrology.api.RetrofitService
import com.one.astrology.data.SunSignInfo

class AztroRepository(private val retrofitService: RetrofitService) {

    /**
    Parameters
    sign :
    Name of the sign.
    List of all signs -
    aries, taurus, gemini, cancer, leo, virgo, libra, scorpio, sagittarius, capricorn, aquarius and pisces.
    day :
    Day can be today,tomorrow or yesterday
     */
    suspend fun getSignInfo(sign: String,day: String) : NetworkState<SunSignInfo> {
        val response = retrofitService.getSignInfo(sign, day)
        return if (response.isSuccessful) {
            val responseBody = response.body()
            if (responseBody != null) {
                NetworkState.Success(responseBody)
            } else {
                NetworkState.Error(response)
            }
        } else {
            NetworkState.Error(response)
        }
    }
}