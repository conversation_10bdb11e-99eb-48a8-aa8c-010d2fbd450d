package com.one.astrology.repositories

import com.one.astrology.api.FcmApi
import com.one.astrology.api.NetworkState
import com.one.astrology.api.RequestNotificaton
import com.one.astrology.api.SendNotificationModel
import com.one.astrology.data.request.openai.OpenResponse
import com.one.core.api.ServiceGenerator
import dagger.Reusable
import javax.inject.Inject

@Reusable
class FcmRepository @Inject constructor(
    private val fcmApi: FcmApi,
) {

    suspend fun send(token:String, title: String, message: String): NetworkState<OpenResponse> {
        ServiceGenerator.setBaseUrl("https://fcm.googleapis.com/")
        val requestNotificaton = RequestNotificaton()
        requestNotificaton.token = token
//        requestNotificaton.sendNotificationModel = SendNotificationModel(title, message)
        requestNotificaton.data = SendNotificationModel(title, message, "message")
        val result =
            ServiceGenerator.createService(FcmApi::class.java).send(requestNotificaton)
        return if (result.isSuccessful) {
            val responseBody = result.body()
            if (responseBody != null) {
                NetworkState.Success(responseBody)
            } else {
                NetworkState.Error(result)
            }
        } else {
            NetworkState.Error(result)
        }
    }
}