package com.one.astrology.repositories

import com.one.astrology.api.OpenAIService
import com.one.astrology.api.OpenApi
import com.one.astrology.data.request.groq.Message
import com.one.astrology.data.request.openai.ChatRequest
import com.one.astrology.data.request.openai.ChatResponse
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Inject

class OpenAIRepository @Inject constructor(
    private val openApi: OpenApi,
) {
    fun createRetrofit(): OpenAIService {
        val retrofit = Retrofit.Builder()
            .baseUrl("https://api.openai.com/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        return retrofit.create(OpenAIService::class.java)
    }

    fun sendMessageToOpenAI() {
        val service = createRetrofit()

        val messages = listOf(
            Message(role = "system", content = "你是占星專家，能夠幫助我解答占星相關問題。"),
            Message(role = "user", content = "今天的星座運勢如何？")
        )

        val request = ChatRequest(
            model = "gpt-3.5-turbo",  // 可以選擇其他模型
            messages = messages
        )

        service.getChatResponse(request).enqueue(object : Callback<ChatResponse> {
            override fun onResponse(call: Call<ChatResponse>, response: Response<ChatResponse>) {
                if (response.isSuccessful) {
                    val chatResponse = response.body()
                    // 處理回應，這裡是取得 ChatGPT 的回應內容
                    val answer = chatResponse?.choices?.get(0)?.message?.content
                    println("ChatGPT 回應: $answer")
                } else {
                    println("API 請求失敗: ${response.message()}")
                }
            }

            override fun onFailure(call: Call<ChatResponse>, t: Throwable) {
                println("請求錯誤: ${t.message}")
            }
        })
    }

}

