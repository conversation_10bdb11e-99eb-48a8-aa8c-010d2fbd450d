package com.one.astrology.repositories

import com.one.astrology.api.AstroApi
import com.one.astrology.di.IoDispatcher
import dagger.Reusable
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

@Reusable
class AstroRepository @Inject constructor(
    private val astroApi: Astro<PERSON><PERSON>,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {
    suspend fun getAllPages() =
        withContext(ioDispatcher) {
            astroApi.getAllPages()
        }

    suspend fun getAstroWiki(name: String) =
        withContext(ioDispatcher) {
            astroApi.getAstroWiki(name)
        }


    suspend fun getData(name: String) =
        withContext(ioDispatcher) {
            astroApi.getAstroData(name)
        }
}


