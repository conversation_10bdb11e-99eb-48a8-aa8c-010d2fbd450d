package com.one.astrology.ui.fragment.analysis.screen

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import java.util.Calendar


@Composable
fun DatePickerField(label: String, dateState: MutableState<String>) {
    val context = LocalContext.current

    OutlinedTextField(
        value = dateState.value,
        onValueChange = {},
        label = { Text(label) },
        modifier = Modifier.fillMaxWidth(),
        readOnly = true,
        trailingIcon = {
            IconButton(onClick = {
                val calendar = Calendar.getInstance()
                DatePickerDialog(
                    context, { _, year, month, day ->
                        dateState.value = "$year-${month + 1}-$day"
                    }, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(
                        Calendar.DAY_OF_MONTH
                    )
                ).show()
            }) {
                Icon(Icons.Default.DateRange, contentDescription = "選擇日期")
            }
        }
    )
}

@Composable
fun TimePickerField(label: String, timeState: MutableState<String>) {
    val context = LocalContext.current

    OutlinedTextField(
        value = timeState.value,
        onValueChange = {},
        label = { Text(label) },
        modifier = Modifier.fillMaxWidth(),
        readOnly = true,
        trailingIcon = {
            IconButton(onClick = {
                val calendar = Calendar.getInstance()
                TimePickerDialog(
                    context,
                    { _, hour, minute ->
                        timeState.value = String.format("%02d:%02d", hour, minute)
                    },
                    calendar.get(Calendar.HOUR_OF_DAY),
                    calendar.get(Calendar.MINUTE),
                    true
                ).show()
            }) {
                Icon(Icons.Default.AddCircle, contentDescription = "選擇時間")
            }
        }
    )
}