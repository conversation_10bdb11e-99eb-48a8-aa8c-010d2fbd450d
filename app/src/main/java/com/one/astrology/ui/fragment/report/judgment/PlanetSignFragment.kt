package com.one.astrology.ui.fragment.report.judgment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.InterpretationType
import com.one.astrology.data.PdfItemData
import com.one.astrology.data.SignPosition
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.db.SignDescData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentPlanetSignBinding
import com.one.astrology.db.DBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.ExplainFragment
import com.one.astrology.ui.fragment.report.adapter.SignDescItemAdapter
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil


/**
 * A simple [Fragment] subclass.
 * Use the [PlanetSignFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class PlanetSignFragment : Fragment() {
    private lateinit var binding: FragmentPlanetSignBinding
    private val signList = ArrayList<SignDescData>()
    private var matchEvent: MatchEvent? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPlanetSignBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        collectData()
    }

    private fun collectData() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
//            clearData()
//            title = getString(matchEvent.chartType.type)
            this.matchEvent = matchEvent

//            pdfData.name = matchEvent.horoscopeA.name
//            var chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
//            var pdfTitle = getString(R.string.pdf_title, chartName)
//            binding.tvTitle.text = pdfTitle
//            pdfData.chart = matchEvent.chartType

            when (matchEvent.chartType) {
                Chart.Natal -> {
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.Transit -> {
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.SecondaryProgression -> {
                    binding.tvSignPosition.visibility = View.GONE
                    binding.recyclerViewSign.visibility = View.GONE
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.TertiaryProgression -> {
                    binding.tvSignPosition.visibility = View.GONE
                    binding.recyclerViewSign.visibility = View.GONE
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.Synastry -> {
//                    pdfData.name = getString(
//                        R.string.synastry_name,
//                        matchEvent.horoscopeB.name,
//                        matchEvent.horoscopeA.name
//                    )
//                    chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
//                    pdfTitle = getString(R.string.pdf_title, chartName)
//                    binding.tvTitle.text = pdfTitle
                    binding.tvSignPosition.visibility = View.GONE
                    binding.recyclerViewSign.visibility = View.GONE
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    matchEvent.horoscopeB.aspectList =
                        EphemerisUtil.aspects(
                            requireContext(),
                            matchEvent.chartType,
                            matchEvent.horoscopeB.planetList,
                            matchEvent.horoscopeA.planetList,
                            isMatch = true,
                            isGetScore = false
                        )
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.Composite -> {
//                    pdfData.name = getString(
//                        R.string.both_name,
//                        matchEvent.horoscopeA.name,
//                        matchEvent.horoscopeB.name
//                    )
//                    chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
//                    pdfTitle = getString(R.string.pdf_title, chartName)
//                    binding.tvTitle.text = pdfTitle
//                    (binding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(3).visibility =
//                        View.GONE
//                    (binding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(4).visibility =
//                        View.GONE
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                else -> {

                }
            }

//            initTab()
        }
    }

    private fun initData(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        initSign(horoscopeA, horoscopeB)
    }

    private fun initSign(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        val signList = AssetsToObjectUtil.getSignList(requireContext())
        val planetBeanList =
            horoscopeA.getPlanetBeanList().sortedWith(compareBy { it.id })
        for (planet in planetBeanList) {
            if (!planet.isChecked) {
                continue
            }
            val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
            val signBean: SignBean = signList[strings[0].toInt()]
            val signPosition = SignPosition(signBean)
            signPosition.houseData = EphemerisUtil.house(planet.longitude, horoscopeB.houses.cusps)
            LogUtil.i("星位 " + planet.chName + " " + signBean.chName + strings[1] + "°" + strings[2] + signPosition.houseData!!.index + "宮\n")

            querySign(planet.chName, signBean.chName)
            if (signPosition.houseData == null) {
                continue
            }
//            queryHouse(signPosition.houseData!!.index, planet.chName)
        }
        initRecycleViewSign()
//        initRecycleViewSignHouse()
    }

    private fun querySign(planetName: String, signName: String) {
        val data = DBHelper.querySign(
            requireContext(),
            matchEvent?.chartType!!, planetName, signName
        )
        if (data != null) {
            signList.add(data)
            val pdfItemData = PdfItemData()
            pdfItemData.type = InterpretationType.sign
            pdfItemData.subTitle = getString(R.string.planet_sign_title, data.planet, data.sign)
            if (data.descList.isNotEmpty()) {
                pdfItemData.desc = data.descList[0]
            }
            pdfItemData.descList = data.descList
//            pdfData.pdfItemData.add(pdfItemData)
        }
    }

    private fun initRecycleViewSign() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewSign.layoutManager = layoutManager
        val signDescItemAdapter = SignDescItemAdapter()
        signDescItemAdapter.isEmptyViewEnable = true
        signDescItemAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        binding.recyclerViewSign.adapter = signDescItemAdapter
        signDescItemAdapter.submitList(signList)
        signDescItemAdapter.addOnItemChildClickListener(R.id.tvMore) { _, _, position ->
            val item = signDescItemAdapter.items[position]
            val title = getString(R.string.planet_sign_title, item.planet, item.sign)
            toExplain(title, item.descList)
        }
    }

    private fun toExplain(title: String, descList: ArrayList<String>) {
        if (descList.size > 1) {
            ExplainFragment.newInstance(title, descList)
                .show(requireActivity().supportFragmentManager, "")
        } else {
            com.one.core.util.IntentUtil.searchWeb(requireActivity(), title)
        }
    }
}