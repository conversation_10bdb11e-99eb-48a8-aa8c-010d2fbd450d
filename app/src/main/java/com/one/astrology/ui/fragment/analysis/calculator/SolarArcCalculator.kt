package com.one.astrology.ui.fragment.analysis.calculator

import com.one.astrology.data.PlanetPosition
import com.one.core.util.LogUtil
import kotlin.math.abs


data class SolarArcEvent(
    var age: Int,
    var planetArc: String,
    var planetNatal: String,
    var aspect: String,
    var importantEvents: String
)

class SolarArcCalculator(private val natalChart: List<PlanetPosition>) {

    // 設定有效相位角度範圍
    private val aspectOrb = 1.0  // 允許誤差 ±1°

    // 主要相位角度
    private val majorAspects = listOf(0.0, 90.0, 180.0, 120.0, 60.0)

    // 計算推運盤（所有行星前進 "年齡" 度數）
    private fun calculateSolarArc(age: Int): List<PlanetPosition> {
        return natalChart.map { planet ->
            val newDegree = (planet.angle + age) % 360  // 超過 360° 需取餘數
            PlanetPosition(planet.chName, newDegree)
        }
    }

    /**
     * 太陽弧推運的應用
     * 1. 重要事件的觸發
     * 當太陽弧行星與本命行星形成強烈相位（特別是合相、對分、刑），往往代表生命中某個領域會發生重大變化。
     * 太陽弧 ASC 合本命太陽 → 個人成長、自我意識提升
     * 太陽弧 MC 合本命木星 → 事業發展、機會提升
     * 太陽弧月亮合本命冥王 → 情感或家庭的深刻轉變
     * 2. 事業、財富與感情發展
     * 若太陽弧 MC（事業宮頂點）與木星、金星或天王星產生相位，通常代表事業機會增加。
     * 若太陽弧金星與本命太陽、火星或木星形成相位，可能代表戀愛、婚姻或財務成長的機會。
     * 3. 健康與人生關鍵轉折
     * 太陽弧土星、冥王星與個人行星（如月亮、火星）形成困難相位，可能帶來挑戰、壓力或重大責任。
     * 太陽弧海王星與個人行星的困難相位，可能導致迷惘、健康問題或能量低迷的時期。
     */
    // 檢查推運行星與本命行星的相位
    fun detectAspects(age: Int): List<SolarArcEvent> {
        val progressedChart = calculateSolarArc(age)
        val solarArcEvents = mutableListOf<SolarArcEvent>()
        for (progressed in progressedChart) {
            for (natal in natalChart) {
                val aspect = checkAspect(progressed.angle, natal.angle)
                if (aspect != null) {
                    val solarArcEvent =
                        SolarArcEvent(age, progressed.chName, natal.chName, aspect, "")
                    LogUtil.d("太陽弧推運 ${progressed.chName} $aspect 本命${natal.chName} @ 年齡 $age")
                    if (progressed.chName == "上升" && aspect == "合相" && natal.chName == "太陽") {
                        solarArcEvent.importantEvents = "個人成長、自我意識提升"
                    } else if (progressed.chName == "中天" && aspect == "合相" && natal.chName == "木星") {
                        solarArcEvent.importantEvents = "事業發展、機會提升"
                    } else if (progressed.chName == "月亮" && aspect == "合相" && natal.chName == "冥王星") {
                        solarArcEvent.importantEvents = "情感或家庭的深刻轉變"
                    } else if (progressed.chName == "中天" && (natal.chName == "木星" || natal.chName == "金星" || natal.chName == "天王星")) {
                        solarArcEvent.importantEvents = "事業機會增加"
                    } else if (progressed.chName == "金星" && (natal.chName == "太陽" || natal.chName == "火星" || natal.chName == "木星")) {
                        solarArcEvent.importantEvents = "代表戀愛、婚姻或財務成長的機會"
                    } else if (progressed.chName == "土星" && (aspect == "四分相" || aspect == "對分相") && (natal.chName == "太陽" || natal.chName == "火星" || natal.chName == "月亮")) {
                        solarArcEvent.importantEvents = "可能帶來挑戰、壓力或重大責任"
                    } else if (progressed.chName == "冥王星" && (aspect == "四分相" || aspect == "對分相") && (natal.chName == "太陽" || natal.chName == "火星" || natal.chName == "月亮")) {
                        solarArcEvent.importantEvents = "可能帶來挑戰、壓力或重大責任"
                    } else if (progressed.chName == "海王星" && (aspect == "四分相" || aspect == "對分相") && (natal.chName == "太陽" || natal.chName == "火星" || natal.chName == "月亮")) {
                        solarArcEvent.importantEvents = "可能導致迷惘、健康問題或能量低迷的時期"
                    }
                    if (solarArcEvent.importantEvents.isNotEmpty()) {
                        solarArcEvents.add(solarArcEvent)
                    }
                }
            }
        }
        return solarArcEvents
    }


    // 判斷是否形成主要相位
    private fun checkAspect(deg1: Double, deg2: Double): String? {
        val angleDiff = abs(deg1 - deg2) % 360
        for (aspect in majorAspects) {
            if (abs(angleDiff - aspect) <= aspectOrb) {
                return getAspectName(aspect)
            }
        }
        return null
    }

    // 相位名稱對應
    private fun getAspectName(deg: Double): String {
        return when (deg) {
            0.0 -> "合相" //  (Conjunction)
            90.0 -> "四分相" //  (Square)
            180.0 -> "對分相" //  (Opposition)
            120.0 -> "三分相" //  (Trine)
            60.0 -> "六分相" //  (Sextile)
            else -> "未知相位"
        }
    }
}
