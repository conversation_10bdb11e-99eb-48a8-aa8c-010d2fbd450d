package com.one.astrology.ui.fragment.viewmodel.calculate

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.google.android.gms.maps.model.LatLng
import com.google.gson.GsonBuilder
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.SignPosition
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.TransitData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.EventKey
import com.one.astrology.ui.activity.SplashActivity
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import java.util.Calendar
import java.util.Date


class CalculateWorker(private val appContext: Context, workerParams: WorkerParameters) :
    Worker(appContext, workerParams) {

    private fun autoTransitCalculate(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        index: Int
    ) {
        val transitDataBox = ObjectBox.get().boxFor(
            TransitData::class.java
        )
//        transitDataBox.removeAll()
        val signList = AssetsToObjectUtil.getSignList(context)
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.Transit,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val houseMap = mutableMapOf<String, String>()

        var horoscopeB: Horoscope
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = signRecordB.birthday
        val day: Int
        when (index) {
            0 -> {
                day = 30
            }

            1 -> {
                day = 365
            }

            2 -> {
                day = 365 * 5
            }

            3 -> {
                day = 365 * 10
            }

            else -> {
                day = 365 * 30
            }
        }
        for (i in 0..day) {
            horoscopeB = EphemerisUtil.calculate(
                context,
                Chart.Transit,
                signRecordB.name,
                calendar.timeInMillis,
                LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude)
            )
            val planetBeanList =
                horoscopeB.getPlanetBeanList().sortedWith(compareBy {
                    it.id
                })
            val planetList = AssetsToObjectUtil.getPlanetList(context)
            for (planet in planetBeanList) {
                val planetTemp = planetList.find { it.id == planet.id }
                if (planetTemp != null) {
                    if (!planetTemp.isFortune) {
                        continue
                    }
                }
                val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
                val signBean: SignBean = signList[strings[0].toInt()]
                val signPosition = SignPosition(signBean)
                signPosition.houseData = EphemerisUtil.house(planet.longitude, horoscopeA.houses.cusps)
                val key = planet.chName + signPosition.houseData?.index + "宮"
                val date = Date(calendar.timeInMillis)
                val timeString: String = FormatUtils.dateToString(date, "yyyy/MM/dd")
                LogUtil.i("$key : $timeString")
                if (houseMap.containsKey(key)) {
                    continue
                }
                val transitData = TransitData()
                transitData.name = signRecordA.name
                transitData.timeString = timeString
                transitData.house = key
                transitData.createTime = System.currentTimeMillis()
                houseMap[key] = timeString
                transitDataBox.put(transitData)
            }
            calendar.add(Calendar.DATE, 1)
            val time = FormatUtils.longToString(calendar.timeInMillis, "yyyy/MM/dd HH:mm")
            LogUtil.i("time $time")
        }


        val gson = GsonBuilder().setPrettyPrinting().create()
        val json = gson.toJson(houseMap).toString()
        LogUtil.i(json)
        LiveEventBus.get<String>(EventKey.AutoTransitCalculate).postDelay(json, 0)
        showNotification()
    }


    override fun doWork(): Result {
        val gson = GsonBuilder().setPrettyPrinting().create()
        val birthDataAString = inputData.getString("birthDataAString")
        val birthDataBString = inputData.getString("birthDataBString")
        val index = inputData.getInt("index", 0)
        val birthDataA = gson.fromJson(birthDataAString, BirthData::class.java)
        val birthDataB = gson.fromJson(birthDataBString, BirthData::class.java)
        autoTransitCalculate(
            applicationContext,
            birthDataA,
            birthDataB,
            index
        )
        return Result.success()
    }

    private fun showNotification() {
        val channelLove = NotificationChannel(
            "auto",
            "Channel",
            NotificationManager.IMPORTANCE_HIGH
        )
        channelLove.description = "自動推運"
        channelLove.enableLights(true)
        channelLove.enableVibration(true)

        val notificationIntent = Intent(appContext, SplashActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        notificationIntent.action = "auto_calculation"

        val contentIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivity(
                appContext,
                0,
                notificationIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
            )
        } else {
            PendingIntent.getActivity(
                appContext,
                0,
                notificationIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        }
        // 發送通知
        val notificationManager =
            appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channelLove)
        // 設置通知的標題、文本和圖標
        val builder = NotificationCompat.Builder(appContext, "default")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("處理完成")
            .setContentText("數據處理已完成！")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(contentIntent)
            .setChannelId("auto")
            .setAutoCancel(true)

        // 設置通知的聲音、震動和燈光等效果
        builder.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
        builder.setVibrate(longArrayOf(1000, 1000, 1000, 1000, 1000))
        builder.setLights(Color.RED, 3000, 3000)

        // 發送通知
        notificationManager.notify("tag", 100, builder.build())

//        with(NotificationManagerCompat.from(appContext)) {
//            if (ActivityCompat.checkSelfPermission(appContext, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
//                return
//            }
//            notify("tag", 100, builder.build())
//        }
    }
}