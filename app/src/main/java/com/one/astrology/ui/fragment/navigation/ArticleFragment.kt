package com.one.astrology.ui.fragment.navigation

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.gms.tasks.Task
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.one.astrology.BuildConfig
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.News
import com.one.astrology.data.Topics
import com.one.astrology.data.db.AstroData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentArticleBinding
import com.one.astrology.databinding.ItemNewsHeaderBinding
import com.one.astrology.db.OtherDBHelper
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_BIRTH_DATA_A
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_BIRTH_DATA_B
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_CHART
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_DATA_BANK
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_DATA_BANK_LIST
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_TITLE
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_TOPIC
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.adapter.AstroItemAdapter
import com.one.astrology.ui.fragment.adapter.NewsItemAdapter
import com.one.astrology.ui.fragment.bottomSheet.SelectBottomSheetFragment
import com.one.astrology.ui.fragment.bottomSheet.SingleSelectBottomSheetFragment
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.core.firebase.RemoteConfigUtil
import com.one.core.util.LogUtil
import com.one.core.view.LoadingDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * ArticleFragment
 */
@AndroidEntryPoint
class ArticleFragment : BaseFragment(R.layout.fragment_article) {

    private lateinit var itemAdapter: AstroItemAdapter
    private lateinit var binding: FragmentArticleBinding


    override fun onResume() {
        super.onResume()
        requireActivity().title = getString(R.string.article)
        LogUtil.setCurrentScreen("文章頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (rootView == null) {
            binding = FragmentArticleBinding.inflate(inflater, container, false)
            rootView = binding.root
        } else {
            (rootView?.parent as? ViewGroup)?.removeView(rootView)
        }
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!hasInitializedRootView) {
            hasInitializedRootView = true
            if (BuildConfig.IS_DEV) {
                initAstroData()
            }

            fetchConfig()
        }
    }


    private fun initAstroData() {
        var astroList = OtherDBHelper.queryDetailDataList(requireContext())
        val itemNewsHeaderBinding = ItemNewsHeaderBinding.inflate(layoutInflater)
        itemNewsHeaderBinding.tvHeader.text = getString(R.string.astrolabe_database)
        itemNewsHeaderBinding.ivRenew.visibility = View.VISIBLE
        itemNewsHeaderBinding.ivRenew.setOnClickListener {
            astroList = OtherDBHelper.queryDetailDataList(requireContext())
            itemAdapter.submitList(astroList)
        }
        itemNewsHeaderBinding.ivMore.visibility = View.VISIBLE
        itemNewsHeaderBinding.ivMore.setOnClickListener {
            val bundle = Bundle()
            bundle.putSerializable(ARG_DATA_BANK_LIST, astroList)
            findNavController().navigate(R.id.dataBankListFragment, bundle)
        }
        binding.container.addView(itemNewsHeaderBinding.root)
        initRecyclerView(astroList)
    }

    private fun initRecyclerView(astroList: ArrayList<AstroData>) {
        val recyclerView = RecyclerView(requireContext())
        itemAdapter = AstroItemAdapter()
        itemAdapter.submitList(astroList)
        val layoutManager = LinearLayoutManager(requireContext())

        layoutManager.orientation = RecyclerView.HORIZONTAL
        recyclerView.layoutManager = layoutManager

        itemAdapter.addOnItemChildClickListener(R.id.tvMore) { adapter, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.tvMore) {
                val birthData = BirthData()
                if (item.time == null) {
                    return@addOnItemChildClickListener
                }

                birthData.name = item.name.toString()
                birthData.birthday = item.timeInMillis!!
                birthData.birthplaceArea = item.place
                if (item.place == null) {
                    return@addOnItemChildClickListener
                }
                var places = item.place!!.split(',')
                if (places.isEmpty()) {
                    places = item.place!!.split('，')
                }
                lifecycleScope.launch {
                    val latLng = LocationUtil.addressToLatLng(  requireContext(), places[0])
                    birthData.birthplaceLatitude = latLng.latitude
                    birthData.birthplaceLongitude = latLng.longitude
                    toBirth(birthData)
                }
            }
        }

        itemAdapter.addOnItemChildClickListener(R.id.ivAdd) { _, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.ivAdd) {
                val birthData = BirthData()
                if (item.time == null) {
                    return@addOnItemChildClickListener
                }
                birthData.name = item.name.toString()
                birthData.birthday = item.timeInMillis!!
                birthData.birthplaceLatitude = item.latitude!!.toDouble()
                birthData.birthplaceLongitude = item.longitude!!.toDouble()
                addData(birthData)
            }
        }

        itemAdapter.addOnItemChildClickListener(R.id.tvSource) { adapter, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.tvSource) {
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(item.wikipedia)
                startActivity(intent)
            }
        }

        itemAdapter.setOnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, position: Int ->
            val item = itemAdapter.items[position]
            val bundle = Bundle()
            bundle.putParcelable(ARG_DATA_BANK, item)
            findNavController().navigate(R.id.dataBankFragment, bundle)
        }
        recyclerView.adapter = itemAdapter
        binding.container.addView(recyclerView)
    }

    private fun fetchConfig() {
        val loadingDialog = LoadingDialog(requireActivity())
        loadingDialog.show()
        RemoteConfigUtil.fetchConfig(requireActivity()) { task: Task<Void?>, firebaseRemoteConfig: FirebaseRemoteConfig ->
            if (task.isSuccessful) {
                var json = firebaseRemoteConfig.getString("news")
                if (BuildConfig.IS_DEV) {
                    json = firebaseRemoteConfig.getString("news_dev")
                }
                if (json.isEmpty()) {
                    return@fetchConfig
                }
                var topicString = firebaseRemoteConfig.getString("topicV2")
                if (BuildConfig.IS_DEV) {
                    topicString = firebaseRemoteConfig.getString("topic_dev")
                }
                val news = AssetsToObjectUtil.getNewsList(json)
                if (topicString.isEmpty()) {
                    return@fetchConfig
                }
                val topics = AssetsToObjectUtil.getTopicList(topicString)
                initRecycleView(news, topics)
            } else {
                // 無法取得 Remote Config
//                val news = AssetsToObjectUtil.getNewsList(requireContext())
//                val topics = AssetsToObjectUtil.getTopicList(requireContext())
//                initRecycleView(news, topics)
            }
            loadingDialog.dismiss()
        }
    }

    private fun initRecycleView(news: News, topics: Topics) {
        if (news.size == 0) {
            return
        }
        var title = ""
        for (item in news) {
            if (item.isVisible != true) {
                continue
            }

            for (content in item.content) {
                if (content.isVisible != true) {
                    continue
                }
                if (title != item.title) {
                    content.typeTitle = item.title
                    title = item.title
                }
            }

            val itemNewsHeaderBinding = ItemNewsHeaderBinding.inflate(layoutInflater)
            itemNewsHeaderBinding.tvHeader.text = item.title
            binding.container.addView(itemNewsHeaderBinding.root)
            initHorizontalRecyclerView(item, topics)
        }
    }

    private fun initHorizontalRecyclerView(newsItem: News.NewsItem, topics: Topics) {
        val recyclerView = RecyclerView(requireContext())
        val itemAdapter = NewsItemAdapter()
        itemAdapter.submitList(newsItem.content)
        val layoutManager = LinearLayoutManager(requireContext())

        layoutManager.orientation = RecyclerView.HORIZONTAL
        recyclerView.layoutManager = layoutManager

        itemAdapter.addOnItemChildClickListener(R.id.tvMore) { adapter, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.tvMore) {
                if (item.birthdayData != null) {
                    toBirth(item.birthdayData)
                    return@addOnItemChildClickListener
                }
                if (item.url?.isEmpty() == true) {
                    toAnalyzeFragment(item, topics)
                    return@addOnItemChildClickListener
                }
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(item.url)
                startActivity(intent)
            }
        }

        itemAdapter.addOnItemChildClickListener(R.id.ivAdd) { adapter, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.ivAdd) {
                if (item.birthdayData != null) {
                    addData(item.birthdayData)
                }
            }
        }
        itemAdapter.setOnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, position: Int ->
//            val item = itemAdapter.items[position]
//            toAnalyzeFragment(item, topics)
        }
        recyclerView.adapter = itemAdapter
        binding.container.addView(recyclerView)
    }

    private fun addData(birthData: BirthData) {
        val dialog = MaterialDialog(requireContext(), MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.message(null, getString(R.string.add_to_this_record), null)
        dialog.positiveButton(null, getString(R.string.yes)) {
            val itemBoxSignRecord = ObjectBox.get().boxFor(BirthData::class.java)
            birthData.createTime = System.currentTimeMillis()
            itemBoxSignRecord.put(birthData)
            Toast.makeText(
                requireContext(),
                getString(R.string.added_astrolabe_profile_successfully), Toast.LENGTH_SHORT
            ).show()
        }
        dialog.negativeButton(null, getString(R.string.no)) {
            LogUtil.d()
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun toBirth(birthData: BirthData) {
        val bundle = Bundle()
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
        bundle.putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
        bundle.putBoolean(KeyDefine.IsSystem, true)
        startActivity(SignDetailActivity::class.java, bundle)
    }

    private fun toAnalyzeFragment(item: News.NewsItem.Content, topics: Topics) {
        when (item.type) {
            Chart.Natal.nameEng -> {
                val onClickListener = View.OnClickListener {
                    val topic = topics.find { topicItem ->
                        topicItem.title == item.title
                    }
                    val birthDataA = it.tag as BirthData
                    val bundle = Bundle()
                    bundle.putSerializable(ARG_TITLE, item.title)
                    bundle.putParcelable(ARG_BIRTH_DATA_A, birthDataA)
                    bundle.putSerializable(ARG_TOPIC, topic)
                    bundle.putSerializable(ARG_CHART, Chart.Natal)
                    findNavController().navigate(R.id.analyzeFragment, bundle)
                }
                SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                    requireActivity().supportFragmentManager,
                    "SelectBottomSheetFragment"
                )
            }

            Chart.Synastry.nameEng -> {
                val matchCallBack = object : SelectBottomSheetFragment.MatchCallBack {
                    override fun onClick(list: List<BirthData?>) {
                        if (list.size == 2) {
                            val birthDataA = list[0]!!
                            val birthDataB = list[1] ?: return
                            val topic = topics.find { topicItem ->
                                topicItem.title == item.title
                            }

                            val bundle = Bundle()
                            bundle.putString(ARG_TITLE, item.title)
                            bundle.putParcelable(ARG_BIRTH_DATA_A, birthDataA)
                            bundle.putParcelable(ARG_BIRTH_DATA_B, birthDataB)
                            bundle.putSerializable(ARG_TOPIC, topic)
                            bundle.putSerializable(ARG_CHART, Chart.Synastry)
                            findNavController().navigate(R.id.analyzeFragment, bundle)
                        }
                    }
                }
                SelectBottomSheetFragment.newInstance(matchCallBack).show(
                    requireActivity().supportFragmentManager,
                    "SelectBottomSheetFragment"
                )
            }
        }
    }
}