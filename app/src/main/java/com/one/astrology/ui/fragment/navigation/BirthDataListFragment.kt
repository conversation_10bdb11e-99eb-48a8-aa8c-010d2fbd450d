package com.one.astrology.ui.fragment.navigation

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.core.net.toUri
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS
import com.google.android.material.appbar.AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.SettingsPreferencesDataStore
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentBirthDataListBinding
import com.one.astrology.databinding.LayoutEmptyBinding
import com.one.astrology.event.AddSignRecordEvent
import com.one.astrology.event.EventKey
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.adapter.BirthDataItemAdapter
import com.one.astrology.ui.fragment.bottomSheet.ChartSelectFragment
import com.one.astrology.ui.fragment.bottomSheet.FilterFragment
import com.one.astrology.ui.fragment.dialog.BirthDataDialogFragment
import com.one.astrology.ui.fragment.viewmodel.FirestoreViewModel
import com.one.astrology.ui.fragment.viewmodel.SharedViewModel
import com.one.astrology.ui.fragment.viewmodel.SynastryAnalyzeViewModel
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.LogUtil
import com.one.core.view.LoadingDialog
import io.objectbox.Box


/**
 * 記錄頁
 */
class BirthDataListFragment : BaseFragment(R.layout.fragment_birth_data_list) {
    private var list: MutableList<BirthData> = ArrayList()
    private var birthDataBox: Box<BirthData>? = null
    private lateinit var birthDataItemAdapter: BirthDataItemAdapter
    private lateinit var binding: FragmentBirthDataListBinding
    private val sharedViewModel by viewModels<SharedViewModel>()
    private val fireStoreViewModel by viewModels<FirestoreViewModel>()
    private val synastryAnalyzeViewModel: SynastryAnalyzeViewModel by viewModels()

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("記錄頁", this.javaClass.simpleName)
        initData()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentBirthDataListBinding.inflate(inflater, container, false)
        return binding.root
    }

    private fun initMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_birth_data_list, menu)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_export -> {
                        val csvFile = synastryAnalyzeViewModel.exportBirthDataToCsv(
                            list,
                            "BirthData"
                        )
                        if (csvFile != null) {
                            synastryAnalyzeViewModel.uploadFileToFirebase(
                                requireActivity(),
                                csvFile.toUri()
                            )
                        }
                        true
                    }

                    R.id.action_import -> {
                        true
                    }

                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        if (BuildConfig.IS_DEV) {
            initMenu()
        }
        initView(view)
        requireActivity().title = getString(R.string.record)
        LiveEventBus.get(EventKey.AddUserBirthData, AddSignRecordEvent::class.java)
            .observeStickyForever {
                if (it != null) {
                    initData()
                }
            }
        LiveEventBus.get(EventKey.UpdateUserBirthData, String::class.java).observeStickyForever {
            initData()
        }
        LiveEventBus.get(EventKey.FilterSort, Int::class.java).observeStickyForever {
            initFilterSort(it)
        }
        sharedViewModel.data.observe(viewLifecycleOwner) {
            initData()
        }
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(charSequence: CharSequence?, p1: Int, p2: Int, p3: Int) {
                filterList(charSequence.toString())
            }

            override fun afterTextChanged(p0: Editable?) {

            }
        })
        binding.ivFilter.setOnClickListener {
            showFilter()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initFilterSort(it: Int) {
        birthDataItemAdapter.items = ArrayList()
        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        val birthDataList: MutableList<BirthData>
        when (it) {
            R.id.rbTimeAsc -> {
                birthDataList = birthDataBox.query().order(BirthData_.id).build().find()
            }

            R.id.rbTimeDesc -> {
                birthDataList = birthDataBox.query().orderDesc(BirthData_.id).build().find()
            }

            R.id.rbBirthdayAsc -> {
                birthDataList = birthDataBox.query().order(BirthData_.birthday).build().find()
            }

            R.id.rbBirthdayDesc -> {
                birthDataList =
                    birthDataBox.query().orderDesc(BirthData_.birthday).build().find()
            }

            R.id.rbNameAsc -> {
                birthDataList = birthDataBox.query().order(BirthData_.name).build().find()
            }

            R.id.rbNameDesc -> {
                birthDataList = birthDataBox.query().orderDesc(BirthData_.name).build().find()
            }

            else -> {
                birthDataList = birthDataBox.query().order(BirthData_.id).build().find()
            }
        }
        birthDataItemAdapter.items = birthDataList
        birthDataItemAdapter.notifyDataSetChanged()
        birthDataItemAdapter.isEmptyViewEnable = true
    }

    private fun showFilter() {
        FilterFragment().show(requireActivity().supportFragmentManager, "")
    }

    fun filterList(text: String) {
        val arrayList = list.filter {
            it.name.lowercase().contains(text.lowercase()) ||
                    it.tag.lowercase().contains(text.lowercase()) ||
                    it.birthdayString?.lowercase()?.contains(text.lowercase()) ?: false ||
                    it.birthplaceArea?.lowercase()?.contains(text.lowercase()) ?: false
        }
        birthDataItemAdapter.submitList(arrayList.toMutableList())
    }

    override fun initView(view: View) {
        birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        initRecycleView()

        binding.fabAdd.setOnClickListener {
            showBirthDataFragment()
        }
    }

    private fun showBirthDataFragment() {
        val dialog = BirthDataDialogFragment()
        dialog.show(requireActivity().supportFragmentManager, "")
    }

    private fun initData() {
        launchWhenStarted {
            val loadingDialog = LoadingDialog(requireActivity())
            loadingDialog.show()
            val it = SettingsPreferencesDataStore.getFilterSort(requireContext())
            initFilterSort(it)
            loadingDialog.dismiss()
        }
    }

    private fun initRecycleView() {
        list = birthDataBox!!.query().orderDesc(BirthData_.id).build().find()
        val layoutParams = binding.toolbar.layoutParams as AppBarLayout.LayoutParams
        if (list.size == 0) {
            layoutParams.scrollFlags = 0
        } else {
            layoutParams.scrollFlags = SCROLL_FLAG_SCROLL or SCROLL_FLAG_ENTER_ALWAYS
        }
        birthDataItemAdapter = BirthDataItemAdapter {
            if (birthDataItemAdapter.getSelected().size == 2) {
                val userBirthDataA = birthDataItemAdapter.getSelected()[0]
                val userBirthDataB = birthDataItemAdapter.getSelected()[1]
                ChartSelectFragment.newInstance {
                    val chart = it.tag as Chart
                    val bundle = Bundle()
                    bundle.putParcelable(KeyDefine.UserBirthDataA, userBirthDataA)
                    bundle.putParcelable(KeyDefine.UserBirthDataB, userBirthDataB)
                    bundle.putString(KeyDefine.Chart, chart.toStorageValue())
                    startActivity(SignDetailActivity::class.java, bundle)
                }.show(
                    requireActivity().supportFragmentManager,
                    "ChartSelectFragment"
                )
            }
        }

        val layoutManager = LinearLayoutManager(activity)
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager

        birthDataItemAdapter.isEmptyViewEnable = false
        val layoutEmpty = LayoutEmptyBinding.inflate(requireActivity().layoutInflater)
        layoutEmpty.tvEmptyDesc.text = getString(R.string.there_are_no_records_yet)
        birthDataItemAdapter.emptyView = layoutEmpty.root

        birthDataItemAdapter.addOnItemChildClickListener(R.id.ivMore) { adapter, view, position ->
            if (view.id == R.id.ivMore) {
                showMenu(view)
            }
        }

        birthDataItemAdapter.setOnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, position: Int ->
            val birthData = birthDataItemAdapter.items[position]
            val bundle = Bundle()
            bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
            bundle.putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
            startActivity(SignDetailActivity::class.java, bundle)
        }
        binding.recyclerView.adapter = birthDataItemAdapter
        binding.recyclerView.setOnScrollChangeListener { _, _, _, _, oldScrollY ->
            if (oldScrollY < 0) {
                binding.fabAdd.hide()
            } else {
                binding.fabAdd.show()
            }
        }
    }

    private fun showMenu(view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.popup_menu, popup.menu)
        val birthData = view.tag as BirthData
        popup.setOnMenuItemClickListener {
            when (it.itemId) {
                R.id.option_share -> {
                    shareData(birthData)
                }

                R.id.option_edit -> {
                    editData(birthData)
                }

                R.id.option_remove -> {
                    removeData(birthData)
                }
            }
            true
        }
        popup.setOnDismissListener {
            // Respond to popup being dismissed.
        }
        popup.show()
    }

    private fun shareData(birthData: BirthData) {
        fireStoreViewModel.uploadData(birthData)
    }

    private fun editData(birthData: BirthData) {
        if (!birthData.isHide) {
            val dialog = BirthDataDialogFragment(birthData)
            dialog.show(requireActivity().supportFragmentManager, "")
        } else {
            Toast.makeText(
                requireActivity(),
                getString(R.string.information_is_hidden_and_cannot_be_edited), Toast.LENGTH_LONG
            ).show()
        }
    }

    private fun removeData(birthData: BirthData) {
        val dialog = MaterialDialog(requireContext(), MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.message(null, getString(R.string.whether_you_want_to_delete_this_record), null)
        dialog.positiveButton(null, getString(R.string.yes)) {
            birthDataBox!!.remove(birthData)
            initData()
        }
        dialog.negativeButton(null, getString(R.string.no)) {
            LogUtil.d()
            dialog.dismiss()
        }
        dialog.show()
    }
}