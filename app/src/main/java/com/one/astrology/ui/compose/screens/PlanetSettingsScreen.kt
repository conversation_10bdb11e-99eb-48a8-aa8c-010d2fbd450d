package com.one.astrology.ui.compose.screens

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.event.EventKey
import com.one.astrology.ui.viewmodel.SettingsViewModel
import com.one.core.util.LogUtil

/**
 * 行星設定頁面
 */
@Composable
fun PlanetSettingsScreen(viewModel: SettingsViewModel) {
    val context = LocalContext.current
    val planetList by viewModel.planetList.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 標題
        Text(
            text = "選擇要顯示的行星",
            color = colorResource(id = R.color.colorPrimary),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 行星列表
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp)
            ) {
                items(planetList) { planet ->
                    PlanetItem(
                        planet = planet,
                        onCheckedChange = { isChecked ->
                            val updatedList = planetList.map { item ->
                                if (item.id == planet.id) {
                                    // 修改原始對象而不是創建新對象
                                    item.isChecked = isChecked
                                    LogUtil.d("Setting planet ${item.chName} isChecked to $isChecked")
                                    item
                                } else {
                                    item
                                }
                            }
                            // 立即保存更改
                            viewModel.savePlanetList(updatedList)

                            // 發送事件通知星盤頁面重新加載數據
                            LiveEventBus.get<Boolean>(EventKey.RefreshChart).post(true)
                        }
                    )
                }
            }
        }
    }
}

/**
 * 行星項目
 */
@Composable
fun PlanetItem(
    planet: PlanetBean,
    onCheckedChange: (Boolean) -> Unit
) {
    val isChecked = planet.isChecked

    val backgroundColor by animateColorAsState(
        targetValue = if (isChecked) colorResource(id = R.color.very_light_grey) else Color.White,
        animationSpec = tween(300),
        label = "backgroundColor"
    )

    val textColor by animateColorAsState(
        targetValue = if (isChecked) colorResource(id = R.color.colorPrimary) else colorResource(id = R.color.grey),
        animationSpec = tween(300),
        label = "textColor"
    )

    val iconColor by animateColorAsState(
        targetValue = if (isChecked) colorResource(id = R.color.colorPrimary) else colorResource(id = R.color.grey),
        animationSpec = tween(300),
        label = "iconColor"
    )

    val scale by animateFloatAsState(
        targetValue = if (isChecked) 1.02f else 1.0f,
        animationSpec = tween(300),
        label = "scale"
    )

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .scale(scale),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isChecked) 2.dp else 1.dp),
        shape = RoundedCornerShape(10.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onCheckedChange(!isChecked) }
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 行星圖標
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = planet.chName,
                tint = iconColor,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 行星名稱
            Text(
                text = planet.chName,
                color = textColor,
                fontWeight = if (isChecked) FontWeight.Bold else FontWeight.Normal,
                fontSize = 16.sp,
                modifier = Modifier.weight(1f)
            )

            // 選擇框
            Checkbox(
                checked = isChecked,
                onCheckedChange = { onCheckedChange(it) },
                colors = CheckboxDefaults.colors(
                    checkedColor = colorResource(id = R.color.colorAccent),
                    uncheckedColor = colorResource(id = R.color.grey)
                ),
                modifier = Modifier.scale(1.2f)
            )
        }
    }
}
