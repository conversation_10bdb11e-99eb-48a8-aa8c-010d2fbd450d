package com.one.astrology.ui.fragment.report

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.QuickAdapterHelper
import com.google.gson.reflect.TypeToken
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.constant.AssetsPath
import com.one.astrology.data.Horoscope
import com.one.astrology.data.SignPosition
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FooterScoreBinding
import com.one.astrology.databinding.FragmentScoreBinding
import com.one.astrology.databinding.LayoutEmptyBinding
import com.one.astrology.db.BasicDBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.ExplainFragment
import com.one.astrology.ui.fragment.adapter.ScoreItemAdapter
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.JsonTool
import com.one.astrology.util.Util
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.IntentUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint


/**
 * 評分頁
 */
@AndroidEntryPoint
class ScoreFragment : BaseFragment(R.layout.fragment_score) {

    private lateinit var scoreItemAdapter: ScoreItemAdapter
    private lateinit var binding: FragmentScoreBinding
    private var title: String? = null

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("評分頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentScoreBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(view: View) {
        title = getString(Chart.Synastry.type)
        initRecycleView()
        collectData()
    }

    private fun collectData() {
        getEvent()
    }

    private fun getSynastryAspectList(
        horoscopeA: Horoscope,
        horoscopeB: Horoscope
    ): MutableList<AspectData> {
        val synastryAspectList: MutableList<AspectData> = ArrayList()
        synastryAspectList.addAll(
            getHouseScore(
                horoscopeA,
                horoscopeB
            )
        )

        synastryAspectList.addAll(
            getHouseScore(
                horoscopeB,
                horoscopeA
            )
        )

        synastryAspectList.addAll(
            EphemerisUtil.aspects(
                requireContext(),
                Chart.Synastry,
                horoscopeA.getPlanetBeanList(),
                horoscopeB.getPlanetBeanList(),
                isMatch = true,
                isGetScore = true
            )
        )
        return synastryAspectList
    }

    class Score {
        var totalScore = 0
        var positiveScore = 0
        var negativeScore = 0
    }

    private fun calculateScore(synastryAspectList: MutableList<AspectData>): Score {
        val score = Score()
        var i = 0
        val querySynastryAspectList = BasicDBHelper.querySynastryAspectList(requireContext())

        while (i < synastryAspectList.size) {
            val synastryAspect: AspectData = synastryAspectList[i]
            LogUtil.i(i.toString() + " : " + synastryAspect.planetA + synastryAspect.type + synastryAspect.planetB + " = " + synastryAspect.score)
            if (synastryAspect.score == 0) {
                val synastryAspectData = query(synastryAspect, querySynastryAspectList)
                if (synastryAspectData != null) {
                    synastryAspect.score = synastryAspectData.score
                    if (synastryAspectData.score == 0) {
                        i++
                        continue
                    }
                } else {
                    i++
                    continue
                }
            }
            score.totalScore += synastryAspect.score
            if (synastryAspect.score > 0) {
                score.positiveScore += synastryAspect.score
            } else {
                score.negativeScore += synastryAspect.score
            }
            LogUtil.i(
                i.toString() + " : " + synastryAspect.planetA + synastryAspect.type +
                        synastryAspect.planetB + " " + synastryAspect.score
            )
            i++
        }

        return score
    }

    private fun initData(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        launchWhenStarted {
            val synastryAspectList: MutableList<AspectData> =
                getSynastryAspectList(horoscopeA, horoscopeB)
            val score = calculateScore(synastryAspectList)

            val list = synastryAspectList.filter { synastryAspect ->
                synastryAspect.score != 0
            }
            scoreItemAdapter.submitList(sort(list as ArrayList<AspectData>))

            val inflater = LayoutInflater.from(requireContext())
            val bindingFooter = FooterScoreBinding.inflate(inflater, null, false)
            bindingFooter.tvTotalScore.text = getString(R.string.total_score, score.totalScore)
            bindingFooter.tvPositiveScore.text =
                getString(R.string.positive_score, score.positiveScore)
            bindingFooter.tvNegativeScore.text =
                getString(R.string.negative_score, score.negativeScore)

            val helper = QuickAdapterHelper.Builder(scoreItemAdapter).build()
            val footerAdapter = FooterAdapter()
            footerAdapter.submitList(arrayListOf(score))
            helper.addAfterAdapter(footerAdapter)
            binding.recyclerViewAspect.adapter = helper.adapter
            LogUtil.i("${score.totalScore} ${score.positiveScore} ${score.negativeScore}")
        }
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
            title = getString(matchEvent.chartType.type)
            when (matchEvent.chartType) {
                Chart.Synastry -> {
                    initData(
                        matchEvent.horoscopeA,
                        matchEvent.horoscopeB
                    )
                }

                else -> {
                }
            }
        }
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    /**
     * 太陽落入7宮 4
     * 太陽落入1宮 3
     * 月亮落入7宮 3
     * 月亮落入1宮 3
     * 金星落入7宮 3
     * 金星落入1宮 2
     * 木星落入7宮 2
     * 上升落入7宮 3
     */
    private fun getHouseScore(
        horoscopeA: Horoscope, horoscopeB: Horoscope
    ): List<AspectData> {
        val synastryAspectList: MutableList<AspectData> = ArrayList()
        val signList: List<SignBean>? = JsonTool.parseJson(
            Util.loadStringFromAssets(
                requireActivity(), AssetsPath.SIGN
            ), object : TypeToken<List<SignBean?>?>() {}.type
        )
        for (planet in horoscopeA.getPlanetBeanList()) {
            if (!planet.isChecked) {
                continue
            }
            val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
            if (strings.size == 0) {
                LogUtil.e(planet.chName + " " + planet.longitude)
                continue
            }
            if (signList == null) {
                continue
            }
            val signIndex = strings[0].toInt()
            if (signIndex >= signList.size || signIndex < 0) {
                LogUtil.e("signList.size(${signList.size}) <= signIndex($signIndex)")
                continue
            }
            val signBean: SignBean = signList[signIndex]
            val signPosition = SignPosition(signBean)
            signPosition.houseData = EphemerisUtil.house(planet.longitude, horoscopeB.houses.cusps)
            val index = signPosition.houseData!!.index
            LogUtil.i(planet.chName + "-->" + signBean.chName + strings[1] + "°" + strings[2] + index + "宮\n")

            if (planet.chName == "太陽" && index == 7) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "太陽"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "7宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 4
                synastryAspectList.add(synastryAspect)
            } else if (planet.chName == "太陽" && index == 1) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "太陽"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "1宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 3
                synastryAspectList.add(synastryAspect)
            } else if (planet.chName == "月亮" && index == 7) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "月亮"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "7宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 3
                synastryAspectList.add(synastryAspect)
            } else if (planet.chName == "月亮" && index == 1) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "月亮"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "1宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 3
                synastryAspectList.add(synastryAspect)
            } else if (planet.chName == "金星" && index == 7) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "金星"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "7宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 3
                synastryAspectList.add(synastryAspect)
            } else if (planet.chName == "金星" && index == 1) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "金星"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "1宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 2
                synastryAspectList.add(synastryAspect)
            } else if (planet.chName == "木星" && index == 7) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "木星"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "7宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 2
                synastryAspectList.add(synastryAspect)
            } else if (planet.chName == "上升" && index == 7) {
                val synastryAspect =
                    AspectData()
                synastryAspect.planetA = "上升"
                synastryAspect.nameA = horoscopeA.name
                synastryAspect.type = "落入"
                synastryAspect.planetB = "7宮"
                synastryAspect.nameB = horoscopeB.name
                synastryAspect.score = 3
                synastryAspectList.add(synastryAspect)
            }
        }
        return synastryAspectList
    }

    private fun sort(tempFileInfoList: ArrayList<AspectData>): ArrayList<AspectData> {
        tempFileInfoList.sortWith { m1, m2 ->
            val result: Int = m2.score - m1.score
            result
        }
        return tempFileInfoList
    }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewAspect.layoutManager = layoutManager
        scoreItemAdapter = ScoreItemAdapter()
        scoreItemAdapter.isEmptyViewEnable = true
        val inflater = LayoutInflater.from(requireContext())
        val bindingEmpty = LayoutEmptyBinding.inflate(inflater, null, false)
        scoreItemAdapter.emptyView = (bindingEmpty.root)
        binding.recyclerViewAspect.adapter = scoreItemAdapter
        scoreItemAdapter.setOnItemClickListener { _, view, _ -> // 相位說明
            val aspect: AspectData = view.tag as AspectData
            synastryAspectDescription(aspect)
        }
    }

    private fun synastryAspectDescription(aspect: AspectData) {
        val query = title + " " + aspect.planetA + aspect.type + aspect.planetB
        if (BuildConfig.IS_DEV) {
            val synastryAspect = query(aspect)
//            if (aspect.degree == 60 && synastryAspect == null) {
//                aspect.degree = 120
//                synastryAspect = query(aspect)
//            }
            if (synastryAspect != null) {
                if (synastryAspect.descList.isNotEmpty()) {
                    ExplainFragment.newInstance(query, synastryAspect.descList)
                        .show(requireActivity().supportFragmentManager, "")
                } else {
                    IntentUtil.searchWeb(requireActivity(), query)
                }
            } else {
                IntentUtil.searchWeb(requireActivity(), query)
            }
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun query(aspect: AspectData): AspectData? {
        val synastryAspectData = BasicDBHelper.querySynastryAspect(
            requireContext(),
            aspect.planetA,
            aspect.degree.toString(),
            aspect.planetB,
        )
        if (synastryAspectData != null) {
            return synastryAspectData
        }
        return null
    }

    fun query(aspect: AspectData, aspectDataList: ArrayList<AspectData>): AspectData? {
        val item = aspectDataList.find {
            (it.planetA == aspect.planetA && it.type == aspect.type && it.planetB == aspect.planetB) ||
                    (it.planetA == aspect.planetB && it.type == aspect.type && it.planetB == aspect.planetA)
        }
        if (item != null) {
            return item
        }
        return null
    }

    inner class FooterAdapter : BaseQuickAdapter<Score, FooterAdapter.VH>() {

        inner class VH(
            parent: ViewGroup,
            val binding: FooterScoreBinding = FooterScoreBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            ),
        ) : RecyclerView.ViewHolder(binding.root)

        override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
            return VH(parent)
        }

        override fun onBindViewHolder(holder: VH, position: Int, item: Score?) {
            if (item != null) {
                holder.binding.tvTotalScore.text = getString(R.string.total_score, item.totalScore)
                holder.binding.tvPositiveScore.text =
                    getString(R.string.positive_score, item.positiveScore)
                holder.binding.tvNegativeScore.text =
                    getString(R.string.negative_score, item.negativeScore)
            }
        }
    }
}

