package com.one.astrology.ui.fragment.dialog

import android.app.AlertDialog
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.databinding.DialogFragmentSelectionBinding
import com.one.astrology.ui.fragment.adapter.MultiAdapter
import com.one.core.util.LogUtil

class SelectionDialogFragment(private val mOnClickListener: MatchCallBack) :
    DialogFragment(R.layout.dialog_fragment_selection) {

    private var multiAdapter: MultiAdapter = MultiAdapter()
    private lateinit var binding: DialogFragmentSelectionBinding

    interface MatchCallBack {
        fun onClick(list: List<BirthData?>)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DialogFragmentSelectionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("SelectionDialogFragment", this.javaClass.simpleName)
    }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(requireActivity())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        val signRecordBox = ObjectBox.get().boxFor(
            BirthData::class.java
        )
        val signRecordList = signRecordBox.query().orderDesc(BirthData_.id).build().find()
        multiAdapter.isEmptyViewEnable = true
        multiAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        multiAdapter.submitList(signRecordList)
        binding.recyclerView.adapter = multiAdapter
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(requireActivity())
        val inflater = requireActivity().layoutInflater
        binding = DialogFragmentSelectionBinding.inflate(inflater, null, false)

        initRecycleView()

        binding.btOK.setOnClickListener {
            val list = multiAdapter.getSelected()
            if (list.size == 2) {
                mOnClickListener.onClick(list)
            }
            dismiss()
        }

        builder.setView(binding.root)
        val dialog = builder.create()
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        return dialog
    }
}