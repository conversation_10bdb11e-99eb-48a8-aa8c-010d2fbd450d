package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.R
import com.one.astrology.callback.TranslateCallback
import com.one.astrology.data.db.AstroData
import com.one.astrology.databinding.ItemAstroBinding
import com.one.astrology.util.TranslateUtil
import com.one.core.util.LogUtil

class AstroItemVAdapter :
    BaseQuickAdapter<AstroData, AstroItemVAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemAstroBinding = ItemAstroBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: BaseViewHolder,
        position: Int,
        item: AstroData?
    ) {
        if (item != null) {
            holder.binding.tvTitle.text = position.toString() + ". " + item.name
            if (!item.biographyCn.isNullOrEmpty()) {
                holder.binding.progressBar.visibility = View.GONE
                holder.binding.tvContent.text = item.biographyCn
            } else {
                translate(item, holder.binding.tvContent, holder.binding.progressBar)
            }

            if (!item.wikipedia.isNullOrEmpty()) {
                holder.binding.tvSource.visibility = View.VISIBLE
                holder.binding.tvSource.text = context.getString(R.string.wikipedia)
            } else {
                holder.binding.tvSource.visibility = View.GONE
            }
        }
        holder.binding.tvMore.text = context.getString(R.string.view_the_astrolabe)
    }

    private fun translate(data: AstroData, textView: TextView, progressBar: ProgressBar) {
        progressBar.visibility = View.VISIBLE
        TranslateUtil.translate(data.biography.toString(), object : TranslateCallback {
            override fun onSuccess(message: String) {
                data.biographyCn = message
                textView.text = message
                progressBar.visibility = View.GONE
            }

            override fun onFailure(message: String?) {
                if (message != null) {
                    LogUtil.d(message)
                }
                progressBar.visibility = View.GONE
            }
        })
    }

    private fun translate(string: String, textView: TextView, progressBar: ProgressBar) {
        progressBar.visibility = View.VISIBLE
        TranslateUtil.translate(string, object : TranslateCallback {
            override fun onSuccess(message: String) {
                textView.text = message
                progressBar.visibility = View.GONE
            }

            override fun onFailure(message: String?) {
                if (message != null) {
                    LogUtil.d(message)
                }
                textView.text = string
                progressBar.visibility = View.GONE
            }
        })
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}