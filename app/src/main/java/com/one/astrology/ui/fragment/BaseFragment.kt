package com.one.astrology.ui.fragment

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.one.core.view.LoadingDialog


abstract class BaseFragment(fragment: Int) : Fragment(fragment) {
    var hasInitializedRootView = false
    var rootView: View? = null
    private lateinit var loadingDialog: LoadingDialog
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        if (!::loadingDialog.isInitialized) {
            loadingDialog = LoadingDialog(requireContext())
        }
    }

    fun isLoading(isVisible: Boolean) {
        if (!isAdded) {
            return
        }
        if (!::loadingDialog.isInitialized) {
            loadingDialog = LoadingDialog(requireContext())
        }
        if (isVisible) {
            loadingDialog.show()
        } else {
            loadingDialog.dismiss()
        }
    }

    open fun initView(view: View) {

    }

    fun startActivity(clz: Class<*>?) {
        startActivity(Intent(requireContext(), clz))
    }

    fun startActivity(clz: Class<*>?, bundle: Bundle?) {
        val intent = Intent()
        intent.setClass(requireContext(), clz!!)
        if (bundle != null) {
            intent.putExtras(bundle)
        }
        startActivity(intent)
    }

    protected open val trackScreenView: Boolean = true

    override fun onResume() {
        super.onResume()

        if (trackScreenView) setCurrentScreen(this.javaClass.simpleName)
    }

    private var firebaseAnalytics: FirebaseAnalytics = Firebase.analytics

    private fun setCurrentScreen(screenName: String) = firebaseAnalytics.run {
        val bundle = Bundle()
        bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName)
        bundle.putString(
            FirebaseAnalytics.Param.SCREEN_CLASS,
            <EMAIL>
        )
        logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }

}