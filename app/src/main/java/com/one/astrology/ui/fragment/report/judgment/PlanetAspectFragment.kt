package com.one.astrology.ui.fragment.report.judgment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.InterpretationType
import com.one.astrology.data.PdfItemData
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentPlanetAspectBinding
import com.one.astrology.db.DBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.ExplainFragment
import com.one.astrology.ui.fragment.report.adapter.AspectDescItemAdapter
import com.one.astrology.util.EphemerisUtil

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [PlanetAspectFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class PlanetAspectFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private var param1: String? = null
    private var param2: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    private lateinit var binding: FragmentPlanetAspectBinding
    private var aspectList = ArrayList<AspectData>()
    private var matchEvent: MatchEvent? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPlanetAspectBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        collectData()
    }

    private fun collectData() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
//            clearData()
//            title = getString(matchEvent.chartType.type)
            this.matchEvent = matchEvent

//            pdfData.name = matchEvent.horoscopeA.name
//            var chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
//            var pdfTitle = getString(R.string.pdf_title, chartName)
//            binding.tvTitle.text = pdfTitle
//            pdfData.chart = matchEvent.chartType

            when (matchEvent.chartType) {
                Chart.Natal -> {
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.Transit -> {
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.SecondaryProgression -> {
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.TertiaryProgression -> {
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.Synastry -> {
//                    pdfData.name = getString(
//                        R.string.synastry_name,
//                        matchEvent.horoscopeB.name,
//                        matchEvent.horoscopeA.name
//                    )
//                    chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
//                    pdfTitle = getString(R.string.pdf_title, chartName)
//                    binding.tvTitle.text = pdfTitle
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    matchEvent.horoscopeB.aspectList =
                        EphemerisUtil.aspects(
                            requireContext(),
                            matchEvent.chartType,
                            matchEvent.horoscopeB.planetList,
                            matchEvent.horoscopeA.planetList,
                            isMatch = true,
                            isGetScore = false
                        )
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.Composite -> {
//                    pdfData.name = getString(
//                        R.string.both_name,
//                        matchEvent.horoscopeA.name,
//                        matchEvent.horoscopeB.name
//                    )
//                    chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
//                    pdfTitle = getString(R.string.pdf_title, chartName)
//                    binding.tvTitle.text = pdfTitle
//                    (binding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(3).visibility =
//                        View.GONE
//                    (binding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(4).visibility =
//                        View.GONE
//                    binding.tvFly.visibility = View.GONE
//                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                else -> {

                }
            }

//            initTab()
        }
    }

    private fun initData(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        initAspect(horoscopeA)
    }

    private fun initAspect(horoscopeA: Horoscope) {
        for (aspect in horoscopeA.aspectList) {
            queryAspect(aspect)
        }
        initRecycleViewAspect()
    }

    private fun queryAspect(aspect: AspectData) {
        val data = DBHelper.queryAspect(
            requireContext(),
            matchEvent?.chartType!!,
            aspect.planetA,
            aspect.degree,
            aspect.planetB,
        )
        val findAspect = aspectList.find {
            it.planetA == aspect.planetB && it.planetB == aspect.planetA
        }
        if (findAspect != null) {
            return
        }
        if (data != null) {
            data.type = aspect.type
            if (data.desc.isNullOrEmpty()) {
                return
            }
            aspectList.add(data)
            val pdfItemData = PdfItemData()
            pdfItemData.type = InterpretationType.aspect
            pdfItemData.subTitle = getString(
                R.string.planet_aspect_title,
                data.planetA,
                data.type,
                data.planetB
            )
            if (data.descList.isNotEmpty()) {
                pdfItemData.desc = data.descList[0]
            }
            pdfItemData.descList = data.descList
//            pdfData.pdfItemData.add(pdfItemData)
        }
    }

    private fun initRecycleViewAspect() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewAspect.layoutManager = layoutManager
        val aspectDescItemAdapter = AspectDescItemAdapter()
        aspectDescItemAdapter.isEmptyViewEnable = true
        aspectDescItemAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        binding.recyclerViewAspect.adapter = aspectDescItemAdapter
        aspectDescItemAdapter.submitList(aspectList)

        aspectDescItemAdapter.addOnItemChildClickListener(R.id.tvMore) { _, _, position ->
            val item = aspectDescItemAdapter.items[position]
            val title = getString(
                R.string.planet_aspect_title,
                item.planetA,
                item.type,
                item.planetB
            )
            toExplain(title, item.descList)
        }
    }

    private fun toExplain(title: String, descList: ArrayList<String>) {
        if (descList.size > 1) {
            ExplainFragment.newInstance(title, descList)
                .show(requireActivity().supportFragmentManager, "")
        } else {
            com.one.core.util.IntentUtil.searchWeb(requireActivity(), title)
        }
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment PlanetAspectFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            PlanetAspectFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }
}