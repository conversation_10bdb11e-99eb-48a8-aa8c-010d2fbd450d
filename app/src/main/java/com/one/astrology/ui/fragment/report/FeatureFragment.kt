package com.one.astrology.ui.fragment.report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.constant.ArabicPart.arabicParts
import com.one.astrology.data.bean.Dignities
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.ReceptionData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateViewModel
import com.one.astrology.util.ArabicPartUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint

/**
 * 特徵頁
 */
@AndroidEntryPoint
class FeatureFragment : Fragment(R.layout.fragment_feature) {

    private val viewModel by activityViewModels<CalculateViewModel>()

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("特徵頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                FeatureScreen(viewModel)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        collectData()
    }

    private fun collectData() {
        initMatch(viewModel.matchEvent)
        getEvent()
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
            when (matchEvent.chartType) {
                Chart.Celestial -> {
                    // 處理天體圖表
                }

                Chart.Natal -> {
                    val planetList = matchEvent.horoscopeA.planetList
                    val aspectList = matchEvent.horoscopeA.aspectList
                    val featureList =
                        EphemerisUtil.getReception(requireContext(), planetList, aspectList)
                    viewModel.updateFeatureList(featureList)

                    val arabicPlanetList = ArrayList<PlanetBean>()
                    arabicParts.forEach {
                        val arabic = ArabicPartUtil.calculateArabicPartAngle(
                            it.id,
                            planetList,
                            matchEvent.horoscopeA.houses
                        )
                        arabicPlanetList.add(arabic)
                    }
                    viewModel.updateArabicPlanetList(arabicPlanetList)
                }

                else -> {
                    LogUtil.d("matchEvent default")
                }
            }
        }
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observe(this) { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }
}

@Composable
fun FeatureScreen(viewModel: CalculateViewModel) {
    val featureList by viewModel.featureList.collectAsState()
    val arabicPlanetList by viewModel.arabicPlanetList.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            "互容接納",
            style = MaterialTheme.typography.titleMedium,
            color = colorResource(id = R.color.colorPrimary),
        )

        if (featureList.isEmpty()) {
            EmptyState()
        } else {
            FeatureList(featureList)
        }

        Spacer(modifier = Modifier.height(24.dp))
        Text(
            "阿拉伯點",
            style = MaterialTheme.typography.titleMedium,
            color = colorResource(id = R.color.colorPrimary),
        )

        if (arabicPlanetList.isEmpty()) {
            Text("暫無阿拉伯點資料")
        } else {
            ArabicPlanetList(arabicPlanetList)
        }
    }
}

@Composable
fun ArabicPlanetList(list: List<PlanetBean>) {
    LazyColumn {
        items(list) { item ->
            ArabicPlanetItem(item)
        }
    }
}

@Composable
fun ArabicPlanetItem(item: PlanetBean) {
    var expanded by remember { mutableStateOf(false) }

    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { expanded = !expanded },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "${item.chName} ${item.enName}",
                        style = MaterialTheme.typography.titleMedium,
                        color = colorResource(id = R.color.colorPrimary),
                    )
                    Text(
                        text = "${item.signBean.chName} ${item.signBean.degree + item.signBean.minute}°" +
                                "  ${item.signBean.houseData.index}宮 ${item.signBean.houseData.degree}°",
                        style = MaterialTheme.typography.bodySmall,
                        color = colorResource(id = R.color.colorPrimary),
                    )
                }
                Icon(
                    imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (expanded) "摺疊" else "展開"
                )
            }

            AnimatedVisibility(visible = expanded) {
                Text(
                    text = "描述：${item.description}",
                    modifier = Modifier.padding(top = 8.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = colorResource(id = R.color.colorPrimary),
                )
            }
        }
    }
}

@Composable
fun EmptyState() {
    Text(
        text = "暫無特徵數據",
        color = colorResource(id = R.color.colorPrimary),
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    )
}

@Composable
fun FeatureList(featureList: List<ReceptionData>) {
    LazyColumn {
        items(featureList) { item ->
            FeatureItem(item)
        }
    }
}

@Composable
fun FeatureItem(item: ReceptionData) {
    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            val typeA = when (item.typeA) {
                Dignities.Ruler -> "本垣"
                Dignities.Exalt -> "曜升"
                else -> ""
            }

            val typeB = when (item.typeB) {
                Dignities.Ruler -> "本垣"
                Dignities.Exalt -> "曜升"
                else -> ""
            }

            val message = when {
                item.isReception && item.hasAspect && item.isMutual ->
                    "${item.planetNameA} (${typeA}) 與 ${item.planetNameB} (${typeB}) 互容接納"

                item.isReception && item.hasAspect ->
                    "${item.planetNameA} 被 ${item.planetNameB} 接納 (${typeA} ${typeB})"

                item.isMutual ->
                    "${item.planetNameA} (${typeA}) 與 ${item.planetNameB} (${typeB}) 互容"

                else -> ""
            }

            Text(text = message, color = colorResource(id = R.color.colorPrimary))
        }
    }
}