package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.data.entity.BirthData
import com.one.astrology.databinding.ItemSelectionBinding
import com.one.core.util.FormatUtils

class MultiAdapter : BaseQuickAdapter<BirthData, MultiAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemSelectionBinding = ItemSelectionBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

    override fun onBindViewHolder(
        holder: BaseViewHolder,
        position: Int,
        item: BirthData?
    ) {
        if (item != null) {
            holder.binding.tvName.text = item.name

            if (item.isHide) {
                holder.binding.tvBirthday.text = "資訊隱藏"
            } else {
                holder.binding.tvBirthday.text =
                    FormatUtils.longToString(item.birthday, "yyyy/MM/dd HH:mm")
            }
            holder.binding.checkbox.isChecked = item.isChecked
            holder.binding.checkbox.tag = position
            holder.binding.checkbox.setOnClickListener {
                setOnClickListener(holder, position)
            }
            holder.binding.tvTag.text = item.tag
            holder.binding.root.tag = item
            holder.binding.root.setOnClickListener {
                holder.binding.checkbox.isChecked = !holder.binding.checkbox.isChecked
                setOnClickListener(holder, position)
            }
        }
    }

    private fun setOnClickListener(viewHolder: BaseViewHolder, position: Int) {
        items[position].isChecked = viewHolder.binding.checkbox.isChecked
        if (getSelected().size > 2 && viewHolder.binding.checkbox.isChecked) {
            viewHolder.binding.checkbox.isChecked = false
            items[position].isChecked = false
            Toast.makeText(context, "選擇兩個項目進行分析!", Toast.LENGTH_SHORT).show()
            return
        }
    }

    fun getSelected(): List<BirthData> {
        return items.filter { it.isChecked }
    }

}