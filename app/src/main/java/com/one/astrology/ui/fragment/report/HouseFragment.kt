package com.one.astrology.ui.fragment.report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.type.Chart
import com.one.astrology.db.DBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.fragment.ExplainFragment
import com.one.core.util.IntentUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HouseViewModel @Inject constructor() : ViewModel() {
    private val _planetList = MutableStateFlow<List<PlanetBean>>(emptyList())
    val planetList: StateFlow<List<PlanetBean>> = _planetList.asStateFlow()

    private val _signList = MutableStateFlow<List<SignBean>>(emptyList())
    val signList: StateFlow<List<SignBean>> = _signList.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private var matchEvent: MatchEvent? = null

    fun updateData(horoscope: Horoscope) {
        viewModelScope.launch {
            _isLoading.value = true
            _planetList.value = horoscope.planetList
            _signList.value = horoscope.houses.signBeanList
            _isLoading.value = false
        }
    }

    fun setMatchEvent(event: MatchEvent?) {
        matchEvent = event
    }

    fun getMatchEvent(): MatchEvent? = matchEvent
}

/**
 * 宮位頁
 */
@AndroidEntryPoint
class HouseFragment : Fragment() {

    private val viewModel: HouseViewModel by viewModels()

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("宮位頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                MaterialTheme {
                    HouseScreen(
                        viewModel = viewModel,
                        onSignClick = { position, signBean -> query(position, signBean) },
                        onPlanetClick = { position, planetBean -> query(position, planetBean) }
                    )
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        collectData()
    }

    private fun collectData() {
        getEvent()
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java).observeStickyForever {
            viewModel.setMatchEvent(it)
            when (it.chartType) {
                Chart.Natal -> {
                    initData(it.horoscopeA)
                }

                else -> {
                    initData(it.horoscopeA)
                }
            }
        }
    }

    private fun initData(horoscope: Horoscope) {
        viewModel.updateData(horoscope)
    }

    fun query(position: Int, signBean: SignBean) {
        val house = requireActivity().getString(R.string.house, position + 1)
        val query = house + signBean.chName
        if (BuildConfig.IS_DEV) {
            when (viewModel.getMatchEvent()?.chartType) {
                Chart.Natal -> {
                    val data = DBHelper.queryHouseSign(
                        requireContext(),
                        Chart.Natal,
                        position + 1,
                        signBean.chName
                    )
                    if (data != null) {
                        showData(query, data.descList)
                    } else {
                        IntentUtil.searchWeb(requireActivity(), query)
                    }
                }

                else -> {
                    IntentUtil.searchWeb(requireActivity(), query)
                }
            }
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    fun query(position: Int, planetBean: PlanetBean) {
        val query = requireActivity().getString(
            R.string.house_fly,
            position + 1,
            planetBean.signBean.houseData.index
        )
        if (BuildConfig.IS_DEV) {
            val data = DBHelper.queryFly(
                requireContext(),
                viewModel.getMatchEvent()?.chartType!!,
                position + 1,
                planetBean.signBean.houseData.index
            )
            if (data != null) {
                showData(query, data.descList)
            } else {
                IntentUtil.searchWeb(requireActivity(), query)
            }
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun showData(query: String, desc: ArrayList<String>) {
        if (desc.isNotEmpty()) {
            ExplainFragment.newInstance(query, desc)
                .show(requireActivity().supportFragmentManager, "")
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }
}

@Composable
fun HouseScreen(
    viewModel: HouseViewModel,
    onSignClick: (Int, SignBean) -> Unit,
    onPlanetClick: (Int, PlanetBean) -> Unit
) {
    val planetList by viewModel.planetList.collectAsState()
    val signList by viewModel.signList.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    Box(modifier = Modifier.fillMaxSize()) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(horizontal = 10.dp, vertical = 10.dp),
                verticalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                itemsIndexed(signList) { index, sign ->
                    HouseItem(
                        index = index,
                        sign = sign,
                        planetList = planetList,
                        onSignClick = { onSignClick(index, sign) },
                        onPlanetClick = { planet -> onPlanetClick(index, planet) }
                    )
                }
            }

        }
    }
}

@Composable
fun HouseItem(
    index: Int,
    sign: SignBean,
    planetList: List<PlanetBean>,
    onSignClick: () -> Unit,
    onPlanetClick: (PlanetBean) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onSignClick)
            .padding(start = 3.dp, end = 3.dp, top = 3.dp, bottom = 3.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = AppShapes.small
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp, vertical = 5.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 宮位索引 (weight = 2)
            Text(
                text = "${index + 1}宮",
                style = MaterialTheme.typography.bodySmall,
                color = colorResource(id = R.color.colorPrimary),
                modifier = Modifier.weight(2f),
                textAlign = TextAlign.Center
            )

            // 星座信息 (weight = 3)
            Row(
                modifier = Modifier.weight(5f),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = sign.symbol,
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontFamily = FontFamily(Font(R.font.astro_one_font)),
                        fontWeight = FontWeight.Normal,
                    ),
                    color = sign.getSafeColor()
                )
                Spacer(modifier = Modifier.width(3.dp))
                Text(
                    text = sign.chName,
                    style = MaterialTheme.typography.bodySmall,
                    color = colorResource(id = R.color.colorPrimary)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "${sign.degree}${sign.minute}",
                    style = MaterialTheme.typography.bodySmall,
                    color = colorResource(id = R.color.colorPrimary)
                )
            }

            // 守護星 (weight = 2)
            Column(
                modifier = Modifier.weight(3f),
                horizontalAlignment = Alignment.Start
            ) {
                if (sign.ruler.contains("、")) {
                    val rulers = sign.ruler.split("、")
                    Column {
                        for (i in rulers.indices) {
                            val planet = planetList.find { it.chName == rulers[i] }
                            if (planet != null) {
                                Row {
                                    Text(
                                        text = planet.symbol,
                                        style = MaterialTheme.typography.bodySmall.copy(
                                            fontFamily = FontFamily(Font(R.font.astro_one_font)),
                                            fontWeight = FontWeight.Normal,
                                        ),
                                        color = planet.getSafeColor()
                                    )

                                    Spacer(modifier = Modifier.width(3.dp))

                                    Text(
                                        text = planet.chName,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = colorResource(id = R.color.colorPrimary),
                                        modifier = Modifier.clickable { onPlanetClick(planet) }
                                    )
                                }
                            }

                        }
                    }
                } else {
                    val planet = planetList.find { it.chName == sign.ruler }
                    if (planet != null) {
                        Row {
                            Text(
                                text = planet.symbol,
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontFamily = FontFamily(Font(R.font.astro_one_font)),
                                    fontWeight = FontWeight.Normal,
                                ),
                                color = planet.getSafeColor()
                            )

                            Spacer(modifier = Modifier.width(3.dp))

                            Text(
                                text = planet.chName,
                                style = MaterialTheme.typography.bodySmall,
                                color = colorResource(id = R.color.colorPrimary),
                                modifier = Modifier.clickable { onPlanetClick(planet) }
                            )
                        }
                    }
                }
            }
            val context = LocalContext.current
            // 飛入宮位 (weight = 3)
            Column(
                modifier = Modifier.weight(4f),
                horizontalAlignment = Alignment.Start
            ) {
                if (sign.ruler.contains("、")) {
                    val rulers = sign.ruler.split("、")
                    Column {
                        for (i in rulers.indices) {
                            val planet = planetList.find { it.chName == rulers[i] }
                            if (planet != null) {
                                Row {
                                    Text(
                                        text = planet.signBean.symbol,
                                        style = MaterialTheme.typography.bodySmall.copy(
                                            fontFamily = FontFamily(Font(R.font.astro_one_font)),
                                            fontWeight = FontWeight.Normal,
                                        ),
                                        color = planet.signBean.getSafeColor()
                                    )
                                    Spacer(modifier = Modifier.width(3.dp))
                                    Text(
                                        text = context.getString(
                                            R.string.name_house_index,
                                            planet.signBean.chName,
                                            planet.signBean.houseData.index
                                        ),
                                        style = MaterialTheme.typography.bodySmall,
                                        color = colorResource(id = R.color.colorPrimary),
                                        modifier = Modifier.clickable { onPlanetClick(planet) }
                                    )
                                }
                            }
                        }
                    }
                } else {
                    val planet = planetList.find { it.chName == sign.ruler }
                    if (planet != null) {
                        Row {
                            Text(
                                text = planet.signBean.symbol,
                                style = MaterialTheme.typography.bodySmall.copy(
                                    fontFamily = FontFamily(Font(R.font.astro_one_font)),
                                    fontWeight = FontWeight.Normal,
                                ),
                                color = planet.signBean.getSafeColor()
                            )
                            Spacer(modifier = Modifier.width(3.dp))
                            Text(
                                text = context.getString(
                                    R.string.name_house_index,
                                    planet.signBean.chName,
                                    planet.signBean.houseData.index
                                ),
                                style = MaterialTheme.typography.bodySmall,
                                color = colorResource(id = R.color.colorPrimary),
                                modifier = Modifier.clickable { onPlanetClick(planet) }
                            )
                        }
                    }
                }
            }
        }
    }
}