package com.one.astrology.ui.activity

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.one.astrology.data.model.Announcement
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.compose.screens.AnnouncementDetailScreen
import com.one.astrology.viewmodel.AnnouncementViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AnnouncementDetailActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val announcement = intent.getParcelableExtra<Announcement>("announcement")
        
        setContent {
            AppTheme {
                AnnouncementDetailContent(
                    announcement = announcement,
                    onBackClick = { finish() },
                    onLinkClick = { link ->
                        startActivity(Intent(Intent.ACTION_VIEW, link.toUri()))
                    }
                )
            }
        }
    }
}

@Composable
private fun AnnouncementDetailContent(
    announcement: Announcement?,
    onBackClick: () -> Unit,
    onLinkClick: (String) -> Unit,
    viewModel: AnnouncementViewModel = viewModel()
) {
    announcement?.let {
        AnnouncementDetailScreen(
            announcement = it,
            onBackClick = onBackClick,
            onLinkClick = onLinkClick
        )
    }
} 