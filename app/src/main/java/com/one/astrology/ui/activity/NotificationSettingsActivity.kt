package com.one.astrology.ui.activity

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Vibration
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.one.astrology.R
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.fragment.navigation.SettingViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 推播設定頁面
 */
@AndroidEntryPoint
class NotificationSettingsActivity : AppCompatActivity() {

    private val viewModel: SettingViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            AppTheme {
                NotificationSettingsScreen(
                    viewModel = viewModel,
                    onBackPressed = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsScreen(
    viewModel: SettingViewModel,
    onBackPressed: () -> Unit
) {
    val context = LocalContext.current
    val notificationSettings by viewModel.notificationSettings.collectAsState()
    var showTimePickerDialog by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        "推播設定",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = colorResource(id = R.color.colorPrimary)
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 基本設定
            item {
                NotificationSettingsCard(
                    title = "基本設定",
                    content = {
                        // 啟用推播
                        SettingsSwitchItem(
                            icon = Icons.Default.Notifications,
                            title = "每日行運推播",
                            subtitle = "在設定時間推播重要行運事件",
                            isChecked = notificationSettings.isDailyTransitEnabled,
                            onCheckedChange = { viewModel.updateNotificationEnabled(context, it) }
                        )

                        Divider(
                            color = Color.LightGray.copy(alpha = 0.5f),
                            thickness = 0.5.dp,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )

                        // 推播時間設定
                        SettingsClickItem(
                            icon = Icons.Default.Schedule,
                            title = "推播時間",
                            subtitle = notificationSettings.getNotificationTimeString(),
                            enabled = notificationSettings.isDailyTransitEnabled,
                            onClick = { showTimePickerDialog = true }
                        )
                    }
                )
            }

            // 事件類型設定
//            item {
//                NotificationSettingsCard(
//                    title = "事件類型",
//                    content = {
//                        // 相位事件
//                        SettingsSwitchItem(
//                            icon = Icons.Default.Star,
//                            title = "相位事件",
//                            subtitle = "行運行星與本命行星的相位關係",
//                            isChecked = notificationSettings.isAspectEventsEnabled,
//                            enabled = notificationSettings.isDailyTransitEnabled,
//                            onCheckedChange = { viewModel.updateEventTypeSettings(context, aspectEvents = it) }
//                        )
//
//                        Divider(
//                            color = Color.LightGray.copy(alpha = 0.5f),
//                            thickness = 0.5.dp,
//                            modifier = Modifier.padding(horizontal = 16.dp)
//                        )
//
//                        // 宮位變化
//                        SettingsSwitchItem(
//                            icon = Icons.Default.Home,
//                            title = "宮位變化",
//                            subtitle = "行運行星進入不同宮位",
//                            isChecked = notificationSettings.isHouseChangeEnabled,
//                            enabled = notificationSettings.isDailyTransitEnabled,
//                            onCheckedChange = { viewModel.updateEventTypeSettings(context, houseChange = it) }
//                        )
//
//                        Divider(
//                            color = Color.LightGray.copy(alpha = 0.5f),
//                            thickness = 0.5.dp,
//                            modifier = Modifier.padding(horizontal = 16.dp)
//                        )
//
//                        // 星座切換
//                        SettingsSwitchItem(
//                            icon = Icons.Default.Refresh,
//                            title = "星座切換",
//                            subtitle = "行運行星從一個星座進入另一個星座",
//                            isChecked = notificationSettings.isSignChangeEnabled,
//                            enabled = notificationSettings.isDailyTransitEnabled,
//                            onCheckedChange = { viewModel.updateEventTypeSettings(context, signChange = it) }
//                        )
//
//                        Divider(
//                            color = Color.LightGray.copy(alpha = 0.5f),
//                            thickness = 0.5.dp,
//                            modifier = Modifier.padding(horizontal = 16.dp)
//                        )
//
//                        // 逆行事件
//                        SettingsSwitchItem(
//                            icon = Icons.Default.RotateLeft,
//                            title = "逆行事件",
//                            subtitle = "行星開始或結束逆行",
//                            isChecked = notificationSettings.isRetrogradeEnabled,
//                            enabled = notificationSettings.isDailyTransitEnabled,
//                            onCheckedChange = { viewModel.updateEventTypeSettings(context, retrograde = it) }
//                        )
//                    }
//                )
//            }

            // 進階設定
            item {
                NotificationSettingsCard(
                    title = "進階設定",
                    content = {
                        // 重要性等級
//                        SettingsClickItem(
//                            icon = Icons.Default.PriorityHigh,
//                            title = "最小重要性等級",
//                            subtitle = notificationSettings.minimumImportance.displayName,
//                            enabled = notificationSettings.isDailyTransitEnabled,
//                            onClick = { /* TODO: 實現重要性選擇對話框 */ }
//                        )

                        Divider(
                            color = Color.LightGray.copy(alpha = 0.5f),
                            thickness = 0.5.dp,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )

                        // 聲音設定
                        SettingsSwitchItem(
                            icon = Icons.Default.VolumeUp,
                            title = "推播聲音",
                            subtitle = "推播時播放提示音",
                            isChecked = notificationSettings.isSoundEnabled,
                            enabled = notificationSettings.isDailyTransitEnabled,
                            onCheckedChange = { 
                                val currentSettings = notificationSettings.copy(isSoundEnabled = it)
                                viewModel.updateNotificationSettings(context, currentSettings)
                            }
                        )

                        Divider(
                            color = Color.LightGray.copy(alpha = 0.5f),
                            thickness = 0.5.dp,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )

                        // 震動設定
                        SettingsSwitchItem(
                            icon = Icons.Default.Vibration,
                            title = "推播震動",
                            subtitle = "推播時震動提醒",
                            isChecked = notificationSettings.isVibrationEnabled,
                            enabled = notificationSettings.isDailyTransitEnabled,
                            onCheckedChange = { 
                                val currentSettings = notificationSettings.copy(isVibrationEnabled = it)
                                viewModel.updateNotificationSettings(context, currentSettings)
                            }
                        )
                    }
                )
            }
        }
    }

    // 時間選擇對話框
    if (showTimePickerDialog) {
        TimePickerDialog(
            currentHour = notificationSettings.notificationHour,
            currentMinute = notificationSettings.notificationMinute,
            onDismiss = { showTimePickerDialog = false },
            onTimeSelected = { hour, minute ->
                viewModel.updateNotificationTime(context, hour, minute)
                showTimePickerDialog = false
            }
        )
    }
}

@Composable
fun NotificationSettingsCard(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column {
            // 標題
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(colorResource(id = R.color.colorPrimary))
                    .padding(vertical = 12.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = title,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }

            // 內容
            content()
        }
    }
}

/**
 * 設定開關項目
 */
@Composable
fun SettingsSwitchItem(
    icon: ImageVector,
    title: String,
    subtitle: String? = null,
    isChecked: Boolean,
    enabled: Boolean = true,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { if (enabled) onCheckedChange(!isChecked) }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey),
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                color = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey),
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp
            )
            if (subtitle != null) {
                Text(
                    text = subtitle,
                    color = colorResource(id = if (enabled) R.color.grey else R.color.very_light_grey),
                    fontSize = 14.sp
                )
            }
        }

        Switch(
            checked = isChecked,
            enabled = enabled,
            onCheckedChange = { onCheckedChange(it) },
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = colorResource(id = R.color.colorAccent),
                uncheckedThumbColor = Color.White,
                uncheckedTrackColor = colorResource(id = R.color.grey)
            )
        )
    }
}

/**
 * 設定點擊項目
 */
@Composable
fun SettingsClickItem(
    icon: ImageVector,
    title: String,
    subtitle: String? = null,
    enabled: Boolean = true,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { if (enabled) onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey),
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                color = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey),
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp
            )
            if (subtitle != null) {
                Text(
                    text = subtitle,
                    color = colorResource(id = if (enabled) R.color.grey else R.color.very_light_grey),
                    fontSize = 14.sp
                )
            }
        }

        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = null,
            tint = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey)
        )
    }
}

/**
 * 時間選擇對話框
 */
@Composable
fun TimePickerDialog(
    currentHour: Int,
    currentMinute: Int,
    onDismiss: () -> Unit,
    onTimeSelected: (Int, Int) -> Unit
) {
    var hourInput by remember { mutableStateOf(currentHour.toString().padStart(2, '0')) }
    var minuteInput by remember { mutableStateOf(currentMinute.toString().padStart(2, '0')) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(20.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFFDFDFD))
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "選擇推播時間",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(id = R.color.colorPrimary)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 時間輸入框
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    OutlinedTextField(
                        value = hourInput,
                        onValueChange = { newValue ->
                            if (newValue.all { it.isDigit() } && newValue.length <= 2) {
                                val intVal = newValue.toIntOrNull() ?: 0
                                if (intVal in 0..23) hourInput = newValue
                            }
                        },
                        label = { Text("小時") },
                        modifier = Modifier.width(80.dp),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        textStyle = LocalTextStyle.current.copy(
                            textAlign = TextAlign.Center,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                    )

                    Text(" : ", fontSize = 24.sp, fontWeight = FontWeight.Bold)

                    OutlinedTextField(
                        value = minuteInput,
                        onValueChange = { newValue ->
                            if (newValue.all { it.isDigit() } && newValue.length <= 2) {
                                val intVal = newValue.toIntOrNull() ?: 0
                                if (intVal in 0..59) minuteInput = newValue
                            }
                        },
                        label = { Text("分鐘") },
                        modifier = Modifier.width(80.dp),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        textStyle = LocalTextStyle.current.copy(
                            textAlign = TextAlign.Center,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                    )
                }

                Spacer(modifier = Modifier.height(28.dp))

                // 按鈕
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE0E0E0)),
                        shape = RoundedCornerShape(50)
                    ) {
                        Text("取消", color = Color.Black)
                    }
                    Button(
                        onClick = {
                            val hour = hourInput.toIntOrNull() ?: currentHour
                            val minute = minuteInput.toIntOrNull() ?: currentMinute
                            onTimeSelected(hour, minute)
                        },
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(id = R.color.colorPrimary)),
                        shape = RoundedCornerShape(50)
                    ) {
                        Text("確定", color = Color.White)
                    }
                }
            }
        }
    }
}

