package com.one.astrology.ui.compose.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.one.astrology.R
import com.one.astrology.data.bean.PlanetBean

/**
 * 行星詳情項目組件
 * 用於顯示行星的詳細信息
 */
@Composable
fun PlanetDetailItem(planet: PlanetBean) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        // 行星名稱和星座
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = planet.chName,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = colorResource(id = R.color.colorPrimary)
            )
            
            Text(
                text = " 在 ${planet.signBean.degree} ${planet.longitude}°",
                style = MaterialTheme.typography.bodyLarge
            )
            
            if (planet.isRetrograde) {
                Text(
                    text = " 逆行",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Red,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        // 行星詳細解讀
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = getPlanetInterpretation(planet),
            style = MaterialTheme.typography.bodySmall,
            color = Color.DarkGray
        )
    }
}

/**
 * 獲取行星在星座中的解讀文字
 */
private fun getPlanetInterpretation(planet: PlanetBean): String {
    // 簡化版的行星在星座中的解讀，實際應用中可能會更複雜
    return when (planet.chName) {
        "太陽" -> "代表你的核心身份和活力。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "太陽")}。"
        "月亮" -> "代表你的情感和潛意識。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "月亮")}。"
        "水星" -> "代表你的思維和溝通方式。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "水星")}。"
        "金星" -> "代表你的愛情和價值觀。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "金星")}。"
        "火星" -> "代表你的行動力和慾望。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "火星")}。"
        "木星" -> "代表擴張和機會。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "木星")}。"
        "土星" -> "代表限制和責任。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "土星")}。"
        "天王星" -> "代表突破和革新。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "天王星")}。"
        "海王星" -> "代表靈感和幻想。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "海王星")}。"
        "冥王星" -> "代表轉化和權力。在${planet.signBean.chName}中，${getSignInfluence(planet.signBean.chName, "冥王星")}。"
        else -> "在${planet.signBean.chName}中運行。"
    }
}

/**
 * 獲取星座對行星的影響描述
 */
private fun getSignInfluence(signName: String, planetName: String): String {
    return when (signName) {
        "白羊座" -> "帶來積極主動的能量，鼓勵你勇敢前進"
        "金牛座" -> "帶來穩定和持久的能量，注重實際價值"
        "雙子座" -> "帶來靈活多變的能量，促進交流和學習"
        "巨蟹座" -> "帶來情感豐富的能量，注重家庭和安全感"
        "獅子座" -> "帶來自信和創造性的能量，鼓勵表達自我"
        "處女座" -> "帶來精確和分析的能量，注重細節和改進"
        "天秤座" -> "帶來和諧與平衡的能量，注重人際關係"
        "天蠍座" -> "帶來深沉和轉化的能量，鼓勵深度探索"
        "射手座" -> "帶來擴張和探索的能量，追求更高的真理"
        "摩羯座" -> "帶來實際和規律的能量，注重長期目標"
        "水瓶座" -> "帶來創新和獨立的能量，鼓勵突破常規"
        "雙魚座" -> "帶來靈感和同情的能量，連接精神世界"
        else -> "影響你的能量場"
    }
} 