package com.one.astrology.ui.fragment.report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.formatter.PercentFormatter
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.data.Horoscope
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateViewModel
import com.one.core.util.LogUtil
import kotlin.math.roundToInt

/**
 * 比例/元素統計
 */
class ProportionFragment : Fragment() {
    private val viewModel by activityViewModels<CalculateViewModel>()

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("元素統計頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                ProportionContent(viewModel)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        collectData()
    }

    private fun collectData() {
        initMatch(viewModel.matchEvent)
        getEvent()
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java).observe(this) {
            initMatch(it)
        }
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        matchEvent?.let {
            viewModel.updateMatchEvent(it)
        }
    }
}


@Composable
private fun ProportionContent(viewModel: CalculateViewModel) {
    val matchEvent by viewModel.matchEventFlow.collectAsState()

    DisposableEffect(Unit) {
        onDispose {
            LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java).removeObserver { }
        }
    }

    matchEvent.horoscopeA.let {
        Column {
            ProportionDataContent(it)
            ProportionDataListContent(it)
        }
    }
}

@Composable
private fun ProportionDataListContent(horoscope: Horoscope) {
    val list = horoscope.getPlanetBeanList().sortedBy { it.id }.filter { it.isChecked }
    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        StatisticPanel(
            title = "陰陽",
            categories = listOf("陽性" to Color.Red, "陰性" to Color(0xFF2448F2)),
            valueSelector = { it.signBean.yinYang },
            list = list,
            type = 1,
            modifier = Modifier.weight(1f)
        )

        StatisticPanel(
            title = "四元素",
            categories = listOf(
                "火象" to Color.Red,
                "土象" to Color(0xFFF5A623),
                "風象" to Color(0xFF03A55F),
                "水象" to Color(0xFF2448F2)
            ),
            valueSelector = { it.signBean.triplicities },
            list = list,
            type = 2,
            modifier = Modifier.weight(1f)
        )

        StatisticPanel(
            title = "三大性質",
            categories = listOf(
                "開創" to Color.Red,
                "固定" to Color(0xFFF5A623),
                "變動" to Color(0xFF2448F2)
            ),
            valueSelector = { it.signBean.quadruplicities },
            list = list,
            type = 3,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun StatisticPanel(
    title: String,
    categories: List<Pair<String, Color>>,
    valueSelector: (PlanetBean) -> String,
    list: List<PlanetBean>,
    type: Int,
    modifier: Modifier = Modifier
) {

    Column(
        modifier = modifier
            .background(Color(0xFFF9F9F9))
            .padding(8.dp)
            .clip(RoundedCornerShape(12.dp)),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            fontWeight = FontWeight.Bold,
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxHeight(),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {

            categories.forEach { (label, color) ->
                val filtered = list.filter { valueSelector(it) == label }
                val percent =
                    if (list.isNotEmpty()) {
                        ((filtered.size / list.size.toFloat()) * 100).let {
                            (it * 10).roundToInt() / 10f
                        }
                    } else 0f

                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "$label：${filtered.size} (${percent}%)",
                            color = color,
                            style = MaterialTheme.typography.bodySmall,
                        )
                    }
                }

                item {
                    val filteredItems = list.filter { valueSelector(it) == label }
                    if (filteredItems.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "無資料",
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.Gray
                            )
                        }
                    } else {
                        filteredItems.forEach {
                            ProportionItem(it, type)
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ProportionItem(planet: PlanetBean, type: Int) {
    val textColor = when (type) {
        1 -> if (planet.signBean.yinYang == "陽性") Color.Red else Color(0xFF2448F2)
        2 -> when (planet.signBean.triplicities) {
            "火象" -> Color.Red
            "土象" -> Color(0xFFF5A623)
            "風象" -> Color(0xFF03A55F)
            "水象" -> Color(0xFF2448F2)
            else -> Color.Black
        }

        3 -> when (planet.signBean.quadruplicities) {
            "開創" -> Color.Red
            "固定" -> Color(0xFFF5A623)
            "變動" -> Color(0xFF2448F2)
            else -> Color.Black
        }

        else -> Color.Black
    }

    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = planet.chName,
            color = textColor,
            style = MaterialTheme.typography.bodySmall,
        )
        Text(
            text = planet.signBean.chName,
            style = MaterialTheme.typography.bodySmall,
            color = textColor
        )
    }
}

@Composable
fun ProportionDataContent(horoscope: Horoscope) {
    val list = horoscope.getPlanetBeanList().filter { it.isChecked }
    val yinYang = list.groupingBy { it.signBean.yinYang }.eachCount()
    val elements = list.groupingBy { it.signBean.triplicities }.eachCount()
    val properties = list.groupingBy { it.signBean.quadruplicities }.eachCount()

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 10.dp)
            .height(130.dp),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        MPAndroidPieChart(yinYang, Modifier.weight(1f))
        MPAndroidPieChart(elements, Modifier.weight(1f))
        MPAndroidPieChart(properties, Modifier.weight(1f))
    }
}

@Composable
fun MPAndroidPieChart(
    data: Map<String, Int>,
    modifier: Modifier = Modifier
) {
    AndroidView(
        modifier = modifier
            .height(120.dp),
        factory = { context ->
            PieChart(context).apply {
                description.isEnabled = false
                setUsePercentValues(true)
                legend.isEnabled = false
                legend.textSize = 10f
                legend.textColor = Color.White.toArgb()
                legend.verticalAlignment = Legend.LegendVerticalAlignment.CENTER
                legend.horizontalAlignment = Legend.LegendHorizontalAlignment.RIGHT
                legend.orientation = Legend.LegendOrientation.VERTICAL
                legend.setDrawInside(false)
                setEntryLabelTextSize(10f)
                setEntryLabelColor(Color.White.toArgb())
                setHoleRadius(0f)
                setTransparentCircleRadius(0f)
            }
        },
        update = { chart ->
            val entries = data.entries.mapIndexed { index, entry ->
                val color = when (entry.key) {
                    "陽性" -> Color.Red
                    "陰性" -> Color(0xFF2448F2)
                    "火象" -> Color.Red
                    "土象" -> Color(0xFFF5A623)
                    "風象" -> Color(0xFF03A55F)
                    "水象" -> Color(0xFF2448F2)
                    "開創" -> Color.Red
                    "固定" -> Color(0xFFF5A623)
                    "變動" -> Color(0xFF2448F2)
                    else -> Color.White
                }
                PieEntry(entry.value.toFloat(), entry.key, color.toArgb())
            }

            val dataSet = PieDataSet(entries, "").apply {
                colors = entries.map { it.data as Int }.toList()
                valueTextSize = 10f
                valueFormatter = PercentFormatter(chart)
                valueTextColor = Color.White.toArgb()
            }

            chart.data = PieData(dataSet)
            chart.invalidate()
        }
    )
}
