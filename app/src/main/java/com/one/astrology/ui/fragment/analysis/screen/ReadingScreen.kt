package com.one.astrology.ui.fragment.analysis.screen

import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.cardElevation
import com.one.astrology.util.RemoteConfigManager


@Composable
fun ErrorMessage(errorMessage: String?) {
    if (!errorMessage.isNullOrEmpty()) {
        Text(
            text = errorMessage,
            color = Color.Red,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun CategoryButton(title: String, onClick: () -> Unit) {
    Card(
        shape = AppShapes.small,
        elevation = cardElevation(),
        onClick = onClick,
        modifier = Modifier
            .padding(vertical = 5.dp)
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .background(colorResource(id = R.color.colorPrimary)) // 深藍色背景
                .padding(10.dp)
        ) {
            Text(
                text = title,
                color = Color.White,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "",
                color = Color.White,
                fontSize = 14.sp,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
        }

        // 底部按鈕區域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(10.dp),
            contentAlignment = Alignment.CenterEnd
        ) {
            Text(
                text = "查看星盤",
                color = colorResource(id = R.color.colorAccent), // 橙色文字
                fontSize = 14.sp
            )
        }
    }
}

@Composable
fun LoadingContent() {
    CircularProgressIndicator()
}

fun copyToClipboard(context: Context, text: String) {
    if (!BuildConfig.IS_DEV) {
        return
    }
    val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clip = android.content.ClipData.newPlainText("label", text)
    clipboard.setPrimaryClip(clip)

    // 顯示提示訊息
//    Toast.makeText(context, "已複製: $text", Toast.LENGTH_SHORT).show()
}

@Composable
fun TimestampToLocalDateTimeText(birthday: String) {
    Text(
        text = "出生時間：$birthday", color = Color.White,
        fontSize = 14.sp
    )
}

@Composable
fun rememberRemoteConfigState(): State<Boolean> {
    val state = remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        RemoteConfigManager.fetchAndActivate().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                state.value = true
            }
        }
    }

    return state
}