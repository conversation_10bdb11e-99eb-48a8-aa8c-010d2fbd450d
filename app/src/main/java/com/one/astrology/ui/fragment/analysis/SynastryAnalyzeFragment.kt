package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.PairAnalysisResult
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent

import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.cardElevation
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_BIRTH_DATA_A
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_BIRTH_DATA_B
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_CHART
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_TITLE
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_TOPIC
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBarDouble
import com.one.astrology.ui.fragment.viewmodel.SynastryAnalyzeViewModel
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * SynastryAnalyzeFragment
 */
class SynastryAnalyzeFragment : Fragment() {

    private val synastryAnalyzeViewModel: SynastryAnalyzeViewModel by viewModels()
    private val chatViewModel by viewModels<ChatViewModel>()
    private val chartName = "比較盤"

    override fun onResume() {
        super.onResume()
        requireActivity().title = getString(R.string.analysis)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                val analysisResults by synastryAnalyzeViewModel.analysisResults.observeAsState(
                    emptyList()
                )

                AppTheme {
                    Column {
                        MainAnalyzeScreen(chatViewModel)

                        AnalysisResultsScreen(onLoadResultsClick = {
                            lifecycleScope.launch(Dispatchers.IO) {
                                withContext(Dispatchers.Main) {
                                    val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                                    val birthDataList =
                                        birthDataBox?.query()?.orderDesc(BirthData_.id)?.build()
                                            ?.find()
                                    if (birthDataList != null) {
                                        synastryAnalyzeViewModel.getAnalysisResults(
                                            requireContext(),
                                            birthDataList
                                        )
                                    }
                                }
                            }
                        }, onImportCsvClick = {
                            synastryAnalyzeViewModel.downloadLatestFileFromFirebase(requireContext())
                        }, onExportToCsvClick = {
                            synastryAnalyzeViewModel.exportToCsv(
                                requireContext(),
                                analysisResults,
                                "export"
                            )
                        })
                    }
                }
            }
        }
    }


    @Composable
    fun MainAnalyzeScreen(viewModel: ChatViewModel) {
        val context = LocalContext.current
        val matchEvent by viewModel.matchEvent.observeAsState(MatchEvent())
        val isLoading by viewModel.isLoading.observeAsState(false)
        val data by viewModel.data.observeAsState(ReadingData("", "",""))
        val errorMessage by viewModel.error.observeAsState("")

        LaunchedEffect(errorMessage) {
            if (matchEvent.birthDataA.name.isEmpty()) {
                LogUtil.d("errorMessage 更新: $errorMessage")
                viewModel.getTwoBirthData()
                if (matchEvent.birthDataA.name.isEmpty() || matchEvent.birthDataB.name.isEmpty()) {
                    return@LaunchedEffect
                }

                viewModel.calculateHoroscope(
                    context,
                    Chart.Synastry,
                    matchEvent.birthDataA,
                    matchEvent.birthDataB,
                )
                viewModel.init()
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            // 頂部標題欄
            TopBarDouble(
                matchEvent.horoscopeA.name,
                matchEvent.horoscopeA.getBirthdayString(),
                matchEvent.horoscopeB.name,
                matchEvent.horoscopeB.getBirthdayString(),
                onBirthDataSelected = { birthDataList ->
                    if (birthDataList.size >= 2) {
                        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                        val birthDataListSelected =
                            birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
                        birthDataListSelected.forEach {
                            it.isSelected = false
                            birthDataBox.put(it)
                        }
                        matchEvent.birthDataA = birthDataList[0]
                        matchEvent.birthDataA.isSelected = true
                        birthDataBox.put(matchEvent.birthDataA)
                        matchEvent.birthDataB = birthDataList[1]
                        matchEvent.birthDataB.isSelected = true
                        birthDataBox.put(matchEvent.birthDataB)
                        viewModel.calculateHoroscope(
                            context,
                            Chart.Synastry,
                            matchEvent.birthDataA,
                            matchEvent.birthDataB,
                        )
                        viewModel.init()
                    }
                },
                onSwap = {
                    // 交換 horoscopeA 和 horoscopeB
                    val tempHoroscope = matchEvent.horoscopeA
                    matchEvent.horoscopeA = matchEvent.horoscopeB
                    matchEvent.horoscopeB = tempHoroscope

                    // 交換 birthDataA 和 birthDataB
                    val tempBirthData = matchEvent.birthDataA
                    matchEvent.birthDataA = matchEvent.birthDataB
                    matchEvent.birthDataB = tempBirthData

                    // 更新 ViewModel
                    viewModel.matchEvent.value = matchEvent

                    // 確保同步資料
                    viewModel.calculateHoroscope(
                        context,
                        Chart.Synastry,
                        matchEvent.birthDataA,
                        matchEvent.birthDataB
                    )
                    viewModel.init()
                }
            )

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = listOf(
                            "${chartName}整體命運" to "${chartName}：解析雙方間的命運互動與關係動態 (繁體中文):\n",
                            "${chartName}財富互動" to "${chartName}：探索雙方金錢觀、財富管理與物質需求的相互影響 (繁體中文):\n",
                            "${chartName}感情與婚姻" to "${chartName}：剖析雙方戀愛模式、伴侶互動與婚姻走向 (繁體中文):\n",
                            "${chartName}家庭與親緣" to "${chartName}：探討雙方的家庭背景、親子關係及家族業力交織 (繁體中文):\n",
                            "${chartName}事業與志向" to "${chartName}：分析雙方職業方向、共同目標與合作機會 (繁體中文):\n",
                            "${chartName}人際與社交" to "${chartName}：解讀雙方社交互動、人際關係及合作潛力 (繁體中文):\n",
                            "${chartName}健康與體質" to "${chartName}：比較雙方身心健康、生活方式及壓力影響 (繁體中文):\n",
                            "${chartName}挑戰與契機" to "${chartName}：揭示雙方關係中的挑戰、成長機會與突破點 (繁體中文):\n"
                        )
                        CategoryList(
                            context,
                            categories,
                            chatViewModel,
                            matchEvent,
                            chart = Chart.Synastry
                        )
                    }
                }
            }
        }
    }


    @Composable
    fun AnalysisResultsScreen(
        onLoadResultsClick: () -> Unit,
        onImportCsvClick: () -> Unit,
        onExportToCsvClick: () -> Unit
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            Button(
                onClick = { onLoadResultsClick() },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp, bottom = 3.dp, start = 10.dp, end = 10.dp)
            ) {
                Text(text = "載入出生資料並分析結果")
            }
            Button(
                onClick = { onImportCsvClick() },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 3.dp, bottom = 3.dp, start = 10.dp, end = 10.dp)
            ) {
                Text(text = "匯入 CSV 檔案出生資料並分析結果")
            }
            Button(
                onClick = { onExportToCsvClick() },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 3.dp, bottom = 10.dp, start = 10.dp, end = 10.dp)
            ) {
                Text(text = "匯出 CSV 檔案分析結果")
            }
            PairAnalysisList()
        }
    }

    @Composable
    fun PairAnalysisList() {
        val analysisResults by synastryAnalyzeViewModel.analysisResults.observeAsState(emptyList())
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(8.dp)
        ) {
            items(analysisResults) { result ->
                PairAnalysisItem(result, {
                    val topics = AssetsToObjectUtil.getTopicSataSynastryList(requireContext())
                    val bundle = Bundle()
                    bundle.putSerializable(ARG_TITLE, topics[0].title) // Topics.TopicItem
                    bundle.putParcelable(ARG_BIRTH_DATA_A, result.birthDataA)
                    bundle.putParcelable(ARG_BIRTH_DATA_B, result.birthDataB)
                    bundle.putSerializable(ARG_TOPIC, topics[0])
                    bundle.putSerializable(ARG_CHART, Chart.Synastry)
                    findNavController().navigate(R.id.analyzeFragment, bundle)
                }, {
                    val topics = AssetsToObjectUtil.getTopicSataSynastryList(requireContext())
                    val bundle = Bundle()
                    bundle.putSerializable(ARG_TITLE, topics[1].title) // Topics.TopicItem
                    bundle.putParcelable(ARG_BIRTH_DATA_A, result.birthDataA)
                    bundle.putParcelable(ARG_BIRTH_DATA_B, result.birthDataB)
                    bundle.putSerializable(ARG_TOPIC, topics[1])
                    bundle.putSerializable(ARG_CHART, Chart.Synastry)
                    findNavController().navigate(R.id.analyzeFragment, bundle)
                })
            }
        }
    }

    @Composable
    fun PairAnalysisItem(
        result: PairAnalysisResult,
        onScore1Click: () -> Unit,
        onScore2Click: () -> Unit
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            shape = AppShapes.small,
            elevation = cardElevation(),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(10.dp)
            ) {
                // 標題
                Text(
                    text = "${result.birthDataA.name} vs ${result.birthDataB.name}",
                    style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold),
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(10.dp))

                // 雙方適合度分數（可點擊）
                ClickableScoreItem(
                    label = "雙方適合度分數",
                    score = result.score1.toString(),
                    icon = Icons.Default.Favorite,
                    onClick = onScore1Click
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 緣分 7 Point分數（可點擊）
                ClickableScoreItem(
                    label = "緣分 7 Point分數",
                    score = result.score2.toString(),
                    icon = Icons.Default.Star,
                    onClick = onScore2Click
                )

                Spacer(modifier = Modifier.height(10.dp))

                // 總分
                Text(
                    text = "總分: ${result.score1 + result.score2}",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.secondary
                    ),
                    modifier = Modifier.align(Alignment.End) // 右對齊
                )
            }
        }
    }

    @Composable
    fun ClickableScoreItem(label: String, score: String, icon: ImageVector, onClick: () -> Unit) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onClick() } // 點擊事件
                .background(
                    color = MaterialTheme.colorScheme.surface.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(5.dp)
                )
                .padding(2.dp), // 內部額外填充
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            Text(
                text = score,
                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold),
                color = MaterialTheme.colorScheme.secondary
            )
        }
    }
}
