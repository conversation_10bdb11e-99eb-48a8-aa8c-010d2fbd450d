package com.one.astrology.ui.fragment.horoscopes

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.Planet
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.databinding.FragmentHoroscopesBinding
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.bottomSheet.SingleSelectBottomSheetFragment
import com.one.astrology.ui.fragment.report.ChartFragment
import com.one.core.util.LogUtil
import io.objectbox.query.QueryBuilder
import java.util.Calendar


/**
 * 每日星座運勢
 */
class HoroscopesFragment : BaseFragment(R.layout.fragment_horoscopes) {

    private lateinit var me: BirthData
    private lateinit var binding: FragmentHoroscopesBinding

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("HoroscopesFragment", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHoroscopesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().title = getString(R.string.horoscope)
        me = getMe()
        initTab()

        binding.fabUser.setOnClickListener {
            val onClickListener = View.OnClickListener {
                me = it.tag as BirthData
                initTab()
            }
            SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                requireActivity().supportFragmentManager,
                "SelectBottomSheetFragment"
            )
        }
    }

    private fun initTab() {
        val adapter = RecyclerviewAdapter(this)
        binding.viewPager2.adapter = adapter
        binding.viewPager2.isUserInputEnabled = false

        TabLayoutMediator(binding.tabLayout, binding.viewPager2) { tab, position ->
            when (position) {
                0 -> tab.text = getString(R.string.yesterday)
                1 -> tab.text = getString(R.string.today)
                2 -> tab.text = getString(R.string.tomorrow)
            }
        }.attach()

        binding.tabLayout.selectTab(binding.tabLayout.getTabAt(1))
        Handler(Looper.getMainLooper()).postDelayed({
            if (!isAdded) {
                return@postDelayed
            }
            try {
                binding.viewPager2.setCurrentItem(1, false)
            } catch (e: Exception) {
                e.message?.let { LogUtil.e(it) }
            }
        }, 50)
    }

    private fun getMe(): BirthData {
        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        var me = birthDataBox!!.query().equal(
            BirthData_.tag, ("自己"),
            QueryBuilder.StringOrder.CASE_SENSITIVE
        ).orderDesc(BirthData_.id).build().findFirst()
        if (me != null) {
            return me
        } else {
            me = birthDataBox.query().order(BirthData_.id).build().findFirst()
        }
        if (me == null) {
            me = getCurrentTimeRecord()
        }
        return me
    }

    private fun getCurrentTimeRecord(): BirthData {
        val calendar = Calendar.getInstance()
        val birthData = BirthData()
        birthData.name = "今日星座"
        birthData.birthday = calendar.timeInMillis
        birthData.birthplaceLatitude = 25.047998
        birthData.birthplaceLongitude = 121.554483
        return birthData
    }

    inner class RecyclerviewAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

        override fun getItemCount(): Int {
            return 3
        }

        override fun createFragment(position: Int): Fragment {
            val sign = me.calculate(requireContext()).findPlantSign(Planet.Sun).enName
            var fragment: Fragment = ChartFragment()
            when (position) {
                0 -> {
                    fragment = DayHoroscopesFragment()
                    fragment.arguments = Bundle().apply {
                        putParcelable("me", me)
                        putString("sign", sign)
                        putString("day", "yesterday")
                    }
                }
                1 -> {
                    fragment = DayHoroscopesFragment()
                    fragment.arguments = Bundle().apply {
                        putParcelable("me", me)
                        putString("sign", sign)
                        putString("day", "today")
                    }
                }
                2 -> {
                    fragment = DayHoroscopesFragment()
                    fragment.arguments = Bundle().apply {
                        putParcelable("me", me)
                        putString("sign", sign)
                        putString("day", "tomorrow")
                    }
                }
            }
            return fragment
        }
    }
}