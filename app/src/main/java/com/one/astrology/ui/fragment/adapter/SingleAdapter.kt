package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.data.entity.BirthData
import com.one.astrology.databinding.ItemSelectionBinding
import com.one.core.util.FormatUtils

class SingleAdapter :
    BaseQuickAdapter<BirthData, SingleAdapter.BaseViewHolder>() {

    var onClickListener: View.OnClickListener? = null


    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemSelectionBinding = ItemSelectionBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int, item: BirthData?) {
        if (item != null) {
            holder.binding.checkbox.visibility = View.GONE
            holder.binding.tvName.text = item.name
            if (item.isHide) {
                holder.binding.tvBirthday.text = "資訊隱藏"
            } else {
                holder.binding.tvBirthday.text =
                    FormatUtils.longToString(item.birthday, "yyyy/MM/dd HH:mm")
            }

            holder.binding.tvTag.text = item.tag
            holder.binding.root.tag = item
            holder.binding.root.setOnClickListener {
                if (onClickListener != null) {
                    onClickListener!!.onClick(it)
                }
            }
        }
    }

}