package com.one.astrology.ui.fragment


import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebViewClient
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.QuickAdapterHelper
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.Topics
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentAnalyzeBinding
import com.one.astrology.databinding.LayoutFooterBinding
import com.one.astrology.topic.Analyze
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.fragment.adapter.AnalyzeItemAdapter
import com.one.astrology.ui.fragment.bottomSheet.SelectBottomSheetFragment
import com.one.astrology.ui.fragment.bottomSheet.SingleSelectBottomSheetFragment
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.ViewAnimation
import com.one.core.ad.FacebookAdHelp
import com.one.core.ad.GoogleAdHelp
import com.one.core.util.LogUtil


/**
 * AnalyzeFragment
 */
class AnalyzeFragment : BaseFragment(R.layout.fragment_analyze) {

    companion object {
        const val ARG_DATA_BANK = "dataBank"
        const val ARG_DATA_BANK_LIST = "dataBankList"
        const val ARG_CHART = "chart"
        const val ARG_TITLE = "title"
        const val ARG_BIRTH_DATA_A = "birthDataA"
        const val ARG_BIRTH_DATA_B = "birthDataB"
        const val ARG_TOPIC = "topic"
    }

    private var chart: Chart? = null
    private var isRotate: Boolean = false
    private lateinit var aspectItemAdapter: AnalyzeItemAdapter
    private var topicItem: Topics.TopicItem? = null
    private var topicItemContentArrayList: ArrayList<Topics.TopicItem.Content> = ArrayList()
    private var title: String? = null
    private var birthDataA: BirthData? = null
    private var birthDataB: BirthData? = null
    private lateinit var binding: FragmentAnalyzeBinding

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("分析頁", this.javaClass.simpleName)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            title = it.getString(ARG_TITLE)
            birthDataA = it.getParcelable(ARG_BIRTH_DATA_A) as BirthData?
            birthDataB = it.getParcelable(ARG_BIRTH_DATA_B) as BirthData?
            topicItem = it.getSerializable(ARG_TOPIC) as Topics.TopicItem?
            chart = it.getParcelable(ARG_CHART) as Chart?
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAnalyzeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initRecycleView()
        initData()
        initAd()

        ViewAnimation.init(binding.fabSignDetail)
        ViewAnimation.init(binding.fabUser)

        binding.fabSignDetail.setOnClickListener {
            binding.fabAdd.isExpanded = false
            toSignDetailActivity()
        }

        binding.fabUser.setOnClickListener {
            binding.fabAdd.isExpanded = false
            when (chart) {
                Chart.Natal -> {
                    val onClickListener = View.OnClickListener {
                        birthDataA = it.tag as BirthData
                        initData()
                    }
                    SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                        requireActivity().supportFragmentManager,
                        "SelectBottomSheetFragment"
                    )
                }

                Chart.Synastry -> {
                    val matchCallBack = object : SelectBottomSheetFragment.MatchCallBack {
                        override fun onClick(list: List<BirthData?>) {
                            if (list.size == 2) {
                                birthDataA = list[0]!!
                                birthDataB = list[1] ?: return
                                initData()
                            }
                        }
                    }
                    SelectBottomSheetFragment.newInstance(matchCallBack).show(
                        requireActivity().supportFragmentManager,
                        "SelectBottomSheetFragment"
                    )
                }

                else -> {
                    // do nothing
                }
            }

        }

        binding.fabAdd.setOnClickListener {
            isRotate = ViewAnimation.rotateFab(it, !isRotate)
            if (isRotate) {
                ViewAnimation.showIn(binding.fabUser)
                ViewAnimation.showIn(binding.fabSignDetail)
            } else {
                ViewAnimation.showOut(binding.fabUser)
                ViewAnimation.showOut(binding.fabSignDetail)
            }
        }
    }

    private fun initAd() {
        GoogleAdHelp.addView(
            requireContext(),
            "ca-app-pub-1800606262336792/2454137591",
            false,
            binding.bannerContainer
        )
        FacebookAdHelp.addView(
            requireContext(),
            "774558236969325_774571023634713",
            binding.bannerContainer
        )
    }

    private fun toSignDetailActivity() {
        val bundle = Bundle()
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthDataA)
        bundle.putParcelable(KeyDefine.UserBirthDataB, birthDataB)
        bundle.putParcelable(KeyDefine.Chart, chart)
        startActivity(SignDetailActivity::class.java, bundle)
    }

    private fun initData() {
        if (birthDataB == null) {
            requireActivity().title = birthDataA!!.name
        } else {
            requireActivity().title = birthDataA!!.name + " vs " + birthDataB!!.name
        }

        if (topicItem != null) {
            if (!topicItem?.header.isNullOrEmpty()) {
                binding.tvHeader.visibility = View.VISIBLE
                binding.tvHeader.text = topicItem?.header
            }
            when (chart) {
                Chart.Natal -> {
                    val horoscope = EphemerisUtil.calculate(
                        requireContext(),
                        Chart.Natal,
                        birthDataA!!.name,
                        birthDataA!!.birthday,
                        LatLng(birthDataA!!.birthplaceLatitude, birthDataA!!.birthplaceLongitude)
                    )
                    topicItemContentArrayList = Analyze.topicItem(topicItem!!, horoscope)
                }

                Chart.Synastry -> {
                    val horoscopeA = EphemerisUtil.calculate(
                        requireContext(),
                        Chart.Synastry,
                        birthDataA!!.name,
                        birthDataA!!.birthday,
                        LatLng(birthDataA!!.birthplaceLatitude, birthDataA!!.birthplaceLongitude)
                    )
                    val horoscopeB = EphemerisUtil.calculate(
                        requireContext(),
                        Chart.Synastry,
                        birthDataB!!.name,
                        birthDataB!!.birthday,
                        LatLng(birthDataB!!.birthplaceLatitude, birthDataB!!.birthplaceLongitude)
                    )
                    topicItemContentArrayList = Analyze.topicItem(
                        requireContext(),
                        Chart.Synastry,
                        topicItem!!,
                        horoscopeA,
                        horoscopeB
                    )
                }

                else -> {}
            }
        }

        binding.tvTitle.text = title

        if (topicItem?.url == null) {
            binding.webView.visibility = View.GONE
        } else {
            binding.webView.visibility = View.VISIBLE
            binding.webView.settings.javaScriptEnabled = true
            binding.webView.settings.domStorageEnabled = true  // 避免 YouTube 播放器出錯
            binding.webView.webViewClient = WebViewClient()    // 使用內嵌 WebView，而非跳轉到瀏覽器
            binding.webView.loadUrl(topicItem?.url!!)
        }
        topicItemContentArrayList.sortBy { it.id }
        topicItemContentArrayList.sortBy { !it.isChecked }
        aspectItemAdapter.submitList(topicItemContentArrayList)
        val list = topicItemContentArrayList.filter { it.isChecked }
        topicItemContentArrayList.filter { it.isChecked }
        var score = 0
        list.forEach { item ->
            if (item.score != null) {
                item.resultList?.forEach { _ ->
                    score += item.score
                }
                binding.tvScore.visibility = View.VISIBLE
            } else {
                binding.tvScore.visibility = View.GONE
            }
        }
        binding.tvScore.text = "總分 $score 分"
        aspectItemAdapter.notifyDataSetChanged()
        binding.recyclerView.scrollToPosition(0)
    }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        aspectItemAdapter = AnalyzeItemAdapter()
        aspectItemAdapter.isEmptyViewEnable = true
        val layoutEmpty =
            LayoutInflater.from(requireActivity()).inflate(R.layout.layout_empty, null, false)
        aspectItemAdapter.emptyView = layoutEmpty
        val helper = QuickAdapterHelper.Builder(aspectItemAdapter).build()
        val footerAdapter = FooterAdapter()
        footerAdapter.submitList(arrayListOf(""))
        helper.addAfterAdapter(footerAdapter)
        binding.recyclerView.adapter = helper.adapter

        binding.recyclerView.setOnScrollChangeListener { _, _, _, _, oldScrollY ->
            if (oldScrollY < 0) {
                binding.fabAdd.hide()
                isRotate = ViewAnimation.rotateFab(binding.fabAdd, false)
                ViewAnimation.showOut(binding.fabUser)
                ViewAnimation.showOut(binding.fabSignDetail)
            } else {
                binding.fabAdd.show()
            }
        }
    }

}

class FooterAdapter : BaseQuickAdapter<String, FooterAdapter.VH>() {

    class VH(
        parent: ViewGroup,
        val binding: LayoutFooterBinding = LayoutFooterBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(parent)
    }

    override fun onBindViewHolder(holder: VH, position: Int, item: String?) {

    }

}