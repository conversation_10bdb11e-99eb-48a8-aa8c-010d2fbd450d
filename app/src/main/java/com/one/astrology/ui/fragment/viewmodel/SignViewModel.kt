package com.one.astrology.ui.fragment.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SignViewModel @Inject constructor(
) : ViewModel() {

//    fun calculateSign(context: Context, signRecord: UserBirthData): MatchEvent {
//        val date = Date(signRecord.birthday)
//        val calculateSignUtil = CalculateSignUtil(
//            context,
//            signRecord.name,
//            date,
//            signRecord.latitude,
//            signRecord.longitude
//        )
//        if (calculateSignUtil.planetBeanList != null) {
//            LiveEventBus.get(EventKey.MatchEvent)
//                .postDelay(MatchEvent(calculateSignUtil, signRecord), 100)
//            return MatchEvent(calculateSignUtil, signRecord)
//        }
//        return MatchEvent()
//    }
}