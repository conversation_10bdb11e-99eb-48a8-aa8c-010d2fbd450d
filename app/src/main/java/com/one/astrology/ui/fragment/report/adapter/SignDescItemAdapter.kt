package com.one.astrology.ui.fragment.report.adapter

import android.content.Context
import android.text.method.ScrollingMovementMethod
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.db.SignDescData
import com.one.astrology.databinding.ItemSignDescBinding

class SignDescItemAdapter : BaseQuickAdapter<SignDescData, SignDescItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemSignDescBinding = ItemSignDescBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: <PERSON>ViewHolder,
        position: Int,
        item: SignDescData?
    ) {
        if (item != null) {
            holder.binding.tvTitle.text =
                context.getString(R.string.planet_sign_title, item.planet, item.sign)
            if (item.descList.isNotEmpty() && BuildConfig.FLAVOR != "uat") {
                holder.binding.tvDesc.text = item.descList[0]
                if (item.descList.size > 1) {
                    holder.binding.tvMore.text =
                        context.getString(R.string.more_to_explain)
                } else {
                    holder.binding.tvMore.text =
                        context.getString(R.string.search_for_interpretation)
                }
            } else {
                holder.binding.tvDesc.text = context.getString(R.string.no_interpretation)
            }
            holder.binding.tvDesc.movementMethod = ScrollingMovementMethod()
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}