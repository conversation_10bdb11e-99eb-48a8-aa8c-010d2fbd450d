package com.one.astrology.ui.fragment.dialog

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.SettingsPreferencesDataStore
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.event.EventKey
import com.one.astrology.ui.fragment.bottomSheet.FilterFragment

@Composable
fun UserSelectionDialog(
    isMultiSelect: Boolean,
    onUserSelected: (List<BirthData>) -> Unit,
    onDismissRequest: () -> Unit,
    parentFragmentManager: FragmentManager? = null
) {
    var searchQuery by remember { mutableStateOf("") }
    var selectedUser by remember { mutableStateOf<BirthData?>(null) }
    var selectedUsers by remember { mutableStateOf(setOf<BirthData>()) }
    var showMaxSelectionError by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(true) }

    val context = LocalContext.current
    val activity = context as? FragmentActivity
    val birthDataList = remember { mutableStateListOf<BirthData>() }

    LaunchedEffect(Unit) {
        isLoading = true
        val it = SettingsPreferencesDataStore.getFilterSort(context)
        birthDataList.addAll(initFilterSort(it))
        isLoading = false
    }

    LiveEventBus.get(EventKey.FilterSort, Int::class.java).observeStickyForever {
        isLoading = true
        birthDataList.clear()
        birthDataList.addAll(initFilterSort(it))
        isLoading = false
    }

    Dialog(onDismissRequest = onDismissRequest) {
        Surface(
            shape = RoundedCornerShape(24.dp),
            color = Color.White,
            tonalElevation = 8.dp,
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(modifier = Modifier.padding(20.dp)) {
                // Title
                Text(
                    text = if (isMultiSelect) "選擇多個用戶" else "選擇用戶",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(id = R.color.colorPrimary),
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Search bar
                SearchBarWithFilter(
                    searchQuery = searchQuery,
                    onSearchQueryChange = { searchQuery = it },
                    onFilterClick = {
                        (parentFragmentManager ?: activity?.supportFragmentManager)?.let {
                            FilterFragment().show(
                                it,
                                "FilterBottomSheet"
                            )
                        }
                    }
                )

                // Selection count indicator
                if (isMultiSelect) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "已選擇: ${selectedUsers.size}/2",
                            style = MaterialTheme.typography.bodyMedium,
                            color = colorResource(id = R.color.colorPrimary)
                        )

                        if (selectedUsers.isNotEmpty()) {
                            Button(
                                onClick = { selectedUsers = emptySet() },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color.Transparent,
                                    contentColor = colorResource(id = R.color.colorPrimary)
                                ),
                                elevation = ButtonDefaults.buttonElevation(0.dp)
                            ) {
                                Text("清除選擇")
                            }
                        }
                    }
                }

                // Error message
                AnimatedVisibility(visible = showMaxSelectionError) {
                    Text(
                        text = "最多只能選擇兩個項目 🙅‍♂️",
                        color = Color.Red,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Content
                if (isLoading) {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "載入中...",
                            color = Color.Gray,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                } else if (birthDataList.isEmpty()) {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "暫無資料 😢",
                            color = Color.Gray,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                } else {
                    val filteredList = birthDataList.filter {
                        it.name.contains(searchQuery, ignoreCase = true)
                    }

                    if (filteredList.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "找不到符合的結果 🔍",
                                color = Color.Gray,
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    } else {
                        LazyColumn(
                            modifier = Modifier
                                .weight(1f)
                                .padding(top = 4.dp)
                        ) {
                            items(filteredList) { user ->
                                UserItem(
                                    user = user,
                                    isSelected = if (isMultiSelect) selectedUsers.contains(user) else selectedUser == user,
                                    isMultiSelect = isMultiSelect,
                                    onSelect = { selected ->
                                        if (isMultiSelect) {
                                            if (selected && selectedUsers.size >= 2) {
                                                showMaxSelectionError = true
                                            } else {
                                                showMaxSelectionError = false
                                                selectedUsers =
                                                    if (selected) selectedUsers + user else selectedUsers - user
                                            }
                                        } else {
                                            selectedUser = if (selectedUser == user) null else user
                                        }
                                    }
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Cancel button
                    Button(
                        onClick = onDismissRequest,
                        modifier = Modifier
                            .weight(1f)
                            .height(52.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.LightGray,
                            contentColor = Color.DarkGray
                        ),
                        shape = RoundedCornerShape(16.dp),
                        elevation = ButtonDefaults.buttonElevation(defaultElevation = 2.dp)
                    ) {
                        Text("取消", fontSize = 16.sp, fontWeight = FontWeight.Medium)
                    }

                    // Confirm button
                    Button(
                        onClick = {
                            onUserSelected(
                                if (isMultiSelect) selectedUsers.toList() else listOfNotNull(selectedUser)
                            )
                            onDismissRequest()
                        },
                        enabled = if (isMultiSelect) selectedUsers.isNotEmpty() else selectedUser != null,
                        modifier = Modifier
                            .weight(1f)
                            .height(52.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(id = R.color.colorPrimary),
                            contentColor = Color.White,
                            disabledContainerColor = colorResource(id = R.color.colorPrimary).copy(alpha = 0.5f),
                            disabledContentColor = Color.White.copy(alpha = 0.7f)
                        ),
                        shape = RoundedCornerShape(16.dp),
                        elevation = ButtonDefaults.buttonElevation(defaultElevation = 6.dp)
                    ) {
                        Text("確定", fontSize = 16.sp, fontWeight = FontWeight.Bold)
                    }
                }
            }
        }
    }
}

@Composable
fun SearchBarWithFilter(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onFilterClick: () -> Unit
) {
    OutlinedTextField(
        value = searchQuery,
        onValueChange = onSearchQueryChange,
        placeholder = {
            Text("搜尋姓名", color = Color.Gray)
        },
        leadingIcon = {
            Icon(Icons.Default.Search, contentDescription = null)
        },
        trailingIcon = {
            Row {
                if (searchQuery.isNotEmpty()) {
                    IconButton(onClick = { onSearchQueryChange("") }) {
                        Icon(Icons.Default.Clear, contentDescription = "清除")
                    }
                }
                IconButton(onClick = onFilterClick) {
                    Icon(Icons.Default.FilterList, contentDescription = "篩選")
                }
            }
        },
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp)),
        singleLine = true,
        colors = OutlinedTextFieldDefaults.colors(
            focusedBorderColor = colorResource(id = R.color.colorPrimary),
            unfocusedBorderColor = Color.LightGray,
            cursorColor = colorResource(id = R.color.colorPrimary)
        )
    )
}

fun initFilterSort(it: Int): MutableList<BirthData> {
    val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
    val birthDataList: MutableList<BirthData>
    when (it) {
        R.id.rbTimeAsc -> {
            birthDataList = birthDataBox.query().order(BirthData_.id).build().find()
        }

        R.id.rbTimeDesc -> {
            birthDataList = birthDataBox.query().orderDesc(BirthData_.id).build().find()
        }

        R.id.rbBirthdayAsc -> {
            birthDataList = birthDataBox.query().order(BirthData_.birthday).build().find()
        }

        R.id.rbBirthdayDesc -> {
            birthDataList =
                birthDataBox.query().orderDesc(BirthData_.birthday).build().find()
        }

        R.id.rbNameAsc -> {
            birthDataList = birthDataBox.query().order(BirthData_.name).build().find()
        }

        R.id.rbNameDesc -> {
            birthDataList = birthDataBox.query().orderDesc(BirthData_.name).build().find()
        }

        else -> {
            birthDataList = birthDataBox.query().order(BirthData_.id).build().find()
        }
    }
    return birthDataList
}

@Composable
fun UserItem(
    user: BirthData,
    isSelected: Boolean,
    isMultiSelect: Boolean,
    onSelect: (Boolean) -> Unit
) {
    val haptic = LocalHapticFeedback.current
    val scale = animateFloatAsState(
        targetValue = if (isSelected) 1.03f else 1.0f,
        animationSpec = tween(durationMillis = 200),
        label = "selection scale animation"
    )

    Card(
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                colorResource(id = R.color.colorPrimaryDark)
                else colorResource(id = R.color.colorPrimary)
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 8.dp else 4.dp
        ),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp, horizontal = 4.dp)
            .scale(scale.value)
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { onSelect(!isSelected) },
                    onLongPress = {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onSelect(!isSelected)
                    }
                )
            }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // User avatar indicator
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(colorResource(id = R.color.colorAccent)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = user.name.take(1),
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            Column(
                modifier = Modifier
                    .weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = user.name,
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp,
                    color = Color.White,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = user.generateBirthdayString(),
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.8f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            if (isMultiSelect) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = { onSelect(it) },
                    colors = CheckboxDefaults.colors(
                        checkedColor = colorResource(id = R.color.colorAccent),
                        uncheckedColor = Color.White,
                        checkmarkColor = Color.White
                    ),
                    modifier = Modifier.size(24.dp)
                )
            } else {
                RadioButton(
                    selected = isSelected,
                    onClick = { onSelect(!isSelected) },
                    colors = RadioButtonDefaults.colors(
                        selectedColor = colorResource(id = R.color.colorAccent),
                        unselectedColor = Color.White
                    ),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}
