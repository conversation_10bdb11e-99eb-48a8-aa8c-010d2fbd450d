package com.one.astrology.ui.fragment.unnecessary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentMatchBinding
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.bottomSheet.SelectBottomSheetFragment
import com.one.astrology.ui.fragment.report.AspectFragment
import com.one.astrology.ui.fragment.report.ChartFragment
import com.one.astrology.ui.fragment.report.ScoreFragment
import com.one.astrology.ui.fragment.report.SignPositionFragment
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateViewModel
import com.one.core.util.LogUtil
import kotlinx.coroutines.launch
import java.util.Calendar

/**
 * 合盤 - 比較盤
 * 比較盤(Synastry)或(Compatibility)
 * 組合盤(Composite)
 */
class MatchFragment : BaseFragment(R.layout.fragment_match), MenuProvider {

    private var birthDataA: BirthData = BirthData(Calendar.getInstance().timeInMillis)
    private var birthDataB: BirthData = BirthData(Calendar.getInstance().timeInMillis)
    private val viewModel by activityViewModels<CalculateViewModel>()
    private lateinit var binding: FragmentMatchBinding

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("MatchFragment", this.javaClass.simpleName)
        val title: String = birthDataA.name + " vs " + birthDataB.name
        requireActivity().title = getString(Chart.Synastry.type) + " - " + title
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().title = getString(Chart.Synastry.type)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(this, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        // Add menu items here
        menu.clear()
        menuInflater.inflate(R.menu.match_menu, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        if (menuItem.itemId == R.id.action_swap) {
            if (birthDataA == null || birthDataB == null) {
                return true
            }
            val signRecordTemp: BirthData = birthDataA
            birthDataA = birthDataB
            birthDataB = signRecordTemp
            viewLifecycleOwner.lifecycleScope.launch {
                viewModel.doDouble(requireContext(), birthDataA, birthDataB, birthDataB, Chart.Synastry)
                val title: String = birthDataA.name + " vs " + birthDataB.name
                requireActivity().title = getString(Chart.Synastry.type) + " - " + title
            }
        }
        return true
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMatchBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(view: View) {
        binding.btChoose.setOnClickListener(View.OnClickListener {
            val box = ObjectBox.get().boxFor(BirthData::class.java)
            val list: List<BirthData> = box.query().orderDesc(BirthData_.id).build().find()
            if (list.size < 2) {
                Toast.makeText(requireContext(), "尚無紀錄可供合盤", Toast.LENGTH_LONG).show()
                return@OnClickListener
            }
            showSelectionDialogFragment()
        })
        initTab()
    }

    private fun showSelectionDialogFragment() {
        val matchCallBack = object : SelectBottomSheetFragment.MatchCallBack {
            override fun onClick(list: List<BirthData?>) {
                if (list.size == 2) {
                    birthDataA = list[0]!!
                    birthDataB = list[1]!!
                    val title: String = birthDataA.name + " vs " + birthDataB.name
                    requireActivity().title = getString(Chart.Synastry.type) + " - " + title
                    viewLifecycleOwner.lifecycleScope.launch {
                        viewModel.doDouble(
                            requireContext(),
                            birthDataA,
                            birthDataB,
                            birthDataB,
                            Chart.Synastry
                        )
                    }
                }
            }
        }
        SelectBottomSheetFragment.newInstance(
            matchCallBack,
            birthDataA,
            birthDataB
        ).show(requireActivity().supportFragmentManager, "SelectBottomSheetFragment")
    }

    private fun initTab() {
        val adapter = RecyclerviewAdapter(this)
        binding.viewPager2.adapter = adapter

        TabLayoutMediator(
            binding.tabLayout, binding.viewPager2
        ) { tab, position ->
            when (position) {
                0 -> tab.text = getString(R.string.astrology_chart) //"星盤圖表"
                1 -> tab.text = getString(R.string.astral_position) //"星體位置"
                2 -> tab.text = getString(R.string.planetary_aspects) //"行星相位"
                3 -> tab.text = getString(R.string.score) //"評分"
            }
        }.attach()
    }

    class RecyclerviewAdapter(fragment: Fragment?) : FragmentStateAdapter(fragment!!) {
        override fun getItemCount(): Int {
            return 4
        }

        override fun createFragment(position: Int): Fragment {
            // Return a NEW fragment instance in createFragment(int)
            var fragment: Fragment = SignPositionFragment()
            val bundle = Bundle()
            when (position) {
                0 -> {
                    fragment = ChartFragment()
                    fragment.arguments = bundle
                }
                1 -> {
                    fragment = SignPositionFragment()
                    fragment.arguments = bundle
                }
                2 -> {
                    fragment = AspectFragment()
                    fragment.arguments = bundle
                }
                3 -> {
                    fragment = ScoreFragment()
                    fragment.arguments = bundle
                }
            }
            return fragment
        }
    }
}