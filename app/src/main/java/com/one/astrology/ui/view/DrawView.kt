package com.one.astrology.ui.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.Typeface
import android.os.Build
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.HapticFeedbackConstants
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import com.google.android.material.snackbar.Snackbar
import com.google.gson.reflect.TypeToken
import com.one.astrology.R
import com.one.astrology.constant.AssetsPath
import com.one.astrology.data.Horoscope
import com.one.astrology.data.OPoint
import com.one.astrology.data.bean.Aspect
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.entity.PlanetYear
import com.one.astrology.data.entity.dayArray
import com.one.astrology.data.entity.nightArray
import com.one.astrology.data.type.Chart
import com.one.astrology.db.BasicDBHelper
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.JsonTool
import com.one.astrology.util.Util
import com.one.core.util.LogUtil
import kotlin.math.cos
import kotlin.math.sin


/**
 * 星盤圖表
 */
open class DrawView(context: Context?, attrs: AttributeSet?) : View(context, attrs) {
    val mPaint = Paint()
    val symbolPaint = Paint()

    val signList: MutableList<SignBean> = ArrayList()

    var horoscopeA: Horoscope = Horoscope()
    var horoscopeB: Horoscope = Horoscope()
    var horoscope: Horoscope = Horoscope()

    var deltaAngle = 0.0
    var size = 0.0F
    var radius = 0.0F
    var firdaria = 0.0F
    var mainRadius = 0.0F
    var signRadius = 0.0F
    var houseRadius = 0.0F
    var planetRadius = 0.0F
    var planetRadiusB = 0.0F
    val centerPoint = OPoint()

    val pointDataList = ArrayList<PointData>()
    var snackbar: Snackbar? = null
    var chartType: Chart? = null
    var isTouch = false

    enum class PointType {
        PLANET_A, PLANET_B, SIGN, HOUSE, NONE
    }

    data class PointData(
        var pointType: PointType = PointType.NONE,
        var data: Any? = null,
        var oPoint: OPoint,
    )


    init {
        val params = ViewGroup.LayoutParams(
            (getScreenWidth() * 1f).toInt(), (getScreenWidth() * 1f).toInt()
        )
        layoutParams = params
        mPaint.strokeWidth = 3f
        mPaint.isAntiAlias = true
        symbolPaint.strokeWidth = 3f
        symbolPaint.isAntiAlias = true
        symbolPaint.textSize = 45f
        symbolPaint.typeface = resources.getFont(R.font.astro_one_font)
        initSigns()
        initRadius()
    }

    var isShowRuler = false
    open fun init(chartType: Chart, isShowRuler: Boolean) {
        this.isShowRuler = isShowRuler
        LogUtil.d("DrawView.init: isShowRuler=$isShowRuler")
        pointDataList.clear()
        snackbar?.dismiss()
        this.chartType = chartType
    }

    private fun getScreenWidth(): Int {
        val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowMetrics = wm.currentWindowMetrics
            windowMetrics.bounds.width()
        } else {
            val displayMetrics = DisplayMetrics()
            wm.defaultDisplay.getMetrics(displayMetrics)
            displayMetrics.widthPixels
        }
    }

    private fun initSigns() {
        val type = object : TypeToken<List<SignBean?>?>() {}.type
        val signJson = Util.loadStringFromAssets(context, AssetsPath.SIGN)
        val signBeanList = JsonTool.parseJson<List<SignBean>>(signJson, type)
        signList.clear()
        if (signBeanList != null) {
            signList.addAll(signBeanList)
        }
    }

    private fun initRadius() {
        size = layoutParams.height.toFloat()
        centerPoint.y = (0.5 * size).toFloat()
        centerPoint.x = centerPoint.y
        mainRadius = size * 0.49F
        signRadius = mainRadius * 0.85F
        houseRadius = mainRadius * 0.75F
        LogUtil.d("getLayoutParams().width : $size")
        LogUtil.d("centerPoint : " + centerPoint.x + " , " + centerPoint.y)
        LogUtil.d("mainRadius : $mainRadius")
        LogUtil.d("signRadius : $signRadius")
        LogUtil.d("houseRadius : $houseRadius")
        pointDataList.clear()
    }

    open fun setData(horoscopeA: Horoscope) {
        this.horoscopeB = Horoscope()
        this.horoscopeA = horoscopeA
        deltaAngle = 180 - horoscopeA.houses.getAsc()
        planetRadius = mainRadius * 0.55F
        invalidate()
    }

    open fun setDataFirdaria(horoscope: Horoscope) {
        this.horoscope = horoscope
        invalidate()
    }

    open fun setDataPair(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        this.horoscopeA = horoscopeA
        this.horoscopeB = horoscopeB
        deltaAngle = 180 - horoscopeA.houses.getAsc()
        planetRadiusB = mainRadius * 0.55F
        planetRadius = mainRadius * 0.40F
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        mPaint.style = Paint.Style.FILL
        mPaint.color = Color.WHITE
        mPaint.style = Paint.Style.STROKE

        if (horoscopeA.getPlanetBeanList().size == 0) {
            return
        }

        drawCircle(canvas)

        // 畫星座
        drawSigns(canvas)

        // 畫宮位
        drawHouses(canvas)

        // 畫四軸
        drawAxis(canvas)

        // 畫行星 A
        drawPlanets(canvas)

        if (horoscopeB.getPlanetBeanList().size == 0) {
            return
        }

        // 畫行星 B
        drawPlanetsB(canvas)
    }

    private fun drawCircle(canvas: Canvas) {
        mPaint.strokeWidth = 3f
        mPaint.color = ContextCompat.getColor(context, R.color.colorPrimary)

        if (chartType == Chart.Firdaria) {
            radius = size * 0.49F
            firdaria = radius * 0.91F
            mainRadius = radius * 0.83F
            signRadius = radius * 0.68F
            houseRadius = radius * 0.58F
            planetRadius = radius * 0.40F


            canvas.drawCircle(centerPoint.x, centerPoint.y, radius, mPaint)
            canvas.drawCircle(centerPoint.x, centerPoint.y, firdaria, mPaint)
            drawFirdaria(canvas)
        } else {
            mainRadius = size * 0.49F
            signRadius = mainRadius * 0.85F
            houseRadius = mainRadius * 0.75F
        }
        canvas.drawCircle(centerPoint.x, centerPoint.y, mainRadius, mPaint)
        canvas.drawCircle(centerPoint.x, centerPoint.y, signRadius, mPaint)
        canvas.drawCircle(centerPoint.x, centerPoint.y, houseRadius, mPaint)

        if (horoscopeB.getPlanetBeanList().size == 0) {
            planetRadius = mainRadius * 0.50F
            canvas.drawCircle(centerPoint.x, centerPoint.y, planetRadius, mPaint)
            return
        }
        planetRadiusB = mainRadius * 0.55F
        planetRadius = mainRadius * 0.35F
        canvas.drawCircle(centerPoint.x, centerPoint.y, planetRadius, mPaint)
        // 畫第二層內圈
        canvas.drawCircle(centerPoint.x, centerPoint.y, planetRadiusB, mPaint)
    }

    /**
     *
     * 法達星限法是一種時間主星系統 (Time-lord system)，以 75 年為一個循環，分為 9 個大運（七曜、北交點與南交點），
     * 七曜大運之中，又再均分為 7 個副運。
     * 計算Firdaria，第一步要先知道星盤是屬於日間盤或是夜間盤。判斷方法是依據太陽的位置而定，
     * 若太陽在第一宮到第六宮之間（地平面下）是夜間盤；反之，若是太陽在第七宮到第十二宮之間（地平面上）是日間盤。
     * 知道星盤是日間盤或夜間盤之後，你就可以按照下列Firdaria 順序，得知從出生開始每個「大運」的主星，以及掌管的年數：
     *
     * 白天盤：太陽（10年）→ 金星（8年）→ 水星（13年）→ 月亮（9年）→ 土星（11年）→ 木星（12年）→ 火星（7年）→ 北交點（3年）→ 南交點（2年）
     * 夜間盤：月亮（9年）→ 土星（11年）→ 木星（12年）→ 火星（7年）→ 太陽（10年）→ 金星（8年）→ 水星（13年）→ 北交點（3年）→ 南交點（2年）
     * 除了北交點與南交點以外，每一顆行星所掌管的大運年數，都會被平均切分為七個等份，分別再指派一顆行星，作為「副運」主星。
     * 副運主星的分配方式很簡單，第一個都是從「大運」主星開始，然後依照上述行星順序分配。
     *
     */

    private fun drawArc(canvas: Canvas, angle: Float) {
        val paint = Paint()
        paint.strokeWidth = radius - mainRadius
        paint.color = ContextCompat.getColor(context, R.color.greyish_brown_30)
        paint.style = Paint.Style.STROKE
        paint.isAntiAlias = true
        val x = size * 0.05F
        val y = size * 0.05F
        val oval = RectF(x, y, width - x, width - y)
        canvas.drawArc(oval, -90F, angle, false, paint)

        paint.color = ContextCompat.getColor(context, R.color.azul_30)
        canvas.drawArc(oval, angle - 90F, 360 - angle, false, paint)
    }

    protected fun drawFirdaria(canvas: Canvas) {
        // 確認日生盤或夜生盤
        val sun = horoscopeA.getPlanetBeanList().find { it.chName == "太陽" } ?: return

        val birthdayTime = horoscope.birthdayTime - horoscopeA.birthdayTime
        val year75 = 75 * 365 * 24 * 60 * 60L * 1000L
        val age = birthdayTime / year75.toDouble()
        val ageLine = age * 360
        val oneStep = 360 / 75.0
        var angle = 90.0
        var yearPlanet = nightArray
        if (sun.signBean.houseData.index > 6) {
            yearPlanet = dayArray
        }
        var ageLineAngle = angle - ageLine
        if (ageLineAngle < 0) {
            ageLineAngle += 360
        }
        val ptA1 = getAngle(centerPoint, mainRadius, ageLineAngle)
        val ptA2 = getAngle(centerPoint, radius, ageLineAngle)
        val paint = Paint()
        paint.strokeWidth = 5f
        val color = "#FF0000".toColorInt()
        paint.color = color
        paint.textSize = 25f
        canvas.drawLine(ptA1.x, ptA1.y, ptA2.x, ptA2.y, paint)
        drawArc(canvas, ageLine.toFloat())

        yearPlanet.forEach { item ->
            val pt1 = getAngle(centerPoint, firdaria, angle)
            val pt2 = getAngle(centerPoint, mainRadius, angle)
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, mPaint)
            val p1 = getAngle(centerPoint, radius, angle)
            val p2 = getAngle(centerPoint, firdaria, angle)
            canvas.drawLine(p1.x, p1.y, p2.x, p2.y, mPaint)
            val tempAngle = oneStep * item.year!!.toDouble()

            val deputy = yearPlanet.find { it.primary == item.primary }
            var indexDeputy = yearPlanet.indexOf(deputy)
            val step = tempAngle / 7
            var lineAngle = angle - step
            var deputyPlanetAngle = (angle + lineAngle) / 2
            if (item.primary != "北交點" && item.primary != "南交點") {
                for (i in 0 until 7) {
                    val planet = yearPlanet[indexDeputy]
                    val tempLineAngle = lineAngle
                    drawFirdariaDeputyPlanet(canvas, deputyPlanetAngle, planet)
                    val ps1 = getAngle(centerPoint, radius, lineAngle)
                    val ps2 = getAngle(centerPoint, firdaria, lineAngle)
                    canvas.drawLine(ps1.x, ps1.y, ps2.x, ps2.y, mPaint)
                    lineAngle -= step
                    if (lineAngle > 360) {
                        lineAngle -= 360
                    }
                    deputyPlanetAngle = (lineAngle + tempLineAngle) / 2
                    indexDeputy++
                    if (indexDeputy > 6) {
                        indexDeputy = 0
                    }
                }
            }

            val planetAngle = (angle - tempAngle + angle) / 2
            drawFirdariaPlanet(canvas, planetAngle, item)
            angle -= tempAngle
            if (angle < 0) {
                angle += 360
            }
        }
    }

    private fun drawFirdariaDeputyPlanet(canvas: Canvas, planetAngle: Double, item: PlanetYear) {
        val planet = horoscopeA.getPlanetBeanList().find { it.chName == item.primary }
        val pt = getAngle(centerPoint, (firdaria + radius) / 2, planetAngle)
        val symbolPaint = Paint()
        symbolPaint.strokeWidth = 3f
        symbolPaint.isAntiAlias = true
        symbolPaint.textSize = 35f
        symbolPaint.typeface = resources.getFont(R.font.astro_one_font)
        if (planet != null) {
            val color = ("#" + planet.color).toColorInt()
            symbolPaint.color = color
            if (item.primary != "北交點" && item.primary != "南交點") {
                drawText(canvas, planet.symbol, pt.x, pt.y, symbolPaint)
            }
        }
    }

    private fun drawFirdariaPlanet(canvas: Canvas, planetAngle: Double, item: PlanetYear) {
        val planet = horoscopeA.getPlanetBeanList().find { it.chName == item.primary }
        val pt = getAngle(centerPoint, (firdaria + mainRadius) / 2, planetAngle)
        val symbolPaint = Paint()
        symbolPaint.strokeWidth = 3f
        symbolPaint.isAntiAlias = true
        symbolPaint.textSize = 35f
        symbolPaint.typeface = resources.getFont(R.font.astro_one_font)
        if (planet != null) {
            val color = ("#" + planet.color).toColorInt()
            symbolPaint.color = color
            drawText(canvas, planet.symbol, pt.x, pt.y, symbolPaint)
            if (item.primary == "北交點" || item.primary == "南交點") {
                val pt1 = getAngle(centerPoint, (firdaria + radius) / 2, planetAngle)
                drawText(canvas, planet.symbol, pt1.x, pt1.y, symbolPaint)
            }
        }
    }

    // 星座
    open fun drawSigns(canvas: Canvas) {
        // 固定的黃道順序
        val zodiacSigns = listOf(
            "牡羊座", "金牛座", "雙子座", "巨蟹座", "獅子座",
            "處女座", "天秤座", "天蠍座", "射手座", "摩羯座", "水瓶座", "雙魚座"
        )
        // 第一宮的星座名稱
        val ascendantSign = horoscopeA.houses.signBeanList[0].chName
        // 找到第一宮的索引
        val startIndex = zodiacSigns.indexOf(ascendantSign)

        var houseIndex: Int

        for ((index, sign) in signList.withIndex()) {
            // 計算當前星座的宮位 (環繞到 1~12)
            houseIndex = if (index < startIndex) {
                index + (12 - startIndex + 1)
            } else {
                (index - startIndex + 1)
            }

            val color = ("#" + sign.color).toColorInt()
            val pt1 = getPointByAngle(centerPoint, signRadius, sign.angle)
            val pt2 = getPointByAngle(centerPoint, mainRadius, sign.angle)
            mPaint.strokeWidth = 3f
            // 每隔30度的切線
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, mPaint)

            symbolPaint.color = color
            symbolPaint.textSize = 20f

            // 畫星座整宮宮位
            val indexPt =
                getPointByAngle(centerPoint, (signRadius + mainRadius) / 2, sign.angle + 27)
            drawText(canvas, houseIndex.toString(), indexPt.x, indexPt.y, symbolPaint)
            val paint = Paint()

            paint.color = color
            paint.textSize = 50f
            paint.typeface = resources.getFont(R.font.astro_one_font)
            val centerPt =
                getPointByAngle(centerPoint, (signRadius + mainRadius) / 2, sign.angle + 15)
            // 畫星座符號
            drawText(canvas, sign.unicode, centerPt.x, centerPt.y, paint)

            // 畫宮主星
            if (isShowRuler) {
                drawPlanetRuler(canvas, sign)
            }


            val item = pointDataList.find {
                if (it.pointType == PointType.SIGN) {
                    val signBean = it.data as SignBean
                    signBean.chName == sign.chName
                } else {
                    false
                }
            }
            if (item == null) {
                centerPt.y += 20
                pointDataList.add(PointData(PointType.SIGN, sign, centerPt))
            }
        }
    }

    fun drawPlanetRuler(canvas: Canvas, sign: SignBean) {
        //　宮主星
        symbolPaint.textSize = 40f
        if (sign.ruler.contains("、")) {
            val rulers = sign.ruler.split("、")
            var angle = 8
            rulers.forEach {
                val planetData = BasicDBHelper.queryPlanet(context, it)
                if (planetData != null) {
                    val pt = getPointByAngle(
                        centerPoint, (signRadius + mainRadius) / 2, sign.angle + angle
                    )
                    val color = ("#" + planetData.color).toColorInt()
                    symbolPaint.color = color
                    drawText(canvas, planetData.symbol, pt.x, pt.y, symbolPaint)
                }
                angle = 22
            }
        } else {
            val pt = getPointByAngle(centerPoint, (signRadius + mainRadius) / 2, sign.angle + 8)
            val planetData = BasicDBHelper.queryPlanet(context, sign.ruler)
            if (planetData != null) {
                val color = ("#" + planetData.color).toColorInt()
                symbolPaint.color = color
                drawText(canvas, planetData.symbol, pt.x, pt.y, symbolPaint)
            }
        }
    }

    // 宮位
    open fun drawHouses(canvas: Canvas) {
        for (i in 1 until horoscopeA.houses.cusps.size) {
            val angle = horoscopeA.houses.getHouseCusps(i)

            var angleNext = if (i + 1 < horoscopeA.houses.cusps.size) {
                horoscopeA.houses.getHouseCusps(i + 1)
            } else {
                horoscopeA.houses.getHouseCusps(1)
            }
            if (angleNext < angle) {
                angleNext += 360
            }
            val angleCenter = (angleNext + angle) / 2
            val pt1 = getPointByAngle(centerPoint, planetRadius, angle)
            val pt2 = getPointByAngle(centerPoint, signRadius, angle)

            mPaint.pathEffect = null
            // 第二圈到第三圈
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, mPaint)
            val centerRadius = (signRadius + houseRadius) / 2
            val pt3 = getPointByAngle(centerPoint, centerRadius, angleCenter)
            mPaint.textSize = 30f
            mPaint.strokeWidth = 2f
            // 宮位數字
            mPaint.style = Paint.Style.FILL
            mPaint.typeface = Typeface.create("sans", Typeface.BOLD)
            drawText(canvas, i.toString(), pt3.x, pt3.y, mPaint)

            mPaint.textSize = 20f
            mPaint.strokeWidth = 1f
            mPaint.typeface = Typeface.create("sans", Typeface.NORMAL)
            val item = pointDataList.find {
                if (it.pointType == PointType.HOUSE) {
                    val house = it.data as Int
                    house == i
                } else {
                    false
                }
            }
            if (item == null) {
                pointDataList.add(PointData(PointType.HOUSE, i, pt3))
            }
        }
        mPaint.textSize = 15f
    }

    protected fun drawText(canvas: Canvas, str: String, x: Float, y: Float, paint: Paint) {
        paint.textAlign = Paint.Align.CENTER
        paint.isAntiAlias = true

        val textHeight = paint.fontMetrics.let { it.descent - it.ascent }
        canvas.drawText(str, x, y + textHeight / 4, paint)
    }

    open fun onPause() {
        snackbar?.dismiss()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (isTouch) {
            return false
        }
        if (event != null) {
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 增加點擊範圍，提高觸摸精度
                val dis = 40

                // 先檢查行星，因為行星通常是用戶最感興趣的元素
                val planetPoint = pointDataList.find {
                    val x = it.oPoint.x
                    val y = it.oPoint.y
                    (it.pointType == PointType.PLANET_A || it.pointType == PointType.PLANET_B) &&
                            x >= event.x - dis && x <= event.x + dis && y >= event.y - dis && y <= event.y + dis
                }

                if (planetPoint != null) {
                    // 提供視覺反饋
                    performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)

                    when (planetPoint.pointType) {
                        PointType.PLANET_A -> touchPlanet(planetPoint)
                        PointType.PLANET_B -> touchPlanetB(planetPoint)
                        else -> {}
                    }
                    return true
                }

                // 如果沒有點擊到行星，檢查星座和宮位
                val pt = pointDataList.find {
                    val x = it.oPoint.x
                    val y = it.oPoint.y
                    x >= event.x - dis && x <= event.x + dis && y >= event.y - dis && y <= event.y + dis
                }

                if (pt != null) {
                    // 提供視覺反饋
                    performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)

                    when (pt.pointType) {
                        PointType.SIGN -> touchSign(pt)
                        PointType.HOUSE -> touchHouse(pt)
                        else -> {}
                    }
                    return true
                } else {
                    snackbar?.dismiss()
                }
            }
        }
        return super.onTouchEvent(event)
    }

    private fun touchPlanet(pt: PointData) {
        val planetBean = pt.data as PlanetBean
        val signBeanList = horoscopeA.houses.signBeanList
        snackbar = SnackBarView.showPlanet(
            context, signBeanList, planetBean, rootView
        )
    }

    private fun touchPlanetB(pt: PointData) {
        val planetBean = pt.data as PlanetBean
        planetBean.signBean.houseData =
            EphemerisUtil.house(planetBean.longitude, horoscopeA.houses.cusps)
        snackbar = SnackBarView.showPlanetB(
            context, planetBean, rootView
        )
    }

    private fun touchSign(pt: PointData) {
        val signBean = pt.data as SignBean
        val signList = pointDataList.filter {
            if (it.pointType == PointType.PLANET_A) {
                val planet = it.data as PlanetBean
                it.pointType == PointType.PLANET_A && planet.signBean.chName == signBean.chName
            } else {
                false
            }
        }
        val planetBeanList = signList.map {
            it.data as PlanetBean
        } as ArrayList<PlanetBean>
        snackbar = SnackBarView.showSign(
            context, signBean, planetBeanList, rootView
        )
    }

    private fun touchHouse(pt: PointData) {
        val index = pt.data as Int
        val planetList = pointDataList.filter {
            if (it.pointType == PointType.PLANET_A) {
                val planet = it.data as PlanetBean
                planet.signBean.houseData.index == index
            } else {
                false
            }
        }
        val planetBeanList = planetList.map {
            it.data as PlanetBean
        } as ArrayList<PlanetBean>

        val signBean = horoscopeA.houses.signBeanList[index - 1]
        snackbar = SnackBarView.showHouse(
            context, index, planetBeanList, signBean, rootView
        )
    }

    // 四軸
    fun drawAxis(canvas: Canvas) {
        mPaint.strokeWidth = 3f
        mPaint.color = ContextCompat.getColor(context, R.color.green)
        if (horoscopeA.houses.cusps.size == 0) {
            return
        }
        val asc = horoscopeA.houses.cusps[1]
        var pt = getPointByAngle(centerPoint, houseRadius, asc)
        var pt2 = getPointByAngle(centerPoint, planetRadius, asc)
        canvas.drawLine(pt2.x, pt2.y, pt.x, pt.y, mPaint)

        val des = horoscopeA.houses.cusps[7]
        pt = getPointByAngle(centerPoint, houseRadius, des)
        pt2 = getPointByAngle(centerPoint, planetRadius, des)
        canvas.drawLine(pt2.x, pt2.y, pt.x, pt.y, mPaint)

        val mc = horoscopeA.houses.cusps[10]
        pt = getPointByAngle(centerPoint, houseRadius, mc)
        pt2 = getPointByAngle(centerPoint, planetRadius, mc)
        canvas.drawLine(pt2.x, pt2.y, pt.x, pt.y, mPaint)

        val ic = horoscopeA.houses.cusps[4]
        pt = getPointByAngle(centerPoint, houseRadius, ic)
        pt2 = getPointByAngle(centerPoint, planetRadius, ic)
        canvas.drawLine(pt2.x, pt2.y, pt.x, pt.y, mPaint)
    }

    data class PlanetSort(
        var id: Int = 0,
        var angle: Double = 0.0,
        var name: String = "",
        var originalIndex: Int = 0,
        var angleChange: Double = 0.0,
    )

    protected fun sortAngle(planetList: MutableList<PlanetBean>): List<PlanetSort> {
        // 先按角度排序
        val sortedList = planetList
            .filter { it.isChecked }
            .sortedBy { it.longitude }
        LogUtil.d("調整前\n========================== ")
        // 創建 PlanetSort 列表，originalIndex 使用排序後的索引
        val listSort = sortedList
            .mapIndexed { index, planet ->
                LogUtil.d("${planet.chName} isChecked=${planet.isChecked} longitude=${planet.longitude}")
                PlanetSort(
                    id = planet.id,
                    angle = planet.longitude,
                    name = planet.chName,
                    originalIndex = index,
                    angleChange = planet.longitude,
                )
            }
            .toMutableList()

        // 調整角度以避免重疊
        val result = adjustAnglesPreserveOrder(listSort.toMutableList()).sortedBy { it.angle }
        return result
    }

    private fun adjustAnglesPreserveOrder(listSort: MutableList<PlanetSort>): MutableList<PlanetSort> {
        val minAngleDiff = 8.0
        val adjustAngle = 8.0

        var isChange = false
//        LogUtil.d("==========================================")
        for (i in 1 until listSort.size) {
            val prev = listSort[i - 1]
            val curr = listSort[i]

            if (curr.angle < prev.angle) {
                curr.angleChange = (prev.angle + adjustAngle) % 360
                LogUtil.d("當下小於前一個 ${curr.name} ${curr.angle} = ${prev.angle} $adjustAngle")
            }
            val angleDiff = (curr.angle - prev.angle + 360) % 360
//            LogUtil.d("${prev.name} ${prev.angle} ${curr.name} ${curr.angle}")
            if (angleDiff < minAngleDiff) {
                curr.angleChange = (prev.angle + adjustAngle) % 360
                LogUtil.d("間距 $minAngleDiff 過小 ${curr.name} ${curr.angleChange} = ${prev.angle} $adjustAngle")
                isChange = true
            }
        }
        // 處理最後一筆與第一筆的間距
        val first = listSort.first()
        val last = listSort.last()

        val angleDiff = (first.angle - last.angle + 360) % 360
//        LogUtil.d("${last.name} ${last.angle} ${first.name} ${first.angle}")
        if (angleDiff < minAngleDiff) {
            first.angleChange = (last.angle + adjustAngle) % 360
            LogUtil.d("間距 $minAngleDiff 過小 ${first.name} ${first.angleChange} = ${last.angle} $adjustAngle")
            isChange = true
        }
//        LogUtil.d("==========================================")
        if (isChange) {
            val list = listSort.sortedBy { it.angleChange }
            val listSort2 = list
                .mapIndexed { index, planet ->
//                    LogUtil.d("${planet.name} ${planet.angleChange}")
                    PlanetSort(
                        id = planet.id,
                        angle = planet.angleChange,
                        name = planet.name,
                        originalIndex = index,
                        angleChange = planet.angleChange,
                    )
                }
                .toMutableList()
            return adjustAnglesPreserveOrder(listSort2.toMutableList())
        }
        return listSort
    }

    private fun drawPlanets(canvas: Canvas) {
        val planetSortList = sortAngle(horoscopeA.getPlanetBeanList())

        // 先繪製行星連接線和位置點
        for (planetSort in planetSortList) {
            val planet =
                horoscopeA.getPlanetBeanList().find { it.id == planetSort.id } ?: continue
            if (planet.longitude == 0.0) {
                continue
            }

            // 檢查行星是否被勾選
            if (!planet.isChecked) {
                LogUtil.d("Skipping planet ${planet.chName} because isChecked=${planet.isChecked}")
                continue
            }
            val color = ("#" + planet.color).toColorInt()
            mPaint.color = color

            // 使用原始角度繪製行星位置點
            val pt1 = getPointByAngle(centerPoint, planetRadius, planet.longitude)
            // 使用調整後的角度繪製行星符號和連接線
            val pt2 = getPointByAngle(centerPoint, planetRadius * 1.1f, planetSort.angle)
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, mPaint)
            mPaint.style = Paint.Style.FILL
            canvas.drawCircle(pt1.x, pt1.y, 6f, mPaint)

            // 繪製相位線
            mPaint.strokeWidth = 2f
            mPaint.textSize = 30f
            drawAspect(canvas, planet.aspects, pt1)
        }

        // 再繪製行星符號，確保符號在最上層
        for (planetSort in planetSortList) {
            val planet =
                horoscopeA.getPlanetBeanList().find { it.id == planetSort.id } ?: continue
            if (planet.longitude == 0.0 || !planet.isChecked) {
                continue
            }
            val color = ("#" + planet.color).toColorInt()

            // 使用調整後的角度繪製行星符號
            val pt3 = getPointByAngle(centerPoint, planetRadius * 1.2f, planetSort.angle)
            symbolPaint.color = color
            symbolPaint.textSize = 45f
            drawText(canvas, planet.symbol, pt3.x, pt3.y, symbolPaint)

            val item = pointDataList.find {
                if (it.pointType == PointType.PLANET_A) {
                    val planetBean = it.data as PlanetBean
                    planetBean.chName == planet.chName
                } else {
                    false
                }
            }
            if (item == null) {
                pt3.y += 20
                pointDataList.add(PointData(PointType.PLANET_A, planet, pt3))
            }
        }
    }

    private fun drawAspect(canvas: Canvas, aspects: ArrayList<Aspect>, pt1: OPoint) {
        for (aspect in aspects) {
            if (aspect.isDraw) {
                val pt4 = getPointByAngle(centerPoint, planetRadius, aspect.planet.longitude)
                if (aspect.planet.longitude == 0.0) {
                    continue
                }
                if (aspect.deltaDegree >= 1) {
                    val delta = (2 * aspect.deltaDegree).toFloat()
                    val dashPath = DashPathEffect(floatArrayOf(delta, delta), 1.0.toFloat())
                    mPaint.pathEffect = dashPath
                }
                mPaint.strokeWidth = 2f
                mPaint.color = ContextCompat.getColor(
                    context, aspect.type.color
                )
                canvas.drawLine(pt1.x, pt1.y, pt4.x, pt4.y, mPaint)
                mPaint.pathEffect = null
            }
        }
    }

    private fun drawPlanetsB(canvas: Canvas) {
        val planetSortList = sortAngle(horoscopeB.getPlanetBeanList())

        // 先繪製行星連接線和位置點
        for (planetSort in planetSortList) {
            val planet =
                horoscopeB.getPlanetBeanList().find { it.id == planetSort.id } ?: continue
            if (planet.longitude == 0.0 || !planet.isChecked) {
                continue
            }
            val color = ("#" + planet.color).toColorInt()
            mPaint.color = color

            // 使用原始角度繪製行星位置點
            val pt1 = getPointByAngle(centerPoint, planetRadiusB, planet.longitude)
            // 使用調整後的角度繪製行星符號和連接線
            val pt2 = getPointByAngle(centerPoint, planetRadiusB * 1.1f, planetSort.angle)
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, mPaint)
            mPaint.style = Paint.Style.FILL
            canvas.drawCircle(pt1.x, pt1.y, 6f, mPaint)
        }

        // 再繪製行星符號，確保符號在最上層
        for (planetSort in planetSortList) {
            val planet =
                horoscopeB.getPlanetBeanList().find { it.id == planetSort.id } ?: continue
            if (planet.longitude == 0.0 || !planet.isChecked) {
                continue
            }
            val color = ("#" + planet.color).toColorInt()

            // 使用調整後的角度繪製行星符號
            val pt3 = getPointByAngle(centerPoint, planetRadiusB * 1.2f, planetSort.angle)
            symbolPaint.color = color
            symbolPaint.textSize = 45f
            mPaint.textSize = 30f
            // 畫行星符號
            drawText(canvas, planet.symbol, pt3.x, pt3.y, symbolPaint)

            pt3.y += 20
            pointDataList.add(PointData(PointType.PLANET_B, planet, pt3))
        }
    }

    private fun getAngle(pt: OPoint, r: Float, angle: Double): OPoint {
        val x = pt.x + r * cos(angle * Math.PI / 180)
        val y = pt.y - r * sin(angle * Math.PI / 180)
        return OPoint(x.toFloat(), y.toFloat())
    }

    private fun getPointByAngle(pt: OPoint, r: Float, angle: Double): OPoint {
        var angleTemp = angle
        angleTemp += deltaAngle
        val x = pt.x + r * cos(angleTemp * Math.PI / 180)
        val y = pt.y - r * sin(angleTemp * Math.PI / 180)
        return OPoint(x.toFloat(), y.toFloat())
    }

}