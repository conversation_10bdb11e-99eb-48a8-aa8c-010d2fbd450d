package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.PlanetYear
import com.one.astrology.data.type.Chart
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBar
import com.one.astrology.util.FirdariaCalculator
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil
import com.one.core.util.formatDate


/**
 * 本命盤分析
 */
class FirdariaAnalyzeFragment : Fragment() {

    private val chatViewModel by viewModels<ChatViewModel>()
    private var chart = Chart.Firdaria

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                MainAnalyzeScreen(
                    chatViewModel
                )
            }
        }
    }

    @Composable
    fun MainAnalyzeScreen(viewModel: ChatViewModel) {
        val context = LocalContext.current
        var horoscope by remember { mutableStateOf(Horoscope()) }
        var birthData by remember { mutableStateOf(BirthData()) }
        var planetYear by remember { mutableStateOf(PlanetYear()) }
        val isLoading by viewModel.isLoading.observeAsState(false)
        val data by viewModel.data.observeAsState(ReadingData("", "", ""))
        val errorMessage by viewModel.error.observeAsState("")

        LaunchedEffect(errorMessage) {
            LogUtil.d("errorMessage 更新: $errorMessage")
            birthData = viewModel.getBirthData()
            if (birthData.name.isEmpty()) {
                return@LaunchedEffect
            }
            horoscope = viewModel.calculateHoroscope(context, Chart.Natal, birthData)
            val allYearPlanet = FirdariaCalculator.initData(horoscope)
            val currentTimeMillis =
                System.currentTimeMillis().formatDate("yyyy/MM/dd HH:mm")
            for (it in allYearPlanet) {
                if (it.startTime!! >= System.currentTimeMillis()) {
                    break
                } else {
                    planetYear = it
                    val date = planetYear.startTime!!.formatDate("yyyy/MM/dd HH:mm")
                    LogUtil.d(date + " " + currentTimeMillis + " " + planetYear.primary + " " + planetYear.second)
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            // 頂部標題欄
            TopBar(horoscope.name, horoscope.getBirthdayString(), { birthDataList ->
                val selectedBirthData = birthDataList[0]
                if (selectedBirthData.name != birthData.name) {
                    val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                    birthData = viewModel.getBirthData()
                    if (birthData.name.isNotEmpty()) {
                        birthData.isSelected = false
                        birthDataBox.put(birthData)
                    }
                    birthData = selectedBirthData
                    birthData.isSelected = true
                    birthDataBox.put(birthData)
                    horoscope = viewModel.calculateHoroscope(context, Chart.Natal, birthData)
                    val allYearPlanet = FirdariaCalculator.initData(horoscope)
                    val currentTimeMillis =
                        System.currentTimeMillis().formatDate("yyyy/MM/dd HH:mm")
                    for (it in allYearPlanet) {
                        if (it.startTime!! >= System.currentTimeMillis()) {
                            break
                        } else {
                            planetYear = it
                            val date = planetYear.startTime!!.formatDate("yyyy/MM/dd HH:mm")
                            LogUtil.d(date + " " + currentTimeMillis + " " + planetYear.primary + " " + planetYear.second)
                        }
                    }
                    viewModel.init()
                }
            }, onCopy = {})

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                val chartName = getString(chart.type)
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = listOf(
                            "$chartName 運勢解析" to "$chartName 運勢解析 主星 : ${planetYear.primary} 次星 : ${planetYear.second} (繁體中文):\n",
                        )
                        CategoryList(
                            context,
                            categories,
                            viewModel,
                            horoscope = horoscope,
                            chart = Chart.Firdaria
                        )
                    }
                }
            }
        }
    }

}