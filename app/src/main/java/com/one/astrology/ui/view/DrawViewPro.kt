package com.one.astrology.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.Typeface
import android.os.Build
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.ViewGroup
import android.view.WindowManager
import androidx.compose.ui.geometry.Offset
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import com.google.gson.reflect.TypeToken
import com.one.astrology.R
import com.one.astrology.constant.AssetsPath
import com.one.astrology.data.Horoscope
import com.one.astrology.data.OPoint
import com.one.astrology.data.bean.Aspect
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.type.Chart
import com.one.astrology.util.JsonTool
import com.one.astrology.util.Util
import com.one.core.util.LogUtil
import kotlin.math.cos
import kotlin.math.sin


/**
 * 星盤圖表
 */
class DrawViewPro(context: Context?, attrs: AttributeSet?) : DrawView(context, attrs) {

    init {
        val params = ViewGroup.LayoutParams(
            (getScreenWidth() * 1f).toInt(), (getScreenWidth() * 1f).toInt()
        )
        layoutParams = params
        mPaint.strokeWidth = 3f
        mPaint.isAntiAlias = true
        symbolPaint.strokeWidth = 3f
        symbolPaint.isAntiAlias = true
        symbolPaint.textSize = 45f
        symbolPaint.typeface = resources.getFont(R.font.astro_one_font)
        initSigns()
        initRadius()
    }

    private fun getScreenWidth(): Int {
        val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowMetrics = wm.currentWindowMetrics
            windowMetrics.bounds.width()
        } else {
            val displayMetrics = DisplayMetrics()
            wm.defaultDisplay.getMetrics(displayMetrics)
            displayMetrics.widthPixels
        }
    }

    private fun initSigns() {
        val type = object : TypeToken<List<SignBean?>?>() {}.type
        val signJson = Util.loadStringFromAssets(context, AssetsPath.SIGN)
        val signBeanList = JsonTool.parseJson<List<SignBean>>(signJson, type)
        signList.clear()
        if (signBeanList != null) {
            signList.addAll(signBeanList)
        }
    }

    private fun initRadius() {
        size = layoutParams.height.toFloat()
        centerPoint.y = (0.5 * size).toFloat()
        centerPoint.x = centerPoint.y
        mainRadius = size * 0.49F
        signRadius = mainRadius * 0.85F
        houseRadius = mainRadius * 0.75F
        LogUtil.d("getLayoutParams().width : $size")
        LogUtil.d("centerPoint : " + centerPoint.x + " , " + centerPoint.y)
        LogUtil.d("mainRadius : $mainRadius")
        LogUtil.d("signRadius : $signRadius")
        LogUtil.d("houseRadius : $houseRadius")
        pointDataList.clear()
    }

    override fun setData(horoscopeA: Horoscope) {
        this.horoscopeB = Horoscope()
        this.horoscopeA = horoscopeA
        deltaAngle = 180 - horoscopeA.houses.getAsc()
        planetRadius = mainRadius * 0.55F
        invalidate()
    }

    override fun setDataFirdaria(horoscope: Horoscope) {
        this.horoscope = horoscope
        invalidate()
    }

    override fun setDataPair(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        this.horoscopeA = horoscopeA
        this.horoscopeB = horoscopeB
        deltaAngle = 180 - horoscopeA.houses.getAsc()
        planetRadiusB = mainRadius * 0.55F
        planetRadius = mainRadius * 0.40F
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        mPaint.style = Paint.Style.FILL
        mPaint.color = Color.WHITE
        mPaint.style = Paint.Style.STROKE

        if (horoscopeA.getPlanetBeanList().size == 0) {
            return
        }

        drawCircle(canvas)

        // 畫星座
        drawSigns(canvas)

        // 畫宮位
        drawHouses(canvas)

        // 畫四軸
        drawAxis(canvas)

        // 畫行星 A
        drawPlanets(canvas)

        if (horoscopeB.getPlanetBeanList().size == 0) {
            return
        }

        // 畫行星 B
        drawPlanetsB(canvas)
    }

    private fun drawCircle(canvas: Canvas) {
        mPaint.strokeWidth = 3f
        mPaint.color = ContextCompat.getColor(context, R.color.colorPrimary)

        if (chartType == Chart.Firdaria) {
            radius = size * 0.49F
            firdaria = radius * 0.91F
            mainRadius = radius * 0.83F
            signRadius = radius * 0.73F
            houseRadius = radius * 0.63F
            planetRadius = radius * 0.30F

            canvas.drawCircle(centerPoint.x, centerPoint.y, radius, mPaint)
            canvas.drawCircle(centerPoint.x, centerPoint.y, firdaria, mPaint)
            drawFirdaria(canvas)
        } else {
            mainRadius = size * 0.49F
            signRadius = mainRadius * 0.90F
            houseRadius = mainRadius * 0.80F
        }
        canvas.drawCircle(centerPoint.x, centerPoint.y, mainRadius, mPaint)
        canvas.drawCircle(centerPoint.x, centerPoint.y, signRadius, mPaint)
        canvas.drawCircle(centerPoint.x, centerPoint.y, houseRadius, mPaint)

        if (horoscopeB.getPlanetBeanList().size == 0) {
            planetRadius = mainRadius * 0.30F
            canvas.drawCircle(centerPoint.x, centerPoint.y, planetRadius, mPaint)
            return
        }
        planetRadiusB = mainRadius * 0.50F
        planetRadius = mainRadius * 0.20F
        canvas.drawCircle(centerPoint.x, centerPoint.y, planetRadius, mPaint)
        // 畫第二層內圈
        canvas.drawCircle(centerPoint.x, centerPoint.y, planetRadiusB, mPaint)
    }

    // 星座
    override fun drawSigns(canvas: Canvas) {
        // 固定的黃道順序
        val zodiacSigns = listOf(
            "牡羊座", "金牛座", "雙子座", "巨蟹座", "獅子座",
            "處女座", "天秤座", "天蠍座", "射手座", "摩羯座", "水瓶座", "雙魚座"
        )
        // 第一宮的星座名稱
        val ascendantSign = horoscopeA.houses.signBeanList[0].chName
        // 找到第一宮的索引
        val startIndex = zodiacSigns.indexOf(ascendantSign)

        var houseIndex: Int

        for ((index, sign) in signList.withIndex()) {
            // 計算當前星座的宮位 (環繞到 1~12)
            houseIndex = if (index < startIndex) {
                index + (12 - startIndex + 1)
            } else {
                (index - startIndex + 1)
            }

            val color = ("#" + sign.color).toColorInt()
            val pt1 = getPointByAngle(centerPoint, signRadius, sign.angle)
            val pt2 = getPointByAngle(centerPoint, mainRadius, sign.angle)
            mPaint.strokeWidth = 3f
            // 每隔30度的切線
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, mPaint)

            symbolPaint.color = color
            symbolPaint.textSize = 20f

            // 畫星座整宮宮位
            val indexPt =
                getPointByAngle(centerPoint, (signRadius + mainRadius) / 2, sign.angle + 27)
            drawText(canvas, houseIndex.toString(), indexPt.x, indexPt.y, symbolPaint)
            val paint = Paint()

            paint.color = color
            paint.textSize = 40f
            paint.typeface = resources.getFont(R.font.astro_one_font)
            val centerPt =
                getPointByAngle(centerPoint, (signRadius + mainRadius) / 2, sign.angle + 15)
            // 畫星座符號
            drawText(canvas, sign.unicode, centerPt.x, centerPt.y, paint)

            // 畫宮主星
            if (isShowRuler) {
                drawPlanetRuler(canvas, sign)
            }


            val item = pointDataList.find {
                if (it.pointType == PointType.SIGN) {
                    val signBean = it.data as SignBean
                    signBean.chName == sign.chName
                } else {
                    false
                }
            }
            if (item == null) {
                centerPt.y += 20
                pointDataList.add(PointData(PointType.SIGN, sign, centerPt))
            }
        }
    }

    // 宮位
    override fun drawHouses(canvas: Canvas) {
        for (i in 1 until horoscopeA.houses.cusps.size) {
            val angle = horoscopeA.houses.getHouseCusps(i)

            var angleNext = if (i + 1 < horoscopeA.houses.cusps.size) {
                horoscopeA.houses.getHouseCusps(i + 1)
            } else {
                horoscopeA.houses.getHouseCusps(1)
            }
            if (angleNext < angle) {
                angleNext += 360
            }
            val angleCenter = (angleNext + angle) / 2
            val pt1 = getPointByAngle(centerPoint, planetRadius, angle)
            val pt2 = getPointByAngle(centerPoint, signRadius, angle)

            mPaint.pathEffect = null
            // 第二圈到第三圈
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, mPaint)
            val centerRadius = (signRadius + houseRadius) / 2
            val pt3 = getPointByAngle(centerPoint, centerRadius, angleCenter)
            mPaint.textSize = 30f
            mPaint.strokeWidth = 2f
            // 宮位數字
            mPaint.style = Paint.Style.FILL
            mPaint.typeface = Typeface.create("sans", Typeface.BOLD)
            drawText(canvas, i.toString(), pt3.x, pt3.y, mPaint)

            mPaint.textSize = 20f
            mPaint.strokeWidth = 1f
            mPaint.typeface = Typeface.create("sans", Typeface.NORMAL)
            val ptDegree = getPointByAngle(centerPoint, centerRadius, angle - 2.5)
            val degree = horoscopeA.houses.signBeanList[i - 1].degree
            drawText(canvas, degree, ptDegree.x, ptDegree.y, mPaint)

            val ptMinute = getPointByAngle(centerPoint, centerRadius, angle + 2.5)
            val minute = horoscopeA.houses.signBeanList[i - 1].minute
            drawText(canvas, minute, ptMinute.x, ptMinute.y, mPaint)
            val item = pointDataList.find {
                if (it.pointType == PointType.HOUSE) {
                    val house = it.data as Int
                    house == i
                } else {
                    false
                }
            }
            if (item == null) {
                pointDataList.add(PointData(PointType.HOUSE, i, pt3))
            }
        }
        mPaint.textSize = 15f
    }

    private fun drawPlanets(canvas: Canvas) {
        val planetSortList = sortAngle(horoscopeA.getPlanetBeanList())
        for (planetSort in planetSortList) {
            val planet = horoscopeA.getPlanetBeanList().find { it.id == planetSort.id } ?: continue
            if (planet.longitude == 0.0 || !planet.isChecked) {
                continue
            }
            val color = ("#" + planet.color).toColorInt()
            mPaint.color = color

            val pt1 = getPointByAngle(centerPoint, planetRadius, planet.longitude)

            var ptPlanet: OPoint

            val paintPlanet = Paint()
            paintPlanet.color = color
            paintPlanet.typeface = resources.getFont(R.font.astro_one_font)

            val retrogradePaint = Paint()
            retrogradePaint.color = "#FF0000".toColorInt()

            val degreePaint = Paint()
            degreePaint.color = "#000000".toColorInt()

            val paintSign = Paint()
            val colorSign = ("#" + planet.signBean.color).toColorInt()
            paintSign.color = colorSign
            paintSign.typeface = resources.getFont(R.font.astro_one_font)
            var planetStep = (houseRadius - planetRadius) / 10
            if (horoscopeB.getPlanetBeanList().size == 0) {
                retrogradePaint.textSize = 30f
                degreePaint.textSize = 20f
                paintPlanet.textSize = 40f
                paintSign.textSize = 40f
            } else {
                planetStep = (planetRadiusB - planetRadius) / 10
                retrogradePaint.textSize = 25f
                degreePaint.textSize = 20f
                paintPlanet.textSize = 30f
                paintSign.textSize = 30f
            }
            // 逆行
            if (planet.isRetrograde) {
                val pt7 =
                    getPointByAngle(
                        centerPoint,
                        planetRadius + planetStep,
                        planetSort.angle
                    )
                drawText(canvas, "℞", pt7.x, pt7.y, retrogradePaint)
            }

            // 星座分數
            val ptMinute =
                getPointByAngle(
                    centerPoint,
                    planetRadius + planetStep * 2.7f,
                    planetSort.angle
                )
            drawText(canvas, planet.signBean.minute, ptMinute.x, ptMinute.y, degreePaint)

            // 星座符號
            val ptUnicode =
                getPointByAngle(
                    centerPoint,
                    planetRadius + planetStep * 4.7f,
                    planetSort.angle
                )
            drawText(canvas, planet.signBean.unicode, ptUnicode.x, ptUnicode.y, paintSign)

            // 星座度數
            val ptDegree =
                getPointByAngle(
                    centerPoint,
                    planetRadius + planetStep * 6.7f,
                    planetSort.angle
                )
            drawText(canvas, planet.signBean.degree, ptDegree.x, ptDegree.y, degreePaint)

            // 行星符號
            ptPlanet =
                getPointByAngle(
                    centerPoint,
                    planetRadius + planetStep * 8.7f,
                    planetSort.angle
                )
            drawText(canvas, planet.symbol, ptPlanet.x, ptPlanet.y, paintPlanet)
            val item = pointDataList.find {
                if (it.pointType == PointType.PLANET_A) {
                    val planetBean = it.data as PlanetBean
                    planetBean.chName == planet.chName
                } else {
                    false
                }
            }
            if (item == null) {
                ptPlanet.y += 20
                pointDataList.add(PointData(PointType.PLANET_A, planet, ptPlanet))
            }

            mPaint.strokeWidth = 2f
            mPaint.textSize = 25f

            drawAspect(canvas, planet.aspects, pt1)
        }
    }

    private fun drawPlanetsB(canvas: Canvas) {
        val planetSortList = sortAngle(horoscopeB.getPlanetBeanList())
        for (planetSort in planetSortList) {
            val planet = horoscopeB.getPlanetBeanList().find { it.id == planetSort.id } ?: continue
            if (planet.longitude == 0.0) {
                continue
            }
            val color = ("#" + planet.color).toColorInt()
            val paintPlanet = Paint()
            paintPlanet.color = color
            paintPlanet.textSize = 30f
            paintPlanet.typeface = resources.getFont(R.font.astro_one_font)

            val retrogradePaint = Paint()
            retrogradePaint.color = "#FF0000".toColorInt()
            retrogradePaint.textSize = 25f

            val degreePaint = Paint()
            degreePaint.color = "#000000".toColorInt()
            degreePaint.textSize = 20f

            val paintSign = Paint()
            val colorSign = ("#" + planet.signBean.color).toColorInt()
            paintSign.color = colorSign
            paintSign.textSize = 30f
            paintSign.typeface = resources.getFont(R.font.astro_one_font)

            // 逆行
            if (planet.isRetrograde) {
                val pt7 = getPointByAngle(centerPoint, planetRadius * 2.65f, planetSort.angle)
                drawText(canvas, "℞", pt7.x, pt7.y, retrogradePaint)
            }

            // 星座分數
            val ptMinute = getPointByAngle(centerPoint, planetRadius * 2.9f, planetSort.angle)
            drawText(canvas, planet.signBean.minute, ptMinute.x, ptMinute.y, degreePaint)

            // 星座符號
            val ptUnicode = getPointByAngle(centerPoint, planetRadius * 3.2f, planetSort.angle)
            drawText(canvas, planet.signBean.unicode, ptUnicode.x, ptUnicode.y, paintSign)

            // 星座度數
            val ptDegree = getPointByAngle(centerPoint, planetRadius * 3.5f, planetSort.angle)
            drawText(canvas, planet.signBean.degree, ptDegree.x, ptDegree.y, degreePaint)

            // 行星符號
            val ptPlanet: OPoint =
                getPointByAngle(centerPoint, planetRadius * 3.8f, planetSort.angle)
            drawText(canvas, planet.symbol, ptPlanet.x, ptPlanet.y, paintPlanet)

            ptPlanet.y += 20
            pointDataList.add(PointData(PointType.PLANET_B, planet, ptPlanet))
        }
    }

    private fun drawAspect(canvas: Canvas, aspects: ArrayList<Aspect>, pt1: OPoint) {
        val paint = Paint()
        for (aspect in aspects) {
            if (!aspect.isDraw || aspect.planet.longitude == 0.0) {
                continue
            }

            val pt2 = getPointByAngle(centerPoint, planetRadius, aspect.planet.longitude)

            // 設置虛線樣式
            if (aspect.deltaDegree >= 1) {
                val delta = (2 * aspect.deltaDegree).toFloat()
                paint.pathEffect = DashPathEffect(floatArrayOf(delta, delta), 0f)
            } else {
                paint.pathEffect = null
            }

            // 設置畫筆屬性
            paint.strokeWidth = 2f
            paint.color = ContextCompat.getColor(context, aspect.type.color)

            // 繪製相位線
            canvas.drawLine(pt1.x, pt1.y, pt2.x, pt2.y, paint)

            // 計算相位線的中點
            val midPoint = Offset(
                (pt1.x + pt2.x) / 2,
                (pt1.y + pt2.y) / 2
            )

            paint.textSize = 15.0f
            // 測量文本寬度，讓其置中
            val textWidth = paint.measureText(aspect.type.unicode)
            val textHeight = paint.fontMetrics.let { it.descent - it.ascent }
            // 繪製相位符號
            canvas.drawText(
                aspect.type.unicode,
                midPoint.x - textWidth / 2,
                midPoint.y + textHeight / 4,
                paint
            )
        }
    }

    private fun getPointByAngle(pt: OPoint, r: Float, angle: Double): OPoint {
        var angleTemp = angle
        angleTemp += deltaAngle
        val x = pt.x + r * cos(angleTemp * Math.PI / 180)
        val y = pt.y - r * sin(angleTemp * Math.PI / 180)
        return OPoint(x.toFloat(), y.toFloat())
    }

}