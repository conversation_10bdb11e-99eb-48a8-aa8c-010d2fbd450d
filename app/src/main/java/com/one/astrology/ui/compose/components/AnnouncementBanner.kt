package com.one.astrology.ui.compose.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.R
import com.one.astrology.data.model.Announcement
import com.one.astrology.data.model.AnnouncementType

@Composable
fun AnnouncementBanner(
    announcement: Announcement?,
    onAnnouncementClick: (Announcement) -> Unit
) {
    announcement?.let {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = when (it.type) {
                        AnnouncementType.IMPORTANT -> colorResource(id = R.color.red_light)
                        AnnouncementType.NORMAL -> colorResource(id = R.color.colorPrimary)
                        AnnouncementType.ACTIVITY -> colorResource(id = R.color.colorPrimary)
                        AnnouncementType.SYSTEM -> colorResource(id = R.color.colorPrimary)
                    }
                )
                .clickable { onAnnouncementClick(it) }
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Notifications,
                contentDescription = null,
                tint = colorResource(id = R.color.colorAccent),
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = it.title,
                color = Color.White,
                fontSize = 14.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
    }
} 