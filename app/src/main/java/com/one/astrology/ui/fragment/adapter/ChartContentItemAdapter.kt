package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.data.type.ChartType
import com.one.astrology.databinding.ItemChartContentBinding

class ChartContentItemAdapter :
    BaseQuickAdapter<ChartType.ChartTypeItem.Content, ChartContentItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemChartContentBinding= ItemChartContentBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: BaseViewHolder,
        position: Int,
        item: ChartType.ChartTypeItem.Content?
    ) {
        if (item != null) {
            holder.binding.tvName.text = item.name
            holder.binding.tvDescription.text = item.description
            holder.itemView.tag = item
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }
}