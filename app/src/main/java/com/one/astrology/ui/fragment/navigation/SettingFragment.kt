package com.one.astrology.ui.fragment.navigation

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.CloudDownload
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.edit
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import coil.compose.AsyncImage
import com.afollestad.materialdialogs.MaterialDialog
import com.firebase.ui.auth.AuthUI
import com.firebase.ui.auth.FirebaseAuthUIActivityResultContract
import com.firebase.ui.auth.data.model.FirebaseAuthUIAuthenticationResult
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanIntentResult
import com.journeyapps.barcodescanner.ScanOptions
import com.one.astrology.BuildConfig
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.event.AddSignRecordEvent
import com.one.astrology.event.EventKey
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.activity.FeedbackActivity
import com.one.astrology.ui.activity.NotificationSettingsActivity
import com.one.astrology.ui.activity.SettingsComposeActivity
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.viewmodel.FirestoreViewModel
import com.one.astrology.util.AESUtils
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.CsvUtil
import com.one.core.ad.AdCallback
import com.one.core.ad.FacebookAdHelp
import com.one.core.ad.GoogleAdHelp
import com.one.core.billing.BillingClientLifecycle
import com.one.core.callback.FirebaseCallback
import com.one.core.callback.LoginCallback
import com.one.core.data.RemoteConfig
import com.one.core.dialog.AboutDialogFragment
import com.one.core.dialog.DonateDialogFragment
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import com.one.core.view.LoadingDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

/**
 * 設定頁 - Compose版本
 */
@AndroidEntryPoint
class SettingFragment : BaseFragment(R.layout.fragment_setting) {

    private lateinit var loadingDialog: LoadingDialog
    private val viewModel: SettingViewModel by viewModels()
    private val fireStoreViewModel: FirestoreViewModel by viewModels()
    private lateinit var remoteConfig: FirebaseRemoteConfig
    private lateinit var sharedPreferences: SharedPreferences

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("設定頁", this.javaClass.simpleName)
        // 更新登入狀態
        viewModel._loginState.value = viewModel.isLogin()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            // 設置視圖組合策略，以確保在Fragment生命週期內保持Compose狀態
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)

            setContent {
                AppTheme {
                    SettingScreen()
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loadingDialog = LoadingDialog(requireContext())
        loadingDialog.setCancelable(false)
        sharedPreferences = requireContext().getSharedPreferences("device_id", Context.MODE_PRIVATE)

        // 初始化 RemoteConfig
        remoteConfig = Firebase.remoteConfig
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = 3600
        }

        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults)
        remoteConfig.fetchAndActivate().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                checkDeviceId()
            }
        }

        initBillingClientLifecycle()
        observeViewModel()
    }

    @Composable
    fun SettingScreen() {
        // 使用collectAsState來觀察登入狀態變化
        val loginState = viewModel.loginState.collectAsState(initial = false)
        val isLoggedIn = loginState.value
        val canLogin = PermissionsUtil.permissions.canLogin(BuildConfig.IS_DEV)
        val currentUser = viewModel.getCurrentUser()

        Surface(
            modifier = Modifier.fillMaxSize(),
            color = colorResource(id = R.color.white)
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 8.dp, vertical = 12.dp)
            ) {
                // 用戶資料區域
                if (canLogin) {
                    item {
                        UserProfileSection(
                            isLoggedIn = isLoggedIn,
                            userName = currentUser?.displayName,
                            userEmail = currentUser?.email,
                            userPhotoUrl = currentUser?.photoUrl?.toString()
                        )
                    }
                }

                // 設定選項卡片
                item {
                    SettingsCard(
                        canLogin = canLogin,
                        isLoggedIn = isLoggedIn
                    )
                }
            }
        }
    }

    @Composable
    fun UserProfileSection(
        isLoggedIn: Boolean,
        userName: String?,
        userEmail: String?,
        userPhotoUrl: String?
    ) {
        if (isLoggedIn) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 用戶頭像
                AsyncImage(
                    model = userPhotoUrl ?: R.drawable.ic_baseline_person_24,
                    contentDescription = "User Avatar",
                    modifier = Modifier
                        .size(60.dp)
                        .clip(CircleShape)
                        .border(2.dp, colorResource(id = R.color.colorPrimary), CircleShape)
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 用戶名稱
                if (!userName.isNullOrEmpty()) {
                    Text(
                        text = userName,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = colorResource(id = R.color.primary_text)
                    )
                }

                // 用戶電子郵件
                if (!userEmail.isNullOrEmpty()) {
                    Text(
                        text = userEmail,
                        fontSize = 14.sp,
                        color = colorResource(id = R.color.primary_text)
                    )
                }
            }
        }
    }

    @Composable
    fun SettingsCard(
        canLogin: Boolean,
        isLoggedIn: Boolean
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            shape = RoundedCornerShape(12.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(vertical = 8.dp)
            ) {
                // 帳戶設定選項
                if (canLogin) {
                    SettingItem(
                        icon = Icons.Default.AccountCircle,
                        title = if (isLoggedIn) stringResource(R.string.logout) else stringResource(R.string.login),
                        onClick = { if (isLoggedIn) viewModel.logout() else login() }
                    )

                    Divider(modifier = Modifier.padding(horizontal = 16.dp))

                    SettingItem(
                        icon = Icons.Default.CloudUpload,
                        title = "星盤資料上傳",
                        enabled = isLoggedIn && canLogin,
                        onClick = { uploadSignRecord() }
                    )

                    Divider(modifier = Modifier.padding(horizontal = 16.dp))

                    SettingItem(
                        icon = Icons.Default.CloudDownload,
                        title = "星盤資料下載",
                        enabled = isLoggedIn && canLogin,
                        onClick = { downloadSignRecord() }
                    )

                    Divider(modifier = Modifier.padding(horizontal = 16.dp))
                }

                if (RemoteConfig.isOpenBilling) {
                    // 購買與資料選項
                    SettingItem(
                        iconResId = R.drawable.baseline_monetization_on_24,
                        title = "贊助",
                        onClick = { DonateDialogFragment().show(childFragmentManager, "") }
                    )

                    Divider(modifier = Modifier.padding(horizontal = 16.dp))
                }

                SettingItem(
                    iconResId = R.drawable.ic_export,
                    title = "匯出星盤資料",
                    onClick = { viewModel.exportToCsv(requireContext()) }
                )

                Divider(modifier = Modifier.padding(horizontal = 16.dp))

                SettingItem(
                    iconResId = R.drawable.ic_import,
                    title = "匯入星盤資料",
                    onClick = { importCsvLauncher.launch("*/*") }
                )

                Divider(modifier = Modifier.padding(horizontal = 16.dp))

                // 其他選項
                SettingItem(
                    iconResId = R.drawable.ic_feedback,
                    title = "問題回報",
                    onClick = { startActivity(Intent(requireActivity(), FeedbackActivity::class.java)) }
                )

                Divider(modifier = Modifier.padding(horizontal = 16.dp))

                SettingItem(
                    icon = Icons.Default.Notifications,
                    title = "推播設定",
                    onClick = {
                        // 導航到推播設定頁面
                        navigateToNotificationSettings()
                    }
                )

                Divider(modifier = Modifier.padding(horizontal = 16.dp))

                SettingItem(
                    iconResId = R.drawable.baseline_settings_24,
                    title = stringResource(R.string.astrolabe_parameter_settings),
                    onClick = { startActivity(Intent(requireActivity(), SettingsComposeActivity::class.java)) }
                )

                Divider(modifier = Modifier.padding(horizontal = 16.dp))

                SettingItem(
                    iconResId = R.drawable.baseline_qr_code_scanner_24,
                    title = "掃描星盤",
                    onClick = { barcodeLauncher.launch(ScanOptions()) }
                )

                Divider(modifier = Modifier.padding(horizontal = 16.dp))

                SettingItem(
                    iconResId = R.drawable.baseline_share_24,
                    title = "分享",
                    onClick = { shareApp() }
                )

                Divider(modifier = Modifier.padding(horizontal = 16.dp))

                SettingItem(
                    iconResId = R.drawable.baseline_info_24,
                    title = "關於",
                    onClick = { AboutDialogFragment().show(requireActivity().supportFragmentManager, "") }
                )
            }
        }
    }

    @Composable
    fun SettingItem(
        icon: ImageVector? = null,
        iconResId: Int? = null,
        title: String,
        enabled: Boolean = true,
        onClick: () -> Unit
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = enabled) { onClick() }
                .padding(horizontal = 20.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 圖標
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey),
                    modifier = Modifier.size(24.dp)
                )
            } else if (iconResId != null) {
                Icon(
                    painter = painterResource(id = iconResId),
                    contentDescription = title,
                    tint = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey),
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 標題
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey),
                modifier = Modifier.weight(1f)
            )

            // 箭頭圖標
            Icon(
                painter = painterResource(id = R.drawable.baseline_keyboard_arrow_right_24_b),
                contentDescription = null,
                tint = colorResource(id = if (enabled) R.color.colorPrimary else R.color.grey)
            )
        }
    }

    private fun shareApp() {
        val appName = getString(R.string.app_name)
        val intentShare = Intent(Intent.ACTION_SEND)
        intentShare.type = "text/plain"
        intentShare.putExtra(Intent.EXTRA_SUBJECT, "分享")
        intentShare.putExtra(
            Intent.EXTRA_TEXT,
            "$appName\nhttps://play.google.com/store/apps/details?id=${requireActivity().packageName}"
        )
        startActivity(Intent.createChooser(intentShare, appName))
    }

    private fun getDeviceId(): String {
        var deviceId = sharedPreferences.getString("unique_id", null)
        if (deviceId == null) {
            deviceId = UUID.randomUUID().toString()
            sharedPreferences.edit { putString("unique_id", deviceId) }
        }
        return deviceId
    }

    private fun checkDeviceId() {
        val deviceId = getDeviceId()
        LogUtil.d("deviceId $deviceId")
        val allowedDevices = remoteConfig.getString("allowed_devices").split(",")
        val isDeviceAllowed = allowedDevices.contains(deviceId)

        // 根據設備是否被允許來更新權限
        PermissionsUtil.permissions.canLogin = isDeviceAllowed
        PermissionsUtil.permissions.canNoAd = isDeviceAllowed
        PermissionsUtil.permissions.canSaveImage = isDeviceAllowed
        PermissionsUtil.permissions.canSavePdf = isDeviceAllowed

        var remoteConfigDataStr = remoteConfig.getString("remote_config_data")
        if (BuildConfig.IS_DEV) {
            remoteConfigDataStr = remoteConfig.getString("remote_config_data_dev")
        }

        if (remoteConfigDataStr.isNotEmpty()) {
            val remoteConfigData =
                AssetsToObjectUtil.getRemoteConfigData(remoteConfigDataStr)
            RemoteConfig.isOpenFeedback = remoteConfigData.isOpenFeedback
            RemoteConfig.isOpenBilling = remoteConfigData.isOpenBilling
        }
    }

    private fun initBillingClientLifecycle() {
        BillingClientLifecycle.productsIdLiveData.observe(requireActivity()) {
            it.forEach { item ->
                PermissionsUtil.permissions.apply {
                    canLogin = login?.contains(item) == true
                    canNoAd = noAd?.contains(item) == true
                    canSaveImage = saveImage?.contains(item) == true
                    canSavePdf = savePdf?.contains(item) == true
                }
            }
        }
    }

    private var isStop = true

    private fun uploadSignRecord() {
//        loadingDialog.show()
        val adCallback = object : AdCallback {
            override fun onAdLoaded() {
                loadingDialog.dismiss()
            }

            override fun onCompleted(type: String, amount: Int) {
                fireStoreViewModel.uploadUserData(requireActivity())

//                loadingDialog.dismiss()
            }

            override fun onClosed() {
//                loadingDialog.dismiss()
            }
        }
        initAd(adCallback)
    }

    private fun downloadSignRecord() {
        loadingDialog.show()
        val adCallback = object : AdCallback {
            override fun onAdLoaded() {
                loadingDialog.dismiss()
            }

            override fun onCompleted(type: String, amount: Int) {
                fireStoreViewModel.downloadUserData(object : FirebaseCallback {
                    override fun onSuccess() {
                        Toast.makeText(requireContext(), "成功下載", Toast.LENGTH_SHORT).show()
                        loadingDialog.dismiss()
                    }

                    override fun onFailure(message: String) {
                        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                        loadingDialog.dismiss()
                    }
                })
            }

            override fun onClosed() {
                loadingDialog.dismiss()
            }
        }
        initAd(adCallback)
    }

    private fun initAd(adCallback: AdCallback) {
        if (!RemoteConfig.isOpenAd || RemoteConfig.adType == 0) {
            adCallback.onCompleted("", 0)
            return
        }
        if (RemoteConfig.adType == 1) {
            GoogleAdHelp.addRewardedAd(requireActivity(), BuildConfig.GOOGLE_AD_ID, adCallback)
        }
        if (RemoteConfig.adType == 2) {
            FacebookAdHelp.addRewardedAd(requireActivity(), BuildConfig.FB_AD_ID, adCallback)
        }
    }



    private val signInLauncher =
        registerForActivityResult(FirebaseAuthUIActivityResultContract()) { res ->
            onSignInResult(res)
        }

    private fun onSignInResult(result: FirebaseAuthUIAuthenticationResult) {
        val response = result.idpResponse
        if (result.resultCode == AppCompatActivity.RESULT_OK) {
            val firebaseUser = FirebaseAuth.getInstance().currentUser
            if (firebaseUser != null) {
                LogUtil.d("uid : " + firebaseUser.uid)
                LogUtil.d("email :" + firebaseUser.email)
                LogUtil.d("phoneNumber :" + firebaseUser.phoneNumber)
                LogUtil.d("displayName :" + firebaseUser.displayName)
                LogUtil.d("idpToken : " + response?.idpToken)
                if (response != null) {
                    fireStoreViewModel.login(response, object : LoginCallback {
                        override fun onSuccess() {
                            showDialog()
                            Toast.makeText(requireContext(), "登入成功", Toast.LENGTH_SHORT).show()
                        }

                        override fun onFailure(e: Exception) {
                            viewModel.logout()
                            Toast.makeText(requireContext(), e.message, Toast.LENGTH_SHORT).show()
                        }
                    })
                }
            }
        } else {
            if (response != null) {
                Toast.makeText(
                    requireContext(),
                    "登入失敗" + response.error!!.message,
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun showDialog() {
        val dialog = MaterialDialog(requireContext(), MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.title(null, "訊息")
        dialog.message(null, "請選擇上傳備份或下載還原星盤資料?", null)
        dialog.positiveButton(null, "上傳備份") {
            uploadSignRecord()
        }
        dialog.negativeButton(null, "下載還原") {
            downloadSignRecord()
        }
        dialog.show()

    }

    private fun logout() {
        AuthUI.getInstance()
            .signOut(requireContext())
            .addOnCompleteListener {
                LogUtil.d("logout")
                Toast.makeText(requireContext(), "登出成功", Toast.LENGTH_SHORT).show()
            }
    }

    private fun login() {
        val providers = arrayListOf(
            AuthUI.IdpConfig.EmailBuilder().build(),
//            AuthUI.IdpConfig.PhoneBuilder().build(),
            AuthUI.IdpConfig.GoogleBuilder().build(),
//            AuthUI.IdpConfig.FacebookBuilder().build(),
//            AuthUI.IdpConfig.TwitterBuilder().build(),
        )

        // Create and launch sign-in intent
        val signInIntent = AuthUI.getInstance()
            .createSignInIntentBuilder()
            .setAvailableProviders(providers)
            .setLogo(R.mipmap.ic_launcher) // Set logo drawable
            .setTheme(R.style.AppTheme) // Set theme
//            .setTosAndPrivacyPolicyUrls(
//                "https://example.com/terms.html",
//                "https://example.com/privacy.html"
//            )
            .build()
        signInLauncher.launch(signInIntent)
    }

    // Register the launcher and result handler
    private val barcodeLauncher: ActivityResultLauncher<ScanOptions?> = registerForActivityResult(
        ScanContract()
    ) { result: ScanIntentResult? ->
        if (result != null) {
            if (result.contents == null) {
                Toast.makeText(requireContext(), "取消", Toast.LENGTH_LONG).show()
            } else {
                val gson = Gson()
                val contents: String = AESUtils.decrypt(result.contents)
                val birthDataItem: BirthData = gson.fromJson(contents, BirthData::class.java)
                val dialog = MaterialDialog(requireActivity(), MaterialDialog.DEFAULT_BEHAVIOR)
                dialog.title(null, "是否要加入此筆記錄?")
                dialog.message(null, birthDataItem.name, null)
                dialog.positiveButton(null, "是") { _: MaterialDialog? ->
                    dialog.dismiss()
                    try {
                        val birthData = BirthData(birthDataItem)
                        val itemBoxSignRecord = ObjectBox.get().boxFor(BirthData::class.java)
                        itemBoxSignRecord?.put(birthData)
                        LogUtil.d("Scanned: " + result.contents)
                        LiveEventBus.get<AddSignRecordEvent>(EventKey.AddUserBirthData).post(
                            AddSignRecordEvent(birthData)
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                        e.message?.let { LogUtil.e(it) }
                    }
                }
                dialog.negativeButton(null, "否") {
                    dialog.dismiss()
                }
                dialog.show()
            }
        }
    }

    private fun exportToCsv() {
        try {
            val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
            val birthDataList = birthDataBox.query().orderDesc(BirthData_.id).build().find()

            if (birthDataList.isEmpty()) {
                Toast.makeText(requireContext(), "沒有可匯出的資料", Toast.LENGTH_SHORT).show()
                return
            }

            // 使用 CsvUtil 建立標題行
            val csvHeader = CsvUtil.createCsvHeader(
                "姓名", "生日", "出生地緯度", "出生地經度", "出生地",
                "居住地緯度", "居住地經度", "標籤", "是否隱藏"
            )
            val csvData = StringBuilder(csvHeader)

            birthDataList.forEach { birthData ->
                // 使用 CsvUtil 建立資料行，正確處理包含逗號的資料
                val row = CsvUtil.createCsvRow(
                    birthData.name,
                    birthData.birthdayString ?: "",
                    birthData.birthplaceLatitude.toString(),
                    birthData.birthplaceLongitude.toString(),
                    birthData.birthplaceArea ?: "",
                    birthData.residenceLatitude?.toString() ?: "",
                    birthData.residenceLongitude?.toString() ?: "",
                    birthData.tag,
                    birthData.isHide.toString()
                ) + "\n"
                csvData.append(row)
            }

            val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            val timestamp = dateFormat.format(Date())
            val fileName = "birth_data_${timestamp}.csv"
            val file = File(requireContext().getExternalFilesDir(null), fileName)
            file.writeText(csvData.toString())

            val fileUri = androidx.core.content.FileProvider.getUriForFile(
                requireContext(),
                "${requireContext().packageName}.fileprovider",
                file
            )

            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "text/csv"
                putExtra(Intent.EXTRA_STREAM, fileUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                putExtra(Intent.EXTRA_SUBJECT, fileName)
            }
            startActivity(Intent.createChooser(intent, "匯出 CSV 檔案"))

            Toast.makeText(requireContext(), "匯出成功", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            e.message?.let { LogUtil.e(it) }
            Toast.makeText(requireContext(), "匯出失敗: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private val importCsvLauncher: ActivityResultLauncher<String> = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            loadingDialog.show()
            viewModel.importCsvFile(requireContext(), it)
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.uiState.collectLatest { state ->
                when (state) {
                    is SettingUiState.DeviceIdChecked -> {

                    }
                    is SettingUiState.LoggedOut -> {
                        Toast.makeText(requireContext(), "登出成功", Toast.LENGTH_SHORT).show()
                    }
                    is SettingUiState.ScanSuccess -> {
                        LiveEventBus.get<AddSignRecordEvent>(EventKey.AddUserBirthData)
                            .post(AddSignRecordEvent(state.birthData))
                    }
                    is SettingUiState.ScanError -> {
                        Toast.makeText(requireContext(), state.message, Toast.LENGTH_SHORT).show()
                    }
                    is SettingUiState.ExportSuccess -> {
                        val fileUri = androidx.core.content.FileProvider.getUriForFile(
                            requireContext(),
                            "${requireContext().packageName}.fileprovider",
                            state.file
                        )
                        val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
                        val timestamp = dateFormat.format(Date())
                        val fileName = "birth_data_${timestamp}.csv"
                        val intent = Intent(Intent.ACTION_SEND).apply {
                            type = "text/csv"
                            putExtra(Intent.EXTRA_STREAM, fileUri)
                            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                            putExtra(Intent.EXTRA_SUBJECT, fileName)
                        }
                        startActivity(Intent.createChooser(intent, "匯出 CSV 檔案"))
                        Toast.makeText(requireContext(), "匯出成功", Toast.LENGTH_SHORT).show()
                    }
                    is SettingUiState.ExportError -> {
                        Toast.makeText(requireContext(), state.message, Toast.LENGTH_SHORT).show()
                    }
                    is SettingUiState.ImportSuccess -> {
                        loadingDialog.dismiss()
                        val message = "匯入完成：成功 ${state.successCount} 筆，失敗 ${state.errorCount} 筆"
                        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
                        LiveEventBus.get<String>(EventKey.UpdateUserBirthData).post("")
                    }
                    is SettingUiState.ImportError -> {
                        loadingDialog.dismiss()
                        Toast.makeText(requireContext(), state.message, Toast.LENGTH_SHORT).show()
                    }
                    else -> {}
                }
            }
        }
    }

    /**
     * 導航到推播設定頁面
     */
    private fun navigateToNotificationSettings() {
        startActivity(Intent(requireActivity(), NotificationSettingsActivity::class.java))
    }
}