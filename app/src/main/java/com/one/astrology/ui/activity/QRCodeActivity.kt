package com.one.astrology.ui.activity

import android.os.Bundle
import android.widget.ImageView
import com.google.zxing.BarcodeFormat
import com.journeyapps.barcodescanner.BarcodeEncoder
import com.one.astrology.R
import com.one.astrology.util.AESUtils
import com.one.core.activity.BaseActivity
import dagger.hilt.android.AndroidEntryPoint
import java.nio.charset.StandardCharsets
@AndroidEntryPoint
class QRCodeActivity : BaseActivity() {

    private var contents: String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_qr_code)
        val bundle = intent.extras
        if (bundle != null) {
            contents = bundle.getString(ARG_CONTENT)
            contents = AESUtils.encrypt(contents)
            genCode()
        }
    }

    override fun initParams(bundle: Bundle?) {

    }

    private fun genCode() {
        val ivCode = findViewById<ImageView>(R.id.ivCode)
        val encoder = BarcodeEncoder()
        try {
            val bitmap = encoder.encodeBitmap(
                String(
                    contents!!.toByteArray(StandardCharsets.UTF_8),
                    StandardCharsets.ISO_8859_1
                ), BarcodeFormat.QR_CODE, 700, 700
            )
            ivCode.setImageBitmap(bitmap)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    companion object {
        var ARG_CONTENT = "CONTENT"
    }
}