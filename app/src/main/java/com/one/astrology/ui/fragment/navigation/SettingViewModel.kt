package com.one.astrology.ui.fragment.navigation

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.net.Uri
import androidx.core.content.edit
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.google.gson.Gson
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.model.NotificationSettings
import com.one.astrology.data.model.TransitImportance
import com.one.astrology.util.AESUtils
import com.one.astrology.util.CsvUtil
import com.one.astrology.util.NotificationScheduler
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class SettingViewModel @Inject constructor(
    application: Application
) : AndroidViewModel(application) {

    private val _uiState = MutableStateFlow<SettingUiState>(SettingUiState.Initial)
    val uiState: StateFlow<SettingUiState> = _uiState.asStateFlow()

    // 登入狀態流
    val _loginState = MutableStateFlow(false)
    val loginState: StateFlow<Boolean> = _loginState.asStateFlow()

    // 推播設定
    private val _notificationSettings = MutableStateFlow(NotificationSettings.getDefault())
    val notificationSettings: StateFlow<NotificationSettings> = _notificationSettings.asStateFlow()

    private lateinit var remoteConfig: FirebaseRemoteConfig
    private lateinit var sharedPreferences: SharedPreferences

    init {
        sharedPreferences =
            getApplication<Application>().getSharedPreferences("device_id", Context.MODE_PRIVATE)
        initRemoteConfig()
        // 初始化登入狀態
        _loginState.value = isLogin()
        loadNotificationSettings()
    }

    private fun initRemoteConfig() {
        remoteConfig = Firebase.remoteConfig
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = 3600
        }

        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults)
        remoteConfig.fetchAndActivate().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                checkDeviceId()
            }
        }
    }

    private fun getDeviceId(): String {
        var deviceId = sharedPreferences.getString("unique_id", null)
        if (deviceId == null) {
            deviceId = UUID.randomUUID().toString()
            sharedPreferences.edit { putString("unique_id", deviceId) }
        }
        return deviceId
    }

    private fun checkDeviceId() {
        val deviceId = getDeviceId()
        LogUtil.d("deviceId $deviceId")
        val allowedDevices = remoteConfig.getString("allowed_devices").split(",")
        val isDeviceAllowed = allowedDevices.contains(deviceId)

        PermissionsUtil.permissions.apply {
            canLogin = isDeviceAllowed
            canNoAd = isDeviceAllowed
            canSaveImage = isDeviceAllowed
            canSavePdf = isDeviceAllowed
        }

        _uiState.value = SettingUiState.DeviceIdChecked(isDeviceAllowed)
    }

    fun isLogin(): Boolean {
        return Firebase.auth.currentUser != null
    }

    fun getCurrentUser() = Firebase.auth.currentUser

    fun logout() {
        Firebase.auth.signOut()
        _loginState.value = false
        _uiState.value = SettingUiState.LoggedOut
    }

    fun handleScanResult(contents: String) {
        try {
            val decryptedContents = AESUtils.decrypt(contents)
            val birthDataItem = Gson().fromJson(decryptedContents, BirthData::class.java)
            val birthData = BirthData(birthDataItem)
            ObjectBox.get().boxFor(BirthData::class.java)?.put(birthData)
            _uiState.value = SettingUiState.ScanSuccess(birthData)
        } catch (e: Exception) {
            e.printStackTrace()
            _uiState.value = SettingUiState.ScanError(e.message ?: "掃描失敗")
        }
    }

    fun exportToCsv(context: Context) {
        viewModelScope.launch {
            try {
                val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                val birthDataList = birthDataBox.query().orderDesc(BirthData_.id).build().find()

                if (birthDataList.isEmpty()) {
                    _uiState.value = SettingUiState.ExportError("沒有可匯出的資料")
                    return@launch
                }

                // 使用 CsvUtil 建立標題行
                val csvHeader = CsvUtil.createCsvHeader(
                    "姓名", "生日", "出生地緯度", "出生地經度", "出生地",
                    "居住地緯度", "居住地經度", "標籤", "是否隱藏"
                )
                val csvData = StringBuilder(csvHeader)

                birthDataList.forEach { birthData ->
                    // 使用 CsvUtil 建立資料行，正確處理包含逗號的資料
                    val row = CsvUtil.createCsvRow(
                        birthData.name,
                        birthData.birthdayString ?: "",
                        birthData.birthplaceLatitude.toString(),
                        birthData.birthplaceLongitude.toString(),
                        birthData.birthplaceArea ?: "",
                        birthData.residenceLatitude?.toString() ?: "",
                        birthData.residenceLongitude?.toString() ?: "",
                        birthData.tag,
                        birthData.isHide.toString()
                    ) + "\n"
                    csvData.append(row)
                }

                val fileName = "birth_data_${System.currentTimeMillis()}.csv"
                val file = File(context.getExternalFilesDir(null), fileName)
                file.writeText(csvData.toString())

                _uiState.value = SettingUiState.ExportSuccess(file)
            } catch (e: Exception) {
                _uiState.value = SettingUiState.ExportError(e.message ?: "匯出失敗")
            }
        }
    }

    fun importCsvFile(context: Context, uri: Uri) {
        viewModelScope.launch {
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                val reader = inputStream?.bufferedReader()
                val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                var successCount = 0
                var errorCount = 0

                reader?.readLine() // 跳過標題行

                reader?.useLines { lines ->
                    lines.forEach { line ->
                        try {
                            // 使用 CsvUtil 正確解析 CSV 行
                            val columns = CsvUtil.parseCsvLine(line)
                            if (CsvUtil.validateFieldCount(columns, 9)) {
                                val birthdayString = CsvUtil.getFieldSafely(columns, 1)
                                val timestamp = FormatUtils.stringToTimestamp(birthdayString)
                                val birthData = BirthData().apply {
                                    name = CsvUtil.getFieldSafely(columns, 0)
                                    birthday = timestamp
                                    this.birthdayString = birthdayString
                                    birthplaceLatitude = CsvUtil.parseDoubleSafely(CsvUtil.getFieldSafely(columns, 2))
                                    birthplaceLongitude = CsvUtil.parseDoubleSafely(CsvUtil.getFieldSafely(columns, 3))
                                    birthplaceArea = CsvUtil.getFieldSafely(columns, 4)
                                    residenceLatitude = CsvUtil.parseDoubleSafely(CsvUtil.getFieldSafely(columns, 5))
                                    residenceLongitude = CsvUtil.parseDoubleSafely(CsvUtil.getFieldSafely(columns, 6))
                                    tag = CsvUtil.getFieldSafely(columns, 7)
                                    isHide = CsvUtil.parseBooleanSafely(CsvUtil.getFieldSafely(columns, 8))
                                    createTime = System.currentTimeMillis()
                                }
                                birthDataBox.put(birthData)
                                successCount++
                            } else {
                                LogUtil.e("CSV 行欄位數量不足: ${columns.size}, 期望: 9")
                                errorCount++
                            }
                        } catch (e: Exception) {
                            LogUtil.e("解析 CSV 行失敗: ${e.message}")
                            errorCount++
                        }
                    }
                }

                _uiState.value = SettingUiState.ImportSuccess(successCount, errorCount)
            } catch (e: Exception) {
                _uiState.value = SettingUiState.ImportError(e.message ?: "匯入失敗")
            }
        }
    }

    /**
     * 加載推播設定
     */
    private fun loadNotificationSettings() {
        viewModelScope.launch {
            try {
                val box = ObjectBox.get().boxFor(NotificationSettings::class.java)
                val settings = box.query().build().findFirst()
                _notificationSettings.value = settings ?: NotificationSettings.getDefault()
            } catch (e: Exception) {
                LogUtil.e("載入推播設定失敗: ${e.message}")
                _notificationSettings.value = NotificationSettings.getDefault()
            }
        }
    }

    /**
     * 更新推播設定
     */
    fun updateNotificationSettings(context: Context, settings: NotificationSettings) {
        viewModelScope.launch {
            try {
                val box = ObjectBox.get().boxFor(NotificationSettings::class.java)
                settings.lastUpdateTime = System.currentTimeMillis()
                box.put(settings)
                _notificationSettings.value = settings

                // 更新推播排程
                NotificationScheduler.updateSchedule(context, settings)

                LogUtil.d("推播設定已更新")
            } catch (e: Exception) {
                LogUtil.e("更新推播設定失敗: ${e.message}")
            }
        }
    }

    /**
     * 更新推播開關
     */
    fun updateNotificationEnabled(context: Context, enabled: Boolean) {
        viewModelScope.launch {
            val currentSettings = _notificationSettings.value.copy()
            currentSettings.isDailyTransitEnabled = enabled
            updateNotificationSettings(context, currentSettings)
        }
    }

    /**
     * 更新推播時間
     */
    fun updateNotificationTime(context: Context, hour: Int, minute: Int) {
        viewModelScope.launch {
            val currentSettings = _notificationSettings.value.copy()
            currentSettings.setNotificationTime(hour, minute)
            updateNotificationSettings(context, currentSettings)
        }
    }

    /**
     * 更新最小重要性等級
     */
    fun updateMinimumImportance(context: Context, importance: TransitImportance) {
        viewModelScope.launch {
            val currentSettings = _notificationSettings.value.copy()
            currentSettings.minimumImportance = importance
            updateNotificationSettings(context, currentSettings)
        }
    }

    /**
     * 更新事件類型開關
     */
    fun updateEventTypeSettings(
        context: Context,
        aspectEvents: Boolean? = null,
        houseChange: Boolean? = null,
        signChange: Boolean? = null,
        retrograde: Boolean? = null
    ) {
        viewModelScope.launch {
            val currentSettings = _notificationSettings.value.copy()
            aspectEvents?.let { currentSettings.isAspectEventsEnabled = it }
            houseChange?.let { currentSettings.isHouseChangeEnabled = it }
            signChange?.let { currentSettings.isSignChangeEnabled = it }
            retrograde?.let { currentSettings.isRetrogradeEnabled = it }
            updateNotificationSettings(context, currentSettings)
        }
    }
}

sealed class SettingUiState {
    object Initial : SettingUiState()
    data class DeviceIdChecked(val isAllowed: Boolean) : SettingUiState()
    object LoggedOut : SettingUiState()
    data class ScanSuccess(val birthData: BirthData) : SettingUiState()
    data class ScanError(val message: String) : SettingUiState()
    data class ExportSuccess(val file: File) : SettingUiState()
    data class ExportError(val message: String) : SettingUiState()
    data class ImportSuccess(val successCount: Int, val errorCount: Int) : SettingUiState()
    data class ImportError(val message: String) : SettingUiState()
}