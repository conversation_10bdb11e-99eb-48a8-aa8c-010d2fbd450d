package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.databinding.ItemChartBinding

class ChartItemAdapter :
    BaseQuickAdapter<String, ChartItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemChartBinding = ItemChartBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int, item: String?) {
        if (item != null) {
            holder.binding.tvName.text = item
            holder.itemView.tag = item
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: <PERSON>Group,
        viewType: Int
    ): <PERSON>ViewHolder {
        return BaseViewHolder(parent)
    }
}