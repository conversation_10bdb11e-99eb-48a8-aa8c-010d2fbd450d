package com.one.astrology.ui.screens
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChartSelectionScreen() {
    val chartCategories = mapOf(
        "基本星盤" to listOf("Celestial", "Natal"),
        "推運盤" to listOf("Transit", "Secondary Progression", "Tertiary Progression", "Solar Arc"),
        "回歸盤" to listOf("Solar Return", "Lunar Return"),
        "比較盤" to listOf("Synastry", "Composite", "Davison", "Marks"),
        "進階推運比較" to listOf(
            "Synastry Secondary Progression", "Synastry Tertiary Progression",
            "Composite Secondary Progression", "Composite Tertiary Progression",
            "Davison Secondary Progression", "Davison Tertiary Progression",
            "Marks Secondary Progression", "Marks Tertiary Progression"
        ),
        "法達盤" to listOf("Firdaria")
    )

    var selectedChart by remember { mutableStateOf<String?>(null) }

    Scaffold(topBar = {
        TopAppBar(title = { Text("選擇占星盤") })
    }) { paddingValues ->
        LazyColumn(modifier = Modifier.padding(paddingValues)) {
            chartCategories.forEach { (category, charts) ->
                item {
                    Text(
                        category,
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(8.dp)
                    )
                }
                items(charts) { chart ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { selectedChart = chart }
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = (chart == selectedChart),
                            onClick = { selectedChart = chart })
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(chart)
                    }
                }
            }
        }
    }
}