package com.one.astrology.ui.fragment.navigation

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.navigation.fragment.findNavController
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.Category
import com.one.astrology.data.model.aiModels
import com.one.astrology.data.type.Chart
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.cardElevation
import com.one.astrology.util.EncryptedSPUtil


/**
 * 本命格的論斷
 */
class FateFragment : Fragment() {

    private var matchEvent: MatchEvent? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AppTheme {
                    FateScreen()
                }
            }
        }
    }

    @Composable
    fun FateScreen() {
        val categories = listOf(
            Category(
                "個人盤分析",
                "深入解析您的本命星盤，探索性格特質、天賦潛能、人生使命，以及在事業、感情、財運等各領域的發展方向。",
                R.id.natalAnalyzeFragment,
                Chart.Natal
            ),
            Category(
                "法達盤分析",
                "透過法達時期解析您人生不同階段的運勢變化，了解各行星時期的影響，掌握關鍵時機，預測事業、財運、感情等領域的發展。",
                R.id.firdariaAnalyzeFragment,
                Chart.Firdaria
            ),
            Category(
                "比較盤分析",
                "透過分析兩張星盤之間的行星相位，深入了解兩人在感情、友誼或合作關係中的互動模式、挑戰與機遇。",
                R.id.synastryAnalyzeFragment,
                Chart.Synastry
            ),
            Category(
                "組合盤分析",
                "將兩人星盤合併為一，創造出代表關係本質的新星盤，揭示關係的潛在發展方向與核心主題。",
                R.id.compositeAnalyzeFragment,
                Chart.Composite
            ),
            Category(
                "時空盤分析",
                "計算兩人出生時間與地點的中點，建立關係星盤，展現兩人相遇的時空意義與關係發展軌跡。",
                R.id.davisonAnalyzeFragment,
                Chart.Davison
            ),
            Category(
                "馬克思盤分析",
                "運用特殊的合盤技術，揭示兩人關係中的深層心理動態、潛意識互動模式與轉化可能。",
                R.id.marksAnalyzeFragment,
                Chart.Marks
            ),
            Category(
                "行運盤運勢分析",
                "分析當前行星運行對您星盤的影響，預測近期各領域的機遇與挑戰，提供精準的時機掌握建議。",
                R.id.transitAnalysisFragment,
                Chart.Transit
            ),
            Category(
                "次限盤運勢分析",
                "追蹤星盤的漸進式發展，反映您內在意識的成長歷程，展現人生階段性的重要課題與轉變。",
                R.id.secondaryProgressionsFragment,
                Chart.SecondaryProgression
            ),
            Category(
                "三限盤運勢分析",
                "採用每日對應一個月的推演技術，精準分析近期生活變化，預測一至三個月內的事件發展、情緒起伏與人際互動模式。",
                R.id.secondaryProgressionsFragment, //tertiaryProgressionsFragment,
                Chart.TertiaryProgression
            ),
            Category(
                "太陽弧盤運勢分析",
                "運用精確的太陽弧度推演技術，預測人生重大轉折點，洞察關鍵時期的機遇與挑戰。",
                R.id.solarArcFragment,
                Chart.SolarArc
            ),
            Category(
                "太陽返照盤運勢分析",
                "分析生日時太陽回歸的星盤，預測整年運勢主題，為您提供年度發展方向的重要指引。",
                R.id.solarReturnFragment,
                Chart.SolarReturn
            ),
            Category(
                "月亮返照盤運勢分析",
                "解讀月亮回歸時的星盤配置，預測每月情緒、生活、人際關係等方面的細微變化。",
                R.id.solarReturnFragment,
                Chart.LunarReturn
            ),
            Category(
                "組合次限盤運勢分析",
                "透過組合盤的次限推演，洞察兩人關係的長期發展趨勢，預測重要的關係轉折點。",
                R.id.compositeProgressionFragment,
                Chart.CompositeSecondaryProgression
            ),
            Category(
                "組合三限盤運勢分析",
                "運用組合盤的三限推演，分析兩人關係的近期互動變化，把握關係調適的最佳時機。",
                R.id.compositeProgressionFragment,
                Chart.CompositeTertiaryProgression
            ),
            Category(
                "時空次限盤運勢分析",
                "結合時空盤與次限推演，深入探索關係的長期演變軌跡，預測重要的成長與轉變時期。",
                R.id.compositeProgressionFragment,
                Chart.DavisonSecondaryProgression
            ),
            Category(
                "時空三限盤運勢分析",
                "應用時空盤的三限推演，解讀關係近期的互動特質，提供關係調適的具體建議。",
                R.id.compositeProgressionFragment,
                Chart.DavisonTertiaryProgression
            ),
            Category(
                "馬盤次限盤運勢分析",
                "結合馬克思盤與次限推演，探索兩人關係中深層心理發展的長期變化趨勢。",
                R.id.compositeProgressionFragment,
                Chart.MarksSecondaryProgression
            ),
            Category(
                "馬盤三限盤運勢分析",
                "運用馬克思盤的三限推演，分析近期關係中的心理互動模式與潛意識變化。",
                R.id.compositeProgressionFragment,
                Chart.MarksTertiaryProgression
            ),
            Category(
                "世界行運 / 國家運勢",
                "分析全球性行星配置，預測政治、經濟、社會等重大趨勢，為集體意識發展提供洞見。",
                R.id.divinationAnalysisFragment,
                Chart.Natal
            ),
            Category(
                "卜卦分析",
                "結合占星與易經卜卦技術，為特定問題提供即時指引，協助決策與方向選擇。",
                R.id.divinationAnalysisFragment,
                Chart.Natal
            ),
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(0.dp),
            verticalArrangement = Arrangement.spacedBy(0.dp) // 調整按鈕之間的間距
        ) {
            items(categories) { category ->
                CategoryButton(
                    category = category
                )
            }
        }
    }

    @Composable
    fun CategoryButton(category: Category) {
        Card(
            shape = AppShapes.small,
            elevation = cardElevation(),
            modifier = Modifier
                .padding(10.dp)
                .fillMaxWidth()
                .clickable {
                    val bundle = Bundle()
                    bundle.putSerializable("category", category)
                    findNavController().navigate(category.route, bundle)
                }
        ) {
            Column(
                modifier = Modifier
                    .background(colorResource(id = R.color.colorPrimary))
                    .padding(10.dp)
            ) {
                Text(
                    text = category.title,
                    color = Color.White,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = category.description,
                    color = Color.White,
                    fontSize = 14.sp,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(10.dp),
                contentAlignment = Alignment.CenterEnd
            ) {
                Text(
                    text = "查看分析",
                    color = colorResource(id = R.color.colorAccent), // 橙色文字
                    fontSize = 14.sp
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (BuildConfig.IS_DEV) {
            initMenu()
        }
        collectData()
    }

    private fun initMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_fate, menu)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_ai_settings -> {
                        showAIModuleDialog(requireContext())
                        true
                    }

                    R.id.action_force_update -> {
                        showForceUpdateDialog(requireContext())
                        true
                    }

                    R.id.action_view_readings -> {
                        findNavController().navigate(R.id.readingDataListFragment)
                        true
                    }

                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }

    fun showForceUpdateDialog(context: Context) {
        val items = arrayOf(
            "是",
            "否",
        )
        val defaultIndex: Int
        val isForceUpdate = EncryptedSPUtil.getForcedUpdate(context)
        if (isForceUpdate) {
            defaultIndex = 0
        } else {
            defaultIndex = 1
        }
        var selectedModule = items[defaultIndex]

        AlertDialog.Builder(context)
            .setTitle("是否強制更新?")
            .setSingleChoiceItems(items, defaultIndex) { _, which ->
                selectedModule = items[which]
            }
            .setPositiveButton("確定") { _, _ ->
                if (selectedModule == "是") {
                    EncryptedSPUtil.setForcedUpdate(requireContext(), true)
                } else {
                    EncryptedSPUtil.setForcedUpdate(requireContext(), false)
                }

                Toast.makeText(context, "已選擇: $selectedModule", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    @SuppressLint("DefaultLocale")
    fun showAIModuleDialog(context: Context) {
        // 取得所有 AI 模型的資訊
        val aiModuleInfoList = aiModels.map { model ->
            buildString {
                append("${model.id}\n")
                model.developer?.let { append("開發者: $it\n") }
                model.inputPricePerMillion?.let { append("輸入價格: $${String.format("%.2f", it)}/百萬次\n") }
//                model.cachedInputPricePerMillion?.let { append("快取輸入價格: $${String.format("%.2f", it)}/百萬次\n") }
                model.outputPricePerMillion?.let { append("輸出價格: $${String.format("%.2f", it)}/百萬次") }
            }.trim()
        }.toTypedArray()

        // 取得上次選擇的 AI 模組
        val savedModule = EncryptedSPUtil.getAIModule(context)

        // 找到對應的索引，如果找不到，則預設選第一個
        val defaultIndex = aiModels.indexOfFirst { it.id == savedModule }.takeIf { it >= 0 } ?: 0

        AlertDialog.Builder(context)
            .setTitle("選擇 AI 模組")
            .setIcon(R.drawable.ic_ai)
            .setSingleChoiceItems(aiModuleInfoList, defaultIndex) { dialog, which ->
                val selectedModel = aiModels[which]
                // 保存選擇的 AI 模組
                EncryptedSPUtil.setAIModule(context, selectedModel.id)
                Toast.makeText(context, "已選擇: ${selectedModel.id}", Toast.LENGTH_SHORT).show()
            }
            .setPositiveButton("確定", null)
            .setNegativeButton("取消", null)
            .create()
            .apply {
                window?.setBackgroundDrawableResource(R.drawable.dialog_rounded_background)
            }
            .show()
    }

    private fun collectData() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
            this.matchEvent = matchEvent

            when (matchEvent.chartType) {
                Chart.Natal -> {
                    // matchEvent.horoscopeA, matchEvent.horoscopeA
                }

                Chart.Transit -> {

                }

                Chart.SecondaryProgression -> {

                }

                Chart.TertiaryProgression -> {

                }

                Chart.Synastry -> {

                }

                Chart.Composite -> {

                }

                else -> {

                }
            }

        }
    }
}