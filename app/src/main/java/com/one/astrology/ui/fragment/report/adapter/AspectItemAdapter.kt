package com.one.astrology.ui.fragment.report.adapter


import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.R
import com.one.astrology.data.db.AspectData
import com.one.astrology.databinding.ItemAspectBinding
import com.one.astrology.util.EphemerisUtil.Companion.szZodiac

class AspectItemAdapter :
    BaseQuickAdapter<AspectData, AspectItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemAspectBinding = ItemAspectBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: <PERSON><PERSON><PERSON>wHolder,
        position: Int,
        item: AspectData?
    ) {

        if (item != null) {
            holder.binding.tvPlanetNameA.text = item.planetA
            holder.binding.tvAspectType.text = item.type
            if (item.aspectType != null) {
                holder.binding.tvAspectType.setTextColor(
                    ContextCompat.getColor(
                        context,
                        item.aspectType?.color ?: 0
                    )
                )
            }

            holder.binding.tvPlanetNameB.text = item.planetB
            holder.binding.tvType.text = item.direction


            if (item.orb != null) {
                val strAngle = szZodiac(item.orb!!)
                holder.binding.tvOrb.text =
                    context.getString(R.string.orb_degree, strAngle[1], strAngle[2])
                holder.itemView.tag = item
            }
        }

    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}