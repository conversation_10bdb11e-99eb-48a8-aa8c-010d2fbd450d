package com.one.astrology.ui.fragment.analysis

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.Category
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBar
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil

class SolarReturnFragment : Fragment() {
    private val chatViewModel by viewModels<ChatViewModel>()
    private var chartName = ""
    private var category: Category = Category("", "", 0, Chart.SolarReturn)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (arguments != null) {
            category = arguments?.getSerializable("category") as Category
            chartName = getString(category.chart.type)
        }
        return ComposeView(requireContext()).apply {
            setContent {
                MainAnalyzeScreen()
            }
        }
    }

    @Composable
    fun MainAnalyzeScreen() {
        val context = LocalContext.current
        var matchEvent by remember { mutableStateOf(MatchEvent()) }
        var birthData by remember { mutableStateOf(BirthData()) }

        val isLoading by chatViewModel.isLoading.observeAsState(false)
        val data by chatViewModel.data.observeAsState(ReadingData("", "", ""))
        val errorMessage by chatViewModel.error.observeAsState("")
        var dataNow by remember { mutableLongStateOf(System.currentTimeMillis()) }

        // 當 birthData 或 chart 變更時，更新 matchEvent
        LaunchedEffect(category.chart) {
            LogUtil.d("BirthData 或 Chart 變更，更新 matchEvent")
            birthData = chatViewModel.getBirthData()
            if (birthData.name.isNotEmpty()) {
                matchEvent = getMatchEvent(category.chart, birthData, dataNow, context)
            }
            chatViewModel.init()
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            // 上方導航欄
            TopBar(birthData.name, birthData.generateBirthdayString(), { birthDataList ->
                val selectedBirthData = birthDataList[0]
                updateBirthData(birthData, selectedBirthData) { newBirthData ->
                    birthData = newBirthData
                    matchEvent = getMatchEvent(category.chart, birthData, dataNow, context)
                    chatViewModel.init()
                }
            }, { date ->
                dataNow = date.time
            }, onCopy = {})

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                when {
                    isLoading -> LoadingContent()
                    errorMessage.isNotEmpty() -> ErrorMessage(errorMessage)
                    data.title.isNotEmpty() -> {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    }

                    else -> {
                        val categories = getCategories(category.chart, chartName)
                        CategoryList(
                            context,
                            categories,
                            chatViewModel,
                            matchEvent,
                            chart = Chart.SolarReturn
                        )
                    }
                }
            }
        }
    }

    private fun getMatchEvent(
        chart: Chart,
        birthData: BirthData,
        dataNow: Long,
        context: Context
    ): MatchEvent {
        val progressedTime = BirthData()
        progressedTime.birthday = dataNow
        return when (chart) {
            Chart.SolarReturn -> chatViewModel.getSolarReturn(context, birthData, progressedTime)
            Chart.LunarReturn -> chatViewModel.getLunarReturn(context, birthData, progressedTime)
            else -> MatchEvent()
        }
    }

    private fun updateBirthData(
        currentBirthData: BirthData,
        selectedBirthData: BirthData,
        onUpdated: (BirthData) -> Unit
    ) {
        if (selectedBirthData.name != currentBirthData.name) {
            val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)

            // 取消舊的選擇
            if (currentBirthData.name.isNotEmpty()) {
                currentBirthData.isSelected = false
                birthDataBox.put(currentBirthData)
            }

            // 設定新的選擇
            selectedBirthData.isSelected = true
            birthDataBox.put(selectedBirthData)

            // 回傳更新後的資料
            onUpdated(selectedBirthData)
        }
    }

    private fun getCategories(chart: Chart, chartName: String): List<Pair<String, String>> {
        return when (chart) {
            Chart.SolarReturn -> listOf(
                "${chartName}年度命運" to "${chartName}：解析當年度的命運走向與整體運勢 (繁體中文):\n",
                "${chartName}財富與資源" to "${chartName}：探索當年度的財運、資源獲得與金錢管理 (繁體中文):\n",
                "${chartName}感情與人際關係" to "${chartName}：剖析當年度的感情發展、戀愛與人際互動 (繁體中文):\n",
                "${chartName}家庭與親緣" to "${chartName}：解析當年度家庭關係、親密關係與家族動態 (繁體中文):\n",
                "${chartName}事業與志向" to "${chartName}：評估當年度事業發展、目標達成與職場機會 (繁體中文):\n",
                "${chartName}健康與福祉" to "${chartName}：關注當年度身心健康、生活方式與潛在風險 (繁體中文):\n",
                "${chartName}年度挑戰與突破" to "${chartName}：揭示當年度的挑戰、成長契機與突破點 (繁體中文):\n",
                "${chartName}自我成長與內在變化" to "${chartName}：探索當年度的自我成長、內心轉變與人生課題 (繁體中文):\n"
            )

            Chart.LunarReturn -> listOf(
                "${chartName}本月情緒與心理狀態" to "${chartName}：解析本月的情緒變化、內在感受與心理趨勢 (繁體中文):\n",
                "${chartName}本月人際關係" to "${chartName}：探索本月的人際互動、家庭關係與社交狀況 (繁體中文):\n",
                "${chartName}本月事業與財運" to "${chartName}：評估本月的工作動態、財務狀況與經濟流向 (繁體中文):\n",
                "${chartName}本月健康與生活方式" to "${chartName}：關注本月的健康狀態、身體能量與生活習慣 (繁體中文):\n",
                "${chartName}本月重要課題" to "${chartName}：揭示本月需要關注的重點、挑戰與成長方向 (繁體中文):\n"
            )

            else -> emptyList()
        }
    }
}
