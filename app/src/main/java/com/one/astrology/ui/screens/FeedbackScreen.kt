package com.one.astrology.ui.screens

import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import android.widget.Toast
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.ui.fragment.dialog.UserSelectionDialog
import com.one.astrology.ui.view.ImageFullScreenViewer
import com.one.astrology.viewmodel.FeedbackState
import com.one.astrology.viewmodel.FeedbackViewModel
import com.one.core.data.RemoteConfig

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedbackScreen(
    viewModel: FeedbackViewModel = viewModel()
) {
    val backDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

    if (!RemoteConfig.isOpenFeedback) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "問題回報功能目前關閉中",
                style = MaterialTheme.typography.titleLarge
            )
        }
        return
    }


    var title by remember { mutableStateOf("") }
    var content by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf("一般問題") }
    var expanded by remember { mutableStateOf(false) }
    var selectedChart by remember { mutableStateOf<Chart?>(null) }
    var isChartExpanded by remember { mutableStateOf(false) }
    var mediaUris by remember { mutableStateOf<List<Uri>>(emptyList()) }

    val categories = listOf("一般問題", "功能異常", "建議改進", "其他")
    val feedbackState by viewModel.feedbackState.collectAsState()
    val maxMediaCount = 3
    val maxFileSizeMB = 5 // 限制檔案大小 5MB
    val context = LocalContext.current


    var selectedBirthDataList: List<BirthData>? by remember { mutableStateOf(null) }

    var isShowDialog by remember { mutableStateOf(false) }
    if (isShowDialog) {
        var isMultiSelect = false
        when (selectedChart) {
            Chart.Celestial,
            Chart.Natal,
            Chart.Transit,
            Chart.SecondaryProgression,
            Chart.SecondaryProgressionSynastry,
            Chart.TertiaryProgression,
            Chart.TertiaryProgressionSynastry,
            Chart.SolarReturn,
            Chart.SolarArc,
            Chart.LunarReturn,
            Chart.Firdaria -> isMultiSelect = false

            Chart.Synastry,
            Chart.SynastrySecondaryProgression,
            Chart.SynastryTertiaryProgression,
            Chart.Composite,
            Chart.CompositeSecondaryProgression,
            Chart.CompositeTertiaryProgression,
            Chart.Davison,
            Chart.DavisonSecondaryProgression,
            Chart.DavisonTertiaryProgression,
            Chart.Marks,
            Chart.MarksSecondaryProgression,
            Chart.MarksTertiaryProgression -> isMultiSelect = true

            null -> isMultiSelect = false
        }
        UserSelectionDialog(
            onDismissRequest = { isShowDialog = false },
            isMultiSelect = isMultiSelect,
            onUserSelected = { birthDataList ->
                if (birthDataList.isEmpty()) {
                    return@UserSelectionDialog
                }
                selectedBirthDataList = birthDataList
                isShowDialog = false
            }
        )
    }


    // 在 imagePickerLauncher 回調中過濾大小
    val imagePickerLauncher =
        rememberLauncherForActivityResult(ActivityResultContracts.GetContent()) { uri ->
            uri?.let {
                val fileSizeMB = getFileSizeInMB(context, uri)
                if (fileSizeMB <= maxFileSizeMB) {
                    mediaUris = mediaUris + uri
                } else {
                    // 顯示檔案過大的提示，例如使用 Toast
                    Toast.makeText(context, "檔案大小超過 $maxFileSizeMB MB", Toast.LENGTH_SHORT)
                        .show()
                }
            }
        }

    LaunchedEffect(feedbackState) {
        when (feedbackState) {
            is FeedbackState.Success -> {
                title = ""
                content = ""
                mediaUris = emptyList()
                selectedChart = null
                selectedBirthDataList = null
                Toast.makeText(context, "提交成功", Toast.LENGTH_SHORT).show()
                backDispatcher?.onBackPressed()
            }

            else -> {

            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(10.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = it }
        ) {
            OutlinedTextField(
                value = selectedCategory,
                onValueChange = {},
                readOnly = true,
                label = { Text("問題類別") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                categories.forEach { category ->
                    DropdownMenuItem(
                        text = { Text(category) },
                        onClick = {
                            selectedCategory = category
                            expanded = false
                        }
                    )
                }
            }
        }

        OutlinedTextField(
            value = title,
            onValueChange = { title = it },
            label = { Text("標題") },
            modifier = Modifier.fillMaxWidth()
        )

        // 添加星盤種類選擇
        ExposedDropdownMenuBox(
            expanded = isChartExpanded,
            onExpandedChange = { isChartExpanded = it }
        ) {

            OutlinedTextField(
                value = context.getString(selectedChart?.type ?: R.string.select_chart_type),
                onValueChange = {},
                readOnly = true,
                label = { Text("星盤種類") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isChartExpanded) },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )

            ExposedDropdownMenu(
                expanded = isChartExpanded,
                onDismissRequest = { isChartExpanded = false }
            ) {
                Chart.entries.forEach { chart ->
                    DropdownMenuItem(
                        text = { Text(context.getString(chart.type)) },
                        onClick = {
                            selectedChart = chart
                            isChartExpanded = false
                        }
                    )
                }
            }
        }

        if (selectedChart != null) {
            Button(
                onClick = {
                    isShowDialog = true
                },
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Text("選擇出生資料")
            }
        }

        // 顯示選中的出生資料
        selectedBirthDataList?.let { birthDataList ->
            birthDataList.forEach { birthData ->
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(text = "姓名：${birthData.name}", fontSize = 12.sp)
                    Text(text = "出生日期：${birthData.generateBirthdayString()}", fontSize = 12.sp)
                    Text(text = "出生地點：${birthData.birthplaceArea}", fontSize = 12.sp)
                    Text(
                        text = "緯度：${birthData.birthplaceLatitude}, 經度：${birthData.birthplaceLongitude}",
                        fontSize = 12.sp
                    )
                }
            }

        }

        val maxCharCount = 100 // 限制最大字數

        OutlinedTextField(
            value = content,
            onValueChange = {
                if (it.length <= maxCharCount) {
                    content = it
                }
            },
            label = { Text("問題描述") },
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp),
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                IconButton(
                    onClick = {
                        if (mediaUris.size < maxMediaCount) {
                            imagePickerLauncher.launch("image/*,video/*")
                        } else {
                            Toast.makeText(
                                context,
                                "最多只能添加 $maxMediaCount 個影像",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    },
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(Icons.Default.Add, contentDescription = "添加媒體")
                }
                Text(
                    text = "新增相片",
                    fontSize = 12.sp,
                    modifier = Modifier.padding(top = 2.dp)
                )
            }

            var selectedUri by remember { mutableStateOf<Uri?>(null) }

            mediaUris.forEachIndexed { index, uri ->
                Row(
                    modifier = Modifier.padding(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = uri,
                        contentDescription = "Image from URI",
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .size(50.dp)
                            .clickable { selectedUri = uri } // 點擊圖片放大
                    )
                    IconButton(
                        onClick = { mediaUris = mediaUris.filterIndexed { i, _ -> i != index } },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(Icons.Default.Delete, contentDescription = "刪除影像")
                    }
                }
            }

            if (selectedUri != null) {
                Dialog(
                    onDismissRequest = { selectedUri = null }
                ) {
                    ImageFullScreenViewer(
                        mediaUris = listOf(selectedUri),
                        onClose = { selectedUri = null }
                    )
                }
            }
        }

        Button(
            onClick = {
                viewModel.submitFeedback(
                    title = title,
                    content = content,
                    category = selectedCategory,
                    mediaUris = mediaUris,
                    chartType = selectedChart,
                    birthDataList = selectedBirthDataList
                )
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 10.dp),
            enabled = title.isNotBlank() && content.isNotBlank() && feedbackState !is FeedbackState.Loading
        ) {
            Text("提交問題")
        }

        when (feedbackState) {
            is FeedbackState.Loading -> {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }

            is FeedbackState.Error -> {
                Text(
                    text = (feedbackState as FeedbackState.Error).message,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }

            else -> {}
        }
    }
}

// 獲取檔案大小的輔助函數
fun getFileSizeInMB(context: Context, uri: Uri): Long {
    val cursor = context.contentResolver.query(uri, null, null, null, null)
    return cursor?.use {
        val sizeIndex = it.getColumnIndex(OpenableColumns.SIZE)
        if (sizeIndex != -1 && it.moveToFirst()) {
            it.getLong(sizeIndex) / (1024 * 1024)
        } else 0
    } ?: 0
}