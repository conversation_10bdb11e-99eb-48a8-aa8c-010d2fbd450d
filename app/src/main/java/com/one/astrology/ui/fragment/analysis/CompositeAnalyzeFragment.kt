package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBarDouble
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil

class CompositeAnalyzeFragment : Fragment() {

    private val chatViewModel by viewModels<ChatViewModel>()
    private val chartName = "組合盤"

    override fun onResume() {
        super.onResume()
        requireActivity().title = getString(R.string.analysis)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AppTheme {
                    MainAnalyzeScreen(chatViewModel)
                }
            }
        }
    }


    @Composable
    fun MainAnalyzeScreen(viewModel: ChatViewModel) {
        val context = LocalContext.current
        val matchEvent by viewModel.matchEvent.observeAsState(MatchEvent())
        val isLoading by viewModel.isLoading.observeAsState(false)
        val data by viewModel.data.observeAsState(ReadingData("", "", ""))
        val errorMessage by viewModel.error.observeAsState("")

        LaunchedEffect(errorMessage) {
            if (matchEvent.birthDataA.name.isEmpty()) {
                LogUtil.d("errorMessage 更新: $errorMessage")
                viewModel.getTwoBirthData()
                if (matchEvent.birthDataA.name.isEmpty() || matchEvent.birthDataB.name.isEmpty()) {
                    return@LaunchedEffect
                }

                viewModel.getComposite(
                    context,
                    matchEvent.birthDataA,
                    matchEvent.birthDataB,
                )
                viewModel.init()
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            // 頂部標題欄
            TopBarDouble(
                matchEvent.horoscopeA.name,
                matchEvent.horoscopeA.getBirthdayString(),
                matchEvent.horoscopeB.name,
                matchEvent.horoscopeB.getBirthdayString(),
                onBirthDataSelected = { birthDataList ->
                    if (birthDataList.size >= 2) {
                        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                        val birthDataListSelected =
                            birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
                        birthDataListSelected.forEach {
                            it.isSelected = false
                            birthDataBox.put(it)
                        }
                        matchEvent.birthDataA = birthDataList[0]
                        matchEvent.birthDataA.isSelected = true
                        birthDataBox.put(matchEvent.birthDataA)
                        matchEvent.birthDataB = birthDataList[1]
                        matchEvent.birthDataB.isSelected = true
                        birthDataBox.put(matchEvent.birthDataB)

                        viewModel.getComposite(
                            context,
                            matchEvent.birthDataA,
                            matchEvent.birthDataB,
                        )
                        viewModel.init()
                    }
                }
            )

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = listOf(
                            "${chartName}整體命運" to "${chartName}：解析雙方命盤的互動關係與整體命運走向 (繁體中文):\n",
                            "${chartName}財富互動" to "${chartName}：探索雙方金錢觀、財務管理與物質需求的相互影響 (繁體中文):\n",
                            "${chartName}感情與婚姻" to "${chartName}：剖析雙方戀愛模式、親密關係與婚姻的發展趨勢 (繁體中文):\n",
                            "${chartName}家庭與親緣" to "${chartName}：探討雙方的家庭背景、親子關係及家族業力的交織與影響 (繁體中文):\n",
                            "${chartName}事業與志向" to "${chartName}：分析雙方職業目標、事業發展與合作機會的契合度 (繁體中文):\n",
                            "${chartName}人際與社交" to "${chartName}：解讀雙方的社交互動、合作模式與人際關係的影響 (繁體中文):\n",
                            "${chartName}健康與體質" to "${chartName}：比較雙方的身心健康、生活方式及壓力應對的差異 (繁體中文):\n",
                            "${chartName}挑戰與契機" to "${chartName}：揭示雙方關係中的挑戰、成長契機與可能的突破點 (繁體中文):\n"
                        )
                        CategoryList(
                            context,
                            categories,
                            chatViewModel,
                            matchEvent = matchEvent,
                            chart = Chart.Composite
                        )
                    }
                }
            }
        }
    }
}