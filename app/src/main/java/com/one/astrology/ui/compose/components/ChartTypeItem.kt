package com.one.astrology.ui.compose.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.R
import com.one.astrology.data.type.ChartType
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.cardElevation

@Composable
fun ChartTypeItem(
    content: ChartType.ChartTypeItem.Content,
    onClick: () -> Unit
) {
    Card(
        shape = AppShapes.small,
        elevation = cardElevation(),
        modifier = Modifier
            .padding(10.dp)
            .fillMaxWidth()
            .clickable(onClick = onClick)
    ) {
        Column(
            modifier = Modifier
                .background(colorResource(id = R.color.colorPrimary))
                .padding(10.dp)
        ) {
            Text(
                text = content.name,
                color = Color.White,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = content.description,
                color = Color.White,
                fontSize = 14.sp,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(10.dp),
            contentAlignment = Alignment.CenterEnd
        ) {
            Text(
                text = "查看星盤",
                color = colorResource(id = R.color.colorAccent),
                fontSize = 14.sp
            )
        }
    }
} 