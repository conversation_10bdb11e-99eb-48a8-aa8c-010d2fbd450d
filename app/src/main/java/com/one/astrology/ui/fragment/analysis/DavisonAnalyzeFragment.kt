package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBarDouble
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil

class DavisonAnalyzeFragment : Fragment() {

    private val chatViewModel by viewModels<ChatViewModel>()
    private var chartName = "時空盤"

    override fun onResume() {
        super.onResume()
        requireActivity().title = getString(R.string.analysis)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        chartName = getString(R.string.davison)
        return ComposeView(requireContext()).apply {
            setContent {
                AppTheme {
                    MainAnalyzeScreen(chatViewModel)
                }
            }
        }
    }


    @Composable
    fun MainAnalyzeScreen(viewModel: ChatViewModel) {
        val context = LocalContext.current
        val matchEvent by viewModel.matchEvent.observeAsState(MatchEvent())
        val isLoading by viewModel.isLoading.observeAsState(false)
        val data by viewModel.data.observeAsState(ReadingData("", "",""))
        val errorMessage by viewModel.error.observeAsState("")

        LaunchedEffect(errorMessage) {
            if (matchEvent.birthDataA.name.isEmpty()) {
                LogUtil.d("errorMessage 更新: $errorMessage")
                viewModel.getTwoBirthData()
                if (matchEvent.birthDataA.name.isEmpty() || matchEvent.birthDataB.name.isEmpty()) {
                    return@LaunchedEffect
                }

                viewModel.getDavison(
                    context,
                    matchEvent.birthDataA,
                    matchEvent.birthDataB,
                )
                viewModel.init()
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            // 頂部標題欄
            TopBarDouble(
                nameA = matchEvent.birthDataA.name,
                birthDataStringA = matchEvent.birthDataA.generateBirthdayString(),
                nameB = matchEvent.birthDataB.name,
                birthDataStringB = matchEvent.birthDataB.generateBirthdayString(),
                onBirthDataSelected = { birthDataList ->
                    if (birthDataList.size >= 2) {
                        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)

                        // 清除之前選中的出生資料
                        val birthDataListSelected = birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
                        birthDataListSelected.forEach {
                            it.isSelected = false
                            birthDataBox.put(it)
                        }

                        // 更新 matchEvent
                        matchEvent.birthDataA = birthDataList[0].apply { isSelected = true }
                        matchEvent.birthDataB = birthDataList[1].apply { isSelected = true }

                        // 儲存更新後的資料
                        birthDataBox.put(matchEvent.birthDataA)
                        birthDataBox.put(matchEvent.birthDataB)

                        // 呼叫 ViewModel 進行後續操作
                        viewModel.getDavison(
                            context,
                            matchEvent.birthDataA,
                            matchEvent.birthDataB
                        )
                        viewModel.init()
                    } else {
                        // 錯誤處理：資料不夠
                        LogUtil.e("TopBar", "不足兩筆選擇的出生資料")
                    }
                }
            )


            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = listOf(
                            "${chartName}整體命運" to "${chartName}：解析兩者在時空交織下的命運動態與關聯 (繁體中文):\n",
                            "${chartName}財富互動" to "${chartName}：探索雙方金錢觀、財務需求和資源共享的相互影響 (繁體中文):\n",
                            "${chartName}感情與婚姻" to "${chartName}：分析雙方在時空中點的戀愛與婚姻關係交互作用 (繁體中文):\n",
                            "${chartName}家庭與親緣" to "${chartName}：探討雙方的家庭背景、原生環境及家族業力如何交織影響彼此 (繁體中文):\n",
                            "${chartName}事業與志向" to "${chartName}：評估雙方在事業與志向中的合作機會與共同目標 (繁體中文):\n",
                            "${chartName}人際與社交" to "${chartName}：解析雙方在社交與人際互動中的契機與挑戰 (繁體中文):\n",
                            "${chartName}健康與體質" to "${chartName}：比較雙方身心健康、生活方式及壓力應對的互動影響 (繁體中文):\n",
                            "${chartName}挑戰與契機" to "${chartName}：揭示雙方在時空背景中的挑戰、成長契機與突破點 (繁體中文):\n"
                        )
                        CategoryList(
                            context,
                            categories,
                            chatViewModel,
                            matchEvent,
                            chart = Chart.Davison
                        )
                    }
                }
            }
        }
    }


}