package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBar
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil


/**
 * 本命盤分析
 */
class NatalAnalyzeFragment : Fragment() {

    private val chatViewModel by viewModels<ChatViewModel>()
    private var chartName = "本命盤"

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                MainAnalyzeScreen(
                    chatViewModel
                )
            }
        }
    }

    @Composable
    fun MainAnalyzeScreen(viewModel: ChatViewModel) {
        val context = LocalContext.current
        var horoscope by remember { mutableStateOf(Horoscope()) }
        var birthData by remember { mutableStateOf(BirthData()) }
        val clipboardManager = LocalClipboardManager.current

        val isLoading by viewModel.isLoading.observeAsState(false)
        val data by viewModel.data.observeAsState(ReadingData("", "", ""))
        val errorMessage by viewModel.error.observeAsState("")

        LaunchedEffect(errorMessage) {
            LogUtil.d("errorMessage 更新: $errorMessage")
            birthData = viewModel.getBirthData()
            if (birthData.name.isEmpty()) {
                return@LaunchedEffect
            }
            horoscope = viewModel.calculateHoroscope(context, Chart.Natal, birthData)
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            // 頂部標題欄
            TopBar(
                name = horoscope.name,
                birthDataString = horoscope.getBirthdayString(),
                onBirthDataSelected = { birthDataList ->
                    val selectedBirthData = birthDataList[0]
                    if (selectedBirthData.name != birthData.name) {
                        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                        birthData = viewModel.getBirthData()
                        if (birthData.name.isNotEmpty()) {
                            birthData.isSelected = false
                            birthDataBox.put(birthData)
                        }
                        birthData = selectedBirthData
                        birthData.isSelected = true
                        birthDataBox.put(birthData)
                        horoscope = viewModel.calculateHoroscope(context, Chart.Natal, birthData)
                        viewModel.init()
                    }
                },
                onCopy = {
                   val text = horoscope.toAllString(context)
                    clipboardManager.setText(AnnotatedString(text))
                    Toast.makeText(context, "已複製到剪貼簿", Toast.LENGTH_SHORT).show()
                })
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp), contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = listOf(
                            "${chartName}整體命運" to "${chartName}：剖析天賦、性格特質與生命方向 (繁體中文):\n",
                            "${chartName}財富格局" to "${chartName}：探索財富累積模式、金錢觀與物質需求 (繁體中文):\n",
                            "${chartName}感情與婚姻" to "${chartName}：分析戀愛模式、伴侶特質與婚姻趨勢 (繁體中文):\n",
                            "${chartName}家庭與原生環境" to "${chartName}：探討家庭影響、親子關係與家族業力 (繁體中文):\n",
                            "${chartName}事業與志向" to "${chartName}：規劃職涯發展、個人成就與人生目標 (繁體中文):\n",
                            "${chartName}社交與人際關係" to "${chartName}：解析交友模式、影響力與人際互動 (繁體中文):\n",
                            "${chartName}健康與體質" to "${chartName}：關注身心健康、體質特徵與壓力管理 (繁體中文):\n",
                            "${chartName}靈魂課題與成長" to "${chartName}：揭示人生課題、內在挑戰與靈性成長 (繁體中文):\n"
                        )
                        CategoryList(
                            context,
                            categories,
                            viewModel,
                            horoscope = horoscope,
                            chart = Chart.Natal
                        )
                    }
                }
            }
        }
    }

}