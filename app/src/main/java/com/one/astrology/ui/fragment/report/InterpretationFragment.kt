package com.one.astrology.ui.fragment.report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.tabs.TabLayout
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.DescData
import com.one.astrology.data.Horoscope
import com.one.astrology.data.InterpretationType
import com.one.astrology.data.PdfData
import com.one.astrology.data.PdfItemData
import com.one.astrology.data.SignPosition
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.db.FlyData
import com.one.astrology.data.db.HouseDescData
import com.one.astrology.data.db.SignDescData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentInterpretationBinding
import com.one.astrology.db.DBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.topic.CareerAnalyze
import com.one.astrology.ui.fragment.ExplainFragment
import com.one.astrology.ui.fragment.SubscriptionFragment
import com.one.astrology.ui.fragment.report.adapter.AspectDescItemAdapter
import com.one.astrology.ui.fragment.report.adapter.DescItemAdapter
import com.one.astrology.ui.fragment.report.adapter.FlyItemAdapter
import com.one.astrology.ui.fragment.report.adapter.HouseDescItemAdapter
import com.one.astrology.ui.fragment.report.adapter.SignDescItemAdapter
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.FileUtil
import com.one.astrology.util.IntentUtil
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil


/**
 * 解讀頁
 */
class InterpretationFragment : Fragment(R.layout.fragment_interpretation) {

    private lateinit var binding: FragmentInterpretationBinding
    private var title: String? = null
    private var matchEvent: MatchEvent? = null
    private val signList = ArrayList<SignDescData>()
    private val houseList = ArrayList<HouseDescData>()
    private var aspectList = ArrayList<AspectData>()
    private var flyList = ArrayList<FlyData>()
    private var pdfData = PdfData()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentInterpretationBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("解讀頁", this.javaClass.simpleName)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        collectData()
        initView()
    }

    private fun collectData() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    private fun initView() {
        expand(binding.recyclerViewSign, binding.tvSignPosition)
        expand(binding.recyclerViewHouse, binding.tvHouse)
        expand(binding.recyclerViewAspect, binding.tvAspect)
        expand(binding.recyclerViewHouseSign, binding.tvHouseSign)
        expand(binding.recyclerViewFly, binding.tvFly)
        binding.fabUpload.setOnClickListener {
            if (BuildConfig.IS_DEV || PermissionsUtil.permissions.canSavePdf) {
                val file = FileUtil.generatePdf(requireContext(), pdfData)
                if (file != null) {
                    IntentUtil.shareFile(requireContext(), file)
                }
            } else {
                // 導訂閱頁
                SubscriptionFragment().show(childFragmentManager, "")
//                Toast.makeText(requireContext(), "訂閱方案即可使用此功能！", Toast.LENGTH_SHORT)
//                    .show()
            }
        }

        binding.nestedScrollView.setOnScrollChangeListener { _, _, _, _, oldScrollY ->
            if (oldScrollY < 0) {
                binding.fabUpload.hide()
            } else {
                binding.fabUpload.show()
            }
        }
        binding.nestedScrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY -> // the delay of the extension of the FAB is set for 12 items
            if (scrollY > oldScrollY + 2 && binding.fabUpload.isShown) {
                binding.fabUpload.hide()
            }

            // the delay of the extension of the FAB is set for 12 items
            if (scrollY < oldScrollY - 2 && !binding.fabUpload.isShown) {
                binding.fabUpload.show()
            }

            // if the nestedScrollView is at the first item of the list then the
            // extended floating action should be in extended state
            if (scrollY == 0) {
                binding.fabUpload.show()
            }
        })
//        setOnScrollChangeListener(binding.recyclerViewSign)
//        setOnScrollChangeListener(binding.recyclerViewHouse)
//        setOnScrollChangeListener(binding.recyclerViewAspect)
//        setOnScrollChangeListener(binding.recyclerViewHouseSign)
//        setOnScrollChangeListener(binding.recyclerViewFly)
//        setOnScrollChangeListener(binding.recyclerViewCareer)
    }

    private fun setOnScrollChangeListener(recyclerView: RecyclerView) {
        recyclerView.setOnScrollChangeListener { _, _, _, _, oldScrollY ->
            if (oldScrollY < 0) {
                binding.fabUpload.hide()
            } else {
                binding.fabUpload.show()
            }
        }
    }

    private fun initTab() {
        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                    when (tab.text) {
                        getString(R.string.planet_sign) -> {
                            binding.tvSignPosition.visibility = View.GONE
                            binding.recyclerViewSign.visibility = View.VISIBLE

                            binding.tvHouse.visibility = View.GONE
                            binding.recyclerViewHouse.visibility = View.GONE

                            binding.tvAspect.visibility = View.GONE
                            binding.recyclerViewAspect.visibility = View.GONE

                            binding.tvHouseSign.visibility = View.GONE
                            binding.recyclerViewHouseSign.visibility = View.GONE

                            binding.tvFly.visibility = View.GONE
                            binding.recyclerViewFly.visibility = View.GONE

                            binding.tvCareer.visibility = View.GONE
                            binding.recyclerViewCareer.visibility = View.GONE
                        }

                        getString(R.string.planet_house) -> {
                            binding.tvSignPosition.visibility = View.GONE
                            binding.recyclerViewSign.visibility = View.GONE

                            binding.tvHouse.visibility = View.GONE
                            binding.recyclerViewHouse.visibility = View.VISIBLE

                            binding.tvAspect.visibility = View.GONE
                            binding.recyclerViewAspect.visibility = View.GONE

                            binding.tvHouseSign.visibility = View.GONE
                            binding.recyclerViewHouseSign.visibility = View.GONE

                            binding.tvFly.visibility = View.GONE
                            binding.recyclerViewFly.visibility = View.GONE

                            binding.tvCareer.visibility = View.GONE
                            binding.recyclerViewCareer.visibility = View.GONE
                        }

                        getString(R.string.planet_aspect) -> {
                            binding.tvSignPosition.visibility = View.GONE
                            binding.recyclerViewSign.visibility = View.GONE

                            binding.tvHouse.visibility = View.GONE
                            binding.recyclerViewHouse.visibility = View.GONE

                            binding.tvAspect.visibility = View.GONE
                            binding.recyclerViewAspect.visibility = View.VISIBLE

                            binding.tvHouseSign.visibility = View.GONE
                            binding.recyclerViewHouseSign.visibility = View.GONE

                            binding.tvFly.visibility = View.GONE
                            binding.recyclerViewFly.visibility = View.GONE

                            binding.tvCareer.visibility = View.GONE
                            binding.recyclerViewCareer.visibility = View.GONE
                        }

                        getString(R.string.house_sign_desc) -> {
                            binding.tvSignPosition.visibility = View.GONE
                            binding.recyclerViewSign.visibility = View.GONE

                            binding.tvHouse.visibility = View.GONE
                            binding.recyclerViewHouse.visibility = View.GONE

                            binding.tvAspect.visibility = View.GONE
                            binding.recyclerViewAspect.visibility = View.GONE

                            binding.tvHouseSign.visibility = View.GONE
                            binding.recyclerViewHouseSign.visibility = View.VISIBLE

                            binding.tvFly.visibility = View.GONE
                            binding.recyclerViewFly.visibility = View.GONE

                            binding.tvCareer.visibility = View.GONE
                            binding.recyclerViewCareer.visibility = View.GONE
                        }

                        getString(R.string.flying_star) -> {
                            binding.tvSignPosition.visibility = View.GONE
                            binding.recyclerViewSign.visibility = View.GONE

                            binding.tvHouse.visibility = View.GONE
                            binding.recyclerViewHouse.visibility = View.GONE

                            binding.tvAspect.visibility = View.GONE
                            binding.recyclerViewAspect.visibility = View.GONE

                            binding.tvHouseSign.visibility = View.GONE
                            binding.recyclerViewHouseSign.visibility = View.GONE

                            binding.tvFly.visibility = View.GONE
                            binding.recyclerViewFly.visibility = View.VISIBLE

                            binding.tvCareer.visibility = View.GONE
                            binding.recyclerViewCareer.visibility = View.GONE
                        }

                        getString(R.string.career) -> {
                            binding.tvSignPosition.visibility = View.GONE
                            binding.recyclerViewSign.visibility = View.GONE

                            binding.tvHouse.visibility = View.GONE
                            binding.recyclerViewHouse.visibility = View.GONE

                            binding.tvAspect.visibility = View.GONE
                            binding.recyclerViewAspect.visibility = View.GONE

                            binding.tvHouseSign.visibility = View.GONE
                            binding.recyclerViewHouseSign.visibility = View.GONE

                            binding.tvFly.visibility = View.GONE
                            binding.recyclerViewFly.visibility = View.GONE

                            binding.tvCareer.visibility = View.GONE
                            binding.recyclerViewCareer.visibility = View.VISIBLE
                        }
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }
        })
    }

    private fun expand(recyclerView: RecyclerView, textView: TextView) {
        textView.setOnClickListener {
            if (recyclerView.visibility == View.VISIBLE) {
                textView.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    0,
                    0,
                    R.drawable.baseline_expand_less_24,
                    0
                )
                recyclerView.visibility = View.GONE
            } else {
                recyclerView.visibility = View.VISIBLE
                textView.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    0,
                    0,
                    R.drawable.baseline_expand_more_24,
                    0
                )
            }
        }
    }

    private fun clearData() {
        signList.clear()
        houseList.clear()
        aspectList.clear()
        flyList.clear()
        pdfData = PdfData()
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
            clearData()
            title = getString(matchEvent.chartType.type)
            this.matchEvent = matchEvent

            pdfData.name = matchEvent.horoscopeA.name
            var chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
            var pdfTitle = getString(R.string.pdf_title, chartName)
            binding.tvTitle.text = pdfTitle
            pdfData.chart = matchEvent.chartType

            when (matchEvent.chartType) {
                Chart.Natal -> {
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.Transit -> {
                    binding.tvFly.visibility = View.GONE
                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.SecondaryProgression -> {
                    binding.tvSignPosition.visibility = View.GONE
                    binding.recyclerViewSign.visibility = View.GONE
                    binding.tvFly.visibility = View.GONE
                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.TertiaryProgression -> {
                    binding.tvSignPosition.visibility = View.GONE
                    binding.recyclerViewSign.visibility = View.GONE
                    binding.tvFly.visibility = View.GONE
                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                Chart.Synastry -> {
                    pdfData.name = getString(
                        R.string.synastry_name,
                        matchEvent.horoscopeB.name,
                        matchEvent.horoscopeA.name
                    )
                    chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
                    pdfTitle = getString(R.string.pdf_title, chartName)
                    binding.tvTitle.text = pdfTitle
                    binding.tvSignPosition.visibility = View.GONE
                    binding.recyclerViewSign.visibility = View.GONE
                    binding.tvFly.visibility = View.GONE
                    binding.recyclerViewFly.visibility = View.GONE
                    matchEvent.horoscopeB.aspectList =
                        EphemerisUtil.aspects(
                            requireContext(),
                            matchEvent.chartType,
                            matchEvent.horoscopeB.planetList,
                            matchEvent.horoscopeA.planetList,
                            isMatch = true,
                            isGetScore = false
                        )
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.Composite -> {
                    pdfData.name = getString(
                        R.string.both_name,
                        matchEvent.horoscopeA.name,
                        matchEvent.horoscopeB.name
                    )
                    chartName = pdfData.name + " " + getString(matchEvent.chartType.type)
                    pdfTitle = getString(R.string.pdf_title, chartName)
                    binding.tvTitle.text = pdfTitle
                    (binding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(3).visibility =
                        View.GONE
                    (binding.tabLayout.getChildAt(0) as ViewGroup).getChildAt(4).visibility =
                        View.GONE
                    binding.tvFly.visibility = View.GONE
                    binding.recyclerViewFly.visibility = View.GONE
                    initData(matchEvent.horoscopeA, matchEvent.horoscopeA)
                }

                else -> {

                }
            }

            initTab()
        }
    }

    private fun initData(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        initSign(horoscopeA, horoscopeB)
        initAspect(horoscopeA)
        initFly(horoscopeA)
        initCareer(horoscopeA)
    }

    private fun initCareer(horoscopeA: Horoscope) {
        val descList = CareerAnalyze.analyze(requireContext(), horoscopeA)
        initRecycleViewCareer(descList)
    }

    private fun initRecycleViewCareer(descList: ArrayList<DescData>) {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewCareer.layoutManager = layoutManager
        val adapter = DescItemAdapter()
        adapter.isEmptyViewEnable = true
        adapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        binding.recyclerViewCareer.adapter = adapter
        adapter.submitList(descList)

        adapter.addOnItemChildClickListener(R.id.tvMore) { _, _, position ->

        }
    }

    private fun initSign(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        val signList = AssetsToObjectUtil.getSignList(requireContext())
        val planetBeanList =
            horoscopeA.getPlanetBeanList().sortedWith(compareBy { it.id })
        for (planet in planetBeanList) {
            if (!planet.isChecked) {
                continue
            }
            val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
            val signBean: SignBean = signList[strings[0].toInt()]
            val signPosition = SignPosition(signBean)
            signPosition.houseData = EphemerisUtil.house(planet.longitude, horoscopeB.houses.cusps)
            LogUtil.i("星位 " + planet.chName + " " + signBean.chName + strings[1] + "°" + strings[2] + signPosition.houseData!!.index + "宮\n")

            querySign(planet.chName, signBean.chName)
            if (signPosition.houseData == null) {
                continue
            }
            queryHouse(signPosition.houseData!!.index, planet.chName)
        }
        initRecycleViewSign()
        initRecycleViewSignHouse()
    }

    private fun initAspect(horoscopeA: Horoscope) {
        for (aspect in horoscopeA.aspectList) {
            queryAspect(aspect)
        }
        initRecycleViewAspect()
    }

    private fun initFly(horoscopeA: Horoscope) {
        for (i in 0 until horoscopeA.houses.signBeanList.size) {
            val signBean = horoscopeA.houses.signBeanList[i]
            val planetList = horoscopeA.planetList
            val rulers = signBean.ruler.split("、")
            for (ruler in rulers) {
                val planet = planetList.find {
                    it.chName == ruler
                }
                if (planet != null) {
                    queryFly(i + 1, planet.signBean.houseData.index)
                }
            }
        }
        initRecycleViewFly()
    }

    private fun initRecycleViewSign() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewSign.layoutManager = layoutManager
        val signDescItemAdapter = SignDescItemAdapter()
        signDescItemAdapter.isEmptyViewEnable = true
        signDescItemAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        binding.recyclerViewSign.adapter = signDescItemAdapter
        signDescItemAdapter.submitList(signList)
        signDescItemAdapter.addOnItemChildClickListener(R.id.tvMore) { _, _, position ->
            val item = signDescItemAdapter.items[position]
            val title = getString(R.string.planet_sign_title, item.planet, item.sign)
            toExplain(title, item.descList)
        }
    }

    private fun initRecycleViewSignHouse() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewHouse.layoutManager = layoutManager
        val houseDescItemAdapter = HouseDescItemAdapter()
        houseDescItemAdapter.isEmptyViewEnable = true
        houseDescItemAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        binding.recyclerViewHouse.adapter = houseDescItemAdapter
        houseDescItemAdapter.submitList(houseList)

        houseDescItemAdapter.addOnItemChildClickListener(R.id.tvMore) { _, _, position ->
            val item = houseDescItemAdapter.items[position]
            val title = getString(R.string.sign_house_title, item.planet, item.house)
            toExplain(title, item.descList)
        }
    }

    private fun initRecycleViewAspect() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewAspect.layoutManager = layoutManager
        val aspectDescItemAdapter = AspectDescItemAdapter()
        aspectDescItemAdapter.isEmptyViewEnable = true
        aspectDescItemAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        binding.recyclerViewAspect.adapter = aspectDescItemAdapter
        aspectDescItemAdapter.submitList(aspectList)

        aspectDescItemAdapter.addOnItemChildClickListener(R.id.tvMore) { _, _, position ->
            val item = aspectDescItemAdapter.items[position]
            val title = getString(
                R.string.planet_aspect_title,
                item.planetA,
                item.type,
                item.planetB
            )
            toExplain(title, item.descList)
        }
    }

    private fun initRecycleViewFly() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerViewFly.layoutManager = layoutManager
        val flyItemAdapter = FlyItemAdapter()
        flyItemAdapter.isEmptyViewEnable = true
        flyItemAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        binding.recyclerViewFly.adapter = flyItemAdapter
        flyItemAdapter.submitList(flyList)

        flyItemAdapter.addOnItemChildClickListener(R.id.tvMore) { _, _, position ->
            val item = flyItemAdapter.items[position]
            val title = getString(R.string.flying_title, item.houseIndex, item.flyIndex)
            toExplain(title, item.descList)
        }
    }

    private fun toExplain(title: String, descList: ArrayList<String>) {
        if (descList.size > 1) {
            ExplainFragment.newInstance(title, descList)
                .show(requireActivity().supportFragmentManager, "")
        } else {
            com.one.core.util.IntentUtil.searchWeb(requireActivity(), title)
        }
    }

    private fun querySign(planetName: String, signName: String) {
        val data = DBHelper.querySign(
            requireContext(),
            matchEvent?.chartType!!, planetName, signName
        )
        if (data != null) {
            signList.add(data)
            val pdfItemData = PdfItemData()
            pdfItemData.type = InterpretationType.sign
            pdfItemData.subTitle = getString(R.string.planet_sign_title, data.planet, data.sign)
            if (data.descList.isNotEmpty()) {
                pdfItemData.desc = data.descList[0]
            }
            pdfItemData.descList = data.descList
            pdfData.pdfItemData.add(pdfItemData)
        }
    }

    private fun queryHouse(house: Int, planetName: String) {
        val data = DBHelper.queryHouse(
            requireContext(),
            matchEvent?.chartType!!,
            house,
            planetName
        )
        if (data != null) {
            houseList.add(data)
            val pdfItemData = PdfItemData()
            pdfItemData.type = InterpretationType.house
            pdfItemData.subTitle = getString(R.string.sign_house_title, data.planet, data.house)
            if (data.descList.isNotEmpty()) {
                pdfItemData.desc = data.descList[0]
            }
            pdfItemData.descList = data.descList
            pdfData.pdfItemData.add(pdfItemData)
        }
    }

    private fun queryAspect(aspect: AspectData) {
        val data = DBHelper.queryAspect(
            requireContext(),
            matchEvent?.chartType!!,
            aspect.planetA,
            aspect.degree,
            aspect.planetB,
        )
        val findAspect = aspectList.find {
            it.planetA == aspect.planetB && it.planetB == aspect.planetA
        }
        if (findAspect != null) {
            return
        }
        if (data != null) {
            data.type = aspect.type
            if (data.desc.isNullOrEmpty()) {
                return
            }
            aspectList.add(data)
            val pdfItemData = PdfItemData()
            pdfItemData.type = InterpretationType.aspect
            pdfItemData.subTitle = getString(
                R.string.planet_aspect_title,
                data.planetA,
                data.type,
                data.planetB
            )
            if (data.descList.isNotEmpty()) {
                pdfItemData.desc = data.descList[0]
            }
            pdfItemData.descList = data.descList
            pdfData.pdfItemData.add(pdfItemData)
        }
    }

    private fun queryFly(houseIndex: Int, flyIndex: Int) {
        val data = DBHelper.queryFly(
            requireContext(),
            matchEvent?.chartType!!,
            houseIndex,
            flyIndex
        )
        if (data != null) {
            flyList.add(data)
            val pdfItemData = PdfItemData()
            pdfItemData.type = InterpretationType.fly
            pdfItemData.subTitle = getString(R.string.flying_title, data.houseIndex, data.flyIndex)
            if (data.descList.isNotEmpty()) {
                pdfItemData.desc = data.descList[0]
            }
            pdfItemData.descList = data.descList
            pdfData.pdfItemData.add(pdfItemData)
        }
    }
}