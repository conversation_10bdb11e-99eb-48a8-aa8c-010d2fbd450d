package com.one.astrology.ui.fragment.analysis.screen.components

import android.content.Context
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.aiModels
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.analysis.screen.CategoryButton
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.rememberRemoteConfigState
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.RemoteConfigManager
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil

@Composable
fun CategoryList(
    context: Context,
    categories: List<Pair<String, String>>,
    chatViewModel: ChatViewModel,
    matchEvent: MatchEvent? = null,
    horoscope: Horoscope? = null,
    chart: Chart
) {
    if (!isConfigLoaded()) {
        CircularProgressIndicator()
        return
    }

    val (aiModel, groqApiKey, openAiApiKey) = getApiKeys(context)
    if (aiModel == null) {
        ErrorMessage("目前無法使用此服務")
        return
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(10.dp),
        verticalArrangement = Arrangement.spacedBy(5.dp)
    ) {
        items(categories) { (title, prompt) ->
            CategoryButton(title) {
                handleCategoryClick(
                    context,
                    chatViewModel,
                    matchEvent,
                    horoscope,
                    chart,
                    title,
                    prompt,
                    aiModel,
                    groqApiKey,
                    openAiApiKey
                )
            }
        }
        item { QuestionForm(context, chatViewModel, chart, matchEvent, horoscope) }
    }
}

@Composable
fun isConfigLoaded(): Boolean {
    val isLoaded = rememberRemoteConfigState()
    return isLoaded.value
}

fun getApiKeys(context: Context): Triple<String?, String, String> {
    val groqApiKey = RemoteConfigManager.getString("groqApiKey")
    val groqAiModel = RemoteConfigManager.getString("groqAiModel")
    val openAiApiKey = RemoteConfigManager.getString("openAiApiKey")
    var aiModel = RemoteConfigManager.getString("aiModel")

    if (BuildConfig.IS_DEV) {
        aiModel = EncryptedSPUtil.getAIModule(context)
    } else if (listOf(groqApiKey, groqAiModel, openAiApiKey, aiModel).any { it.isEmpty() }) {
        return Triple(null, "", "")
    }
    return Triple(aiModel, groqApiKey, openAiApiKey)
}

fun handleCategoryClick(
    context: Context,
    chatViewModel: ChatViewModel,
    matchEvent: MatchEvent?,
    horoscope: Horoscope?,
    chart: Chart,
    title: String,
    prompt: String,
    aiModel: String,
    groqApiKey: String,
    openAiApiKey: String
) {
    val userInfoAvailable =
        matchEvent?.planetListA?.isNotEmpty() ?: horoscope?.name?.isNotEmpty() ?: false
    if (!userInfoAvailable) {
        chatViewModel.setErrorMessage("沒有設定用戶資訊")
        return
    }

    val chartName = context.getString(chart.type)
    val text = buildString {
        append(prompt)
        matchEvent?.let { append(it.toAllStringA(chartName)) }
        horoscope?.let { append(it.toAllString(context)) }
    }
    if (matchEvent != null) {
        chatViewModel.readingData.birthDataA = matchEvent.birthDataA
        chatViewModel.readingData.birthDataB = matchEvent.birthDataB
    }
    if (horoscope != null) {
        chatViewModel.readingData.birthDataA = BirthData(horoscope)
    }
    chatViewModel.readingData.chart = chart
    chatViewModel.readingData.chartInfo = text

    val isForceUpdate = EncryptedSPUtil.getForcedUpdate(context)

    val aiModules = aiModels.map { Pair(it.id, it.developer) }.toTypedArray()
    val ai = aiModules.find { it.first == aiModel }
    if (ai != null) {
        if (ai.second == "OpenAI") {
            chatViewModel.fetchGPTResponse(
                context, matchEvent?.horoscopeA ?: horoscope!!, title, text, aiModel, openAiApiKey
            )
        } else {
            chatViewModel.fetchGroqResponse(
                isForceUpdate, matchEvent?.horoscopeA ?: horoscope!!, title, text, aiModel, groqApiKey
            )
        }
    }
    LogUtil.d(text)
}

@Composable
fun QuestionForm(
    context: Context,
    chatViewModel: ChatViewModel,
    chart: Chart,
    matchEvent: MatchEvent? = null,
    horoscope: Horoscope? = null
) {
    val (aiModel, groqApiKey, openAiApiKey) = getApiKeys(context)
    var questionText by remember { mutableStateOf("") }

    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = 6.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 0.dp, vertical = 10.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "想問更多問題？",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )

            Spacer(modifier = Modifier.height(12.dp))

            OutlinedTextField(
                value = questionText,
                onValueChange = { questionText = it },
                placeholder = { Text("請輸入您的問題", fontSize = 16.sp) },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(90.dp),
                shape = RoundedCornerShape(12.dp),
                colors = TextFieldDefaults.colors(
                    focusedContainerColor = Color.Transparent, // 選中時的背景
                    unfocusedContainerColor = Color.Transparent, // 未選中時的背景
                    focusedIndicatorColor = colorResource(id = R.color.colorPrimary), // 選中時的邊框顏色
                    unfocusedIndicatorColor = Color.Gray.copy(alpha = 0.5f), // 未選中時的邊框顏色
                    cursorColor = colorResource(id = R.color.colorPrimary) // 游標顏色
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = {
                    val question = "\n問題 : $questionText\n"
                    handleCategoryClick(
                        context,
                        chatViewModel,
                        matchEvent,
                        horoscope,
                        chart = chart,
                        title = "自定義問題",
                        prompt = question,
                        aiModel = aiModel ?: "",
                        groqApiKey = groqApiKey,
                        openAiApiKey = openAiApiKey
                    )
                },
                colors = ButtonDefaults.buttonColors(containerColor = colorResource(id = R.color.colorPrimary)),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .align(Alignment.CenterHorizontally)
            ) {
                Text("提交問題", fontSize = 16.sp, fontWeight = FontWeight.Bold, color = Color.White)
            }
        }
    }
}
