package com.one.astrology.ui.fragment.report.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.data.DescData
import com.one.astrology.databinding.ItemDescBinding

class DescItemAdapter :
    BaseQuickAdapter<DescData, DescItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemDescBinding = ItemDescBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: BaseViewHolder,
        position: Int,
        item: DescData?
    ) {
        if (item != null) {
            holder.binding.tvDesc.text = item.desc
            holder.binding.tvTitle.text = item.title
            if (item.aspect != null) {
                holder.binding.tvSubTitle.text = "有刑冲相位"
                holder.binding.tvSubTitle.visibility = View.VISIBLE
            } else {
                holder.binding.tvSubTitle.visibility = View.GONE
            }
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}