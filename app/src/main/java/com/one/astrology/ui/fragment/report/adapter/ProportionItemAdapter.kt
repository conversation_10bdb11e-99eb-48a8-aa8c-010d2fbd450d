package com.one.astrology.ui.fragment.report.adapter


import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.R
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.databinding.ItemProportionBinding

class ProportionItemAdapter(val type: Int) :
    BaseQuickAdapter<PlanetBean, ProportionItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemProportionBinding = ItemProportionBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: <PERSON><PERSON><PERSON>wHolder,
        position: Int,
        item: PlanetBean?
    ) {
        if (item != null) {
            holder.binding.tvPlanetName.text = item.chName
            holder.binding.tvSignName.text = item.signBean.chName
            when (type) {
                1 -> {
                    if (item.signBean.yinYang == "陽性") {
                        setTextColor(holder.binding.tvPlanetName, R.color.red)
                        setTextColor(holder.binding.tvSignName, R.color.red)
                    } else {
                        setTextColor(holder.binding.tvPlanetName, R.color.azul)
                        setTextColor(holder.binding.tvSignName, R.color.azul)
                    }
                }

                2 -> {
                    when (item.signBean.triplicities) {
                        "火象" -> {
                            setTextColor(holder.binding.tvPlanetName, R.color.red)
                            setTextColor(holder.binding.tvSignName, R.color.red)
                        }

                        "土象" -> {
                            setTextColor(holder.binding.tvPlanetName, R.color.colorPrimaryYellow)
                            setTextColor(holder.binding.tvSignName, R.color.colorPrimaryYellow)
                        }

                        "風象" -> {
                            setTextColor(holder.binding.tvPlanetName, R.color.green)
                            setTextColor(holder.binding.tvSignName, R.color.green)
                        }

                        "水象" -> {
                            setTextColor(holder.binding.tvPlanetName, R.color.azul)
                            setTextColor(holder.binding.tvSignName, R.color.azul)
                        }
                    }
                }

                3 -> {
                    when (item.signBean.quadruplicities) {
                        "開創" -> {
                            setTextColor(holder.binding.tvPlanetName, R.color.red)
                            setTextColor(holder.binding.tvSignName, R.color.red)
                        }

                        "固定" -> {
                            setTextColor(holder.binding.tvPlanetName, R.color.colorPrimaryYellow)
                            setTextColor(holder.binding.tvSignName, R.color.colorPrimaryYellow)
                        }

                        "變動" -> {
                            setTextColor(holder.binding.tvPlanetName, R.color.azul)
                            setTextColor(holder.binding.tvSignName, R.color.azul)
                        }
                    }
                }
            }
        }
    }

    private fun setTextColor(textView: TextView, id: Int) {
        textView.setTextColor(ContextCompat.getColor(context, id))
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}