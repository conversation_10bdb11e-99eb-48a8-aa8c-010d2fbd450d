package com.one.astrology.ui.fragment.report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.work.Data
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.list.listItems
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.button.MaterialButtonToggleGroup
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.loper7.date_time_picker.DateTimeConfig
import com.loper7.date_time_picker.dialog.CardDatePickerDialog
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.adapter.ThemeAdapter
import com.one.astrology.data.Horoscope
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentChartBinding
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateViewModel
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateWorker
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculationState
import com.one.astrology.ui.view.DrawView
import com.one.astrology.ui.view.DrawViewPro
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.FileUtil
import com.one.astrology.util.IntentUtil
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date
import java.util.concurrent.TimeUnit


/**
 * 星盤圖頁
 */
@AndroidEntryPoint
class ChartFragment : BaseFragment(R.layout.fragment_chart) {
    private lateinit var binding: FragmentChartBinding
    private val viewModel by activityViewModels<CalculateViewModel>()
    private var isAnimator = false
    private var isShowRuler = false
    private var timeStep: Long = (5 * 60 * 1000)
    private var timeStepB: Long = (5 * 60 * 1000)
    private var chartType: Chart = Chart.Natal
    private var isInit = false
    private var isSameChart = false
    private lateinit var drawView: DrawView
    private lateinit var drawViewPro: DrawViewPro
    private var isSymbolMode = false

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("星盤圖頁", this.javaClass.simpleName)
    }

    override fun onPause() {
        super.onPause()
        drawView.onPause()
        drawViewPro.onPause()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChartBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isShowRuler = EncryptedSPUtil.getPlanetRuler(requireContext())
        isAnimator = EncryptedSPUtil.getChartAnimation(requireContext())
        isSymbolMode = EncryptedSPUtil.getSymbolMode(requireContext()) // 讀取保存的模式
        drawView = DrawView(requireContext(), null)
        drawViewPro = DrawViewPro(requireContext(), null)
        observeCalculationState()
        initView()
    }

    private fun observeCalculationState() {
        lifecycleScope.launch {
            viewModel.calculationState.collect { state ->
                when (state) {
                    is CalculationState.Calculating -> {
                        binding.loadingProgress.visibility = View.VISIBLE
                    }

                    is CalculationState.Success -> {
                        binding.loadingProgress.visibility = View.GONE
                    }

                    is CalculationState.Error -> {
                        binding.loadingProgress.visibility = View.GONE
                    }

                    else -> {
                        // do nothing
                    }
                }
            }
        }
    }

    fun initView() {
        // 初始化時讀取保存的模式
        isSymbolMode = EncryptedSPUtil.getSymbolMode(requireContext())


        // 根據保存的模式添加對應的視圖
        binding.drawContainer.removeAllViews()
        if (isSymbolMode) {
            binding.drawContainer.addView(drawViewPro)
        } else {
        binding.drawContainer.addView(drawView)
        }

        initHouseSystem()
        initArrayAdapter()
        collectData()
        if (BuildConfig.IS_DEV || PermissionsUtil.permissions.canSaveImage) {
            binding.fabSave.visibility = View.VISIBLE
            binding.fabSave.setOnClickListener {
                launchWhenStarted {
                    val chartName = getString(chartType.type)
                    val fileName = "${viewModel.matchEvent.horoscopeA.name} $chartName.jpg"
                    var file =
                        FileUtil.saveViewToJpg(requireActivity(), drawView, fileName)
                    if (isSymbolMode) {
                        file =
                            FileUtil.saveViewToJpg(requireActivity(), drawViewPro, fileName)
                    }

                    if (file != null) {
                        IntentUtil.shareFile(requireContext(), file)
                    } else {
                        Toast.makeText(requireContext(), "儲存星盤失敗！", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
        binding.fabTheme.setOnClickListener {
            showThemeDialog()
        }
    }

    private fun initArrayAdapter() {
        ArrayAdapter.createFromResource(
            requireContext(),
            R.array.spinnerValue,
            R.layout.spinner_item
        ).also { adapter ->
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinner.adapter = adapter
        }
        ArrayAdapter.createFromResource(
            requireContext(),
            R.array.spinnerValue,
            R.layout.spinner_item
        ).also { adapter ->
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerB.adapter = adapter
        }
        ArrayAdapter.createFromResource(
            requireContext(),
            R.array.houseSystem,
            R.layout.spinner_item
        ).also { adapter ->
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spHouseSystem.adapter = adapter
        }
    }

    private fun initHouseSystem() {
        binding.spHouseSystem.setSelection(0)
        binding.spHouseSystem.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long
            ) {
                val result: String = parent?.getItemAtPosition(position).toString()
                LogUtil.d(result)
                var houseSystem = 'P'
                when (position) {
                    0 -> {
                        houseSystem = 'P'
                    }

                    1 -> {
                        houseSystem = 'K'
                    }

                    2 -> {
                        houseSystem = 'O'
                    }

                    3 -> {
                        houseSystem = 'C'
                    }

                    4 -> {
                        houseSystem = 'R'
                    }

                    5 -> {
                        houseSystem = 'M'
                    }

                    6 -> {
                        houseSystem = 'T'
                    }

                    7 -> {
                        houseSystem = 'B'
                    }

                    8 -> {
                        houseSystem = 'A'
                    }

                    9 -> {
                        houseSystem = 'W'
                    }

                    10 -> {
                        houseSystem = 'V'
                    }
                }
                if (houseSystem == EphemerisUtil.houseSystem) {
                    return
                }
                EphemerisUtil.houseSystem = houseSystem
                LiveEventBus.get<Char>(EventKey.UpdateChart).postDelay(EphemerisUtil.houseSystem, 0)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {

            }
        }
    }

    private fun initSpinnerA() {
        binding.spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long
            ) {
                val result: String = parent?.getItemAtPosition(position).toString()
                LogUtil.d(result)
                val minute: Long = 1 * 60 * 1000
                val day = 24 * 60 * minute
                when (position) {
                    0 -> {
                        timeStep = minute
                    }

                    1 -> {
                        timeStep = 5 * minute
                    }

                    2 -> {
                        timeStep = 30 * minute
                    }

                    3 -> {
                        timeStep = 60 * minute
                    }

                    4 -> {
                        timeStep = 60 * 12 * minute
                    }

                    5 -> {
                        timeStep = day
                    }

                    6 -> {
                        timeStep = day * 10
                    }

                    7 -> {
                        timeStep = day * 20
                    }

                    8 -> {
                        timeStep = day * 30
                    }

                    9 -> {
                        timeStep = day * 180
                    }

                    10 -> {
                        timeStep = day * 365
                    }
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {

            }
        }
        binding.ivArrowLeft.setOnClickListener {
            if (chartType == Chart.SolarReturn) {
                val time = viewModel.matchEvent.timeStamp - timeStep
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthday.setText(timeString)
                viewModel.matchEvent.timeStamp = time
                LiveEventBus.get<Long>(EventKey.UpdateChartA).postDelay(time, 0)
            } else {
                val time = viewModel.matchEvent.horoscopeA.birthdayTime - timeStep
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthday.setText(timeString)
                viewModel.matchEvent.horoscopeA.birthdayTime = time
                LiveEventBus.get<Long>(EventKey.UpdateChartA)
                    .postDelay(viewModel.matchEvent.horoscopeA.birthdayTime, 0)
            }
            isAnimator = false
        }
        binding.ivArrowRight.setOnClickListener {
            if (chartType == Chart.SolarReturn) {
                val time = viewModel.matchEvent.timeStamp + timeStep
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthday.setText(timeString)
                viewModel.matchEvent.timeStamp = time
                LiveEventBus.get<Long>(EventKey.UpdateChartA).postDelay(time, 0)
            } else {
                val time = viewModel.matchEvent.horoscopeA.birthdayTime + timeStep
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthday.setText(timeString)
                viewModel.matchEvent.horoscopeA.birthdayTime = time
                LiveEventBus.get<Long>(EventKey.UpdateChartA).postDelay(time, 0)
            }
            isAnimator = false
        }

        spinnerSelection()
    }

    private fun spinnerSelection() {
        if (isSameChart && isInit) {
            return
        }
        when (chartType) {
            Chart.Marks,
            Chart.Davison,
            Chart.Composite,
            Chart.Synastry,
            Chart.Firdaria,
            Chart.Celestial,
            Chart.Natal -> {
                // 間隔5分鐘
                binding.spinner.setSelection(1)
            }

            Chart.MarksTertiaryProgression,
            Chart.DavisonTertiaryProgression,
            Chart.CompositeTertiaryProgression,
            Chart.SynastryTertiaryProgression,
            Chart.TertiaryProgressionSynastry,
            Chart.TertiaryProgression,
            Chart.LunarReturn -> {
                // 間隔1月
                binding.spinner.setSelection(8)
            }

            Chart.MarksSecondaryProgression,
            Chart.DavisonSecondaryProgression,
            Chart.CompositeSecondaryProgression,
            Chart.SynastrySecondaryProgression,
            Chart.SecondaryProgressionSynastry,
            Chart.SecondaryProgression,
            Chart.SolarArc,
            Chart.SolarReturn -> {
                // 間隔1年
                binding.spinner.setSelection(10)
            }

            Chart.Transit -> {
                // 間隔1天
                binding.spinner.setSelection(5)
            }
        }
        isInit = true
    }

    private fun spinnerSelectionB() {
        if (isSameChart && isInit) {
            return
        }
        when (chartType) {
            Chart.Marks,
            Chart.Davison,
            Chart.Composite,
            Chart.Synastry,
            Chart.Firdaria,
            Chart.Celestial,
            Chart.Natal -> {
                // 間隔5分鐘
                binding.spinnerB.setSelection(1)
            }

            Chart.MarksTertiaryProgression,
            Chart.DavisonTertiaryProgression,
            Chart.CompositeTertiaryProgression,
            Chart.SynastryTertiaryProgression,
            Chart.TertiaryProgressionSynastry,
            Chart.TertiaryProgression,
            Chart.LunarReturn -> {
                // 間隔1月
                binding.spinnerB.setSelection(8)
            }

            Chart.MarksSecondaryProgression,
            Chart.DavisonSecondaryProgression,
            Chart.CompositeSecondaryProgression,
            Chart.SynastrySecondaryProgression,
            Chart.SecondaryProgressionSynastry,
            Chart.SecondaryProgression,
            Chart.SolarArc,
            Chart.SolarReturn -> {
                // 間隔1年
                binding.spinnerB.setSelection(10)
            }

            Chart.Transit -> {
                // 間隔1天
                binding.spinnerB.setSelection(5)
            }
        }
        isInit = true
    }

    private fun initSpinnerB() {
        binding.spinnerB.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long
            ) {
                val result: String = parent?.getItemAtPosition(position).toString()
                LogUtil.d(result)
                val minute: Long = 1 * 60 * 1000
                val day = 24 * 60 * minute
                when (position) {
                    0 -> {
                        timeStepB = minute
                    }

                    1 -> {
                        timeStepB = 5 * minute
                    }

                    2 -> {
                        timeStepB = 30 * minute
                    }

                    3 -> {
                        timeStepB = 60 * minute
                    }

                    4 -> {
                        timeStepB = 60 * 12 * minute
                    }

                    5 -> {
                        timeStepB = day
                    }

                    6 -> {
                        timeStepB = day * 10
                    }

                    7 -> {
                        timeStepB = day * 20
                    }

                    8 -> {
                        timeStepB = day * 30
                    }

                    9 -> {
                        timeStepB = day * 180
                    }

                    10 -> {
                        timeStepB = day * 365
                    }
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {

            }
        }
        binding.ivArrowLeftB.setOnClickListener {
            if (chartType == Chart.SolarReturn) {
                val time = viewModel.matchEvent.timeStamp - timeStepB
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthdayB.setText(timeString)
                viewModel.matchEvent.timeStamp = time
                LiveEventBus.get<Long>(EventKey.UpdateChartA).postDelay(time, 0)
            } else {
                val time = viewModel.matchEvent.horoscopeB.birthdayTime - timeStepB
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthdayB.setText(timeString)
                viewModel.matchEvent.horoscopeB.birthdayTime = time
                LiveEventBus.get<Long>(EventKey.UpdateChartB).postDelay(time, 0)
            }
            isAnimator = false
        }
        binding.ivArrowRightB.setOnClickListener {
            if (chartType == Chart.SolarReturn) {
                val time = viewModel.matchEvent.timeStamp + timeStepB
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthdayB.setText(timeString)
                viewModel.matchEvent.timeStamp = time
                LiveEventBus.get<Long>(EventKey.UpdateChartA).postDelay(time, 0)
            } else {
                val time = viewModel.matchEvent.horoscopeB.birthdayTime + timeStepB
                val timeString = FormatUtils.dateToString(Date(time), "yyyy/MM/dd HH:mm")
                binding.tvBirthdayB.setText(timeString)
                viewModel.matchEvent.horoscopeB.birthdayTime = time
                LiveEventBus.get<Long>(EventKey.UpdateChartB).postDelay(time, 0)
            }
            isAnimator = false
        }
        spinnerSelectionB()
    }

    private fun collectData() {
        initMatch(viewModel.matchEvent)
        getEvent()
    }

    private fun timePickerDialog(time: Long, textView: TextView) {
        val calendar = Calendar.getInstance()
        calendar.time = Date(time)
        val list = intArrayOf(
            DateTimeConfig.YEAR,
            DateTimeConfig.MONTH,
            DateTimeConfig.DAY,
            DateTimeConfig.HOUR,
            DateTimeConfig.MIN
        ).toMutableList()

        CardDatePickerDialog.builder(requireContext())
            .setTitle("選擇出生日期與時間")
            .setBackGroundModel(CardDatePickerDialog.CARD)
            .setDefaultTime(calendar.timeInMillis)
            .setDisplayType(list)
            .setLabelText("年", "月", "日", "時", "分")
            .showDateLabel(true)
            .showFocusDateInfo(true)
            .showBackNow(true)
            .setOnChoose("選擇") { millisecond ->
                val timeString: String =
                    FormatUtils.dateToString(Date(millisecond), "yyyy/MM/dd HH:mm")
                textView.text = timeString
                if (textView == binding.tvBirthday) {
                    viewModel.matchEvent.horoscopeA.birthdayTime = millisecond
                    LiveEventBus.get<Long>(EventKey.UpdateChartA).postDelay(millisecond, 0)
                } else {
                    viewModel.matchEvent.horoscopeB.birthdayTime = millisecond
                    LiveEventBus.get<Long>(EventKey.UpdateChartB).postDelay(millisecond, 0)
                }

                isAnimator = false
            }
            .setOnCancel("取消") { }
            .build().show()
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
            viewModel.matchEvent = matchEvent
            if (viewModel.matchEvent.isSingle) {
                binding.lltB.visibility = View.GONE
            } else {
                binding.lltB.visibility = View.VISIBLE
            }
            isSameChart = matchEvent.chartType == chartType
            chartType = matchEvent.chartType
            initSpinnerA()
            initSpinnerB()

            drawView.init(matchEvent.chartType, isShowRuler)
            drawViewPro.init(matchEvent.chartType, isShowRuler)

            binding.loadingProgress.visibility = View.VISIBLE
            binding.lltChart.visibility = View.VISIBLE
            binding.tvEmptyDesc.visibility = View.GONE
            binding.tvTime.visibility = View.GONE

            val timeString: String =
                FormatUtils.dateToString(
                    Date(matchEvent.horoscopeA.birthdayTime),
                    "yyyy/MM/dd HH:mm"
                )
            binding.tvBirthday.setText(timeString)
            binding.tvBirthday.setOnClickListener {
                timePickerDialog(matchEvent.horoscopeA.birthdayTime, binding.tvBirthday)
            }
            val timeStringB: String =
                FormatUtils.dateToString(
                    Date(matchEvent.horoscopeB.birthdayTime),
                    "yyyy/MM/dd HH:mm"
                )
            binding.tvBirthdayB.setText(timeStringB)
            binding.tvBirthdayB.setOnClickListener {
                timePickerDialog(matchEvent.horoscopeB.birthdayTime, binding.tvBirthdayB)
            }
            binding.btAuto.visibility = View.GONE
            when (matchEvent.chartType) {
                Chart.Celestial -> {
                    celestial(matchEvent.horoscopeA)
                }

                Chart.Natal -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.Transit -> {
                    if (BuildConfig.IS_DEV) {
                        binding.btAuto.visibility = View.VISIBLE
                    }
                    binding.btAuto.setOnClickListener {
                        showMaterialDialog(matchEvent)
                    }
                    transits(matchEvent.horoscopeA, matchEvent.horoscopeB)
                }

                Chart.SecondaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.SecondaryProgressionSynastry -> {
                    mergeChart(matchEvent.horoscopeA, matchEvent.horoscopeB)
                }

                Chart.TertiaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.TertiaryProgressionSynastry -> {
                    mergeChart(matchEvent.horoscopeA, matchEvent.horoscopeB)
                }

                Chart.SolarReturn -> {
                    binding.tvBirthday.setText(
                        FormatUtils.dateToString(
                            Date(matchEvent.horoscopeA.birthdayTime),
                            "yyyy/MM/dd HH:mm"
                        )
                    )
                    singleChart(matchEvent.horoscopeA)
                    binding.tvTime.visibility = View.VISIBLE
                    binding.tvTime.text =
                        getString(
                            R.string.sun_sunshine,
                            EphemerisUtil.formatDateTime(matchEvent.timeStamp)
                        )
                }

                Chart.LunarReturn -> {
                    binding.tvBirthday.setText(
                        FormatUtils.dateToString(
                            Date(matchEvent.horoscopeA.birthdayTime),
                            "yyyy/MM/dd HH:mm"
                        )
                    )
                    singleChart(matchEvent.horoscopeA)
                    binding.tvTime.visibility = View.VISIBLE
                    binding.tvTime.text =
                        getString(
                            R.string.moon_reflection,
                            EphemerisUtil.formatDateTime(matchEvent.timeStamp)
                        )
                }

                Chart.SolarArc -> {
                    mergeChart(matchEvent.horoscopeA, matchEvent.horoscopeB)
                }

                Chart.Synastry -> {
                    mergeChart(matchEvent.horoscopeA, matchEvent.horoscopeB)
                }

                Chart.SynastrySecondaryProgression -> {
                    mergeChart(matchEvent.horoscopeA, matchEvent.horoscopeB)
                }

                Chart.SynastryTertiaryProgression -> {
                    mergeChart(matchEvent.horoscopeA, matchEvent.horoscopeB)
                }

                Chart.Composite -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.CompositeSecondaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.CompositeTertiaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.Davison -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.DavisonSecondaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.DavisonTertiaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.Marks -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.MarksSecondaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.MarksTertiaryProgression -> {
                    singleChart(matchEvent.horoscopeA)
                }

                Chart.Firdaria -> {
                    drawView.setDataFirdaria(matchEvent.horoscopeB)
                    drawViewPro.setDataFirdaria(matchEvent.horoscopeB)
                    singleChart(matchEvent.horoscopeA)
                }

                else -> {
                    LogUtil.d("matchEvent default")
                    binding.lltChart.visibility = View.GONE
                    binding.tvEmptyDesc.visibility = View.VISIBLE
                }
            }
        }
    }

    private fun showMaterialDialog(matchEvent: MatchEvent) {
        MaterialDialog(requireContext()).show {
            val dialog = listItems(R.array.transDay) { _, index, _ ->
                // 自動推運
                // 創建一個後台工作，該工作將在背景線程中處理數據，並發出通知
                // 創建一個Data對象
                val gson = Gson()
                val data = Data.Builder()
                    .putString(
                        "birthDataAString",
                        gson.toJson(matchEvent.birthDataA)
                    )
                    .putString(
                        "birthDataBString",
                        gson.toJson(matchEvent.birthDataB)
                    )
                    .putInt("index", index)
                    .build()

                val workRequest =
                    OneTimeWorkRequest.Builder(CalculateWorker::class.java)
                        .setInitialDelay(0, TimeUnit.SECONDS)
                        .setInputData(data)
                        .build()
                WorkManager.getInstance(requireContext()).enqueue(workRequest)
            }

            dialog.show()
        }
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java).observe(this) {
            initMatch(it)
        }

        LiveEventBus.get(EventKey.CloseSnackbar, Boolean::class.java).observe(this) {
            drawView.onPause()
            drawViewPro.onPause()
        }

        // 監聽設定頁面返回事件，重新計算星盤數據
        LiveEventBus.get(EventKey.RefreshChart, Boolean::class.java).observe(this) {
            if (it) {
                LogUtil.d("Refreshing chart from settings")
                // 重新讀取設定
                isShowRuler = EncryptedSPUtil.getPlanetRuler(requireContext())
                isAnimator = EncryptedSPUtil.getChartAnimation(requireContext())

                LogUtil.d("Settings - isShowRuler: $isShowRuler, isAnimator: $isAnimator")

                // 重新計算星盤數據
                val matchEvent = viewModel.matchEvent


                // 重新初始化星盤視圖
                drawView.init(chartType, isShowRuler)
                drawViewPro.init(chartType, isShowRuler)

                // 重新加載數據
                initMatch(matchEvent)
            }
        }
    }

    private fun celestial(horoscopeA: Horoscope) {
        requireActivity().runOnUiThread {
            setAnimator(horoscopeA)
        }
    }

    private fun transits(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        requireActivity().runOnUiThread {
            setAnimator(horoscopeA, horoscopeB)
        }
    }

    private fun mergeChart(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        requireActivity().runOnUiThread {
            setAnimator(horoscopeA, horoscopeB)
        }
    }

    private fun singleChart(horoscopeA: Horoscope) {
        requireActivity().runOnUiThread {
            setAnimator(horoscopeA)
        }
    }

    private fun setAnimator(horoscopeA: Horoscope) {
        binding.drawContainer.removeAllViews()
        if (isSymbolMode) {
            binding.drawContainer.addView(drawViewPro)
            // 確保設定正確傳遞到DrawViewPro
            drawViewPro.isShowRuler = isShowRuler
            drawViewPro.setData(horoscopeA)
        } else {
            binding.drawContainer.addView(drawView)
            // 確保設定正確傳遞到DrawView
            drawView.isShowRuler = isShowRuler
            drawView.setData(horoscopeA)
        }
        binding.loadingProgress.visibility = View.GONE
    }

    private fun setAnimator(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        binding.drawContainer.removeAllViews()
        if (isSymbolMode) {
            binding.drawContainer.addView(drawViewPro)
            // 確保設定正確傳遞到DrawViewPro
            drawViewPro.isShowRuler = isShowRuler
            drawViewPro.setDataPair(horoscopeA, horoscopeB)
        } else {
            binding.drawContainer.addView(drawView)
            // 確保設定正確傳遞到DrawView
            drawView.isShowRuler = isShowRuler
            drawView.setDataPair(horoscopeA, horoscopeB)
        }
        binding.loadingProgress.visibility = View.GONE
    }

    private fun showThemeDialog() {
        val bottomSheet = BottomSheetDialog(requireContext())
        val customView = layoutInflater.inflate(R.layout.dialog_theme_style, null)

        // 使用 MaterialButtonToggleGroup 替代 RadioGroup
        val styleGroup = customView.findViewById<MaterialButtonToggleGroup>(R.id.styleGroup)
        var currentSymbolMode: Boolean

        // 設置初始選中狀態
        if (isSymbolMode) {
            styleGroup.check(R.id.rbDegree)
        } else {
            styleGroup.check(R.id.rbText)
        }

        styleGroup.addOnButtonCheckedListener { _, checkedId, isChecked ->
            if (isChecked) {
                currentSymbolMode = checkedId == R.id.rbDegree
                if (currentSymbolMode != isSymbolMode) {
                    isSymbolMode = currentSymbolMode
                    EncryptedSPUtil.setSymbolMode(requireContext(), isSymbolMode)

                    binding.drawContainer.removeAllViews()
                    if (isSymbolMode) {
                        binding.drawContainer.addView(drawViewPro)
                        drawViewPro.init(chartType, isShowRuler)
                    } else {
                        binding.drawContainer.addView(drawView)
                        drawView.init(chartType, isShowRuler)
                    }
                    initMatch(viewModel.matchEvent)
                }
            }
        }

        // 設置主題列表
        val themeList = customView.findViewById<RecyclerView>(R.id.themeList)
        themeList.layoutManager = LinearLayoutManager(context)
        themeList.adapter =
            ThemeAdapter(listOf("皮膚", "經典", "Juno", "清爽", "可愛", "夜空")) { index ->
                binding.drawContainer.removeAllViews()

                val selectedTheme = when (index) {
                    0 -> "skin"
                    1 -> "classic"
                    2 -> "juno"
                    3 -> "clean"
                    4 -> "cute"
                    5 -> "night"
                    else -> "classic"
                }

                EncryptedSPUtil.setTheme(requireContext(), selectedTheme)

                if (!isSymbolMode) {
                    binding.drawContainer.addView(drawView)
                    drawView.init(chartType, isShowRuler)
                } else {
                    binding.drawContainer.addView(drawViewPro)
                    drawViewPro.init(chartType, isShowRuler)
                }

                bottomSheet.dismiss()
            }

        // 設置標題
        val titleView = customView.findViewById<TextView>(R.id.tvTitle)
        titleView.text = "星盤外觀設定"

        bottomSheet.setContentView(customView)
        bottomSheet.show()
    }

}
