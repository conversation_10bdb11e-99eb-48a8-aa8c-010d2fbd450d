package com.one.astrology.ui.fragment.viewmodel

import android.app.Activity
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.firebase.ui.auth.IdpResponse
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.ktx.database
import com.google.firebase.database.ktx.getValue
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ktx.toObject
import com.google.firebase.ktx.Firebase
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.ObjectBox
import com.one.astrology.data.AstrologyReading
import com.one.astrology.data.User
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.SocialData
import com.one.astrology.data.firestore.BirthDataList
import com.one.astrology.event.EventKey
import com.one.core.callback.FirebaseCallback
import com.one.core.callback.LoginCallback
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class FirestoreViewModel @Inject constructor(
) : ViewModel() {

    private val _loading = MutableStateFlow(false)
    val loading = _loading.asSharedFlow()

    fun login(idpResponse: IdpResponse, callback: LoginCallback) {
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        val user = User()
        if (firebaseUser != null) {
            user.uid = firebaseUser.uid
            user.displayName = firebaseUser.displayName
            user.email = firebaseUser.email
            user.phoneNumber = firebaseUser.phoneNumber
            user.idpToken = idpResponse.idpToken
            user.currentTime = FormatUtils.longToString(
                System.currentTimeMillis(),
                "yyyy/MM/dd HH:mm"
            )
            writeNewUserFirebaseFirestore(user, callback)
        }
    }

    private fun writeNewUser(user: User, callback: LoginCallback) {
        val database = Firebase.database.reference
        database.child(USERS).child(user.uid).setValue(user).addOnSuccessListener {
            // Write was successful!
            callback.onSuccess()
        }.addOnFailureListener {
            // Write failed
            callback.onFailure(it)
        }
    }

    private fun writeAiResponseFirebaseFirestore(
        user: User,
        astrologyReading: AstrologyReading,
        callback: LoginCallback
    ) {
        val db = FirebaseFirestore.getInstance()
        db.collection("astrology_readings").document(user.uid).set(astrologyReading)
            .addOnSuccessListener {
                callback.onSuccess()
                LogUtil.d("DocumentSnapshot successfully written!")
            }
            .addOnFailureListener {
                LogUtil.e("Error writing document : " + it.message)
                callback.onFailure(it)
            }
    }

    private fun writeNewUserFirebaseFirestore(user: User, callback: LoginCallback) {
        val db = FirebaseFirestore.getInstance()
        db.collection(USERS).document(user.uid).set(user)
            .addOnSuccessListener {
                callback.onSuccess()
                LogUtil.d("DocumentSnapshot successfully written!")
            }
            .addOnFailureListener {
                LogUtil.e("Error writing document : " + it.message)
                callback.onFailure(it)
            }

//        val docRef = db.collection(BIRTH_DATA).document(user.uid)
//        docRef.get().addOnSuccessListener { document ->
//            if (document.data != null) {
//                downloadUserData(document)
//            } else {
//                uploadUserData()
//                LogUtil.d("No such document")
//            }
//        }.addOnFailureListener { exception ->
//            LogUtil.d("get failed with : " + exception.message)
//        }
    }

    fun uploadUserData(context: Activity) {
        val database = Firebase.database.reference
        isLoading(true)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        if (firebaseUser != null) {
            val box = ObjectBox.get().boxFor(BirthData::class.java)
            val birthDataList = BirthDataList()
            birthDataList.dataList = box.all
            birthDataList.currentTime = FormatUtils.longToString(
                System.currentTimeMillis(),
                "yyyy/MM/dd HH:mm"
            )
            val uid = firebaseUser.uid
            database.child(BIRTH_DATA).child(uid).setValue(birthDataList).addOnSuccessListener {
                // Write was successful!
                Toast.makeText(context, "成功上傳", Toast.LENGTH_SHORT).show()
                isLoading(false)
            }.addOnFailureListener {
                // Write failed
                it.message?.let { it1 -> LogUtil.e(it1) }
                Toast.makeText(context, it.message, Toast.LENGTH_SHORT).show()
                isLoading(false)
            }
        }

    }

    fun downloadUserData(firebaseCallback: FirebaseCallback) {
        isLoading(true)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        if (firebaseUser != null) {
//            Firebase.database("https://astrology-78f27-d1593.firebaseio.com/")
            val database = Firebase.database.reference
            val uid = firebaseUser.uid
            database.child(BIRTH_DATA).child(uid).get().addOnSuccessListener {
                val birthDataList = it.getValue(BirthDataList::class.java)
                val box = ObjectBox.get().boxFor(BirthData::class.java)
                box.removeAll()
                if (birthDataList != null) {
                    box.put(birthDataList.dataList)
                    LiveEventBus.get<String>(EventKey.UpdateUserBirthData).post("")
                }
                firebaseCallback.onSuccess()
                isLoading(false)
            }.addOnFailureListener {
                LogUtil.e("Error getting data " + it.message)
                it.message?.let { it1 -> firebaseCallback.onFailure(it1) }
                isLoading(false)
            }
        }
    }

    fun uploadData(birthData: BirthData) {
        val database = Firebase.database.reference
        isLoading(true)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        if (firebaseUser != null) {
            val socialData = SocialData()
            socialData.birthData = birthData
            socialData.currentTime = FormatUtils.longToString(
                System.currentTimeMillis(),
                "yyyy/MM/dd HH:mm"
            )
            val uid = firebaseUser.uid
            database.child(COMMUNITY).child(uid).setValue(birthData)
        }
        isLoading(false)
    }

    fun downloadData(birthData: BirthData) {
        val database = Firebase.database.reference
        isLoading(true)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        if (firebaseUser != null) {
            database.child(COMMUNITY).get()
                .addOnSuccessListener {
                    val dataList = it.getValue<SocialData>()
                    val box = ObjectBox.get().boxFor(BirthData::class.java)
                    box.removeAll()
                    if (dataList != null) {
//                        box.put(dataList.list)
                        LiveEventBus.get<String>(EventKey.UpdateUserBirthData).post("")
                    }
//                    firebaseCallback.onSuccess()
                    isLoading(false)
                }.addOnFailureListener {
                    Log.e("firebase", "Error getting data", it)
//                    it.message?.let { it1 -> firebaseCallback.onFailure(it1) }
                    isLoading(false)
                }
        }
        isLoading(false)
    }

    private fun isLoading(isLoading: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                _loading.emit(isLoading)
            }
        }
    }

    private fun downloadUserData(document: DocumentSnapshot) {
        val birthDataList = document.toObject<BirthDataList>() ?: return
        val signRecordBox = ObjectBox.get().boxFor(BirthData::class.java)
        signRecordBox.removeAll()
        signRecordBox.put(birthDataList.dataList)
        LiveEventBus.get<String>(EventKey.UpdateUserBirthData).post("")
    }


    fun downloadFirebaseFirestore() {
        isLoading(true)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        if (firebaseUser != null) {
            val db = FirebaseFirestore.getInstance()
            val docRef = db.collection(BIRTH_DATA).document(firebaseUser.uid)
            docRef.get().addOnSuccessListener { document ->
                if (document.data != null) {
                    downloadUserData(document)
                    isLoading(false)
                } else {
                    isLoading(false)
                }
            }.addOnFailureListener { exception ->
                LogUtil.d("get failed with : " + exception.message)
                isLoading(false)
            }
        }
    }

    fun uploadFirebaseFirestore() {
        isLoading(true)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        if (firebaseUser != null) {
            val db = FirebaseFirestore.getInstance()
            val signRecordBox = ObjectBox.get().boxFor(BirthData::class.java)
            val userBirthDataList = BirthDataList()
            userBirthDataList.dataList = signRecordBox.all
            userBirthDataList.currentTime = FormatUtils.longToString(
                System.currentTimeMillis(),
                "yyyy/MM/dd HH:mm"
            )
            db.collection(BIRTH_DATA).document(firebaseUser.uid).set(userBirthDataList)
                .addOnSuccessListener {
                    LogUtil.d("DocumentSnapshot successfully written!")
                    isLoading(false)
                }
                .addOnFailureListener { e ->
                    LogUtil.e("Error writing document : " + e.message)
                    isLoading(false)
                }
        }
//        writeSignRecord()
    }

    companion object {
        private const val USERS = "users"
        private const val BIRTH_DATA = "birth_data"
        private const val COMMUNITY = "community"
    }
}