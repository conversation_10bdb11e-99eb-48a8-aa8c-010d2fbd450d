package com.one.astrology.ui.fragment.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.ktx.auth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ktx.toObject
import com.google.firebase.ktx.Firebase
import com.one.astrology.ObjectBox
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.core.util.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class RecordViewModel @Inject constructor(
) : ViewModel() {
    private val _success = MutableSharedFlow<MutableList<BirthData>>()
    val success = _success.asSharedFlow()

    private val _failed = MutableSharedFlow<String>()
    val failed = _failed.asSharedFlow()

    fun remove(signRecords: MutableList<BirthData>) {
        val user = Firebase.auth.currentUser
        if (user != null) {
            val db = FirebaseFirestore.getInstance()
            db.collection("SignRecords").document(user.uid).set(signRecords)
                .addOnSuccessListener {
                    LogUtil.d("DocumentSnapshot successfully written!")
                }
                .addOnFailureListener { e ->
                    LogUtil.e("Error writing document : " + e.message)
                }
        } else {
            val signRecordBox = ObjectBox.get().boxFor(BirthData::class.java)
            signRecordBox.removeAll()
            signRecordBox.put(signRecords)
        }
    }

    suspend fun getSignRecodeList() {
        val user = Firebase.auth.currentUser
        if (user != null) {
            val db = FirebaseFirestore.getInstance()
            val docRef = db.collection("SignRecords").document(user.uid)
            docRef.get().addOnSuccessListener { document ->
                if (document.data != null) {
                    val userBirthDataesList = document.toObject<com.one.astrology.data.firestore.BirthDataList>()
                    if (userBirthDataesList != null) {
                        viewModelScope.launch(Dispatchers.IO) {
                            withContext(Dispatchers.Main) {
                                _success.emit(userBirthDataesList.dataList)
                            }
                        }
                    } else {
                        errorHandle("SignRecords is null")
                    }
                } else {
                    LogUtil.d("No such document")
                    errorHandle("No such document")
                }
            }.addOnFailureListener { exception ->
                LogUtil.d("get failed with : " + exception.message)
                exception.message?.let { errorHandle(it) }
            }
        } else {
            val signRecordBox = ObjectBox.get().boxFor(BirthData::class.java)
            _success.emit(signRecordBox!!.query().orderDesc(BirthData_.id).build().find())
        }
    }

    private fun errorHandle(message: String) {
        viewModelScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                _failed.emit(message)
            }
        }
    }
}