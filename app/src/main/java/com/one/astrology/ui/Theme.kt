package com.one.astrology.ui


import android.annotation.SuppressLint
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Shapes
import androidx.compose.material3.Typography
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.R


@SuppressLint("ComposableNaming")
@Composable
fun CustomLightColorScheme(): ColorScheme {
    return lightColorScheme(
        primary = colorResource(id = R.color.colorPrimary), // 主要顏色
        secondary = colorResource(id = R.color.colorAccent), // 輔助顏色
        background = colorResource(id = R.color.white), // 主背景顏色
        surface = colorResource(id = R.color.white), // 卡片、對話框背景
        error = colorResource(id = R.color.red), // 錯誤狀態顏色
        onPrimary = colorResource(id = R.color.white), // 主要按鈕內文字顏色
        onSecondary = colorResource(id = R.color.white), // 次要按鈕內文字顏色
        onBackground = colorResource(id = R.color.colorPrimary), // 背景上文字顏色
        onSurface = colorResource(id = R.color.colorPrimary), // 卡片內文字顏色
        onError = colorResource(id = R.color.white) // 錯誤訊息內文字顏色
    )
}

@SuppressLint("ComposableNaming")
@Composable
fun CustomDarkColorScheme(): ColorScheme {
    return lightColorScheme(
        primary = colorResource(id = R.color.colorPrimary), // 主要顏色
        secondary = colorResource(id = R.color.colorAccent), // 輔助顏色
        background = colorResource(id = R.color.white), // 主背景顏色
        surface = colorResource(id = R.color.white), // 卡片、對話框背景
        error = colorResource(id = R.color.red), // 錯誤狀態顏色
        onPrimary = colorResource(id = R.color.white), // 主要按鈕內文字顏色
        onSecondary = colorResource(id = R.color.white), // 次要按鈕內文字顏色
        onBackground = colorResource(id = R.color.colorPrimary), // 背景上文字顏色
        onSurface = colorResource(id = R.color.colorPrimary), // 卡片內文字顏色
        onError = colorResource(id = R.color.white) // 錯誤訊息內文字顏色
    )
}

val AppTypography = Typography(
    displayLarge = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Bold,
        fontSize = 30.sp
    ),
    displayMedium = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Bold,
        fontSize = 24.sp
    ),
    displaySmall = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Bold,
        fontSize = 20.sp
    ),
    headlineLarge = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Bold,
        fontSize = 18.sp
    ),
    headlineMedium = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Bold,
        fontSize = 16.sp
    ),
    headlineSmall = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp
    ),
    titleLarge = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 18.sp
    ),
    titleMedium = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp
    ),
    titleSmall = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp
    ),

    bodyLarge = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 18.sp
    ),
    bodyMedium = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp
    ),
    bodySmall = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp
    ),

    labelLarge = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 18.sp
    ),
    labelMedium = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp
    ),
    labelSmall = TextStyle(
        fontFamily = FontFamily.Serif,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp
    ),
)

val AppShapes = Shapes(
    small = RoundedCornerShape(5.dp),
    medium = RoundedCornerShape(8.dp),
    large = RoundedCornerShape(16.dp)
)

@Composable
fun cardElevation() = CardDefaults.cardElevation(5.dp)

@Composable
fun AppTheme(
    darkTheme: Boolean = false, // 可根據系統設定切換主題
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        CustomDarkColorScheme()
    } else {
        CustomLightColorScheme()
    }
    MaterialTheme(
        colorScheme = colorScheme,
        typography = AppTypography,
        shapes = AppShapes
    ) {
        // 在這裡使用自訂主題包裹你的內容
        content()
    }
}
