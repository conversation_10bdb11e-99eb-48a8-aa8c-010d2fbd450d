package com.one.astrology.ui.fragment.analysis

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.Category
import com.one.astrology.data.Horoscope
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBar
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil

class SecondaryProgressionsFragment : Fragment() {
    private val chatViewModel by viewModels<ChatViewModel>()
    private var chartName = ""
    private var category: Category = Category("", "", 0, Chart.SecondaryProgression)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (arguments != null) {
            category = arguments?.getSerializable("category") as Category
            chartName = getString(category.chart.type)
        }
        return ComposeView(requireContext()).apply {
            setContent {
                MainAnalyzeScreen()
            }
        }
    }

    @Composable
    fun MainAnalyzeScreen() {
        val context = LocalContext.current
        var horoscope by remember { mutableStateOf(Horoscope()) }
        var birthData by remember { mutableStateOf(BirthData()) }

        val isLoading by chatViewModel.isLoading.observeAsState(false)
        val data by chatViewModel.data.observeAsState(ReadingData("", "", ""))
        val errorMessage by chatViewModel.error.observeAsState("")
        var dataNow by remember { mutableLongStateOf(System.currentTimeMillis()) }

        LaunchedEffect(errorMessage) {
            LogUtil.d("errorMessage 更新: $errorMessage")
            birthData = chatViewModel.getBirthData()
            if (birthData.name.isEmpty()) {
                return@LaunchedEffect
            }
            val birthDataNow = BirthData(birthData)
            birthDataNow.birthday = System.currentTimeMillis()
            horoscope = getHoroscope(
                chart = category.chart,
                context,
                birthData,
                System.currentTimeMillis()
            )
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            TopBar(birthData.name, birthData.generateBirthdayString(), { birthDataList ->
                val selectedBirthData = birthDataList[0]
                if (selectedBirthData.name != birthData.name) {
                    val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                    birthData = chatViewModel.getBirthData()
                    if (birthData.name.isNotEmpty()) {
                        birthData.isSelected = false
                        birthDataBox.put(birthData)
                    }
                    birthData = selectedBirthData
                    birthData.isSelected = true
                    birthDataBox.put(birthData)
                    val birthDataNow = BirthData(birthData)
                    birthDataNow.birthday = System.currentTimeMillis()
                    horoscope = getHoroscope(
                        chart = category.chart,
                        context,
                        birthData,
                        System.currentTimeMillis()
                    )
                    chatViewModel.init()
                }
            }, { date ->
                dataNow = date.time
            }, onCopy = {})

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = getCategories(category.chart, chartName)
                        CategoryList(context, categories, chatViewModel, horoscope = horoscope, chart = Chart.SecondaryProgression)
                    }
                }
            }
        }
    }

    private fun getHoroscope(
        chart: Chart,
        context: Context,
        birthData: BirthData,
        dataNow: Long,
    ): Horoscope {
        val progressedTime = BirthData()
        progressedTime.birthday = dataNow
        return when (chart) {
            Chart.SecondaryProgression -> chatViewModel.getSecondary(
                context,
                birthData,
                progressedTime
            )

            Chart.TertiaryProgression -> chatViewModel.getSecondary(
                context,
                birthData,
                progressedTime
            )

            else -> Horoscope()
        }
    }

    private fun getCategories(chart: Chart, chartName: String): List<Pair<String, String>> {
        return when (chart) {
            Chart.SecondaryProgression -> listOf(
                "${chartName}命運演變" to "${chartName}：分析盤中行星推移的變化，揭示個人命運在長期過程中的發展與轉變 (繁體中文):\n",
                "${chartName}財運長期變化" to "${chartName}：探索盤中行星的變化對財運、金錢流向及資源獲取的長期影響 (繁體中文):\n",
                "${chartName}感情與婚姻變遷" to "${chartName}：剖析盤對感情關係、婚姻及伴侶互動的長期發展趨勢 (繁體中文):\n",
                "${chartName}家庭與親緣演化" to "${chartName}：解析盤如何影響家庭關係、親子動態及家族運勢的演化 (繁體中文):\n",
                "${chartName}事業與職業發展" to "${chartName}：分析盤中行星推移對事業方向、職業成長及工作環境的影響 (繁體中文):\n",
                "${chartName}健康與生活方式" to "${chartName}：探索盤中行星推移對身心健康、生活方式及生活習慣的影響 (繁體中文):\n",
                "${chartName}內在變化與自我成長" to "${chartName}：揭示盤推移過程中，個人內在世界的變化與自我成長的契機 (繁體中文):\n",
                "${chartName}人生大事與重要事件" to "${chartName}：解析盤中關鍵行星推移所引發的重要人生事件與轉折點 (繁體中文):\n"
            )

            Chart.TertiaryProgression -> listOf(
                "${chartName}命運演變" to "${chartName}：分析盤中行星推移的變化，揭示個人命運在長期過程中的發展與轉變 (繁體中文):\n",
                "${chartName}財運長期變化" to "${chartName}：探索盤中行星的變化對財運、金錢流向及資源獲取的長期影響 (繁體中文):\n",
                "${chartName}感情與婚姻變遷" to "${chartName}：剖析盤對感情關係、婚姻及伴侶互動的長期發展趨勢 (繁體中文):\n",
                "${chartName}家庭與親緣演化" to "${chartName}：解析盤如何影響家庭關係、親子動態及家族運勢的演化 (繁體中文):\n",
                "${chartName}事業與職業發展" to "${chartName}：分析盤中行星推移對事業方向、職業成長及工作環境的影響 (繁體中文):\n",
                "${chartName}健康與生活方式" to "${chartName}：探索盤中行星推移對身心健康、生活方式及生活習慣的影響 (繁體中文):\n",
                "${chartName}內在變化與自我成長" to "${chartName}：揭示盤推移過程中，個人內在世界的變化與自我成長的契機 (繁體中文):\n",
                "${chartName}人生大事與重要事件" to "${chartName}：解析盤中關鍵行星推移所引發的重要人生事件與轉折點 (繁體中文):\n"
            )

            else -> emptyList()
        }
    }
}
