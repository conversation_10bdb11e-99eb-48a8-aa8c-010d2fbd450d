package com.one.astrology.ui.fragment.unnecessary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.MaterialTheme
import androidx.compose.ui.Modifier
import com.google.android.gms.tasks.Task
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.mukesh.MarkDown
import com.one.astrology.R
import com.one.astrology.constant.AssetsPath
import com.one.astrology.databinding.FragmentMarkdownBinding
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.util.Util
import com.one.core.firebase.RemoteConfigUtil
import com.one.core.util.LogUtil
import com.one.core.view.LoadingDialog
import java.io.File
import java.net.URL

/**
 * MarkdownFragment
 */
class MarkdownFragment : BaseFragment(R.layout.fragment_markdown) {

    private lateinit var binding: FragmentMarkdownBinding

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("MarkdownFragment", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMarkdownBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        fetchConfig()
    }

    private fun fetchConfig() {
        val loadingDialog = LoadingDialog(requireActivity())
        loadingDialog.show()
        RemoteConfigUtil.fetchConfig(requireActivity()) { task: Task<Void?>, firebaseRemoteConfig: FirebaseRemoteConfig ->
            if (task.isSuccessful) {
                val markdownUrl = firebaseRemoteConfig.getString("markdownUrl")
                setMarkDownContent(URL(markdownUrl))
            } else {
                // 無法取得 Remote Config
                val file = Util.loadStringFromAssets(requireActivity(), AssetsPath.MAIN)
                if (file != null) {
                    setMarkDownContent(file)
                }
            }
            loadingDialog.dismiss()
        }
    }

    private fun setMarkDownContent(markdownUrl: URL) {
        binding.markdown.setContent {
            MaterialTheme {
                MarkDown(
                    url = markdownUrl,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }

    private fun setMarkDownContent(text: String) {
        binding.markdown.setContent {
            MaterialTheme {
                MarkDown(
                    text = text,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }

    private fun setMarkDownContent(markdownFile: File) {
        binding.markdown.setContent {
            MaterialTheme {
                MarkDown(
                    file = markdownFile,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
}