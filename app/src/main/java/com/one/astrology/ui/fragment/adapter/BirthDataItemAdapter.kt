package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.entity.BirthData
import com.one.astrology.databinding.ItemBirthDataBinding
import com.one.astrology.util.LocationUtil
import com.one.core.util.FormatUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class BirthDataItemAdapter(val onClickListener: OnClickListener) :
    BaseQuickAdapter<BirthData, BirthDataItemAdapter.BaseViewHolder>() {

    private fun setOnClickListener(viewHolder: BaseViewHolder, item: BirthData) {
        item.isChecked = viewHolder.binding.checkbox.isChecked
        if (getSelected().size > 2 && viewHolder.binding.checkbox.isChecked) {
            viewHolder.binding.checkbox.isChecked = false
            item.isChecked = false
            Toast.makeText(context, "選擇兩個項目進行分析!", Toast.LENGTH_SHORT).show()
            return
        }
    }

    fun getSelected(): List<BirthData> {
        return items.filter { it.isChecked }
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int, item: BirthData?) {
        if (item != null) {
            holder.binding.tvName.text = item.name

            if (item.birthplaceArea.isNullOrEmpty()) {
                GlobalScope.launch(Dispatchers.Main) {
                    item.birthplaceArea = LocationUtil.latLngToArea(LatLng(item.birthplaceLatitude, item.birthplaceLongitude),)
                }
            }
            holder.binding.tvBirthplace.text = item.birthplaceArea

            holder.binding.checkbox.isChecked = item.isChecked

            holder.binding.tvTag.text = item.tag
            holder.binding.ivMore.tag = item
            if (item.isHide) {
                holder.binding.tvTime.text = "資訊隱藏"
            } else {
                if (item.birthdayString.isNullOrEmpty()) {
                    item.birthdayString =
                        FormatUtils.longToString(item.birthday, "yyyy/MM/dd HH:mm")
                }
                holder.binding.tvTime.text = item.birthdayString
            }
            holder.binding.checkbox.setOnClickListener {
                setOnClickListener(holder, item)
                onClickListener.onClick(it)
            }
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemBirthDataBinding = ItemBirthDataBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)
}