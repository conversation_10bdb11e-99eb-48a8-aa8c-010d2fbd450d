package com.one.astrology.ui.activity

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.lifecycle.viewmodel.compose.viewModel
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.compose.screens.AnnouncementListScreen
import com.one.astrology.viewmodel.AnnouncementViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AnnouncementListActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            AppTheme {
                val viewModel: AnnouncementViewModel = viewModel()
                val announcements by viewModel.announcements.collectAsState()
                
                AnnouncementListScreen(
                    announcements = announcements,
                    onAnnouncementClick = { announcement ->
                        val intent = Intent(this, AnnouncementDetailActivity::class.java)
                        intent.putExtra("announcement", announcement)
                        startActivity(intent)
                    }
                )
            }
        }
    }
} 