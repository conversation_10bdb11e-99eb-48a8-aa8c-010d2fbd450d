package com.one.astrology.ui.activity


import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.gms.tasks.Task
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.one.astrology.BuildConfig
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.PrefKey
import com.one.astrology.data.MyObjectBox
import com.one.astrology.data.db.SettingAspectData
import com.one.astrology.data.db.SettingAspectData_
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.ActivitySplashBinding
import com.one.astrology.db.BasicDBHelper
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.CopyAssetFiles
import com.one.astrology.util.EphemerisFileChecker
import com.one.astrology.util.launchWhenStarted
import com.one.core.data.RemoteConfig
import com.one.core.firebase.RemoteConfigUtil
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import com.one.core.util.PrefUtils
import dagger.hilt.android.AndroidEntryPoint
import io.objectbox.BoxStore
import io.objectbox.Factory
import java.io.InputStream


@SuppressLint("CustomSplashScreen")
@AndroidEntryPoint
class SplashActivity : PermissionActivity() {

    private var mStartTime: Long = 0 // 開始時間
    private lateinit var binding: ActivitySplashBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        checkPermission(false)
        super.onCreate(savedInstanceState)

        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)

        mStartTime = System.currentTimeMillis()
    }

    override fun initParams(bundle: Bundle?) {
        LogUtil.d()
    }

    override fun onResume() {
        super.onResume()
        LogUtil.d("onResume")
        postDelayed()
    }

    private fun initData() {
        binding.progressBar.visibility = View.VISIBLE
        launchWhenStarted {
            // Check if ephemeris files are available
            if (!EphemerisFileChecker.checkEphemerisFiles(this@SplashActivity)) {
                // Copy ephemeris files from assets
                LogUtil.d("Copying ephemeris files from assets")
                CopyAssetFiles(".*\\.se1", this@SplashActivity).copy()

                // Check again after copying
                if (!EphemerisFileChecker.checkEphemerisFiles(this@SplashActivity)) {
                    LogUtil.e("Failed to copy ephemeris files")
                }
            }

            initAspectData()
            fetchConfig()
        }
    }

    private fun initAspectData() {
        val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
        val aspectList = BasicDBHelper.querySettingAspectList(this)
        if (itemBox.all.size == aspectList.size) {
            return
        }
        for (it in aspectList) {
            val data = SettingAspectData(
                it.id, it.chName, it.enName, it.degree, it.orb, it.isOpen
            )
            itemBox.put(data)
        }
        putDegree(0, PrefKey.KEY_DEGREE_0)
        putDegree(60, PrefKey.KEY_DEGREE_60)
        putDegree(90, PrefKey.KEY_DEGREE_90)
        putDegree(120, PrefKey.KEY_DEGREE_120)
        putDegree(180, PrefKey.KEY_DEGREE_180)

    }

    companion object {
        private const val SHOW_TIME_MIN = 50 // 最小顯示時間
        const val ASSET_NAME = "db/database.db"
    }

    private var inputStreamFactory: Factory<InputStream> =
        Factory { resources.assets.open(ASSET_NAME) }

    // TODO
    private val box: BoxStore by lazy {
        MyObjectBox.builder().initialDbFile(inputStreamFactory).androidContext(this).build()
    }

    private fun putDegree(degree: Int, key: String) {
        val orb = getOrb(this, key)
        if (orb == -1) {
            return
        }
        val itemBox = ObjectBox.get().boxFor(SettingAspectData::class.java)
        var item = itemBox.query(
            SettingAspectData_.enName.equal(Chart.Natal.nameEng).and(
                SettingAspectData_.degree.equal(
                    degree
                )
            )
        ).build().findFirst()
        if (item != null) {
            item.orb = orb
        } else {
            item = SettingAspectData(
                0, getString(Chart.Natal.type), Chart.Natal.nameEng, degree, orb, true
            )
        }

        itemBox.put(item)
        PrefUtils.removeFromPrefs(this, key)
    }

    private fun getOrb(context: Context, key: String): Int {
        return PrefUtils.getFromPrefs(context, key, "-1")?.toInt() ?: -1
    }

    private fun postDelayed() {
        Handler(Looper.getMainLooper()).postDelayed({
            val loadingTime = System.currentTimeMillis() - mStartTime // 計算一下總共花費的時間
            if (loadingTime < SHOW_TIME_MIN) { // 如果比最小顯示時間還短，就延時進入MainActivity，否則直接進入
                Handler(Looper.getMainLooper()).postDelayed({
                    initData()
                }, SHOW_TIME_MIN - loadingTime)
            } else {
                initData()
            }
        }, SHOW_TIME_MIN.toLong())
    }

    private fun toMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        var bundle = getIntent().extras
        if (bundle == null) {
            bundle = Bundle()
        }
        intent.putExtras(bundle)
        startActivity(intent)
        finish()
    }

    private var retry = 0

    // 檢查版本
    private fun fetchConfig() {
        retry++
        RemoteConfigUtil.fetchConfig(this) { task: Task<Void?>, firebaseRemoteConfig: FirebaseRemoteConfig ->
            if (task.isSuccessful) {
                RemoteConfig.isOpenAd = firebaseRemoteConfig.getBoolean("isOpenAd")
                RemoteConfig.adType = firebaseRemoteConfig.getLong("adType").toInt()
                RemoteConfig.isOpenLogin = firebaseRemoteConfig.getBoolean("isOpenLogin")

                val permissions = firebaseRemoteConfig.getString("permissions_data")
                if (permissions.isNotEmpty()) {
                    PermissionsUtil.permissions = AssetsToObjectUtil.getPermissions(permissions)
                }

                var remoteConfigDataStr = firebaseRemoteConfig.getString("remote_config_data")
                if (BuildConfig.IS_DEV) {
                    remoteConfigDataStr = firebaseRemoteConfig.getString("remote_config_data_dev")
                }

                if (remoteConfigDataStr.isNotEmpty()) {
                    val remoteConfigData =
                        AssetsToObjectUtil.getRemoteConfigData(remoteConfigDataStr)
                    RemoteConfig.isOpenFeedback = remoteConfigData.isOpenFeedback
                    RemoteConfig.isOpenBilling = remoteConfigData.isOpenBilling
                    if (remoteConfigData.isMaintenance) {
                        showDialog(remoteConfigData.maintenanceMessage, false)
                    }
                    if (remoteConfigData.versionCode > BuildConfig.VERSION_CODE) {
                        if (remoteConfigData.updateType == 1) {
                            checkVersion(
                                remoteConfigData.versionCode,
                                remoteConfigData.isForceUpdate,
                                remoteConfigData.apkUrl
                            )
                        } else {
                            checkInAppUpdate(remoteConfigData.isForceUpdate)
                        }
                    } else {
                        toMainActivity()
                    }
                } else {
                    if (retry < 5) {
                        fetchConfig()
                    } else {
                        showDialog(getString(R.string.connection_is_abnormal), true)
                    }
                }
            } else {
                // 無法取得 Remote Config
                if (retry < 5) {
                    fetchConfig()
                } else {
                    showDialog(getString(R.string.connection_is_abnormal), true)
                }
            }
            binding.progressBar.visibility = View.INVISIBLE
        }
    }

    private fun showDialog(message: String, isFinish: Boolean) {
        val dialog = MaterialDialog(this@SplashActivity, MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.title(null, getString(R.string.announcement))
        dialog.message(null, message, null)
        dialog.positiveButton(null, getString(R.string.shut_down)) {
            if (isFinish) {
                finish()
            } else {
                toMainActivity()
            }
        }
        dialog.show()
    }


    private fun checkInAppUpdate(isForceUpdate: Boolean) {
        val appUpdateManager = AppUpdateManagerFactory.create(this)
        val appUpdateInfoTask = appUpdateManager.appUpdateInfo

        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            when (appUpdateInfo.updateAvailability()) {
                UpdateAvailability.UPDATE_AVAILABLE -> {
                    // 有可用更新

                    // 更新優先權（0-5）
                    val updatePriority = appUpdateInfo.updatePriority()
                    // 商店提供更新後過了幾天
                    val stalenessDays = appUpdateInfo.clientVersionStalenessDays() ?: -1
                    // 可用版本號
                    val availableVersionCode = appUpdateInfo.availableVersionCode()

                    LogUtil.d("更新優先權 $updatePriority")
                    LogUtil.d("商店提供更新後過了幾天 $stalenessDays")
                    LogUtil.d("可用版本號 $availableVersionCode")
                    if (appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
                        showUpdateDialog(isForceUpdate, appUpdateInfo)
                    } else {
                        toMainActivity()
                    }
                }

                UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS -> {
                    LogUtil.d("正在更新")
                    showDialog("正在更新...", true)
                }

                UpdateAvailability.UPDATE_NOT_AVAILABLE -> {
                    LogUtil.d("無可用更新")
                    toMainActivity()
                }

                UpdateAvailability.UNKNOWN -> {
                    LogUtil.d("未知錯誤")
                    showDialog("未知錯誤", true)
                }

                else -> {
                    LogUtil.d("else")
                    showDialog("未知錯誤", true)
                }
            }
        }

        appUpdateInfoTask.addOnFailureListener {
            LogUtil.d(it.message.toString())
            toMainActivity()
        }
    }

    private fun showUpdateDialog(isForceUpdate: Boolean, appUpdateInfo: AppUpdateInfo) {
        val dialog = MaterialDialog(this@SplashActivity, MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.title(null, getString(R.string.check_for_version_updates))
        dialog.message(null, getString(R.string.whether_update_new_version_already_exists), null)
        dialog.positiveButton(null, getString(R.string.yes)) {
            val appUpdateManager = AppUpdateManagerFactory.create(this)
            appUpdateManager.startUpdateFlowForResult(
                appUpdateInfo, AppUpdateType.IMMEDIATE, this, 1000
            )
        }
        dialog.negativeButton(null, getString(R.string.no)) {
            LogUtil.d()
            if (isForceUpdate) {
                finish()
            } else {
                toMainActivity()
            }
        }
        dialog.show()
    }

    private fun checkVersion(versionCode: Int, isForceUpdate: Boolean, apkURL: String) {
        if (versionCode > BuildConfig.VERSION_CODE) {
            val dialog =
                MaterialDialog(this@SplashActivity, MaterialDialog.DEFAULT_BEHAVIOR)
            dialog.title(null, "檢查版本更新")
            dialog.message(null, "已有新版本是否進行更新?", null)
            dialog.positiveButton(null, "是") {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(apkURL))
                startActivity(intent)
            }
            dialog.negativeButton(null, "否") {
                LogUtil.d()
                if (isForceUpdate) {
                    finish()
                } else {
                    toMainActivity()
                }
            }
            dialog.show()
        } else {
            toMainActivity()
        }
    }

}