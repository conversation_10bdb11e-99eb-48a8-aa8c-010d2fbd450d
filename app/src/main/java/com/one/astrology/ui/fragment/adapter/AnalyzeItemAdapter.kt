package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.card.MaterialCardView
import com.one.astrology.R
import com.one.astrology.data.Topics
import com.one.astrology.databinding.ItemAnalyzeBinding


class AnalyzeItemAdapter : BaseQuickAdapter<Topics.TopicItem.Content, AnalyzeItemAdapter.VH>() {

    class VH(
        parent: ViewGroup,
        val binding: ItemAnalyzeBinding = ItemAnalyzeBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: VH,
        position: Int,
        item: Topics.TopicItem.Content?
    ) {
        if (item != null) {
            holder.binding.tvTitle.text = item.title
            holder.binding.tvCondition.text = item.condition


            if (item.description.isEmpty()) {
                holder.binding.tvDescribe.visibility = View.GONE
            } else {
                holder.binding.tvDescribe.visibility = View.VISIBLE
                holder.binding.tvDescribe.text = item.description
            }


            if (item.isChecked) {
                holder.itemView.findViewById<MaterialCardView>(R.id.cardView)
                    .setCardBackgroundColor(
                        ContextCompat.getColor(context, R.color.colorPrimary)
                    )
                var result = ""
                item.resultList?.forEach {
                    result += " ✓ $it\n"
                }
                if (result.length > 1) {
                    result = result.substring(0, result.length - 1)
                    holder.binding.tvResult.text = result
                    holder.binding.tvDescribe.text = item.description
                    holder.binding.tvResult.visibility = View.VISIBLE
                }
                if (item.score == null) {
                    holder.binding.tvScore.visibility = View.GONE
                } else {
                    holder.binding.tvScore.visibility = View.VISIBLE
                    if (item.score > 0) {
                        holder.binding.tvScore.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.colorPrimaryYellow
                            )
                        )
                        holder.binding.tvResult.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.colorPrimaryYellow
                            )
                        )
                    } else {
                        holder.binding.tvScore.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.red_light
                            )
                        )
                        holder.binding.tvResult.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.red_light
                            )
                        )
                    }
                    var score = 0
                    item.resultList?.forEach {
                        score += item.score
                    }
                    holder.binding.tvScore.text = "${score}分"
                }
            } else {
                holder.binding.tvResult.visibility = View.GONE
                if (item.score == null) {
                    holder.binding.tvScore.visibility = View.GONE
                } else {
                    holder.binding.tvScore.visibility = View.VISIBLE
                    holder.binding.tvScore.text = "${item.score}分"
                    holder.binding.tvScore.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.white
                        )
                    )
                }
                holder.itemView.findViewById<MaterialCardView>(R.id.cardView)
                    .setCardBackgroundColor(
                        ContextCompat.getColor(context, R.color.grey)
                    )
            }

            holder.itemView.tag = item
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): VH {
        return VH(parent)
    }
}