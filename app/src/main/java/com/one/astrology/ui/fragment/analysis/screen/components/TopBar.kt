package com.one.astrology.ui.fragment.analysis.screen.components

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.fragment.analysis.screen.TimestampToLocalDateTimeText
import com.one.astrology.ui.fragment.dialog.UserSelectionDialog
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


@Composable
fun TopBar(
    name: String,
    birthDataString: String,
    onBirthDataSelected: (List<BirthData>) -> Unit,
    onDateTimePicker: ((Date) -> Unit)? = null,
    onCopy: () -> Unit,
    onSwap: (() -> Unit)? = null,
) {
    var isShowDialog by remember { mutableStateOf(false) }
    if (isShowDialog) {
        UserSelectionDialog(
            onDismissRequest = { isShowDialog = false },
            isMultiSelect = false,
            onUserSelected = onBirthDataSelected
        )
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(colorResource(id = R.color.colorPrimary))
            .padding(start = 10.dp, top = 0.dp, end = 10.dp, bottom = 10.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TopBarTitle(
                name,
                birthDataString,
                onDateTimePicker,
                onAutoTransit = null,
                onCopy,
                { isShowDialog = true },
                onSwap
            )
            Spacer(modifier = Modifier.weight(1f))
            SelectUserButton { isShowDialog = true }
        }
    }
}

@Composable
fun TopBarDouble(
    nameA: String,
    birthDataStringA: String,
    nameB: String,
    birthDataStringB: String,
    onBirthDataSelected: (List<BirthData>) -> Unit,
    onDateTimePicker: ((Date) -> Unit)? = null,
    onCopy: (() -> Unit)? = null,
    onSwap: (() -> Unit)? = null,
) {
    var isShowDialog by remember { mutableStateOf(false) }
    if (isShowDialog) {
        UserSelectionDialog(
            onDismissRequest = { isShowDialog = false },
            isMultiSelect = true,
            onUserSelected = onBirthDataSelected
        )
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(colorResource(id = R.color.colorPrimary))
            .padding(start = 10.dp, top = 0.dp, end = 10.dp, bottom = 10.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            TopBarTitle(
                nameA,
                birthDataStringA,
                nameB,
                birthDataStringB,
                onDateTimePicker,
                onCopy,
                { isShowDialog = true },
                onSwap
            )
            Spacer(modifier = Modifier.weight(1f))
            if (onSwap != null) {
                SwapButton(onSwap)
            }
            SelectUserButton { isShowDialog = true }
        }
    }
}

@Composable
fun TopBarSingle(
    birthData: BirthData,
    onBirthDataSelected: (List<BirthData>) -> Unit,
    onAutoTransit: () -> Unit,
    onDateTimePicker: (Date) -> Unit,
    onCopy: (() -> Unit)? = null,
    onSwap: (() -> Unit)? = null,
) {
    var isShowDialog by remember { mutableStateOf(false) }
    UserSelectionDialogWrapper(
        isShowDialog,
        onDismiss = { isShowDialog = false },
        onUserSelected = onBirthDataSelected
    )

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(colorResource(id = R.color.colorPrimary))
            .padding(start = 10.dp, top = 0.dp, end = 10.dp, bottom = 10.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TopBarTitle(
                birthData.name,
                birthData.generateBirthdayString(),
                onDateTimePicker,
                onAutoTransit = onAutoTransit,
                onCopy,
                { isShowDialog = true },
                onSwap
            )
            Spacer(modifier = Modifier.weight(1f))
            AutoTransitButton(onAutoTransit)
            SelectUserButton { isShowDialog = true }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CenteredToolbar(
    onAutoTransit: (() -> Unit)? = null,
    onCopy: (() -> Unit)?,
    onEdit: (() -> Unit)?,
    onSwap: (() -> Unit)?
) {
    TopAppBar( // CenterAlignedTopAppBar
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = colorResource(id = R.color.colorPrimary), // 背景色 (紫色)
            titleContentColor = Color.White, // 標題顏色
            navigationIconContentColor = Color.White, // 返回按鈕顏色
            actionIconContentColor = Color.Yellow // 右側圖示按鈕顏色
        ),
        title = {
            Text(
                text = "星盤解讀",
                color = Color.White,
                fontSize = 22.sp,
                fontWeight = FontWeight.Bold
            )
        },
//        navigationIcon = {
//            IconButton(onClick = { /* 處理返回 */ }) {
//                Icon(Icons.Default.Menu, contentDescription = "菜單")
//            }
//        },
        actions = {
            if (onAutoTransit != null) {
                IconButton(onClick = onAutoTransit) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_baseline_search_24),
                        contentDescription = "自動推運",
                        colorFilter = ColorFilter.tint(Color.White),
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(colorResource(id = R.color.colorPrimary))
                    )
                }
            }
            if (onCopy != null) {
                IconButton(onClick = onCopy) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_copy),
                        contentDescription = "複製",
                        tint = Color.White
                    )
                }
            }

            if (onSwap != null) {
                IconButton(onClick = onSwap) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_baseline_swap_horiz_24),
                        contentDescription = "交換",
                        tint = Color.White
                    )
                }
            }

            if (onEdit != null) {
                IconButton(onClick = onEdit) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_edit_person),
                        contentDescription = "新增",
                        tint = Color.White
                    )
                }
            }

        }
    )
}

@Composable
fun UserInfo(name: String, birthDataString: String) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = colorResource(id = R.color.white_t10)
        ),
        shape = AppShapes.small,
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(10.dp) // 內部間距
        ) {
            Text(
                text = name, color = Color.White, fontSize = 20.sp
            )
            Spacer(Modifier.height(10.dp))
            TimestampToLocalDateTimeText(birthDataString)
        }
    }
}

@Composable
fun TransitTimePicker(onDateTimePicker: ((Date) -> Unit)?) {
    if (onDateTimePicker != null) {
        val calendar = remember { Calendar.getInstance() }
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        var transitTime by remember { mutableStateOf(dateFormat.format(calendar.time)) }

        DateTimePicker(
            transitTime = transitTime,
            onDateTimeChange = { selectedTime ->
                val formattedTime = dateFormat.format(selectedTime)
                transitTime = formattedTime
                onDateTimePicker(selectedTime)
            }
        )
    }
}


@Composable
fun TopBarTitle(
    name: String,
    birthDataString: String,
    onDateTimePicker: ((Date) -> Unit)? = null,
    onAutoTransit: (() -> Unit)? = null,
    onCopy: (() -> Unit)? = null,
    onEdit: () -> Unit,
    onSwap: (() -> Unit)? = null,
) {
    Column(modifier = Modifier) {
        CenteredToolbar(
            onAutoTransit = onAutoTransit,
            onCopy = onCopy,
            onEdit = onEdit,
            onSwap = onSwap
        )
        UserInfo(name, birthDataString)
        Spacer(Modifier.height(10.dp))
        // 顯示推運時間並且提供編輯功能
        if (onDateTimePicker != null) {
            TransitTimePicker(onDateTimePicker)
        }
    }
}

@Composable
fun DateTimePicker(
    transitTime: String,
    onDateTimeChange: (Date) -> Unit
) {
    val context = LocalContext.current
    val calendar = Calendar.getInstance()
    Card(
        colors = CardDefaults.cardColors(
            containerColor = colorResource(id = R.color.white_t10)
        ),
        shape = AppShapes.small,
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.padding(10.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(text = "推運時間：$transitTime", color = Color.White, fontSize = 14.sp)
            Spacer(modifier = Modifier.weight(1f))
            IconButton(
                onClick = {
                    showDateTimePicker(context, calendar) { selectedTime ->
                        onDateTimeChange(selectedTime)
                    }
                },
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "編輯推運時間",
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

// 彈出日期 & 時間選擇器
fun showDateTimePicker(context: Context, calendar: Calendar, onDateTimeSelected: (Date) -> Unit) {
    DatePickerDialog(
        context,
        { _, year, month, dayOfMonth ->
            calendar.set(year, month, dayOfMonth)

            // 日期選擇完畢，彈出時間選擇器
            TimePickerDialog(
                context,
                { _, hour, minute ->
                    calendar.set(Calendar.HOUR_OF_DAY, hour)
                    calendar.set(Calendar.MINUTE, minute)
                    onDateTimeSelected(calendar.time)
                },
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                true
            ).show()
        },
        calendar.get(Calendar.YEAR),
        calendar.get(Calendar.MONTH),
        calendar.get(Calendar.DAY_OF_MONTH)
    ).show()
}

@Composable
fun TopBarTitle(
    name: String,
    birthDataString: String,
    nameB: String,
    birthDataStringB: String,
    onDateTimePicker: ((Date) -> Unit?)? = null,
    onCopy: (() -> Unit)? = null,
    onEdit: (() -> Unit)? = null,
    onSwap: (() -> Unit)? = null,
) {
    // 取得當前時間作為推運時間的預設值
    val calendar = remember { Calendar.getInstance() }
    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    var transitTime by remember { mutableStateOf(dateFormat.format(calendar.time)) }

    Column(modifier = Modifier) {
        CenteredToolbar(
            onCopy = onCopy,
            onEdit = onEdit,
            onSwap = onSwap
        )
        UserInfo(name, birthDataString)

        Spacer(Modifier.height(10.dp))

        UserInfo(nameB, birthDataStringB)

        Spacer(Modifier.height(10.dp))
        // 顯示推運時間並且提供編輯功能
        if (onDateTimePicker != null) {
            DateTimePicker(
                transitTime = transitTime,
                onDateTimeChange = { selectedTime ->
                    val formattedTime = dateFormat.format(selectedTime)
                    transitTime = formattedTime
                    onDateTimePicker(selectedTime)
                }
            )
        }
    }
}

@Composable
fun AutoTransitButton(onClick: () -> Unit) {
    IconButton(onClick = onClick) {
        Image(
            painter = painterResource(id = R.drawable.ic_baseline_search_24),
            contentDescription = "自動推運",
            colorFilter = ColorFilter.tint(Color.White),
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(colorResource(id = R.color.colorPrimary))
        )
    }
}

@Composable
fun SwapButton(onClick: () -> Unit) {
    IconButton(onClick = onClick) {
        Image(
            painter = painterResource(id = R.drawable.ic_baseline_swap_horiz_24),
            contentDescription = "交換",
            colorFilter = ColorFilter.tint(Color.White),
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(colorResource(id = R.color.colorPrimary))
        )
    }
}

@Composable
fun SelectUserButton(onClick: () -> Unit) {
    IconButton(onClick = onClick) {
        Image(
            painter = painterResource(id = R.drawable.ic_edit_person),
            contentDescription = "選擇檔案",
            colorFilter = ColorFilter.tint(Color.White),
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(colorResource(id = R.color.colorPrimary))
        )
    }
}

@Composable
fun UserSelectionDialogWrapper(
    isShowDialog: Boolean,
    onDismiss: () -> Unit,
    onUserSelected: (List<BirthData>) -> Unit
) {
    if (isShowDialog) {
        UserSelectionDialog(
            onDismissRequest = onDismiss,
            isMultiSelect = false,
            onUserSelected = onUserSelected
        )
    }
}
