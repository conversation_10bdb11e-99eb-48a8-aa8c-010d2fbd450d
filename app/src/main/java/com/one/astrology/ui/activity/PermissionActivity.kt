package com.one.astrology.ui.activity

import android.Manifest
import android.content.DialogInterface
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import com.one.core.R
import com.one.core.activity.BaseActivity
import com.one.core.util.LogUtil
import permissions.dispatcher.NeedsPermission
import permissions.dispatcher.OnNeverAskAgain
import permissions.dispatcher.OnPermissionDenied
import permissions.dispatcher.OnShowRationale
import permissions.dispatcher.PermissionRequest
import permissions.dispatcher.RuntimePermissions

@RuntimePermissions
abstract class PermissionActivity : BaseActivity() {
    private var mIsCheckPermission = false
    var isAllowPermissions: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (mIsCheckPermission) {
            showPermissionsAllowWithPermissionCheck()
        }
    }

    override fun onResume() {
        super.onResume()
        LogUtil.d("onResume")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // NOTE: delegate the permission handling to generated function
        onRequestPermissionsResult(requestCode, grantResults)
    }

    override fun checkPermission(isCheckPermission: Boolean) {
        mIsCheckPermission = isCheckPermission
    }

    override fun onPause() {
        super.onPause()
        LogUtil.i("onPause")
    }

    // 用戶允許權限
    @NeedsPermission(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    open fun showPermissionsAllow() {
        LogUtil.i("permissions :" + "用戶允許權限")
        isAllowPermissions = true
    }

    // 向用戶說明為什麼需要這些權限
    @OnShowRationale(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun onShowRationale(request: PermissionRequest) {
        LogUtil.i("permissions : " + "向用戶說明為什麼需要這些權限")
        AlertDialog.Builder(this)
            .setTitle("請求授權")
            .setMessage(R.string.permission_rationale)
            .setPositiveButton(R.string.button_allow) { _: DialogInterface?, _: Int -> request.proceed() }
            .setNegativeButton(R.string.button_deny) { _: DialogInterface?, _: Int -> request.cancel() }
            .show()
    }

    // 用戶拒絕授權回調（可選）
    @OnPermissionDenied(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun onPermissionDenied() {
        LogUtil.i("permissions : " + "用戶拒絕")
        //        Toast.makeText(this, R.string.permission_camera_denied, Toast.LENGTH_SHORT).show();
    }

    // 用戶勾選了“不再提醒”時調用（可選）
    @OnNeverAskAgain(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    open fun onNeverAskAgain() {
        LogUtil.i("permissions : " + "不再提醒")
        //        Toast.makeText(this, R.string.permission_camera_neverask, Toast.LENGTH_SHORT).show();
    }

    fun AppCompatActivity.askPermission(
        permission: String,
        onPermissionGranted: (() -> Unit)? = null,
        onShouldShowRequestRational: ((() -> Unit) -> Unit)? = null,
        onPermissionDenied: (() -> Unit)? = null
    ) {
        val requestPermission = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted: Boolean ->
            if (isGranted) onPermissionGranted?.invoke()
            else onPermissionDenied?.invoke()
        }

        if (checkRecordAudioPermission(permission))
            onPermissionGranted?.invoke()
        else if (shouldShowRequestPermissionRationale(permission))
            onShouldShowRequestRational?.invoke { requestPermission.launch(permission) }
        else
            requestPermission.launch(permission)
    }

    private fun AppCompatActivity.checkRecordAudioPermission(permission: String): Boolean {
        if (ActivityCompat.checkSelfPermission(
                this,
                permission
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                this, arrayOf(
                    permission
                ), 22
            )
            return false
        }
        return true
    }
}