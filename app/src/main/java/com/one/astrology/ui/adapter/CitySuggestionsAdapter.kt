package com.one.astrology.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.one.astrology.R
import com.one.astrology.data.model.City

class CitySuggestionsAdapter(
    private val onCitySelected: (City) -> Unit
) : ListAdapter<City, CitySuggestionsAdapter.CityViewHolder>(CityDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CityViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_city_suggestion, parent, false)
        return CityViewHolder(view)
    }

    override fun onBindViewHolder(holder: CityViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CityViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvCityName: TextView = itemView.findViewById(R.id.tvCityName)
        private val tvCountry: TextView = itemView.findViewById(R.id.tvCountry)

        fun bind(city: City) {
            tvCityName.text = city.name
            tvCountry.text = city.country
            itemView.setOnClickListener { onCitySelected(city) }
        }
    }

    private class CityDiffCallback : DiffUtil.ItemCallback<City>() {
        override fun areItemsTheSame(oldItem: City, newItem: City): Boolean {
            return oldItem.name == newItem.name && 
                   oldItem.latitude == newItem.latitude && 
                   oldItem.longitude == newItem.longitude
        }

        override fun areContentsTheSame(oldItem: City, newItem: City): Boolean {
            return oldItem == newItem
        }
    }
} 