package com.one.astrology.ui.compose.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Surface
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.one.astrology.R
import com.one.astrology.data.model.Announcement
import com.one.astrology.data.model.AnnouncementType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnnouncementListScreen(
    announcements: List<Announcement>,
    onAnnouncementClick: (Announcement) -> Unit
) {
    var selectedCategory by remember { mutableStateOf(0) }
    val categories = listOf("全部", "重要", "活動", "系統")

    Column(modifier = Modifier.fillMaxSize()) {
        // 頂部標題欄
        TopAppBar(
            title = {
                Text(
                    text = "公告",
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.White
            )
        )

        // 分類標籤
        ScrollableTabRow(
            selectedTabIndex = selectedCategory,
            containerColor = Color.White,
            contentColor = colorResource(id = R.color.colorPrimary),
            edgePadding = 0.dp
        ) {
            categories.forEachIndexed { index, category ->
                Tab(
                    selected = selectedCategory == index,
                    onClick = { selectedCategory = index },
                    text = { Text(category) }
                )
            }
        }

        // 公告列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            val filteredAnnouncements = when (selectedCategory) {
                0 -> announcements
                1 -> announcements.filter { it.type == AnnouncementType.IMPORTANT }
                2 -> announcements.filter { it.type == AnnouncementType.ACTIVITY }
                3 -> announcements.filter { it.type == AnnouncementType.SYSTEM }
                else -> announcements
            }

            items(filteredAnnouncements) { announcement ->
                AnnouncementItem(
                    announcement = announcement,
                    onClick = { onAnnouncementClick(announcement) }
                )
            }
        }
    }
}

@Composable
private fun AnnouncementItem(
    announcement: Announcement,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = announcement.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.Black
                )
                
                if (announcement.type == AnnouncementType.IMPORTANT) {
                    Surface(
                        color = colorResource(id = R.color.red_light),
                        shape = MaterialTheme.shapes.small
                    ) {
                        Text(
                            text = "重要",
                            color = Color.White,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = announcement.publishTime,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
            
            if (!announcement.isVisible) {
                Text(
                    text = "未讀",
                    color = colorResource(id = R.color.colorPrimary),
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
} 