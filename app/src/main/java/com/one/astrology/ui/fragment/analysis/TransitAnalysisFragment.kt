package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBar
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil


/**
 * 運勢分析
 */
class TransitAnalysisFragment : Fragment() {

    private val chatViewModel by viewModels<ChatViewModel>()
    private val chartName = "行運盤"

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                MainAnalyzeScreen(
                    chatViewModel
                )
            }
        }
    }

    @Composable
    fun MainAnalyzeScreen(viewModel: ChatViewModel) {
        val context = LocalContext.current
        var matchEvent by remember { mutableStateOf(MatchEvent()) }
        var birthData by remember { mutableStateOf(BirthData()) }

        val isLoading by viewModel.isLoading.observeAsState(false)
        val data by viewModel.data.observeAsState(ReadingData("", "", ""))
        val errorMessage by viewModel.error.observeAsState("")
        var dataNow by remember { mutableLongStateOf(System.currentTimeMillis()) }

        LaunchedEffect(errorMessage) {
            LogUtil.d("errorMessage 更新: $errorMessage")
            birthData = viewModel.getBirthData()
            if (birthData.name.isEmpty()) {
                return@LaunchedEffect
            }
            val birthDataNow = BirthData(birthData)
            birthDataNow.birthday = dataNow
            matchEvent =
                viewModel.calculateHoroscope(context, Chart.Transit, birthData, birthDataNow)
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            TopBar(
                matchEvent.horoscopeA.name,
                matchEvent.horoscopeA.getBirthdayString(), { birthDataList ->
                    val selectedBirthData = birthDataList[0]
                    if (selectedBirthData.name != birthData.name) {
                        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                        birthData = viewModel.getBirthData()
                        if (birthData.name.isNotEmpty()) {
                            birthData.isSelected = false
                            birthDataBox.put(birthData)
                        }
                        birthData = selectedBirthData
                        birthData.isSelected = true
                        birthDataBox.put(birthData)
                        val birthDataNow = BirthData(birthData)
                        birthDataNow.birthday = dataNow
                        matchEvent =
                            viewModel.calculateHoroscope(
                                context,
                                Chart.Transit,
                                birthData,
                                birthDataNow
                            )
                        viewModel.init()
                    }
                }, { date ->
                    dataNow = date.time
                }, onCopy = {}
            )

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = listOf(
                            "${chartName}當前行運" to "${chartName}：解析當前行星在天空中的運行，及其對個人命盤的即時影響 (繁體中文):\n",
                            "${chartName}財運變動" to "${chartName}：探索行運盤中行星對財運、金錢流動及資源配置的影響 (繁體中文):\n",
                            "${chartName}感情與人際互動" to "${chartName}：剖析當前行運對感情狀況、人際互動與關係變化的影響 (繁體中文):\n",
                            "${chartName}家庭與親緣" to "${chartName}：解析當前行運如何影響家庭關係、親密關係與家族動態 (繁體中文):\n",
                            "${chartName}事業與目標" to "${chartName}：分析行運盤中行星如何對事業發展、職業目標及工作環境產生影響 (繁體中文):\n",
                            "${chartName}健康狀況與生活方式" to "${chartName}：探索行運盤中行星如何影響身心健康、生活方式及健康挑戰 (繁體中文):\n",
                            "${chartName}挑戰與機遇" to "${chartName}：揭示當前行運中的挑戰與成長契機，如何促進自我發展與突破 (繁體中文):\n",
                            "${chartName}自我認識與內心變化" to "${chartName}：解析當前行運對自我認識、心理狀態與內在變化的影響 (繁體中文):\n"
                        )
                        CategoryList(
                            context,
                            categories,
                            chatViewModel,
                            matchEvent,
                            chart = Chart.Transit
                        )
                    }
                }
            }
        }
    }
}



