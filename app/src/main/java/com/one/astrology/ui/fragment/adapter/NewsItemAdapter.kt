package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.R
import com.one.astrology.data.News
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.ItemNewsBinding

class NewsItemAdapter :
    BaseQuickAdapter<News.NewsItem.Content, NewsItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemNewsBinding = ItemNewsBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: BaseViewHolder,
        position: Int,
        item: News.NewsItem.Content?
    ) {
        if (item != null) {
            holder.binding.tvTitle.text = item.title
            holder.binding.tvContent.text = item.text

            if (item.text.isNotEmpty()) {
                holder.binding.tvContent.text = item.text
                holder.binding.tvContent.visibility = View.VISIBLE
            } else {
                holder.binding.tvContent.visibility = View.GONE
            }
            holder.binding.tvSource.text = item.source

            if (item.birthdayData != null) {
                holder.binding.ivAdd.visibility = View.VISIBLE
            } else {
                holder.binding.ivAdd.visibility = View.GONE
            }

            if (item.type == Chart.Natal.nameEng || item.type == Chart.Synastry.nameEng) {
                holder.binding.tvMore.text = context.getString(R.string.view_the_astrolabe)
                holder.binding.tvMore.visibility = View.VISIBLE
                holder.binding.lltBottom.visibility = View.VISIBLE
            } else {
                if (item.url?.isNotEmpty() == true) {
                    holder.binding.tvMore.visibility = View.VISIBLE
                    holder.binding.lltBottom.visibility = View.VISIBLE
                } else {
                    holder.binding.tvMore.visibility = View.GONE
                    holder.binding.lltBottom.visibility = View.GONE
                }
            }
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}