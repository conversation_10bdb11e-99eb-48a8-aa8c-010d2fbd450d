package com.one.astrology.ui.activity


import android.os.Build
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.core.view.get
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.messaging.FirebaseMessaging
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.SettingsPreferencesDataStore
import com.one.astrology.databinding.ActivityMainBinding
import com.one.astrology.ui.fragment.AnalyzeFragment
import com.one.astrology.ui.fragment.analysis.CompositeAnalyzeFragment
import com.one.astrology.ui.fragment.analysis.CompositeProgressionFragment
import com.one.astrology.ui.fragment.analysis.DavisonAnalyzeFragment
import com.one.astrology.ui.fragment.analysis.DivinationAnalysisFragment
import com.one.astrology.ui.fragment.analysis.FirdariaAnalyzeFragment
import com.one.astrology.ui.fragment.analysis.MarksAnalyzeFragment
import com.one.astrology.ui.fragment.analysis.NatalAnalyzeFragment
import com.one.astrology.ui.fragment.analysis.ReadingDataListFragment
import com.one.astrology.ui.fragment.analysis.SecondaryProgressionsFragment
import com.one.astrology.ui.fragment.analysis.SolarArcFragment
import com.one.astrology.ui.fragment.analysis.SolarReturnFragment
import com.one.astrology.ui.fragment.analysis.SynastryAnalyzeFragment
import com.one.astrology.ui.fragment.analysis.TransitAnalysisFragment
import com.one.astrology.ui.fragment.bank.DataBankFragment
import com.one.astrology.ui.fragment.bank.DataBankListFragment
import com.one.astrology.ui.fragment.bottomSheet.SettingBottomSheetFragment
import com.one.astrology.ui.fragment.horoscopes.HoroscopesFragment
import com.one.astrology.ui.fragment.navigation.ArticleFragment
import com.one.astrology.ui.fragment.navigation.BirthDataListFragment
import com.one.astrology.ui.fragment.navigation.FateFragment
import com.one.astrology.ui.fragment.navigation.HomeFragment
import com.one.astrology.ui.fragment.navigation.SettingFragment
import com.one.astrology.ui.fragment.unnecessary.MatchFragment
import com.one.astrology.util.launchWhenStarted
import com.one.core.data.RemoteConfig
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : PermissionActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var currentFragment: Fragment

    //    private val fcmViewModel by viewModels<FcmViewModel>()


    private val navHostFragment by lazy {
        supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
    }

    private val navController by lazy {
        navHostFragment.navController
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Android 33 需判斷
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            askPermission(android.Manifest.permission.POST_NOTIFICATIONS)
        }

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        supportFragmentManager.registerFragmentLifecycleCallbacks(fragmentLifecycleCallbacks, true)

        navController.addOnDestinationChangedListener(listener)
        getFcmToken()


        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                backPressed()
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        navController.removeOnDestinationChangedListener(listener)
    }

    private val listener =
        NavController.OnDestinationChangedListener { controller, destination, arguments ->
            val bundle = Bundle()
            val currentFragmentClassName =
                (controller.currentDestination as FragmentNavigator.Destination).className
            bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, destination.label.toString())
            bundle.putString(FirebaseAnalytics.Param.SCREEN_CLASS, currentFragmentClassName)
            FirebaseAnalytics.getInstance(this)
                .logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
        }

    private val fragmentLifecycleCallbacks = object : FragmentManager.FragmentLifecycleCallbacks() {
        override fun onFragmentViewCreated(
            fm: FragmentManager, fragment: Fragment, v: View, savedInstanceState: Bundle?
        ) {
            currentFragment = fragment
            when (currentFragment) {
                is TransitAnalysisFragment,
                is SecondaryProgressionsFragment,
                is SolarArcFragment,
                is SolarReturnFragment,
                is DivinationAnalysisFragment,
                is SynastryAnalyzeFragment,
                is CompositeAnalyzeFragment,
                is DavisonAnalyzeFragment,
                is CompositeProgressionFragment,
                is MarksAnalyzeFragment,
                is FirdariaAnalyzeFragment,
                is ReadingDataListFragment,
                is NatalAnalyzeFragment -> {
                    binding.appbar.visibility = View.GONE
                    binding.bottomNavigation.visibility = View.GONE
                }

                is DataBankFragment,
                is DataBankListFragment,
                is AnalyzeFragment -> {
                    binding.appbar.visibility = View.VISIBLE
                    binding.bottomNavigation.visibility = View.GONE
                }

                is HomeFragment,
                is ArticleFragment,
                is BirthDataListFragment,
                is FateFragment,
                is MatchFragment,
                is HoroscopesFragment,
                is SettingFragment -> {
                    binding.appbar.visibility = View.VISIBLE
                    binding.bottomNavigation.visibility = View.VISIBLE
                }

                else -> {
                    LogUtil.d("else")
                }
            }
        }
    }

//    override fun onNewIntent(intent: Intent) {
//        super.onNewIntent(intent)
//        initView()
//    }

    override fun initParams(bundle: Bundle?) {

    }

    private fun initView() {
        setSupportActionBar(binding.toolbar)
        setupBottomNavigation()
    }

    private fun setupBottomNavigation() {
        if (BuildConfig.IS_DEV) {
            binding.bottomNavigation.menu.clear()
            binding.bottomNavigation.inflateMenu(R.menu.bottom_navigation_menu_dev)
//            binding.bottomNavigation.menu[3].isVisible = BuildConfig.IS_DEV
            binding.bottomNavigation.menu[4].isVisible =
                RemoteConfig.isOpenLogin || BuildConfig.IS_ENABLE_LOGIN
        } else {
            binding.bottomNavigation.menu.clear()
            binding.bottomNavigation.inflateMenu(R.menu.bottom_navigation_menu)
        }

        binding.bottomNavigation.setupWithNavController(navController)

        binding.bottomNavigation.setOnItemSelectedListener {
            title = it.title
            when (it.itemId) {
                R.id.homeFragment -> {
                    navController.navigate(R.id.homeFragment)
                }

                R.id.articleFragment -> {
                    navController.navigate(R.id.articleFragment)
                }

                R.id.birthDataListFragment -> {
                    navController.navigate(R.id.birthDataListFragment)
                }

                R.id.socialFragment -> {
                    navController.navigate(R.id.socialFragment)
                }

                R.id.horoscopesFragment -> {
                    navController.navigate(R.id.horoscopesFragment)
                }

                R.id.fortuneFragment -> {
                    navController.navigate(R.id.fortuneFragment)
                }

                R.id.fateFragment -> {
                    navController.navigate(R.id.fateFragment)
                }

                R.id.matchFragment -> {
                    navController.navigate(R.id.matchFragment)
                }

                R.id.settingFragment -> {
                    navController.navigate(R.id.settingFragment)
                }

                else -> {
                    Toast.makeText(this, "Unknown menu item", Toast.LENGTH_SHORT).show()
                }
            }
            true
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
//        menu.clear()
//        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_settings) {
            SettingBottomSheetFragment().show(
                supportFragmentManager, "SettingBottomSheetFragment"
            )
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    fun backPressed() {
        if (!navController.popBackStack()) {
            val dialog = MaterialDialog(this, MaterialDialog.DEFAULT_BEHAVIOR)
            dialog.message(null, getString(R.string.withdraw_from_the_program), null)
            dialog.positiveButton(null, getString(R.string.yes)) {
                finish()
            }
            dialog.negativeButton(null, getString(R.string.no)) {
                dialog.dismiss()
            }
            dialog.show()
        }
    }

    private fun getFcmToken() {
        LogUtil.d("getFcmToken")
        FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
            if (!task.isSuccessful) {
                LogUtil.e("Fetching FCM registration token failed " + task.exception)
                return@OnCompleteListener
            }

            // Get new FCM registration token
            val token = task.result
            LogUtil.d("FCM token : $token")
            launchWhenStarted {
                SettingsPreferencesDataStore.putFcmToken(this@MainActivity, token)
            }
//            lifecycleScope.launchWhenStarted {
//                fcmViewModel.send(token, "test", "message").collect {
////                    LogUtil.d(it.id)
//                }
//            }

//            updateMeFcmToken(token)
        })
    }
}