package com.one.astrology.ui.fragment.analysis

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Geocoder
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.aiModels
import com.one.astrology.data.type.Chart
import com.one.astrology.ui.fragment.analysis.screen.DatePickerField
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.TimePickerField
import com.one.astrology.ui.fragment.analysis.screen.rememberRemoteConfigState
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.RemoteConfigManager
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * 卜卦分析
 * 問題的具體內容（要清楚且具體，例如「這份工作適合我嗎？」而非「我的未來如何？」）
 * 提出問題的時間與地點（卜卦占星需要問題產生的確切時間與地點來起盤）
 *
 * **檢查星盤的可判讀性**
 * 有些星盤可能顯示「不可解讀」的狀態，例如：
 * - **上升點 0°-3° 或 27°-29°**：問題可能過早或太晚，不適合解讀。
 * - **月亮空亡（Void of Course Moon）**：代表事情不會有進一步發展或結果已定。
 * - **土星在第七宮**：占星師可能有誤判風險，需特別謹慎解釋。
 * - **水星逆行**：可能需要重新審視問題，或代表誤解與變數。
 */
class DivinationAnalysisFragment : Fragment() {
    private val chatViewModel by viewModels<ChatViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                MainAnalyzeScreen()
            }
        }
    }

    @Composable
    fun MainAnalyzeScreen() {
        Column {
            val context = LocalContext.current

            val isLoading by chatViewModel.isLoading.observeAsState(false)
            val data by chatViewModel.data.observeAsState(ReadingData("", "", ""))
            val errorMessage by chatViewModel.error.observeAsState("")
            val dateState = remember { mutableStateOf("") }
            val timeState = remember { mutableStateOf("") }
            val placeState = remember { mutableStateOf("") }
            val questState = remember { mutableStateOf("") }


            val isConfigLoaded = rememberRemoteConfigState()

            if (isConfigLoaded.value) {
                val groqApiKey = RemoteConfigManager.getString("groqApiKey")
                val groqAiModel = RemoteConfigManager.getString("groqAiModel")

                val openAiApiKey = RemoteConfigManager.getString("openAiApiKey")
                var aiModel = RemoteConfigManager.getString("aiModel")
                if (BuildConfig.IS_DEV) {
                    aiModel = EncryptedSPUtil.getAIModule(context)
                } else {
                    if (groqApiKey.isEmpty() || groqAiModel.isEmpty() || openAiApiKey.isEmpty() || aiModel.isEmpty()) {
                        ErrorMessage("目前無法使用此服務")
                        return
                    }
                }
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(0.dp),
                    contentAlignment = Alignment.Center
                ) {
                    if (isLoading) {
                        LoadingContent()
                    } else {
                        if (errorMessage.isNotEmpty()) {
                            ErrorMessage(errorMessage)
                        } else if (data.title.isNotEmpty()) {
                            val bundle = Bundle().apply {
                                putParcelable("reading_data", data)
                            }
                            findNavController().navigate(R.id.markdownFragment, bundle)
                            chatViewModel.init()
                        } else {
                            var latitudeData = 0.0
                            var longitudeData = 0.0
                            FormScreen(
                                dateState,
                                timeState,
                                placeState,
                                questState, { latitude, longitude ->
                                    latitudeData = latitude
                                    longitudeData = longitude
                                }
                            ) {
                                val dateString = dateState.value + " " + timeState.value
                                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                                val date = sdf.parse(dateString)
                                val timestamp = date?.time
                                val coordinates = getLatLngFromAddress(context, placeState.value)
                                LogUtil.d(dateString)

                                val birthData = BirthData()
                                birthData.name = "卜卦"
                                if (timestamp != null) {
                                    birthData.birthday = timestamp
                                }
                                birthData.birthplaceArea = placeState.value
                                if (coordinates != null) {
                                    birthData.birthplaceLatitude = latitudeData
                                    birthData.birthplaceLongitude = longitudeData
                                }
                                val horoscope =
                                    chatViewModel.calculateHoroscope(
                                        context,
                                        Chart.Natal,
                                        birthData
                                    )
                                chatViewModel.readingData.birthDataA = birthData
                                chatViewModel.readingData.chart = Chart.Natal
                                sendReq(
                                    context,
                                    aiModel,
                                    horoscope,
                                    openAiApiKey,
                                    groqApiKey,
                                    questState.value
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun FormScreen(
        dateState: MutableState<String>,
        timeState: MutableState<String>,
        placeState: MutableState<String>,
        questState: MutableState<String>,
        onLocationRetrieved: (Double, Double) -> Unit,
        onClick: () -> Unit
    ) {
        val latitude = remember { mutableStateOf(0.0) }
        val longitude = remember { mutableStateOf(0.0) }
        val locationError = remember { mutableStateOf("") }
        val coroutineScope = rememberCoroutineScope()
        val context = LocalContext.current

        Column {
            TopAppBar(
                title = { Text("卜卦分析", color = Color.White) },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = colorResource(id = R.color.colorPrimary)
                )
            )

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                Spacer(modifier = Modifier.height(0.dp))

                DatePickerField(label = "選擇日期", dateState = dateState)
                TimePickerField(label = "選擇時間", timeState = timeState)

                BirthPlaceInputField(
                    coroutineScope = coroutineScope,
                    context = context,
                    onLocationRetrieved = { lat, lon ->
                        latitude.value = lat
                        longitude.value = lon
                        // 將經緯度轉換為地址
                        coroutineScope.launch {
                            try {
                                val geocoder = Geocoder(context, Locale.getDefault())
                                val addresses = geocoder.getFromLocation(lat, lon, 1)
                                if (!addresses.isNullOrEmpty()) {
                                    val address = addresses[0].getAddressLine(0)
                                    placeState.value = address
                                } else {
                                    locationError.value = "無法獲取地址"
                                }
                            } catch (e: Exception) {
                                locationError.value = "無法獲取地址"
                                e.printStackTrace()
                            }
                        }
                        onLocationRetrieved(lat, lon)
                    }
                )

                // 顯示位置錯誤信息
                if (locationError.value.isNotEmpty()) {
                    Text(
                        text = locationError.value,
                        color = Color.Red,
                        fontSize = 14.sp,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }

                Spacer(modifier = Modifier.height(10.dp))

                SectionTitle("占卜問題")

                OutlinedTextField(
                    value = questState.value,
                    onValueChange = {
                        if (it.length <= 200) {
                            questState.value = it
                        }
                    },
                    label = { Text("請描述您的問題（最多200字）\n問題應該是具體、可回答的，例如：「這份工作我應該接受嗎？」比「我的未來如何？」更適合。") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    maxLines = 5,
                )

                Spacer(modifier = Modifier.height(20.dp))

                Button(
                    onClick = {
                        // 檢查經緯度是否有效
                        if (latitude.value == 0.0 && longitude.value == 0.0) {
                            locationError.value = "請選擇有效的位置"
                            return@Button
                        }
                        locationError.value = ""
                        onClick()
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = colorResource(id = R.color.colorPrimary),
                        contentColor = Color.White,
                        disabledContainerColor = Color.White,
                        disabledContentColor = Color.White,
                    )
                ) {
                    Text("提交")
                }
            }
        }
    }


    @Composable
    fun SectionTitle(text: String) {
        Text(
            text,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(vertical = 8.dp)
        )
    }

    @Composable
    fun BirthPlaceInputField(
        coroutineScope: CoroutineScope,
        context: Context,
        onLocationRetrieved: (Double, Double) -> Unit
    ) {
        var textState by remember { mutableStateOf(TextFieldValue("")) }
        var isSearching by remember { mutableStateOf(false) }
        var latitude by remember { mutableStateOf(0.0) }
        var longitude by remember { mutableStateOf(0.0) }
        var locationError by remember { mutableStateOf("") }

        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = textState,
                    onValueChange = {
                        textState = it
                        locationError = ""
                    },
                    label = { Text("請輸入地點") },
                    modifier = Modifier.weight(1f),
                    isError = locationError.isNotEmpty()
                )

                Spacer(modifier = Modifier.width(8.dp))

                // 搜尋按鈕
                IconButton(
                    onClick = {
                        if (textState.text.isEmpty()) {
                            locationError = "請輸入地點"
                            return@IconButton
                        }
                        isSearching = true
                        coroutineScope.launch {
                            try {
                                val latLng = LocationUtil.addressToLatLng(context, textState.text)
                                latitude = latLng.latitude
                                longitude = latLng.longitude
                                onLocationRetrieved(latLng.latitude, latLng.longitude)
                                locationError = ""
                            } catch (e: Exception) {
                                locationError = "無法找到該地點"
                                e.printStackTrace()
                            }
                            isSearching = false
                        }
                    },
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape)
                        .background(colorResource(id = R.color.colorPrimary))
                ) {
                    if (isSearching) {
                        CircularProgressIndicator(modifier = Modifier.size(24.dp))
                    } else {
                        Box(
                            modifier = Modifier
                                .background(Color.Transparent) 
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_baseline_search_24),
                                contentDescription = "搜尋",
                                tint = colorResource(id = R.color.white),
                                modifier = Modifier.background(Color.Transparent)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))

                // GPS 按鈕
                IconButton(
                    onClick = {
                        if (checkLocationPermission()) {
                            LocationUtil.getCurrentLocation(requireActivity(),  PermissionsUtil.LOCATION_PERMISSION_REQUEST_CODE) { location ->
                                isSearching = true
                                latitude = location.latitude
                                longitude = location.longitude
                                onLocationRetrieved(location.latitude, location.longitude)
                                
                                // 將經緯度轉換為地址
                                coroutineScope.launch {
                                    try {
                                        val geocoder = Geocoder(context, Locale.getDefault())
                                        val addresses = geocoder.getFromLocation(location.latitude, location.longitude, 1)
                                        if (!addresses.isNullOrEmpty()) {
                                            val address = addresses[0].getAddressLine(0)
                                            textState = TextFieldValue(address)
                                            locationError = ""
                                        } else {
                                            locationError = "無法獲取地址"
                                        }
                                    } catch (e: Exception) {
                                        locationError = "無法獲取地址"
                                        e.printStackTrace()
                                    }
                                    isSearching = false
                                }
                            }
                        } else {
                            locationError = "請授予位置權限"
                        }
                    },
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape)
                        .background(colorResource(id = R.color.colorPrimary))
                ) {
                    if (isSearching) {
                        CircularProgressIndicator(modifier = Modifier.size(24.dp))
                    } else {
                        Box(
                            modifier = Modifier
                                .background(Color.Transparent)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_gps),
                                contentDescription = "GPS定位",
                                tint = colorResource(id = R.color.white),
                                modifier = Modifier.background(Color.Transparent)
                            )
                        }
                    }
                }
            }

            // 顯示錯誤信息
            if (locationError.isNotEmpty()) {
                Text(
                    text = locationError,
                    color = Color.Red,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            // 顯示經緯度
            if (latitude != 0.0 || longitude != 0.0) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 4.dp),
                    horizontalArrangement = Arrangement.Start
                ) {
                    Text(
                        text = "緯度：${String.format("%.6f", latitude)} 經度：${
                            String.format(
                                "%.6f",
                                longitude
                            )
                        }",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }

    private fun checkLocationPermission(): Boolean {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissions(
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                LOCATION_PERMISSION_REQUEST_CODE
            )
            return false
        }
        return true
    }

    private fun getLatLngFromAddress(context: Context, address: String): Pair<Double, Double>? {
        val geocoder = Geocoder(context, Locale.getDefault())
        try {
            val addressList = geocoder.getFromLocationName(address, 1)
            if (!addressList.isNullOrEmpty()) {
                val location = addressList[0]
                return Pair(location.latitude, location.longitude)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    private fun sendReq(
        context: Context,
        aiModel: String,
        horoscope: Horoscope,
        openAiApiKey: String,
        groqApiKey: String,
        question: String
    ) {
        val text = StringBuilder()
        val string = horoscope.toAllString(context)
        val notInterpretable = horoscope.findNotInterpretable(context)
        notInterpretable.forEach {
            if (it.first) {
                text.append(it.second + "\n")
            }
        }
        text.append("\n使用卜卦占星的方式解答下列問題 : \n")
        text.append("時間 : ${horoscope.getBirthdayString()}\n")
        text.append("星盤如下 : \n")
        text.append(string)
        text.append("問題 : $question")
        chatViewModel.readingData.chartInfo = text.toString()

        val title = "卜卦占星"

        val aiModules = aiModels.map { Pair(it.id, it.developer) }.toTypedArray()
        val ai = aiModules.find { it.first == aiModel }
        if (ai != null) {
            if (ai.second == "OpenAI") {
                chatViewModel.fetchGPTResponse(
                    context,
                    horoscope,
                    title,
                    text.toString(),
                    ai.first,
                    openAiApiKey
                )
            } else {
                chatViewModel.fetchGroqResponse(
                    true,
                    horoscope,
                    title,
                    text.toString(),
                    ai.first,
                    groqApiKey
                )
            }
        }
        LogUtil.d(text.toString())
    }

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
    }
}


