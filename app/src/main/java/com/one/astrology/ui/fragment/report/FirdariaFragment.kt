package com.one.astrology.ui.fragment.report

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseSingleItemAdapter
import com.chad.library.adapter.base.QuickAdapterHelper
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.entity.PlanetYear
import com.one.astrology.data.entity.dayArray
import com.one.astrology.data.entity.nightArray
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentFirdariaBinding
import com.one.astrology.databinding.ItemHeaderFirdariaBinding
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.adapter.FirdariaItemAdapter
import com.one.core.util.LogUtil
import com.one.core.util.formatDate
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar


/**
 * 法達運勢表
 */
@AndroidEntryPoint
class FirdariaFragment : Fragment(R.layout.fragment_firdaria) {

    private lateinit var binding: FragmentFirdariaBinding
    private lateinit var firdariaItemAdapter: FirdariaItemAdapter

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("法達運勢頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentFirdariaBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initRecycleView()
        collectData()
    }

    private fun collectData() {
        getEvent()
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    private fun getTimeStep(startTime: Long, year: Int): Long {
        val cal: Calendar = Calendar.getInstance()
        cal.timeInMillis = startTime
//        val year = yearPlanet[indexDeputy].year
        cal.add(Calendar.YEAR, year)
        LogUtil.d(
            "startTime ${startTime.formatDate("yyyy/MM/dd")}  timeInMillis ${
                cal.timeInMillis.formatDate(
                    "yyyy/MM/dd"
                )
            }"
        )
        val subTime = cal.timeInMillis - startTime
        val timeStep = (subTime / 7)
        LogUtil.d("subTime $subTime timeStep $timeStep")
        return timeStep
    }

    private fun initData(horoscope: Horoscope): ArrayList<PlanetYear> {
        val sun = horoscope.getPlanetBeanList().find { it.chName == "太陽" }
            ?: return ArrayList()

        var yearPlanet = ArrayList<PlanetYear>(nightArray)
        if (sun.signBean.houseData.index > 6) {
            yearPlanet = ArrayList(dayArray)
        }
        val allYearPlanet = arrayListOf<PlanetYear>()
        var count = 0

        val primaryPlanetList = getPrimaryPlanetList(horoscope)

        for (i in 0 until primaryPlanetList.size) {
            val item = primaryPlanetList[i]
            val deputy = yearPlanet.find { it.primary == item.primary }
            var indexDeputy = yearPlanet.indexOf(deputy)

            if (item.primary != "北交點" && item.primary != "南交點") {
                var timeStep = 0L
                for (j in 0 until 7) {
                    val planet =
                        PlanetYear(yearPlanet[indexDeputy].primary, yearPlanet[indexDeputy].year)
                    if (j == 0) {
                        planet.second = item.primary
                        timeStep = getTimeStep(item.startTime!!, yearPlanet[indexDeputy].year!!)
                    } else {
                        planet.primary = item.primary
                        planet.second = yearPlanet[indexDeputy].primary
                    }
                    if (count == 0) {
                        planet.second = item.primary
                        planet.startTime = item.startTime
                    } else {
                        if (j == 0) {
                            LogUtil.d()
                            planet.startTime = item.startTime
                        } else {
                            val timeInMillis = allYearPlanet[count - 1].startTime!!
                            planet.startTime = timeInMillis + timeStep
                            LogUtil.d(
                                "timeInMillis ${timeInMillis.formatDate("yyyy/MM/dd")} startTime ${
                                    planet.startTime!!.formatDate(
                                        "yyyy/MM/dd"
                                    )
                                }"
                            )
                        }
                    }
                    indexDeputy++
                    if (indexDeputy > 6) {
                        indexDeputy = 0
                    }
                    allYearPlanet.add(planet)
                    count++
                }
            } else {
                val planet = PlanetYear(item.primary, item.year)
                planet.second = item.primary
                planet.startTime = item.startTime
                allYearPlanet.add(planet)
                count++
            }
        }

        return allYearPlanet
    }

    private fun getPrimaryPlanetList(horoscope: Horoscope): ArrayList<PlanetYear> {
        val sun = horoscope.getPlanetBeanList().find { it.chName == "太陽" }
            ?: return ArrayList()
        var yearPlanet = ArrayList<PlanetYear>(nightArray)
        if (sun.signBean.houseData.index > 6) {
            yearPlanet = ArrayList(dayArray)
        }
        val primaryPlanetList = arrayListOf<PlanetYear>()
        for (i in 0 until yearPlanet.size) {
            val item = yearPlanet[i]
            val primaryPlanet = PlanetYear(item.primary, item.year)
            if (i == 0) {
                primaryPlanet.startTime = horoscope.birthdayTime
            } else {
                val cal: Calendar = Calendar.getInstance()
                cal.timeInMillis = primaryPlanetList[i - 1].startTime!!
                LogUtil.d("timeInMillis ${cal.timeInMillis.formatDate("yyyy/MM/dd")} add ${yearPlanet[i - 1].year}")
                cal.add(Calendar.YEAR, yearPlanet[i - 1].year!!)
                primaryPlanet.startTime = cal.timeInMillis
            }
            LogUtil.d("timeInMillis ${primaryPlanet.startTime!!.formatDate("yyyy/MM/dd")}")
            primaryPlanetList.add(primaryPlanet)
        }
        return primaryPlanetList
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
            val allYearPlanet = initData(matchEvent.horoscopeA)
            when (matchEvent.chartType) {
                Chart.Firdaria -> {
                    requireActivity().runOnUiThread {
                        firdariaItemAdapter.submitList(allYearPlanet)
                    }
                }

                else -> {

                }
            }
        }
    }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        firdariaItemAdapter = FirdariaItemAdapter()
        binding.recyclerView.adapter = firdariaItemAdapter
        firdariaItemAdapter.setOnItemClickListener { _, _, _ ->
        }

        val helper = QuickAdapterHelper.Builder(firdariaItemAdapter).build()

        val headerAdapter = HeaderAdapter()
        headerAdapter.item = ""
        helper.addBeforeAdapter(headerAdapter)
        binding.recyclerView.adapter = helper.adapter
    }
}

class HeaderAdapter : BaseSingleItemAdapter<Any, HeaderAdapter.VH>() {
    class VH(
        parent: ViewGroup,
        val binding: ItemHeaderFirdariaBinding = ItemHeaderFirdariaBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(parent)
    }

    override fun onBindViewHolder(holder: VH, item: Any?) {
        // 可以在此绑定数据
    }
}