package com.one.astrology.ui.fragment

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.databinding.FragmentExplainBinding
import com.one.astrology.ui.fragment.adapter.ExplainItemAdapter
import com.one.core.util.IntentUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint


private const val ARG_TITLE = "title"
private const val ARG_DESC = "desc"
private const val ARG_DESC_LIST = "desc_list"


/**
 * 解釋頁
 */
@AndroidEntryPoint
class ExplainFragment : BottomSheetDialogFragment(R.layout.fragment_explain) {

    private lateinit var binding: FragmentExplainBinding
    private var title: String? = null
    private var desc: String? = null
    private var descList = ArrayList<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            title = it.getString(ARG_TITLE)
            desc = it.getString(ARG_DESC)
            descList = it.getStringArrayList(ARG_DESC_LIST) as ArrayList<String>
        }
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("解釋頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentExplainBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        dialog.behavior.state = BottomSheetBehavior.STATE_COLLAPSED
        return dialog
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initRecycleView()
        initView()
    }

    private fun initView() {
        binding.tvTitle.text = title
        binding.tvMore.setOnClickListener {
            title?.let { it1 -> IntentUtil.searchWeb(requireActivity(), it1) }
            dismiss()
        }
    }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        val explainItemAdapter = ExplainItemAdapter()
        explainItemAdapter.isEmptyViewEnable = true
        explainItemAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty_explain)
        if (BuildConfig.FLAVOR != "uat") {
            if (descList.isNotEmpty()) {
                explainItemAdapter.submitList(descList)
            }
            if (desc != null) {
                explainItemAdapter.submitList(arrayListOf(desc!!))
            }
        }

        binding.recyclerView.adapter = explainItemAdapter
    }

    companion object {
        @JvmStatic
        fun newInstance(title: String, desc: String) =
            ExplainFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_TITLE, title)
                    putString(ARG_DESC, desc)
                }
            }

        @JvmStatic
        fun newInstance(title: String, descList: ArrayList<String>) =
            ExplainFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_TITLE, title)
                    putStringArrayList(ARG_DESC_LIST, descList)
                }
            }
    }
}


