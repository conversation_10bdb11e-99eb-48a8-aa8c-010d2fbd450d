package com.one.astrology.ui.fragment.bank

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.QuickAdapterHelper
import com.chad.library.adapter.base.loadState.LoadState
import com.chad.library.adapter.base.loadState.trailing.TrailingLoadStateAdapter
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.db.AstroData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentDataBankListBinding
import com.one.astrology.db.OtherDBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_DATA_BANK
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_DATA_BANK_LIST
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.adapter.AstroItemVAdapter
import com.one.astrology.ui.fragment.adapter.CustomLoadMoreAdapter
import com.one.astrology.ui.fragment.bottomSheet.AstroFilterFragment
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.core.util.LogUtil
import kotlinx.coroutines.launch

private const val MAX_PAGE = 10

/**
 * 資料庫列表葉
 */
class DataBankListFragment : BaseFragment(R.layout.fragment_data_bank_list) {

    private var categories: String? = null
    private var searchText: CharSequence? = null
    private var page = 0
    private var isFirstPage: Boolean = true
    private lateinit var itemAdapter: AstroItemVAdapter
    private lateinit var binding: FragmentDataBankListBinding
    private lateinit var astroDataArrayList: ArrayList<AstroData>
    private var id = 0L

    override fun onResume() {
        super.onResume()
        requireActivity().title = getString(R.string.astrolabe_database)
        LogUtil.setCurrentScreen(getString(R.string.astrolabe_database), this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (rootView == null) {
            binding = FragmentDataBankListBinding.inflate(inflater, container, false)
            rootView = binding.root
        } else {
            (rootView?.parent as? ViewGroup)?.removeView(rootView)
        }
        return rootView
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            val serializable = it.getSerializable(ARG_DATA_BANK_LIST)
            if (serializable is ArrayList<*>) {
                @Suppress("UNCHECKED_CAST")
                astroDataArrayList = serializable as ArrayList<AstroData>
            } else {
                // 處理 ARG_CONTENT 不是 ArrayList<AstroData> 的情況
                LogUtil.e("DataBankListFragment", "ARG_CONTENT is not of the expected type")
            }
        } ?: run {
            // 當 arguments 為 null 時，做額外處理
            LogUtil.e("DataBankListFragment", "Arguments are null!")
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!hasInitializedRootView) {
            hasInitializedRootView = true
            initView()
            if (!::astroDataArrayList.isInitialized) {
                // 如果未初始化，給它一個預設值
                astroDataArrayList = ArrayList()
            }
            initRecyclerView(astroDataArrayList)
        }
    }

    private fun initView() {
        LiveEventBus.get(EventKey.AstroFilter, String::class.java).observe(this) {
            LogUtil.d(it)
            categories = it
            if (categories.isNullOrEmpty()) {
                request()
            } else {
                isFirstPage = true
                page = 0
                id = 0L
                itemAdapter.submitList(ArrayList())
                request()
            }
        }
        binding.ivFilter.setOnClickListener {
            showFilter()
        }
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(charSequence: CharSequence?, p1: Int, p2: Int, p3: Int) {
                searchText = charSequence
                if (searchText.isNullOrEmpty()) {
                    request()
                    return
                }
                isFirstPage = true
                page = 0
                id = 0L
                itemAdapter.submitList(ArrayList())
                request()
            }

            override fun afterTextChanged(p0: Editable?) {

            }
        })
    }

    private fun showFilter() {
        AstroFilterFragment().show(requireActivity().supportFragmentManager, "")
    }

    private fun initRecyclerView(astroList: ArrayList<AstroData>) {
        itemAdapter = AstroItemVAdapter()
        isFirstPage = false
        itemAdapter.submitList(astroList)

        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager

        itemAdapter.addOnItemChildClickListener(R.id.tvMore) { _, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.tvMore) {
                val birthData = BirthData()
                if (item.time == null) {
                    return@addOnItemChildClickListener
                }

                birthData.name = item.name.toString()
                birthData.birthday = item.timeInMillis!!
                birthData.birthplaceArea = item.place
                if (item.place == null) {
                    return@addOnItemChildClickListener
                }
                var places = item.place!!.split(',')
                if (places.isEmpty()) {
                    places = item.place!!.split('，')
                }
                lifecycleScope.launch {
                    val latLng = LocationUtil.addressToLatLng(  requireContext(), places[0])
                    birthData.birthplaceLatitude = latLng.latitude
                    birthData.birthplaceLongitude = latLng.longitude
                    toBirth(birthData)
                }
            }
        }

        itemAdapter.addOnItemChildClickListener(R.id.ivAdd) { _, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.ivAdd) {
                val birthData = BirthData()
                if (item.time == null) {
                    return@addOnItemChildClickListener
                }
                birthData.name = item.name.toString()
                birthData.birthday = item.timeInMillis!!
                birthData.birthplaceLatitude = item.latitude!!.toDouble()
                birthData.birthplaceLongitude = item.longitude!!.toDouble()
                addData(birthData)
            }
        }

        itemAdapter.addOnItemChildClickListener(R.id.tvSource) { _, view, position ->
            val item = itemAdapter.items[position]
            if (view.id == R.id.tvSource) {
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(item.wikipedia)
                startActivity(intent)
            }
        }

        itemAdapter.setOnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, position: Int ->
            val item = itemAdapter.items[position]
            val bundle = Bundle()
            bundle.putParcelable(ARG_DATA_BANK, item)
            findNavController().navigate(R.id.dataBankFragment, bundle)
        }
        binding.recyclerView.adapter = itemAdapter

        initLoadMoreAdapter()
    }

    private fun query(): ArrayList<AstroData> {
        if (itemAdapter.items.isNotEmpty()) {
            id = itemAdapter.items[itemAdapter.items.size - 1].id!!
        }
        val astroDataArrayList = if (searchText.isNullOrEmpty() && !categories.isNullOrEmpty()) {
            OtherDBHelper.queryCategories(requireContext(), id, categories.toString())
        } else if (!searchText.isNullOrEmpty() && categories.isNullOrEmpty()) {
            OtherDBHelper.queryDetailDataList(requireContext(), id, searchText.toString())
        } else if (!searchText.isNullOrEmpty() && !categories.isNullOrEmpty()) {
            OtherDBHelper.queryDetailDataList(
                requireContext(),
                id,
                searchText.toString(),
                categories.toString()
            )
        } else {
            OtherDBHelper.queryDetailDataList(requireContext())
        }
        return astroDataArrayList
    }

    private fun request() {
        val astroList = query()
        if (isFirstPage) {
            itemAdapter.submitList(astroList)
            isFirstPage = false
        } else {
            itemAdapter.addAll(astroList)
        }

        if (astroList.size < MAX_PAGE) {
            // 没有分页数据了
            /*
            Set the status to not loaded, and there is no paging data.
            设置状态为未加载，并且没有分页数据了
            */
            helper.trailingLoadState = LoadState.NotLoading(true)
        } else {
            // 后续还有分页数据
            /*
            Set the state to not loaded, and there is also paginated data
            设置状态为未加载，并且还有分页数据
            */
            helper.trailingLoadState = LoadState.NotLoading(false)
        }
        page++
    }

    private lateinit var helper: QuickAdapterHelper
    private fun initLoadMoreAdapter() {
        val loadMoreAdapter = CustomLoadMoreAdapter()
        loadMoreAdapter.isAutoLoadMore = true
        loadMoreAdapter.preloadSize = 0
        loadMoreAdapter.setOnLoadMoreListener(object : TrailingLoadStateAdapter.OnTrailingListener {
            override fun onLoad() {
                LogUtil.d()
                request()
            }

            override fun isAllowLoading(): Boolean {
                LogUtil.d()
                return true
            }

            override fun onFailRetry() {
                LogUtil.d()
                request()
            }
        })

        helper = QuickAdapterHelper.Builder(itemAdapter)
            .setTrailingLoadStateAdapter(loadMoreAdapter)
            .build()
        helper.trailingLoadState = LoadState.NotLoading(false)
        binding.recyclerView.adapter = helper.adapter
        binding.recyclerView.setHasFixedSize(false)
    }


    private fun addData(birthData: BirthData) {
        val dialog = MaterialDialog(requireContext(), MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.message(null, getString(R.string.add_to_this_record), null)
        dialog.positiveButton(null, getString(R.string.yes)) {
            val itemBoxSignRecord = ObjectBox.get().boxFor(BirthData::class.java)
            birthData.createTime = System.currentTimeMillis()
            itemBoxSignRecord.put(birthData)
            Toast.makeText(
                requireContext(),
                getString(R.string.added_astrolabe_profile_successfully), Toast.LENGTH_SHORT
            ).show()
        }
        dialog.negativeButton(null, getString(R.string.no)) {
            LogUtil.d()
            dialog.dismiss()
        }
        dialog.show()
    }

    private fun toBirth(birthData: BirthData) {
        val bundle = Bundle()
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
        bundle.putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
        bundle.putBoolean(KeyDefine.IsSystem, true)
        startActivity(SignDetailActivity::class.java, bundle)
    }
}