package com.one.astrology.ui.fragment.viewmodel.calculate

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.Horoscope
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.util.CalendarUtil
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.EphemerisUtil.Companion.getHouseCusps
import com.one.astrology.util.EphemerisUtil.Companion.getJulDay
import com.one.astrology.util.EphemerisUtil.Companion.initData
import com.one.core.util.LogUtil
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlin.math.floor
import kotlin.math.round
import kotlin.math.roundToInt

open class ProgressionViewModel : BaseCalculateViewModel() {

    private fun getSecondary(
        context: Context,
        name: String,
        birthMillis: Long,
        latLng: LatLng,
        progressedTime: BirthData
    ): Horoscope {
        val progressedDate = Calendar.getInstance()
        progressedDate.timeInMillis = birthMillis

        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        var formatted = formatter.format(progressedDate.timeInMillis)

        println("次限推運精確日期時間: $formatted")

        val secondaryDate =
            calculateSecondaryProgressionDate(birthMillis, progressedTime.birthday)

        println("secondaryDate $secondaryDate")

        val progressedDays =
            calculateSecondaryProgressionYearsCalendar(birthMillis, progressedTime.birthday)

        progressedDate.add(Calendar.DATE, progressedDays)
        val initSuccess = initData(context)
        if (!initSuccess) {
            LogUtil.e("Failed to initialize Swiss Ephemeris in calculate")
        }

        formatted = formatter.format(progressedDate.timeInMillis)
        println("次限推運精確日期時間: $formatted")

        val julDay = getJulDay(progressedDate.timeInMillis, latLng)
        LogUtil.i("julDay $julDay")
        val houseSystem = EncryptedSPUtil.getHouseSystem(context)

        // 處理宮位
        val houses = getHouseCusps(
            julDay,
            LatLng(latLng.latitude, latLng.longitude),
            houseSystem
        )

        val horoscope = EphemerisUtil.getHoroscope(
            context,
            Chart.SecondaryProgression,
            name,
            secondaryDate,
            latLng,
            houses
        )
        horoscope.houses = houses

        return horoscope

    }

    object AstrologyConstants {
        const val SIDEREAL_MONTH = 27.321661 // 恆星月長度
    }


    fun calculateSecondaryProgressionDate(
        birthDate: Long,    // 毫秒表示的出生時間
        targetDate: Long    // 毫秒表示的目標時間
    ): Long {
        println("計算次限推運日期:")
        println("出生日期 (timestamp): $birthDate")
        println("目標日期 (timestamp): $targetDate")

        // 計算從出生到目標日的實際天數（包含小數）
        val diffInMillis = targetDate - birthDate
        val daysFromBirth = diffInMillis.toDouble() / (1000 * 60 * 60 * 24)
        println("出生到目標日期的精確天數: $daysFromBirth")

        // 計算年齡
        val ageInYears = daysFromBirth / 365.25
        println("年齡（精確）: $ageInYears 年")

        // 次限推運：出生後 ageInYears 天的星象代表人生第 ageInYears 年
        val daysToAdd = ageInYears // 不取整，保留小數
        val wholeDays = floor(daysToAdd).toInt()
        val fractionalDay = daysToAdd - wholeDays

        val hours = floor(fractionalDay * 24).toInt()
        val minutes = floor((fractionalDay * 24 - hours) * 60).toInt()
        val seconds = (((fractionalDay * 24 - hours) * 60 - minutes) * 60).roundToInt()

        // 將進度加上原始出生時間
        val progressedMillis = birthDate +
                wholeDays * 24L * 60 * 60 * 1000 +
                hours * 60L * 60 * 1000 +
                minutes * 60L * 1000 +
                seconds * 1000L

        println("次限推運精確日期時間 (timestamp): $progressedMillis")

        return progressedMillis
    }


    fun calculateTertiaryProgressionDateCalendar(birthMillis: Long, targetMillis: Long): Long {
        println("計算三限推運日期（Calendar 版）:")

        val secondsFromBirth = (targetMillis - birthMillis) / 1000.0
        val daysFromBirth = secondsFromBirth / 86400.0
        println("出生到目標日期的精確天數: $daysFromBirth")

        val ageInMonths = daysFromBirth / AstrologyConstants.SIDEREAL_MONTH
        println("月齡（精確）: $ageInMonths 月")
        println("使用恆星月長度: ${AstrologyConstants.SIDEREAL_MONTH} 天")

        val daysToAdd = round(ageInMonths).toInt()
        println("需要添加的天數: $daysToAdd")

        val calendar = Calendar.getInstance()
        calendar.timeInMillis = birthMillis
        calendar.add(Calendar.DATE, daysToAdd)

        println("三限推運日期: ${calendar.time}")
        return calendar.timeInMillis
    }


    fun calculateSecondaryProgressionYearsCalendar(birthMillis: Long, targetMillis: Long): Int {
        // 計算毫秒差轉為天數
        val millisDiff = targetMillis - birthMillis
        val daysBetween = millisDiff / 86_400_000.0 // 一天 = 86400000 毫秒

        // 平均一年 365.25 天
        val years = daysBetween / 365.25

        // 四捨五入回傳整數年數
        return years.roundToInt()
    }


    private fun getTertiary(
        context: Context,
        name: String,
        time: Long,
        latLng: LatLng,
        progressedTime: BirthData
    ): Horoscope {
        val calendarA = Calendar.getInstance()
        calendarA.timeInMillis = time

        val day = CalendarUtil.getDayDiff(progressedTime.birthday, time)
        val month = (day / 27.321661).roundToInt()
        calendarA.add(Calendar.DATE, month)

        return EphemerisUtil.calculate(
            context,
            Chart.TertiaryProgression,
            name,
            calendarA.timeInMillis,
            latLng
        )
    }

    fun getSecondaryProgression(
        context: Context,
        birthData: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeA = getSecondary(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
            progressedTime
        )
        matchEvent.horoscopeB.birthdayTime = progressedTime.birthday

        val horoscopeB = Horoscope()
        horoscopeB.birthdayTime = progressedTime.birthday

        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        matchEvent.horoscopeA.birthdayTime = birthData.birthday

        return matchEvent
    }

    fun getTertiaryProgression(
        context: Context,
        birthData: BirthData,
        progressedTime: BirthData,
    ): MatchEvent {

        val horoscopeA = getTertiary(
            context,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude),
            progressedTime
        )

        matchEvent.horoscopeB.birthdayTime = progressedTime.birthday

        val horoscopeB = Horoscope()
        horoscopeB.birthdayTime = progressedTime.birthday

        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        matchEvent.horoscopeA.birthdayTime = birthData.birthday

        return matchEvent
    }

    fun getSecondaryProgressionSynastry(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.SecondaryProgressionSynastry,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val horoscopeB = getSecondary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )
        horoscopeB.birthdayTime = progressedTime.birthday
        horoscopeA.aspectList =
            EphemerisUtil.aspects(
                context,
                Chart.SecondaryProgressionSynastry,
                horoscopeB.planetList,
                horoscopeA.planetList,
                true, true
            )
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getTertiaryProgressionSynastry(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {
        val horoscopeA = EphemerisUtil.calculate(
            context,
            Chart.TertiaryProgressionSynastry,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude)
        )

        val horoscopeB = getTertiary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )
        horoscopeB.birthdayTime = progressedTime.birthday
        horoscopeA.aspectList =
            EphemerisUtil.aspects(
                context,
                Chart.TertiaryProgressionSynastry,
                horoscopeB.planetList,
                horoscopeA.planetList,
                true, true
            )
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getSynastrySecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeA = getSecondary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            progressedTime
        )

        val horoscopeB = getSecondary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )
        horoscopeA.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getSynastryTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeA = getTertiary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            progressedTime
        )

        val horoscopeB = getTertiary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )
        horoscopeA.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscopeA
        matchEvent.horoscopeB = horoscopeB
        return matchEvent
    }

    fun getCompositeSecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeSecondaryA = getSecondary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            progressedTime
        )

        val horoscopeSecondaryB = getSecondary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )

        val horoscope =
            EphemerisUtil.calculateComposite(
                context,
                Chart.CompositeSecondaryProgression,
                horoscopeSecondaryA,
                horoscopeSecondaryB
            )
        horoscope.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscope

        return matchEvent
    }

    fun getCompositeTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val horoscopeSecondaryA = getTertiary(
            context,
            signRecordA.name,
            signRecordA.birthday,
            LatLng(signRecordA.birthplaceLatitude, signRecordA.birthplaceLongitude),
            progressedTime
        )

        val horoscopeSecondaryB = getTertiary(
            context,
            signRecordB.name,
            signRecordB.birthday,
            LatLng(signRecordB.birthplaceLatitude, signRecordB.birthplaceLongitude),
            progressedTime
        )

        val horoscope =
            EphemerisUtil.calculateComposite(
                context,
                Chart.CompositeTertiaryProgression,
                horoscopeSecondaryA,
                horoscopeSecondaryB
            )
        horoscope.birthdayTime = progressedTime.birthday
        matchEvent.horoscopeA = horoscope

        return matchEvent
    }

    fun getDavisonSecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        matchEvent.horoscopeA = getSecondary(
            context,
            signRecordA.name,
            time,
            LatLng(birthplaceLatitude, birthplaceLongitude),
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }

    fun getDavisonTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2


        matchEvent.horoscopeA = getTertiary(
            context,
            signRecordA.name,
            time,
            LatLng(birthplaceLatitude, birthplaceLongitude),
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }

    fun getMarksSecondary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        val timeA = (signRecordA.birthday + time) / 2
        val latitudeA = (signRecordA.birthplaceLatitude + birthplaceLatitude) / 2
        val longitudeA = (signRecordA.birthplaceLongitude + birthplaceLongitude) / 2

        matchEvent.horoscopeA = getSecondary(
            context,
            signRecordA.name,
            timeA,
            LatLng(latitudeA, longitudeA),
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }

    fun getMarksTertiary(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        progressedTime: BirthData
    ): MatchEvent {

        val time = (signRecordA.birthday + signRecordB.birthday) / 2
        val birthplaceLatitude =
            (signRecordA.birthplaceLatitude + signRecordB.birthplaceLatitude) / 2
        val birthplaceLongitude =
            (signRecordA.birthplaceLongitude + signRecordB.birthplaceLongitude) / 2

        val timeA = (signRecordA.birthday + time) / 2
        val latitudeA = (signRecordA.birthplaceLatitude + birthplaceLatitude) / 2
        val longitudeA = (signRecordA.birthplaceLongitude + birthplaceLongitude) / 2

        matchEvent.horoscopeA = getTertiary(
            context,
            signRecordA.name,
            timeA,
            LatLng(latitudeA, longitudeA),
            progressedTime
        )
        matchEvent.horoscopeA.birthdayTime = progressedTime.birthday
        return matchEvent
    }
}