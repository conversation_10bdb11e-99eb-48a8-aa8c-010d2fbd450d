package com.one.astrology.ui.compose.screens

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.one.astrology.R
import com.one.astrology.data.db.ChartData
import com.one.astrology.data.db.SettingAspectData
import com.one.astrology.ui.viewmodel.SettingsViewModel

/**
 * 相位設定頁面
 */
@Composable
fun AspectSettingsScreen(viewModel: SettingsViewModel) {
    val context = LocalContext.current
    val chartList by viewModel.chartList.collectAsState()
    val aspectList by viewModel.aspectList.collectAsState()
    val currentChart by viewModel.currentChart.collectAsState()

    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(8.dp)
    ) {
        // 左側星盤類型列表
        Card(
            modifier = Modifier
                .weight(1f)
                .padding(4.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column {
                // 標題
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(colorResource(id = R.color.colorPrimary))
                        .padding(vertical = 10.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "星盤類型",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                }
                
                // 列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(4.dp)
                ) {
                    items(chartList) { chart ->
                        ChartTypeItem(
                            chart = chart,
                            isSelected = chart.enName == currentChart,
                            onClick = {
                                viewModel.updateSelectedChart(chart.enName)
                            }
                        )
                    }
                }
            }
        }
        
        // 右側相位設定列表
        Card(
            modifier = Modifier
                .weight(2f)
                .padding(4.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column {
                // 標題列
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(colorResource(id = R.color.colorPrimary))
                        .padding(vertical = 10.dp, horizontal = 8.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "角度",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 15.sp,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "容許度",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 15.sp,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "數值",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 15.sp,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "啟用",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 15.sp,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                }
                
                // 列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(4.dp)
                ) {
                    items(aspectList) { aspect ->
                        AspectItem(
                            aspect = aspect,
                            onOrbChange = { newOrb ->
                                viewModel.updateOrb(currentChart, aspect.degree!!, newOrb)
                            },
                            onStatusChange = { isEnabled ->
                                viewModel.updateAspectStatus(currentChart, aspect.degree!!, isEnabled)
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 星盤類型項目
 */
@Composable
fun ChartTypeItem(
    chart: ChartData,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) colorResource(id = R.color.very_light_grey) else Color.White,
        animationSpec = tween(300),
        label = "backgroundColor"
    )
    
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.02f else 1.0f,
        animationSpec = tween(300),
        label = "scale"
    )
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .scale(scale)
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp, horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 選中指示器
        if (isSelected) {
            Box(
                modifier = Modifier
                    .width(4.dp)
                    .height(24.dp)
                    .background(
                        colorResource(id = R.color.colorPrimary),
                        shape = RoundedCornerShape(topEnd = 2.dp, bottomEnd = 2.dp)
                    )
            )
            Spacer(modifier = Modifier.width(8.dp))
        } else {
            Spacer(modifier = Modifier.width(12.dp))
        }
        
        Text(
            text = chart.chName,
            color = if (isSelected) colorResource(id = R.color.colorPrimary) else colorResource(id = R.color.grey),
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
            fontSize = 15.sp
        )
    }
}

/**
 * 相位設定項目
 */
@Composable
fun AspectItem(
    aspect: SettingAspectData,
    onOrbChange: (Int) -> Unit,
    onStatusChange: (Boolean) -> Unit
) {
    var showOrbPicker by remember { mutableStateOf(false) }
    val isEnabled = aspect.isOpen == true
    
    val alpha by animateFloatAsState(
        targetValue = if (isEnabled) 1f else 0.5f,
        animationSpec = tween(300),
        label = "alpha"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (aspect.degree!! % 2 == 0) Color.White else colorResource(id = R.color.very_light_grey),
        animationSpec = tween(300),
        label = "backgroundColor"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isEnabled) 2.dp else 1.dp),
        shape = RoundedCornerShape(10.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 角度
            Text(
                text = "${aspect.degree}°",
                color = colorResource(id = R.color.colorPrimary).copy(alpha = alpha),
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )
            
            // 容許度
            Text(
                text = "容許度",
                color = colorResource(id = R.color.grey).copy(alpha = alpha),
                fontSize = 14.sp,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )
            
            // 數值
            Box(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 4.dp)
                    .clip(RoundedCornerShape(6.dp))
                    .background(colorResource(id = R.color.very_light_grey).copy(alpha = alpha))
                    .border(
                        width = 1.dp,
                        color = colorResource(id = R.color.grey).copy(alpha = alpha),
                        shape = RoundedCornerShape(6.dp)
                    )
                    .clickable { if (isEnabled) showOrbPicker = true }
                    .padding(vertical = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "${aspect.orb}",
                    color = colorResource(id = R.color.colorPrimary).copy(alpha = alpha),
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }
            
            // 啟用
            Box(
                modifier = Modifier
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Checkbox(
                    checked = isEnabled,
                    onCheckedChange = { onStatusChange(it) },
                    colors = CheckboxDefaults.colors(
                        checkedColor = colorResource(id = R.color.colorAccent),
                        uncheckedColor = colorResource(id = R.color.grey)
                    ),
                    modifier = Modifier.scale(1.2f)
                )
            }
        }
    }
    
    // 容許度選擇對話框
    if (showOrbPicker) {
        OrbPickerDialog(
            currentValue = aspect.orb!!,
            onDismiss = { showOrbPicker = false },
            onValueSelected = { 
                onOrbChange(it)
                showOrbPicker = false
            }
        )
    }
}

/**
 * 容許度選擇對話框
 */
@Composable
fun OrbPickerDialog(
    currentValue: Int,
    onDismiss: () -> Unit,
    onValueSelected: (Int) -> Unit
) {
    val values = (0..10).toList()
    
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
            ) {
                Text(
                    text = "選擇容許度",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp,
                    color = colorResource(id = R.color.colorPrimary)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                ) {
                    items(values) { value ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { onValueSelected(value) }
                                .padding(vertical = 12.dp, horizontal = 16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = value.toString(),
                                fontSize = 16.sp,
                                color = if (value == currentValue) colorResource(id = R.color.colorPrimary) else Color.Black,
                                fontWeight = if (value == currentValue) FontWeight.Bold else FontWeight.Normal,
                                modifier = Modifier.weight(1f)
                            )
                            
                            if (value == currentValue) {
                                Box(
                                    modifier = Modifier
                                        .size(24.dp)
                                        .background(colorResource(id = R.color.colorPrimary), CircleShape),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = Color.White,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                        
                        if (value != values.last()) {
                            Divider(
                                color = Color.LightGray.copy(alpha = 0.5f),
                                thickness = 0.5.dp,
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}
