package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.ReadingDataEntity
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.cardElevation
import com.one.core.util.FormatUtils

class ReadingDataListFragment : Fragment() {
    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                Column(modifier = Modifier.fillMaxSize()) {
                    TopAppBar(
                        title = {
                            Text(
                                "分析紀錄",
                                color = Color.White,
                                textAlign = TextAlign.Center
                            )
                        },
                        colors = TopAppBarDefaults.topAppBarColors(
                            containerColor = colorResource(id = R.color.colorPrimary)
                        )
                    )
                    ReadingDataListScreen()
                }
            }
        }
    }

    @Composable
    fun ReadingDataListScreen() {
        val readingDataBox = ObjectBox.get().boxFor(ReadingDataEntity::class.java)
        val readings = remember { readingDataBox.all.sortedByDescending { it.createTime } }
        val listState = rememberLazyListState()

        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .padding(10.dp),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {
            items(readings) { reading ->
                AnimatedVisibility(visible = true) {
                    ReadingDataCard(reading)
                }
            }
        }
    }

    @Composable
    fun ReadingDataCard(reading: ReadingDataEntity) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(10.dp)
                .clickable {
                    val bundle = Bundle().apply {
                        putParcelable(
                            "reading_data", ReadingData(
                                title = reading.title,
                                content = reading.content,
                                chartInfo = reading.chartInfo,
                                chart = reading.chart,
                                birthDataA = reading.birthDataA,
                                birthDataB = reading.birthDataB
                            )
                        )
                    }
                    findNavController().navigate(R.id.markdownFragment, bundle)
                },
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            shape = AppShapes.small,
            elevation = cardElevation(),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(10.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = reading.title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = colorResource(id = R.color.colorPrimary)
                    )
                    Text(
                        text = FormatUtils.longToString(reading.createTime, "yyyy/MM/dd HH:mm"),
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
                Spacer(modifier = Modifier.height(0.dp))
                Text(
                    text = reading.content.take(100) + "...",
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
        }
    }
}