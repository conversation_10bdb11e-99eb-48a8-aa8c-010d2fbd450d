package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.data.entity.TransitData
import com.one.astrology.databinding.ItemFourtuneBinding
import com.one.core.util.FormatUtils

class FortuneItemAdapter :
    BaseQuickAdapter<TransitData, FortuneItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemFourtuneBinding = ItemFourtuneBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int, item: TransitData?) {
        if (item != null) {
            holder.binding.tvName.text = item.name
            holder.binding.tvHouse.text = item.house
            holder.binding.tvTimeString.text = item.timeString
            holder.binding.tvCreateTime.text = FormatUtils.longToString(item.createTime, "MM/dd HH:mm")
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }
}