package com.one.astrology.ui.fragment.bottomSheet


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.data.SettingsPreferencesDataStore
import com.one.astrology.databinding.FragmentFilterBinding
import com.one.astrology.event.EventKey
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.LogUtil


/**
 * 過濾頁
 */
class FilterFragment : BottomSheetDialogFragment(R.layout.fragment_filter) {

    private lateinit var binding: FragmentFilterBinding
    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("過濾頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentFilterBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        launchWhenStarted {
            val it = SettingsPreferencesDataStore.getFilterSort(requireContext())
            initFilterSort(it)
        }

        binding.radioGroup.setOnCheckedChangeListener { _, checkedId ->
            LiveEventBus.get<Int>(EventKey.FilterSort).postDelay(checkedId, 0)
            launchWhenStarted {
                SettingsPreferencesDataStore.putFilterSort(requireContext(), checkedId)
            }
        }
    }

    private fun initFilterSort(it: Int) {
        when (it) {
            R.id.rbTimeAsc -> {
                binding.rbTimeAsc.isChecked = true
            }

            R.id.rbTimeDesc -> {
                binding.rbTimeDesc.isChecked = true
            }

            R.id.rbBirthdayAsc -> {
                binding.rbBirthdayAsc.isChecked = true
            }

            R.id.rbBirthdayDesc -> {
                binding.rbBirthdayDesc.isChecked = true
            }

            R.id.rbNameAsc -> {
                binding.rbNameAsc.isChecked = true
            }

            R.id.rbNameDesc -> {
                binding.rbNameDesc.isChecked = true
            }

            else -> {
                binding.rbTimeAsc.isChecked = true
            }
        }
    }
}