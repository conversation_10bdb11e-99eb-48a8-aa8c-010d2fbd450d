package com.one.astrology.ui.activity

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.R
import com.one.astrology.data.model.DailyAstrologyInfo
import com.one.astrology.data.model.PlanetDailyStatus
import com.one.astrology.ui.compose.components.PlanetDetailItem
import com.one.astrology.viewmodel.DailyAstrologyViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 每日星象詳情頁
 */
@AndroidEntryPoint
class DailyAstrologyDetailActivity : AppCompatActivity() {

    private val viewModel by viewModels<DailyAstrologyViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 如果數據為空，加載數據
        if (viewModel.dailyAstrologyInfo.value == null) {
            viewModel.loadTodayAstrologyData(this, LatLng(0.0,0.0))
        }
        
        setContent {
            DailyAstrologyDetailScreen(
                viewModel = viewModel,
                onBackPressed = { finish() }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DailyAstrologyDetailScreen(
    viewModel: DailyAstrologyViewModel,
    onBackPressed: () -> Unit
) {
    val dailyAstrologyInfo by viewModel.dailyAstrologyInfo.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("今日星象") },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = colorResource(id = R.color.colorPrimary),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(100.dp))
                CircularProgressIndicator(
                    color = colorResource(id = R.color.colorPrimary)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "載入中...",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        } else {
            dailyAstrologyInfo?.let { info ->
                DailyAstrologyDetailContent(
                    info = info,
                    paddingValues = paddingValues
                )
            }
        }
    }
}

@Composable
fun DailyAstrologyDetailContent(
    info: DailyAstrologyInfo,
    paddingValues: PaddingValues
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
            .padding(horizontal = 16.dp)
    ) {
        item {
            Spacer(modifier = Modifier.height(16.dp))
            
            // 日期標題
            Text(
                text = info.date,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 月相資訊
//            Column(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .background(
//                        color = colorResource(id = R.color.colorPrimary).copy(alpha = 0.1f),
//                        shape = MaterialTheme.shapes.medium
//                    )
//                    .padding(16.dp)
//            ) {
//                Text(
//                    text = "今日月相",
//                    style = MaterialTheme.typography.titleSmall,
//                    fontWeight = FontWeight.Bold,
//                    color = colorResource(id = R.color.colorPrimary)
//                )
//
//                Spacer(modifier = Modifier.height(8.dp))
//
//                Text(
//                    text = info.moonPhase,
//                    style = MaterialTheme.typography.bodyLarge
//                )
//            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 行星位置標題
            Text(
                text = "行星位置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
        }
        
        // 行星列表
        items(info.planets) { planet ->
            PlanetDetailItem(planet = planet)
            
            Divider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                color = Color.LightGray.copy(alpha = 0.5f)
            )
        }
        
        item {
            Spacer(modifier = Modifier.height(24.dp))
            
            // 相位標題
            Text(
                text = "今日重要相位",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 相位列表
            if (info.importantAspects.isNotEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = colorResource(id = R.color.colorPrimary).copy(alpha = 0.1f),
                            shape = MaterialTheme.shapes.medium
                        )
                        .padding(16.dp)
                ) {
                    info.importantAspects.forEach { aspect ->
                        Text(
                            text = "• ${aspect.planetA} ${aspect.type} ${aspect.planetB}",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                    }
                }
            } else {
                Text(
                    text = "今日無重要相位",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 星象解讀
            Text(
                text = "今日星象解讀",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = colorResource(id = R.color.colorPrimary).copy(alpha = 0.1f),
                        shape = MaterialTheme.shapes.medium
                    )
                    .padding(16.dp)
            ) {
                // 簡單的解讀文字，實際應用中可能會從AI或數據庫獲取更詳細的解讀
                Text(
                    text = generateInterpretation(info),
                    style = MaterialTheme.typography.bodyMedium,
                    lineHeight = 24.sp
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

/**
 * 生成簡單的星象解讀文字
 */
private fun generateInterpretation(info: DailyAstrologyInfo): String {
    // 實際應用中，這部分可能會從AI服務或預設的解讀數據庫獲取
    val moonPhaseText = when (info.moonPhase) {
        "新月" -> "新的開始，適合規劃和播種新的想法。"
        "滿月" -> "事物達到頂峰，情緒可能會比較高漲。"
        else -> "月相處於變化階段，保持彈性和適應性。"
    }
    
    // 檢查是否有水星、金星或火星逆行
    val retrogradeTexts = mutableListOf<String>()
    info.planets.forEach { planet ->
        if (planet.isRetrograde) {
            when (planet.chName) {
                "水星" -> retrogradeTexts.add("水星逆行期間，溝通可能會出現誤解，簽約和購買電子產品需謹慎。")
                "金星" -> retrogradeTexts.add("金星逆行期間，感情和財務方面可能需要重新評估。")
                "火星" -> retrogradeTexts.add("火星逆行期間，行動力可能受阻，不適合開始新計劃。")
                else -> retrogradeTexts.add("${planet.chName}逆行，相關領域可能需要審視和調整。")
            }
        }
    }
    
    // 組合文字
    val interpretationBuilder = StringBuilder()
    interpretationBuilder.append(moonPhaseText).append("\n\n")
    
    if (retrogradeTexts.isNotEmpty()) {
        interpretationBuilder.append(retrogradeTexts.joinToString("\n\n"))
        interpretationBuilder.append("\n\n")
    }
    
    // 添加相位解讀
    if (info.importantAspects.isNotEmpty()) {
        interpretationBuilder.append("今日重要相位提示：行星之間的互動帶來能量流動，注意把握機會和避開潛在衝突。")
    }
    
    return interpretationBuilder.toString()
}

@Composable
fun PlanetDetailItem(planet: PlanetDailyStatus) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        // 行星名稱和星座
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = planet.name,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = colorResource(id = R.color.colorPrimary)
            )
            
            Text(
                text = " 在 ${planet.signName} ${planet.degree}°",
                style = MaterialTheme.typography.bodyLarge
            )
            
            if (planet.isRetrograde) {
                Text(
                    text = " 逆行",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Red,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        // 行星詳細解讀
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = getPlanetInterpretation(planet),
            style = MaterialTheme.typography.bodySmall,
            color = Color.DarkGray
        )
    }
}

/**
 * 獲取行星在星座中的解讀文字
 */
private fun getPlanetInterpretation(planet: PlanetDailyStatus): String {
    // 簡化版的行星在星座中的解讀，實際應用中可能會更複雜
    return when (planet.name) {
        "太陽" -> "代表你的核心身份和活力。在${planet.signName}中，${getSignInfluence(planet.signName, "太陽")}。"
        "月亮" -> "代表你的情感和潛意識。在${planet.signName}中，${getSignInfluence(planet.signName, "月亮")}。"
        "水星" -> "代表你的思維和溝通方式。在${planet.signName}中，${getSignInfluence(planet.signName, "水星")}。"
        "金星" -> "代表你的愛情和價值觀。在${planet.signName}中，${getSignInfluence(planet.signName, "金星")}。"
        "火星" -> "代表你的行動力和慾望。在${planet.signName}中，${getSignInfluence(planet.signName, "火星")}。"
        "木星" -> "代表擴張和機會。在${planet.signName}中，${getSignInfluence(planet.signName, "木星")}。"
        "土星" -> "代表限制和責任。在${planet.signName}中，${getSignInfluence(planet.signName, "土星")}。"
        "天王星" -> "代表突破和革新。在${planet.signName}中，${getSignInfluence(planet.signName, "天王星")}。"
        "海王星" -> "代表靈感和幻想。在${planet.signName}中，${getSignInfluence(planet.signName, "海王星")}。"
        "冥王星" -> "代表轉化和權力。在${planet.signName}中，${getSignInfluence(planet.signName, "冥王星")}。"
        else -> "在${planet.signName}中運行。"
    }
}

/**
 * 獲取星座對行星的影響描述
 */
private fun getSignInfluence(signName: String, planetName: String): String {
    return when (signName) {
        "白羊座" -> "帶來積極主動的能量，鼓勵你勇敢前進"
        "金牛座" -> "帶來穩定和持久的能量，注重實際價值"
        "雙子座" -> "帶來靈活多變的能量，促進交流和學習"
        "巨蟹座" -> "帶來情感豐富的能量，注重家庭和安全感"
        "獅子座" -> "帶來自信和創造性的能量，鼓勵表達自我"
        "處女座" -> "帶來精確和分析的能量，注重細節和改進"
        "天秤座" -> "帶來和諧與平衡的能量，注重人際關係"
        "天蠍座" -> "帶來深沉和轉化的能量，鼓勵深度探索"
        "射手座" -> "帶來擴張和探索的能量，追求更高的真理"
        "摩羯座" -> "帶來實際和規律的能量，注重長期目標"
        "水瓶座" -> "帶來創新和獨立的能量，鼓勵突破常規"
        "雙魚座" -> "帶來靈感和同情的能量，連接精神世界"
        else -> "影響你的能量場"
    }
} 