package com.one.astrology.ui.fragment.report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.type.Chart
import com.one.astrology.db.DBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.fragment.ExplainFragment
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateViewModel
import com.one.astrology.util.EphemerisUtil.Companion.szZodiac
import com.one.astrology.viewmodel.OpenViewModel
import com.one.core.util.IntentUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AspectViewModel @Inject constructor() : ViewModel() {
    private val _aspectList = MutableStateFlow<List<AspectData>>(emptyList())
    val aspectList: StateFlow<List<AspectData>> = _aspectList.asStateFlow()
    private val _planetList = MutableStateFlow<List<PlanetBean>>(emptyList())
    val planetList: StateFlow<List<PlanetBean>> = _planetList.asStateFlow()
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private var chart: Chart? = null
    private var title: String? = ""

    fun setChart(chart: Chart) {
        this.chart = chart
    }

    fun getChart(): Chart? = chart

    fun setTitle(title: String) {
        this.title = title
    }

    fun getTitle(): String? = title

    fun updateAspectList(horoscope: Horoscope) {
        viewModelScope.launch {
            _isLoading.value = true
            _aspectList.value = horoscope.aspectList
            _planetList.value = horoscope.planetList
            _isLoading.value = false
        }
    }
}

/**
 * 相位頁
 */
@AndroidEntryPoint
class AspectFragment : Fragment() {

    private val viewModel: AspectViewModel by viewModels()
    private val calculateViewModel by activityViewModels<CalculateViewModel>()
    private val openViewModel by viewModels<OpenViewModel>()

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("相位頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                MaterialTheme {
                    AspectScreen(
                        viewModel = viewModel,
                        onAspectClick = { aspect ->
                            aspectDescQuery(
                                viewModel.getChart()!!,
                                aspect
                            )
                        }
                    )
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        collectData()
    }

    private fun collectData() {
        getEvent()
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        val title = matchEvent?.chartType?.let { getString(it.type) }
        if (matchEvent != null) {
            viewModel.setChart(matchEvent.chartType)
            if (title != null) {
                viewModel.setTitle(title)
            }
            viewModel.updateAspectList(matchEvent.horoscopeA)
        }
    }

    private fun aspectDescQuery(chart: Chart, aspect: AspectData) {
        val query = viewModel.getTitle() + " " + aspect.planetA + aspect.type + aspect.planetB
        if (BuildConfig.IS_DEV) {
            val aspectData = query(chart, aspect)
            if (aspectData != null) {
                if (aspectData.descList.isNotEmpty()) {
                    ExplainFragment.newInstance(query, aspectData.descList)
                        .show(requireActivity().supportFragmentManager, "")
                } else {
                    IntentUtil.searchWeb(requireActivity(), query)
                }
            } else {
                IntentUtil.searchWeb(requireActivity(), query)
            }
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun query(chart: Chart, aspect: AspectData): AspectData? {
        return DBHelper.queryAspect(
            requireContext(),
            chart,
            aspect.planetA,
            aspect.degree,
            aspect.planetB,
        )
    }
}

@Composable
fun AspectScreen(
    viewModel: AspectViewModel,
    onAspectClick: (AspectData) -> Unit
) {
    val aspectList by viewModel.aspectList.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val planetList by viewModel.planetList.collectAsState()
    Box(modifier = Modifier.fillMaxSize()) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(horizontal = 10.dp, vertical = 10.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(aspectList) { aspect ->
                    AspectItem(
                        planetList = planetList,
                        aspect = aspect,
                        onClick = { onAspectClick(aspect) }
                    )
                }
            }
        }
    }
}

@Composable
fun AspectItem(
    planetList: List<PlanetBean>,
    aspect: AspectData,
    onClick: () -> Unit
) {
    val context = LocalContext.current

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(horizontal = 3.dp, vertical = 3.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = AppShapes.small
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                // 行星A (weight = 1)
                val planet = planetList.find { it.chName == aspect.planetA }
                if (planet != null) {
                    Text(
                        text = planet.symbol,
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontFamily = FontFamily(Font(R.font.astro_one_font)),
                            fontWeight = FontWeight.Normal,
                        ),
                        color = planet.getSafeColor()
                    )
                    Spacer(modifier = Modifier.width(3.dp))
                }

                Text(
                    text = aspect.planetA,
                    style = MaterialTheme.typography.bodySmall,
                    color = colorResource(id = R.color.colorPrimary),
                    textAlign = TextAlign.Center
                )
            }

            // 相位類型 (weight = 1)
            Text(
                text = aspect.type,
                style = MaterialTheme.typography.bodySmall,
                color = colorResource(id = aspect.aspectType?.color ?: R.color.colorPrimary),
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )

            // 行星B (weight = 1)
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                val planetB = planetList.find { it.chName == aspect.planetB }
                if (planetB != null) {
                    Text(
                        text = planetB.symbol,
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontFamily = FontFamily(Font(R.font.astro_one_font)),
                            fontWeight = FontWeight.Normal,
                        ),
                        color = planetB.getSafeColor()
                    )
                    Spacer(modifier = Modifier.width(3.dp))
                }
                Text(
                    text = aspect.planetB,
                    style = MaterialTheme.typography.bodySmall,
                    color = colorResource(id = R.color.colorPrimary),
                    textAlign = TextAlign.Center
                )
            }


            // 入相/出相 (weight = 1)
            Text(
                text = if (aspect.isApplying == true) "入相" else "出相",
                style = MaterialTheme.typography.bodySmall,
                color = colorResource(id = R.color.colorPrimary),
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )

            // 容許度 (weight = 1)

            val strAngle = aspect.orb?.let { szZodiac(it) }
            Text(
                text = context.getString(
                    R.string.orb_degree, strAngle?.get(1) ?: 0,
                    strAngle?.get(2) ?: 0
                ),
                style = MaterialTheme.typography.bodySmall,
                color = colorResource(id = R.color.colorPrimary),
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )
        }
    }
}