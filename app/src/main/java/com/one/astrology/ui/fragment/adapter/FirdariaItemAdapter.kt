package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.data.entity.PlanetYear
import com.one.astrology.databinding.ItemFirdariaBinding
import com.one.core.util.formatDate

class FirdariaItemAdapter : BaseQuickAdapter<PlanetYear, FirdariaItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemFirdariaBinding = ItemFirdariaBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int, item: PlanetYear?) {
        if (item != null) {
            holder.binding.tvPrimary.text = item.primary
            holder.binding.tvSecond.text = item.second
            if (item.startTime != null) {
                holder.binding.tvTimeString.text = item.startTime!!.formatDate("yyyy/MM/dd HH:mm")
            }
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }
}