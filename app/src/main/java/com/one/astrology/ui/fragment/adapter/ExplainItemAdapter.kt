package com.one.astrology.ui.fragment.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.R
import com.one.astrology.databinding.ItemExplainBinding

class ExplainItemAdapter :
    BaseQuickAdapter<String, ExplainItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemExplainBinding = ItemExplainBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: BaseViewHolder,
        position: Int,
        item: String?
    ) {
        if (item != null) {
            holder.binding.tvTitle.text = context.getString(R.string.title_s, position + 1)
            val text = removeTheBlankOfString(item)
            holder.binding.tvDesc.text = text
        }
    }

    private fun removeTheBlankOfString(str: String): String {
        var endPosition = str.length - 1
        for (i in str.length - 1 downTo 0) {
            if (str[i] == '\n') endPosition-- else break
        }
        return str.substring(0, endPosition + 1)
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}