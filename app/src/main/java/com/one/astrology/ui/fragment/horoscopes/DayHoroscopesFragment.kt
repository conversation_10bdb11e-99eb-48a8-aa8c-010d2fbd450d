package com.one.astrology.ui.fragment.horoscopes

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.lifecycle.ViewModelProvider
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.api.RetrofitService
import com.one.astrology.callback.TranslateCallback
import com.one.astrology.constant.Planet
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.databinding.FragmentDayHoroscopesBinding
import com.one.astrology.repositories.AztroRepository
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.util.TranslateUtil
import com.one.astrology.viewmodel.AztroViewModel
import com.one.astrology.viewmodel.ViewModelFactory
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import io.objectbox.query.QueryBuilder
import java.util.Calendar

/**
 * 每日星座運勢
 */
class DayHoroscopesFragment : BaseFragment(R.layout.fragment_day_horoscopes) {

    private var me: BirthData? = null

    private lateinit var binding: FragmentDayHoroscopesBinding

    lateinit var aztroViewModel: AztroViewModel

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("DayHoroscopesFragment", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentDayHoroscopesBinding.inflate(inflater, container, false)
        return binding.root
    }

    var day = "today"
    var sign: String = "scorpio"

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().title = getString(R.string.horoscope)

        binding.progressBar.bringToFront()

        if (arguments != null) {
            sign = arguments?.getString("sign", "scorpio").toString()
            day = arguments?.getString("day", "today").toString()
            me = arguments?.getSerializable("me") as BirthData
            when (day) {
                "today" -> {
                    val calendar = Calendar.getInstance()
                    binding.tvCurrentDate.text =
                        FormatUtils.longToString(calendar.timeInMillis, "yyyy/MM/dd HH:mm")
                }
                "yesterday" -> {
                    val calendar = Calendar.getInstance()
                    calendar.add(Calendar.DATE, -1)
                    binding.tvCurrentDate.text =
                        FormatUtils.longToString(calendar.timeInMillis, "yyyy/MM/dd HH:mm")
                }
                "tomorrow" -> {
                    val calendar = Calendar.getInstance()
                    calendar.add(Calendar.DATE, +1)
                    binding.tvCurrentDate.text =
                        FormatUtils.longToString(calendar.timeInMillis, "yyyy/MM/dd HH:mm")
                }
            }

        }

        if (me != null) {
            initMe(me!!)
        } else {
            initMe(getMe())
        }

        initSign()
    }

    private fun initSign() {
        val retrofitService = RetrofitService.getInstance()
        val mainRepository = AztroRepository(retrofitService)

        aztroViewModel =
            ViewModelProvider(this, ViewModelFactory(mainRepository))[AztroViewModel::class.java]

        aztroViewModel.getSignInfo(sign, day)

        aztroViewModel.signInfoMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
//                translate(it.currentDate, binding.tvCurrentDate)
                translate(it.color, binding.tvColor)
                translate(it.compatibility, binding.tvCompatibility)
                translate(it.dateRange, binding.tvDateRange)
                translate(it.description, binding.tvDescription)
                translate(it.luckyNumber, binding.tvLuckyNumber)
                translate(it.luckyTime, binding.tvLuckyTime)
                translate(it.mood, binding.tvMood)
            } else {
                binding.lltContent.visibility = View.GONE
                binding.tvErrorMessage.visibility = View.VISIBLE
                binding.tvErrorMessage.text = requireContext().getString(R.string.error, "取得運勢異常")
            }
            binding.progressBar.visibility = View.GONE
        }
    }

    private fun getMe(): BirthData {
        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
        var me = birthDataBox!!.query().equal(
            BirthData_.tag, (getString(R.string.me)),
            QueryBuilder.StringOrder.CASE_SENSITIVE
        ).orderDesc(BirthData_.id).build().findFirst()
        if (me != null) {
            return me
        } else {
            me = birthDataBox.query().orderDesc(BirthData_.id).build().findFirst()
        }
        if (me == null) {
            me = getCurrentTimeRecord()
        }
        return me
    }

    private fun getCurrentTimeRecord(): BirthData {
        val calendar = Calendar.getInstance()
        val birthData = BirthData()
        birthData.name = getString(R.string.today_sign)
        birthData.birthday = calendar.timeInMillis
        birthData.birthplaceLatitude = 25.047998
        birthData.birthplaceLongitude = 121.554483
        return birthData
    }

    private fun initMe(me: BirthData) {
        val horoscope = me.calculate(requireContext())
        sign = horoscope.findPlantSign(Planet.Sun).enName
        binding.tvName.text = me.name
        binding.tvBirthday.text = FormatUtils.longToString(me.birthday, "yyyy/MM/dd HH:mm")
        binding.tvSunSign.text = horoscope.findPlantSign(Planet.Sun).chName
        binding.tvSunSignSymbol.text = horoscope.findPlantSign(Planet.Sun).symbol
    }

    private fun translate(string: String, textView: AppCompatTextView) {
        binding.progressBar.visibility = View.VISIBLE
        TranslateUtil.translate(string, object : TranslateCallback {
            override fun onSuccess(message: String) {
                binding.lltContent.visibility = View.VISIBLE
                binding.tvErrorMessage.visibility = View.GONE
                if (!isAdded) {
                    LogUtil.e("!isAdded")
                    return
                }
                textView.text = message
                when (textView.id) {
                    R.id.tvCompatibility -> {
                        textView.text = requireContext().getString(R.string.compatibility, message)
                    }
                    R.id.tvMood -> {
                        textView.text = requireContext().getString(R.string.mood, message)
                    }
                    R.id.tvColor -> {
                        textView.text = requireContext().getString(R.string.luckyColor, message)
                    }
                    R.id.tvLuckyNumber -> {
                        textView.text = requireContext().getString(R.string.luckyNumber, message)
                    }
                    R.id.tvLuckyTime -> {
                        textView.text = requireContext().getString(R.string.luckyTime, message)
                    }
                }
                binding.progressBar.visibility = View.GONE
                LogUtil.d("onSuccess : $message")
            }

            override fun onFailure(message: String?) {
                if (message != null) {
                    LogUtil.d(message)
                }
                textView.text = string
                when (textView.id) {
                    R.id.tvCompatibility -> {
                        textView.text = requireContext().getString(R.string.compatibility, string)
                    }
                    R.id.tvMood -> {
                        textView.text = requireContext().getString(R.string.mood, string)
                    }
                    R.id.tvColor -> {
                        textView.text = requireContext().getString(R.string.luckyColor, string)
                    }
                    R.id.tvLuckyNumber -> {
                        textView.text = requireContext().getString(R.string.luckyNumber, string)
                    }
                    R.id.tvLuckyTime -> {
                        textView.text = requireContext().getString(R.string.luckyTime, string)
                    }
                }
//                binding.lltContent.visibility = View.GONE
//                binding.tvErrorMessage.visibility = View.VISIBLE
//                binding.tvErrorMessage.text = requireContext().getString(R.string.error, message)
                binding.progressBar.visibility = View.GONE
            }
        })
    }
}