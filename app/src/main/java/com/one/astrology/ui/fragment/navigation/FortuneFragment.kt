package com.one.astrology.ui.fragment.navigation

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.TransitData
import com.one.astrology.databinding.FragmentFortuneBinding
import com.one.astrology.ui.fragment.adapter.FortuneItemAdapter
import com.one.astrology.ui.fragment.viewmodel.FortuneViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FortuneFragment : Fragment(R.layout.fragment_fortune) {

    companion object {
        fun newInstance() = FortuneFragment()
    }

    private lateinit var binding: FragmentFortuneBinding
    private lateinit var viewModel: FortuneViewModel

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentFortuneBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel = ViewModelProvider(this)[FortuneViewModel::class.java]
        initRecycleView()
    }

    private fun initRecycleView() {
        val transitDataBox = ObjectBox.get().boxFor(
            TransitData::class.java
        )
        val list = transitDataBox.all
        val contentItemAdapter = FortuneItemAdapter()
        contentItemAdapter.setOnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, position: Int ->

        }
        contentItemAdapter.submitList(list)
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        binding.recyclerView.adapter = contentItemAdapter
    }
}