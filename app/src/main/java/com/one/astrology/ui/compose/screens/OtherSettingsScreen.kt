package com.one.astrology.ui.compose.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.event.EventKey
import com.one.astrology.ui.viewmodel.SettingsViewModel
import com.one.core.util.LogUtil

/**
 * 其他設定頁面
 */
@Composable
fun OtherSettingsScreen(viewModel: SettingsViewModel) {
    val context = LocalContext.current
    val houseSystem by viewModel.houseSystem.collectAsState()
    val chartAnimation by viewModel.chartAnimation.collectAsState()
    val planetRuler by viewModel.planetRuler.collectAsState()

    var showHouseSystemDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 宮位系統設定
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column {
                // 標題
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(colorResource(id = R.color.colorPrimary))
                        .padding(vertical = 10.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "宮位系統",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                }

                // 宮位系統選擇
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showHouseSystemDialog = true }
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Home,
                        contentDescription = "宮位系統",
                        tint = colorResource(id = R.color.colorPrimary),
                        modifier = Modifier.size(24.dp)
                    )

                    Spacer(modifier = Modifier.width(16.dp))

                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "宮位系統",
                            color = colorResource(id = R.color.colorPrimary),
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = getHouseSystemName(houseSystem),
                            color = colorResource(id = R.color.grey),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }

        // 顯示設定
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column {
                // 標題
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(colorResource(id = R.color.colorPrimary))
                        .padding(vertical = 10.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "顯示設定",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                }

                // 星盤動畫
//                SettingsSwitchItem(
//                    icon = Icons.Default.Animation,
//                    title = "星盤動畫",
//                    isChecked = chartAnimation,
//                    onCheckedChange = { viewModel.updateChartAnimation(context, it) }
//                )
//
//                Divider(
//                    color = Color.LightGray.copy(alpha = 0.5f),
//                    thickness = 0.5.dp,
//                    modifier = Modifier.padding(horizontal = 16.dp)
//                )

                // 顯示宮主星
                SettingsSwitchItem(
                    icon = Icons.Default.Star,
                    title = "顯示宮主星",
                    isChecked = planetRuler,
                    onCheckedChange = { viewModel.updatePlanetRuler(context, it) }
                )
            }
        }
    }

    // 宮位系統選擇對話框
    if (showHouseSystemDialog) {
        HouseSystemDialog(
            currentSystem = houseSystem,
            onDismiss = { showHouseSystemDialog = false },
            onSystemSelected = {
                viewModel.updateHouseSystem(context, it)
                showHouseSystemDialog = false
            }
        )
    }
}

/**
 * 設定開關項目
 */
@Composable
fun SettingsSwitchItem(
    icon: ImageVector,
    title: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    // 發送事件通知星盤頁面重新加載數據
    val handleChange: (Boolean) -> Unit = { newValue ->
        onCheckedChange(newValue)
        LogUtil.d("Setting changed to $newValue")
        LiveEventBus.get<Boolean>(EventKey.RefreshChart).post(true)
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { handleChange(!isChecked) }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = colorResource(id = R.color.colorPrimary),
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = title,
            color = colorResource(id = R.color.colorPrimary),
            fontWeight = FontWeight.Bold,
            fontSize = 16.sp,
            modifier = Modifier.weight(1f)
        )

        Switch(
            checked = isChecked,
            onCheckedChange = { handleChange(it) },
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = colorResource(id = R.color.colorAccent),
                uncheckedThumbColor = Color.White,
                uncheckedTrackColor = colorResource(id = R.color.grey)
            )
        )
    }
}

/**
 * 宮位系統選擇對話框
 */
@Composable
fun HouseSystemDialog(
    currentSystem: Char,
    onDismiss: () -> Unit,
    onSystemSelected: (Char) -> Unit
) {
    // 發送事件通知星盤頁面重新加載數據
    val handleSystemSelected: (Char) -> Unit = { system ->
        onSystemSelected(system)
        LogUtil.d("House system changed to $system")
        LiveEventBus.get<Boolean>(EventKey.RefreshChart).post(true)
    }
    val houseSystems = listOf(
        HouseSystemItem('P', "普拉西德斯 (Placidus)"),
        HouseSystemItem('K', "科赫 (Koch)"),
        HouseSystemItem('O', "波菲里 (Porphyry)"),
        HouseSystemItem('R', "雷吉歐蒙塔努斯 (Regiomontanus)"),
        HouseSystemItem('C', "坎帕努斯 (Campanus)"),
        HouseSystemItem('A', "等宮制 (Equal House)"),
        HouseSystemItem('E', "等宮制 (Equal House)"),
        HouseSystemItem('V', "維蒂烏斯等宮制 (Vehlow Equal)"),
        HouseSystemItem('W', "整宮制 (Whole Sign)"),
        HouseSystemItem('X', "子午線制 (Meridian Houses)"),
        HouseSystemItem('H', "地平經度制 (Azimuthal / Horizon)"),
        HouseSystemItem('T', "托普森制 (Topocentric / Polich-Page)"),
        HouseSystemItem('B', "阿爾卡比提烏斯 (Alcabitius)"),
        HouseSystemItem('M', "摩里努斯 (Morinus)"),
        HouseSystemItem('U', "庫魯辛斯基–皮薩–戈爾策 (Krusinski-Pisa-Goelzer)"),
        HouseSystemItem('G', "高德區段制 (Gauquelin Sectors)")
    )


    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
            ) {
                Text(
                    text = "選擇宮位系統",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp,
                    color = colorResource(id = R.color.colorPrimary)
                )

                Spacer(modifier = Modifier.height(16.dp))

                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(400.dp)
                ) {
                    items(houseSystems) { system ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { handleSystemSelected(system.code) }
                                .padding(vertical = 12.dp, horizontal = 16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = system.name,
                                fontSize = 16.sp,
                                color = if (system.code == currentSystem) colorResource(id = R.color.colorPrimary) else Color.Black,
                                fontWeight = if (system.code == currentSystem) FontWeight.Bold else FontWeight.Normal,
                                modifier = Modifier.weight(1f)
                            )

                            if (system.code == currentSystem) {
                                Box(
                                    modifier = Modifier
                                        .size(24.dp)
                                        .background(colorResource(id = R.color.colorPrimary), CircleShape),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = Color.White,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }

                        if (system != houseSystems.last()) {
                            Divider(
                                color = Color.LightGray.copy(alpha = 0.5f),
                                thickness = 0.5.dp,
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 宮位系統項目數據類
 */
data class HouseSystemItem(
    val code: Char,
    val name: String
)

/**
 * 獲取宮位系統名稱
 */
fun getHouseSystemName(code: Char): String {
    return when (code) {
        'P' -> "普拉西德斯 (Placidus)"                  // 1
        'K' -> "科赫 (Koch)"                            // 2
        'O' -> "波菲里 (Porphyry)"                     // 3
        'R' -> "雷吉歐蒙塔努斯 (Regiomontanus)"         // 4
        'C' -> "坎帕努斯 (Campanus)"                   // 5
        'A' -> "等宮制 (Equal House)"                   // 6
        'E' -> "等宮制 (Equal House)"                   // 6 (同A)
        'V' -> "維蒂烏斯等宮 (Vehlow Equal)"            // 7
        'W' -> "整宮制 (Whole Sign)"                    // 8
        'X' -> "子午線制 / 軸向旋轉系統 (Meridian Houses / Axial Rotation)" // 9
        'T' -> "托普森 / Polich-Page (Topocentric)"     // 10
        'B' -> "阿爾卡比提烏斯 (Alcabitius)"            // 11
        'M' -> "摩里努斯 (Morinus)"                    // 12
        'U' -> "庫爾特 / Krusinski-Pisa-Goelzer"        // 13
        'G' -> "高德區段 (Gauquelin Sectors)"           // 15
        'H' -> "地平經度制 (Azimuthal / Horizon)"       // 無特定數值
        'Y' -> "APC 宮位系統 (APC Houses)"              // 無特定數值
        else -> "普拉西德斯 (Placidus)"
    }
}
