package com.one.astrology.ui.fragment.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.databinding.FragmentSingleSelectBottomSheetBinding
import com.one.astrology.ui.fragment.adapter.SingleAdapter
import com.one.core.util.LogUtil


/**
 * SingleSelectBottomSheetFragment
 */
class SingleSelectBottomSheetFragment :
    BottomSheetDialogFragment(R.layout.fragment_single_select_bottom_sheet) {

    private lateinit var mAdapter: SingleAdapter
    private lateinit var binding: FragmentSingleSelectBottomSheetBinding


    companion object {
        private var onClickListener: View.OnClickListener? = null

        fun newInstance(onClickListener: View.OnClickListener) =
            SingleSelectBottomSheetFragment().apply {
                SingleSelectBottomSheetFragment.onClickListener = onClickListener
            }

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSingleSelectBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("SingleSelectBottomSheetFragment", this.javaClass.simpleName)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initRecycleView()
    }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(requireActivity())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        val signRecordBox = ObjectBox.get().boxFor(BirthData::class.java)
        val signRecordList = signRecordBox.query().orderDesc(BirthData_.id).build().find()

        mAdapter = SingleAdapter()
        mAdapter.isEmptyViewEnable = true
        mAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        mAdapter.submitList(signRecordList)
        mAdapter.onClickListener = View.OnClickListener {
            dismiss()
            onClickListener?.onClick(it)
        }
        binding.recyclerView.adapter = mAdapter
    }

}