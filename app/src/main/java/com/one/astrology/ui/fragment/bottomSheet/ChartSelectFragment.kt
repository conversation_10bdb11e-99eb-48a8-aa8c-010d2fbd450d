package com.one.astrology.ui.fragment.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.one.astrology.R
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentChartSelectBinding
import com.one.astrology.ui.fragment.adapter.ChartItemAdapter
import com.one.core.util.LogUtil


/**
 * ChartSelectFragment
 */
class ChartSelectFragment :
    BottomSheetDialogFragment(R.layout.fragment_chart_select) {
    private lateinit var binding: FragmentChartSelectBinding

    companion object {
        private var onClickListener: OnClickListener? = null

        fun newInstance(onClickListener: OnClickListener) =
            ChartSelectFragment().apply {
                ChartSelectFragment.onClickListener = onClickListener
            }

    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChartSelectBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initRecycleView()
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("ChartSelectFragment", this.javaClass.simpleName)
    }

//    private val basic = arrayOf(
//        "天象盤",
//        "本命盤",
//        "行運盤",
//        "次限盤",
//        "次限比較盤",
//        "三限盤",
//        "三限比較盤",
//        "日返盤",
//        "月返盤",
//        "日弧盤",
//        "法達盤"
//    )
//    private val combine = arrayOf(
//        "比較盤",
//        "比較次限盤",
//        "比較三限盤",
//        "組合盤",
//        "組合次限盤",
//        "組合三限盤",
//        "時空盤",
//        "時空次限盤",
//        "時空三限盤",
//        "馬盤",
//        "馬盤次限盤",
//        "馬盤三限盤"
//    )

    private val mutableMapOfChart = mutableMapOf(
        Pair("比較盤", Chart.Synastry),
//        Pair("比較次限盤", Chart.SynastrySecondaryProgression),
//        Pair("比較三限盤", Chart.SynastryTertiaryProgression),
        Pair("組合盤", Chart.Composite),
        Pair("組合次限盤", Chart.CompositeSecondaryProgression),
        Pair("組合三限盤", Chart.CompositeTertiaryProgression),
        Pair("時空盤", Chart.Davison),
        Pair("時空次限盤", Chart.DavisonSecondaryProgression),
        Pair("時空三限盤", Chart.DavisonTertiaryProgression),
        Pair("馬盤", Chart.Marks),
        Pair("馬盤次限盤", Chart.MarksSecondaryProgression),
        Pair("馬盤三限盤", Chart.MarksTertiaryProgression)
    )

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(activity)
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        val list = mutableMapOfChart.keys.toMutableList()
        val chartItemAdapter = ChartItemAdapter()
        chartItemAdapter.submitList(list)
        chartItemAdapter.setOnItemClickListener { _, view, _ ->
            val data = view.tag as String
            view.tag = mutableMapOfChart[data]
            onClickListener?.onClick(view)
            dismiss()
        }
        binding.recyclerView.adapter = chartItemAdapter
    }
}