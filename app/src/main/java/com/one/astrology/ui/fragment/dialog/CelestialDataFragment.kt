package com.one.astrology.ui.fragment.dialog

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.afollestad.date.year
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.datepicker.MaterialDatePicker
import com.loper7.date_time_picker.DateTimeConfig
import com.loper7.date_time_picker.dialog.CardDatePickerDialog
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentCelestialDataBinding
import com.one.astrology.ui.activity.MapActivity
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.adapter.CitySuggestionsAdapter
import com.one.astrology.ui.fragment.viewmodel.SharedViewModel
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.FormatUtils
import com.one.core.util.InputTools
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date

/**
 * 出生資料頁
 */
class CelestialDataFragment : DialogFragment(R.layout.fragment_celestial_data) {

    private var mDate: Date? = null
    private lateinit var materialDatePicker: MaterialDatePicker<Long>
    private var birthData: BirthData = BirthData()
    private var latitude = 25.047998
    private var longitude = 121.554483

    private lateinit var binding: FragmentCelestialDataBinding
    private lateinit var dialog: AlertDialog
    private val calendar = Calendar.getInstance()
    private val sharedViewModel by viewModels<SharedViewModel>()

    // 新增的變數
    private lateinit var citySuggestionsAdapter: CitySuggestionsAdapter
    private lateinit var mapLauncher: ActivityResultLauncher<Intent>
    private lateinit var locationPermissionLauncher: ActivityResultLauncher<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化地圖選擇 launcher
        mapLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val latLng = MapActivity.getLatLngFromResult(result.data)
                if (latLng != null) {
                    latitude = latLng.latitude
                    longitude = latLng.longitude

                    // 更新座標顯示
                    updateCoordinatesDisplay()
                    updateLocationInfo(latLng)

                    // 更新地址
                    updateLocationAddress(latLng)

                    LogUtil.d("地點經緯度：${latitude}, ${longitude}")
                }
            }
        }

        // 初始化 GPS 權限 launcher
        locationPermissionLauncher = registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                LocationUtil.getCurrentLocation(
                    requireActivity(),
                    PermissionsUtil.LOCATION_PERMISSION_REQUEST_CODE
                ) { location ->
                    latitude = location.latitude
                    longitude = location.longitude

                    // 更新座標顯示
                    updateCoordinatesDisplay()
                    updateLocationInfo(LatLng(latitude, longitude))

                    // 更新地址
                    updateLocationAddress(LatLng(latitude, longitude))
                }
            } else {
                LogUtil.d("定位權限被拒絕")
                Toast.makeText(
                    requireContext(),
                    "需要定位權限才能使用此功能喔！",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(requireActivity())
        val inflater = requireActivity().layoutInflater
        binding = FragmentCelestialDataBinding.inflate(inflater, null, false)
        builder.setView(binding.root)

        birthData = BirthData()
        birthData.name = requireActivity().getString(Chart.Celestial.type)
        birthData.birthplaceLatitude = latitude
        birthData.birthplaceLongitude = longitude

        initView()
        initLocationInput()
        initDatePicker()
        val latLng = LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        toAddresses(latLng)

        dialog = builder.create()
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        return dialog
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("天象盤資訊頁", this.javaClass.simpleName)
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    fun initView() {

        binding.tvDate.setOnClickListener {
            val calendar = Calendar.getInstance()
            calendar.year = calendar[Calendar.YEAR] - 20

            if (mDate != null) {
                calendar.time = mDate!!
            }
            val list = intArrayOf(
                DateTimeConfig.YEAR,
                DateTimeConfig.MONTH,
                DateTimeConfig.DAY,
                DateTimeConfig.HOUR,
                DateTimeConfig.MIN
            ).toMutableList()

            CardDatePickerDialog.builder(requireContext())
                .setTitle("選擇出生日期與時間")
                .setBackGroundModel(CardDatePickerDialog.CARD)
                .setDefaultTime(calendar.timeInMillis)
                .setDisplayType(list)
                .setLabelText("年", "月", "日", "時", "分")
                .showDateLabel(true)
                .showFocusDateInfo(true)
                .showBackNow(true)
                .setOnChoose("選擇") { millisecond ->
                    val timeString: String =
                        FormatUtils.dateToString(Date(millisecond), "yyyy/MM/dd HH:mm")
                    binding.tvDate.setText(timeString)
                    mDate = Date(millisecond)
                }
                .setOnCancel("取消") { }
                .build().show()
        }

        binding.btSave.setOnClickListener {
            launchWhenStarted {
                addData(it)
            }
        }
    }

    /**
     * 初始化地點輸入
     */
    private fun initLocationInput() {
        // 設置城市建議適配器
//        citySuggestionsAdapter = CitySuggestionsAdapter { city ->
//            binding.etCityName.setText(city.name)
//            // 根據城市獲取座標
//            searchLocationByCity(city.name)
//        }

        // 城市輸入監聽
        binding.etCityName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val query = s.toString().trim()
                if (query.length >= 2) {
                    searchCitySuggestions(query)
                } else {
//                    binding.rvCitySuggestions.visibility = View.GONE
                }
            }
        })

        // 地圖按鈕
        binding.btnMap.setOnClickListener {
            val intent = if (latitude != -1.0 && longitude != -1.0) {
                MapActivity.createIntent(requireContext(), latitude, longitude)
            } else {
                MapActivity.createIntent(requireContext())
            }
            mapLauncher.launch(intent)
        }

        // GPS按鈕
        binding.btnGetCurrentLocation.setOnClickListener {
            locationPermissionLauncher.launch(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        // 搜尋按鈕（如果需要的話）
        binding.btnSearch.setOnClickListener {
            // 可以添加搜尋功能
        }
    }

    /**
     * 搜尋城市建議
     */
    private fun searchCitySuggestions(query: String) {
        lifecycleScope.launch {
            try {
                val suggestions = LocationUtil.searchCitySuggestions(query)
                citySuggestionsAdapter.submitList(suggestions)
            } catch (e: Exception) {
                LogUtil.e("搜尋城市失敗", e.message ?: "Unknown error")
            }
        }
    }

    /**
     * 根據城市名稱搜尋位置
     */
    private fun searchLocationByCity(cityName: String) {
        lifecycleScope.launch {
            try {
                val latLng = LocationUtil.addressToLatLng(requireContext(), cityName)
                if (latLng != null) {
                    latitude = latLng.latitude
                    longitude = latLng.longitude
                    updateCoordinatesDisplay()
                    updateLocationInfo(latLng)
                }
            } catch (e: Exception) {
                LogUtil.e("搜尋位置失敗", e.message ?: "Unknown error")
            }
        }
    }

    /**
     * 更新座標顯示
     */
    private fun updateCoordinatesDisplay() {
        if (latitude != -1.0 && longitude != -1.0) {
            // 更新合併的座標顯示（格式：37.7858, -122.4064）
            val coordinatesText = "${formatCoordinate(latitude)}, ${formatCoordinate(longitude)}"
            binding.tvCoordinates.text = coordinatesText
            binding.cvBirthplaceInfo.visibility = View.VISIBLE
        } else {
            binding.cvBirthplaceInfo.visibility = View.GONE
        }
    }

    /**
     * 更新位置資訊卡片
     */
    private fun updateLocationInfo(latLng: LatLng) {
        lifecycleScope.launch {
            try {
                // 使用當前時間來獲取時區偏移量
                val calendar = Calendar.getInstance()
                val year = calendar.get(Calendar.YEAR)
                val month = calendar.get(Calendar.MONTH) + 1
                val day = calendar.get(Calendar.DAY_OF_MONTH)
                val hour = calendar.get(Calendar.HOUR_OF_DAY)
                val minute = calendar.get(Calendar.MINUTE)

                val (offset, _) = EphemerisUtil.getOffset(year, month, day, hour, minute, latLng)

                // 格式化時區顯示
                val timezoneText = formatTimezoneOffset(offset)
                binding.tvTimezone.text = timezoneText

                // 更新座標顯示（格式：37.7858, -122.4064）
                val coordinatesText = "${formatCoordinate(latLng.latitude)}, ${formatCoordinate(latLng.longitude)}"
                binding.tvCoordinates.text = coordinatesText

                binding.cvBirthplaceInfo.visibility = View.VISIBLE
            } catch (e: Exception) {
                LogUtil.e("獲取時區失敗", e.message ?: "Unknown error")
                binding.tvTimezone.text = "--"
            }
        }
    }

    /**
     * 更新地址顯示
     */
    private fun updateLocationAddress(latLng: LatLng) {
        lifecycleScope.launch {
            try {
                val address = LocationUtil.latLngToAddresses(latLng, listOf("city"))
                binding.etCityName.setText(address)

                // 更新 birthData
                birthData.birthplaceLatitude = latLng.latitude
                birthData.birthplaceLongitude = latLng.longitude
            } catch (e: Exception) {
                LogUtil.e("獲取地址失敗", e.message ?: "Unknown error")
            }
        }
    }

    /**
     * 格式化座標
     */
    private fun formatCoordinate(value: Double): String {
        return String.format("%.4f", value)
    }

    /**
     * 格式化時區偏移量
     * @param offset 時區偏移量（小時）
     * @return 格式化的時區字串，例如 "UTC+8" 或 "UTC-5"
     */
    private fun formatTimezoneOffset(offset: Double): String {
        val hours = offset.toInt()
        val minutes = ((offset - hours) * 60).toInt()

        return if (minutes == 0) {
            if (hours >= 0) "UTC+$hours" else "UTC$hours"
        } else {
            val sign = if (offset >= 0) "+" else ""
            String.format("UTC%s%d:%02d", sign, hours, kotlin.math.abs(minutes))
        }
    }

    private fun toAddresses(center: LatLng) {
        launchWhenStarted {
            if (!isAdded) {
                return@launchWhenStarted
            }
            val addressString = LocationUtil.latLngToAddresses(center, listOf("city"))
            latitude = center.latitude
            longitude = center.longitude
            birthData.birthplaceLatitude = center.latitude
            birthData.birthplaceLongitude = center.longitude

            binding.etCityName.setText(addressString)
            updateCoordinatesDisplay()
            updateLocationInfo(center)
        }
    }

    private suspend fun addData(view: View) {
        if (TextUtils.isEmpty(binding.tvDate.text.toString()) ||
            TextUtils.isEmpty(binding.etCityName.text.toString()) ||
            latitude == -1.0 || longitude == -1.0) {
            InputTools.HideKeyboard(view)
            Toast.makeText(
                requireActivity(),
                getString(R.string.please_fill_in_the_information),
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        setData()

        // Update ViewModel before dismissing dialog
        if (isAdded && !isDetached) {
            sharedViewModel.data.postValue("")
        }

        dialog.dismiss()
        toSignDetailActivity()
    }

    private suspend fun setData() {
        birthData.name = getString(Chart.Celestial.type)
        birthData.createTime = System.currentTimeMillis()

        val strTime = binding.tvDate.text.toString()
        val date = FormatUtils.stringToDate(strTime, "yyyy/MM/dd HH:mm")
        calendar.timeInMillis = date.time
        birthData.birthday = calendar.timeInMillis
        mDate = calendar.time

        birthData.birthdayString =
            FormatUtils.longToString(birthData.birthday, "yyyy/MM/dd HH:mm")

        birthData.birthplaceArea = LocationUtil.latLngToArea(
            LatLng(
                birthData.birthplaceLatitude,
                birthData.birthplaceLongitude
            )
        )
    }

    private fun toSignDetailActivity() {
        if (!isAdded || isDetached) {
            return
        }
        val bundle = Bundle()
        bundle.putString(KeyDefine.Chart, Chart.Celestial.toStorageValue())
        bundle.putParcelable(KeyDefine.UserBirthData, birthData)
        startActivity(SignDetailActivity::class.java, bundle)
    }

    private fun startActivity(clz: Class<*>?, bundle: Bundle?) {
        if (isAdded) {  // 確保 Fragment 已經附加到 Activity
            val intent = Intent()
            intent.setClass(requireContext(), clz!!)
            if (bundle != null) {
                intent.putExtras(bundle)
            }
            startActivity(intent)
        }
    }


    private fun initDatePicker() {
        val calendar = Calendar.getInstance()
        mDate = Date(calendar.timeInMillis)
        val timeString: String = FormatUtils.dateToString(mDate, "yyyy/MM/dd HH:mm")
        binding.tvDate.setText(timeString)

        materialDatePicker =
            MaterialDatePicker.Builder.datePicker()
                .setTitleText("選擇出生日期")
                .setSelection(calendar.timeInMillis)
//                .setInputMode(MaterialDatePicker.INPUT_MODE_TEXT)
                .build()

        materialDatePicker.addOnPositiveButtonClickListener {
            val time: String = FormatUtils.dateToString(Date(it), "yyyy/MM/dd")
            binding.tvDate.setText(time)
        }
    }
}