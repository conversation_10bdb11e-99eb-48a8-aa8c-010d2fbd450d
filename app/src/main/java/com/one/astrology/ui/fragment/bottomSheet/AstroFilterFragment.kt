package com.one.astrology.ui.fragment.bottomSheet


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.R
import com.one.astrology.data.SettingsPreferencesDataStore
import com.one.astrology.data.db.Categories
import com.one.astrology.databinding.FragmentAstroFilterBinding
import com.one.astrology.databinding.SpinnerCategoriesBinding
import com.one.astrology.db.OtherDBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.util.launchWhenStarted
import com.one.core.util.LogUtil


/**
 * 星盤分類篩選頁
 */
class AstroFilterFragment : BottomSheetDialogFragment(R.layout.fragment_astro_filter) {

    private lateinit var binding: FragmentAstroFilterBinding
    private var engCategory: String? = null

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("星盤分類篩選頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAstroFilterBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initArrayAdapter()

        launchWhenStarted {
            val it = SettingsPreferencesDataStore.getFilterSort(requireContext())
            initFilterSort(it)
        }

        binding.radioGroup.setOnCheckedChangeListener { _, checkedId ->
            LiveEventBus.get<Int>(EventKey.FilterSort).postDelay(checkedId, 0)
            launchWhenStarted {
                SettingsPreferencesDataStore.putFilterSort(requireContext(), checkedId)
            }
        }

        binding.tvOK.setOnClickListener {
            LiveEventBus.get<String>(EventKey.AstroFilter).postDelay(engCategory, 0)
            dismiss()
        }
        binding.tvCancel.setOnClickListener {
            dismiss()
        }
    }

    private fun initArrayAdapter() {
        val typeList = OtherDBHelper.queryCategoriesType(requireContext())
        val typeArray = typeList.map { it.type }.toSet()
        typeArray.forEach { type ->
            val typeMundane = typeList.filter { it.type == type }
            initSpinner(ArrayList(typeMundane))
        }
    }

    private fun initSpinner(typeList: ArrayList<Categories>) {
        val spinnerArray = typeList.map { it.nameCn }
        val list = ArrayList(spinnerArray)
        list.add(0, "請選擇")
        val spinnerArrayAdapter: ArrayAdapter<String> = ArrayAdapter<String>(
            requireContext(), android.R.layout.simple_spinner_item,
            list
        )
        spinnerArrayAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        val spinnerCategoriesBinding: SpinnerCategoriesBinding = SpinnerCategoriesBinding.inflate(
            LayoutInflater.from(requireContext()), null, false
        )
        spinnerCategoriesBinding.tvType.text = typeList[0].typeCn
        spinnerCategoriesBinding.spinner.adapter = spinnerArrayAdapter
        spinnerCategoriesBinding.spinner.onItemSelectedListener =
            object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long
                ) {
                    val engArray = typeList.map { it.name }
                    if (position == 0) {
                        engCategory = null
                        return
                    }
                    engCategory = engArray[position - 1]
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {

                }
            }
        binding.lltContain.addView(spinnerCategoriesBinding.root)
    }

    private fun initFilterSort(it: Int) {
        when (it) {
            R.id.rbTimeAsc -> {
                binding.rbTimeAsc.isChecked = true
            }

            R.id.rbTimeDesc -> {
                binding.rbTimeDesc.isChecked = true
            }

            R.id.rbBirthdayAsc -> {
                binding.rbBirthdayAsc.isChecked = true
            }

            R.id.rbBirthdayDesc -> {
                binding.rbBirthdayDesc.isChecked = true
            }

            R.id.rbNameAsc -> {
                binding.rbNameAsc.isChecked = true
            }

            R.id.rbNameDesc -> {
                binding.rbNameDesc.isChecked = true
            }

            else -> {
                binding.rbTimeAsc.isChecked = true
            }
        }
    }
}