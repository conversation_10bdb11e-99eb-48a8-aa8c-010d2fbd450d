package com.one.astrology.ui.fragment.report

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.BuildConfig
import com.one.astrology.R
import com.one.astrology.api.groq.GroqApiService
import com.one.astrology.data.Horoscope
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.request.groq.GroqRequest
import com.one.astrology.data.request.groq.GroqResponse
import com.one.astrology.data.request.groq.Message
import com.one.astrology.data.type.Chart
import com.one.astrology.db.DBHelper
import com.one.astrology.event.EventKey
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.AppShapes
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.fragment.ExplainFragment
import com.one.astrology.ui.fragment.viewmodel.calculate.CalculateViewModel
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.EphemerisUtil.Companion.house
import com.one.astrology.util.astro.SolarProximity
import com.one.astrology.util.launchWhenStarted
import com.one.astrology.viewmodel.OpenViewModel
import com.one.core.util.IntentUtil
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SignPositionViewModel @Inject constructor() : ViewModel() {
    private val _planetList = MutableStateFlow<List<PlanetBean>>(emptyList())
    val planetList: StateFlow<List<PlanetBean>> = _planetList.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    fun updatePlanetList(list: List<PlanetBean>) {
        viewModelScope.launch {
            _planetList.value = list
        }
    }

    fun setLoading(loading: Boolean) {
        viewModelScope.launch {
            _isLoading.value = loading
        }
    }
}

/**
 * 星體位置
 */
@AndroidEntryPoint
class SignPositionFragment : BaseFragment(R.layout.fragment_sign_position) {

    private var title: String? = null
    private val viewModel by activityViewModels<CalculateViewModel>()
    private lateinit var matchEvent: MatchEvent
    private val openViewModel by viewModels<OpenViewModel>()
    private val signPositionViewModel: SignPositionViewModel by viewModels()

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("星位頁", this.javaClass.simpleName)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                MaterialTheme {
                    SignPositionScreen(
                        viewModel = signPositionViewModel,
                        onSignClick = { planetBean -> querySign(planetBean) },
                        onHouseClick = { planetBean -> queryHouse(planetBean) },
                        onRetrogradeClick = { planetBean -> queryRetrograde(planetBean) }
                    )
                }
            }
        }
    }

    override fun initView(view: View) {
        title = getString(Chart.Natal.type)
        collectData()
    }

    private fun collectData() {
        getEvent()
    }

    @Composable
    fun SignPositionScreen(
        viewModel: SignPositionViewModel,
        onSignClick: (PlanetBean) -> Unit,
        onHouseClick: (PlanetBean) -> Unit,
        onRetrogradeClick: (PlanetBean) -> Unit
    ) {
        val planetList by viewModel.planetList.collectAsState()
        val isLoading by viewModel.isLoading.collectAsState()

        LaunchedEffect(Unit) {
            if (::matchEvent.isInitialized) {
                when (matchEvent.chartType) {
                    Chart.Transit, Chart.Synastry -> {
                        initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                    }

                    else -> {
                        initData(matchEvent.horoscopeA)
                    }
                }
            }
        }

        Box(modifier = Modifier.fillMaxSize()) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (planetList.isEmpty()) {
                EmptyState()
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(horizontal = 10.dp, vertical = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    items(planetList) { planet ->
                        PlanetItem(
                            planet = planet,
                            onSignClick = { onSignClick(planet) },
                            onHouseClick = { onHouseClick(planet) },
                            onRetrogradeClick = { onRetrogradeClick(planet) }
                        )
                    }
                }
            }
        }
    }

    @Composable
    private fun EmptyState() {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "暫無星體數據",
                style = MaterialTheme.typography.titleMedium
            )
        }
    }

    @Composable
    fun PlanetItem(
        planet: PlanetBean,
        onSignClick: () -> Unit,
        onHouseClick: () -> Unit,
        onRetrogradeClick: () -> Unit
    ) {
        var expanded by remember { mutableStateOf(false) }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 3.dp, end = 3.dp, top = 3.dp, bottom = 3.dp)
                .clickable { expanded = !expanded },
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = AppShapes.small,
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp, vertical = 5.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                // 行星名稱 (weight = 4)
                Row(
                    modifier = Modifier
                        .weight(4f)
                        .padding(horizontal = 0.dp)
                        .clickable(onClick = onSignClick),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = planet.symbol,
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontFamily = FontFamily(Font(R.font.astro_one_font)),
                            fontWeight = FontWeight.Normal,
                        ),
                        color = planet.getSafeColor()
                    )

                    Spacer(modifier = Modifier.width(3.dp))

                    Text(
                        text = planet.chName,
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center,
                        color = colorResource(id = R.color.colorPrimary)
                    )
                }

                // 星座區域 (weight = 7)
                Row(
                    modifier = Modifier
                        .weight(7.5f)
                        .clickable(onClick = onSignClick),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = planet.signBean.symbol,
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontFamily = FontFamily(Font(R.font.astro_one_font)),
                            fontWeight = FontWeight.Normal,
                        ),
                        color = planet.signBean.getSafeColor()
                    )

                    Spacer(modifier = Modifier.width(3.dp))

                    Text(
                        text = planet.signBean.chName,
                        style = MaterialTheme.typography.bodySmall,
                        color = colorResource(id = R.color.colorPrimary)
                    )
                    Text(
                        text = planet.signBean.degree,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(start = 5.dp),
                        color = colorResource(id = R.color.colorPrimary)
                    )
                    Text(
                        text = planet.signBean.minute,
                        style = MaterialTheme.typography.bodySmall,
                        color = colorResource(id = R.color.colorPrimary)
                    )

                    Text(
                        text = planet.signBean.dignity,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(start = 5.dp),
                        color = colorResource(id = R.color.colorPrimary)
                    )
                }

                // 宮位區域 (weight = 5)
                Row(
                    modifier = Modifier
                        .weight(5f)
                        .clickable(onClick = onHouseClick),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${planet.houseData.index}宮",
                        style = MaterialTheme.typography.bodySmall,
                        color = colorResource(id = R.color.colorPrimary)
                    )
                    Text(
                        text = planet.houseData.degree,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(start = 5.dp),
                        color = colorResource(id = R.color.colorPrimary)
                    )
                }

                // 速度
                if (planet.id <= 20) {
                    Text(
                        text = EphemerisUtil.classifySpeed(planet.id, planet.speed),
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier
                            .weight(2f)
                            .clickable(onClick = onRetrogradeClick),
                        textAlign = TextAlign.Center,
                        color = colorResource(id = R.color.colorPrimary)
                    )
                } else {
                    Spacer(modifier = Modifier.weight(2f))
                }

//                Icon(
//                    imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
//                    tint = colorResource(id = R.color.colorPrimary),
//                    contentDescription = if (expanded) "摺疊" else "展開"
//                )
            }

            AnimatedVisibility(visible = expanded) {
                // TODO

                var planetStatus = "${planet.planetNature}"
                planetStatus += when (planet.solarProximity) {
                    SolarProximity.NONE -> ""
                    SolarProximity.CAZIMI -> " 核心內"
                    SolarProximity.COMBUSTION -> " 焦傷"
                    SolarProximity.UNDER_THE_BEAMS -> " 在太陽光束下"
                    null -> ""
                }

                Text(
                    text = planetStatus,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 10.dp, end = 10.dp, top = 0.dp, bottom = 10.dp),
                    color = colorResource(id = R.color.colorPrimary),
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }

    private fun initMatch(matchEvent: MatchEvent?) {
        if (matchEvent != null) {
            this.matchEvent = matchEvent
            title = getString(matchEvent.chartType.type)
            when (matchEvent.chartType) {
                Chart.Celestial -> {
                    initData(matchEvent.horoscopeA)
                }

                Chart.Natal -> {
                    initData(matchEvent.horoscopeA)
                }

                Chart.Transit -> {
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.SecondaryProgression -> {
                    initData(matchEvent.horoscopeA)
                }

                Chart.SecondaryProgressionSynastry -> {
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.TertiaryProgression -> {
                    initData(matchEvent.horoscopeA)
                }

                Chart.TertiaryProgressionSynastry -> {
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.SolarArc -> {
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.Synastry -> {
                    initData(matchEvent.horoscopeB, matchEvent.horoscopeA)
                }

                Chart.Composite -> {
                    initData(matchEvent.horoscopeA)
                }

                Chart.Davison -> {
                    initData(matchEvent.horoscopeA)
                }

                Chart.Marks -> {
                    initData(matchEvent.horoscopeA)
                }

                else -> {
                    initData(matchEvent.horoscopeA)
                }
            }
        }
    }

    private fun getEvent() {
        LiveEventBus.get(EventKey.MatchEvent, MatchEvent::class.java)
            .observeStickyForever { matchEvent: MatchEvent? ->
                initMatch(matchEvent)
            }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initData(horoscopeA: Horoscope, horoscopeB: Horoscope) {
        launchWhenStarted {
            signPositionViewModel.setLoading(true)
            try {
                val signList = AssetsToObjectUtil.getSignList(requireContext())
                val planetBeanList = horoscopeA.getPlanetBeanList().sortedWith(compareBy { it.id })
                val planetList = planetBeanList.filter { it.isChecked }
                val list = ArrayList<PlanetBean>()

                for (planet in planetList) {
                    val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
                    planet.signBean = SignBean(signList[strings[0].toInt()])
                    planet.signBean.degree = "${strings[1]}°"
                    planet.signBean.minute = strings[2]
                    planet.signBean.houseData =
                        house(planet.longitude, horoscopeB.houses.cusps)
                    planet.houseData = house(planet.longitude, horoscopeB.houses.cusps)
                    LogUtil.i("星位 ${planet.chName} ${planet.signBean.chName} ${strings[1]}°${strings[2]} ${planet.signBean.houseData.index}宮")
                    list.add(planet)
                }

                signPositionViewModel.updatePlanetList(list)
            } catch (ex: Exception) {
                ex.message?.let { LogUtil.e(it) }
            }
            signPositionViewModel.setLoading(false)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initData(horoscopeA: Horoscope) {
        launchWhenStarted {
            signPositionViewModel.setLoading(true)
            try {
                val planetBeanList = horoscopeA.getPlanetBeanList().sortedWith(compareBy { it.id })
                val list = planetBeanList.filter { it.isChecked }
                signPositionViewModel.updatePlanetList(list)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
            signPositionViewModel.setLoading(false)
        }
    }

    private fun queryRetrograde(planetBean: PlanetBean) {
        val text = EphemerisUtil.classifySpeed(planetBean.id, planetBean.speed)
        val query = title + " " + planetBean.chName + text
        if (BuildConfig.IS_DEV) {
            when (matchEvent.chartType) {
                Chart.Natal -> {
                    queryRetrogradeDB(planetBean)
                }

                else -> {
                    IntentUtil.searchWeb(requireActivity(), query)
                }
            }
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun queryRetrogradeDB(planetBean: PlanetBean) {
        val text = EphemerisUtil.classifySpeed(planetBean.id, planetBean.speed)
        val query = title + " " + planetBean.chName + text
        val data = DBHelper.queryRetrograde(
            requireContext(),
            planetBean.chName
        )
        if (data != null) {
            showData(query, data.descList)
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun queryHouse(planetBean: PlanetBean) {
        val query =
            title + " " + planetBean.chName + " " + planetBean.signBean.houseData.index + "宮"
        if (BuildConfig.IS_DEV) {
            when (matchEvent.chartType) {
                Chart.Natal,
                Chart.Composite -> {
                    queryHouseDB(matchEvent.chartType, planetBean)
                }

                else -> {
                    queryHouseDB(matchEvent.chartType, planetBean)
                }
            }
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun querySign(planetBean: PlanetBean) {
        val query = title + " " + planetBean.chName + " " + planetBean.signBean.chName
        if (BuildConfig.IS_DEV) {
            when (matchEvent.chartType) {
                Chart.Natal,
                Chart.Composite,
                Chart.Transit -> {
                    querySignDB(matchEvent.chartType, planetBean)
                }

                else -> {
                    IntentUtil.searchWeb(requireActivity(), query)
                }
            }
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun queryHouseDB(chart: Chart, planetBean: PlanetBean) {
        val query =
            title + " " + planetBean.chName + " " + planetBean.signBean.houseData.index + "宮"
        val data = DBHelper.queryHouse(
            requireContext(),
            chart,
            planetBean.signBean.houseData.index,
            planetBean.chName
        )
        if (data != null) {
            showData(query, data.descList)
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun querySignDB(chart: Chart, planetBean: PlanetBean) {
        val query = title + " " + planetBean.chName + " " + planetBean.signBean.chName
//        isLoading(true)
//        RealTimeDBHelp.getSign(
//            chart,
//            planetBean.chName,
//            planetBean.signBean.chName,
//            object : ValueListener {
//                override fun onDataChange(signDescData: SignDescData?) {
//                    if (signDescData != null) {
//                        showData(query, signDescData.descList)
//                    } else {
//                        IntentUtil.searchWeb(requireActivity(), query)
//                    }
//                    isLoading(false)
//                }
//            })

        val data = DBHelper.querySign(
            requireContext(),
            chart,
            planetBean.chName,
            planetBean.signBean.chName
        )
        if (data != null) {
            showData(query, data.descList)
//            val queryText = "請重構下列句子 " + data.descList.toString()
//            queryOpenApi(queryText)
//            fetchGroqResponse(queryText)
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun showData(query: String, descList: ArrayList<String>) {
        if (descList.isNotEmpty()) {
            ExplainFragment.newInstance(query, descList)
                .show(requireActivity().supportFragmentManager, "")
        } else {
            IntentUtil.searchWeb(requireActivity(), query)
        }
    }

    private fun queryOpenApi(query: String) {
        LogUtil.d("query :$query")
        viewLifecycleOwner.lifecycleScope.launch {
            openViewModel.completions(query)
        }

        openViewModel.resultLiveData.observe(viewLifecycleOwner) {
            if (it == null) {
                return@observe
            }

            if (it.choices.isNotEmpty()) {
                LogUtil.d(it.choices[0].text)
                ExplainFragment.newInstance(query, it.choices[0].text)
                    .show(requireActivity().supportFragmentManager, "")
            }
        }
    }

    fun fetchGroqResponse(prompt: String) {
        val apiKey = "********************************************************"
        val service = GroqApiService.create(apiKey)

        val request = GroqRequest(
            model = "application/json",
            messages = listOf(Message("user", prompt))
        )

        service.getChatCompletion(request).enqueue(object : retrofit2.Callback<GroqResponse> {
            override fun onResponse(
                call: retrofit2.Call<GroqResponse>,
                response: retrofit2.Response<GroqResponse>
            ) {
                if (response.isSuccessful) {
                    val reply = response.body()?.choices?.firstOrNull()?.message?.content
                    println("Groq AI 回應：$reply")
                } else {
                    println("請求失敗：${response.errorBody()?.string()}")
                }
            }

            override fun onFailure(call: retrofit2.Call<GroqResponse>, t: Throwable) {
                println("請求錯誤：${t.message}")
            }
        })
    }
}