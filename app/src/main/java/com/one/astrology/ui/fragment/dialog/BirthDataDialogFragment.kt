package com.one.astrology.ui.fragment.dialog

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.afollestad.date.year
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.textfield.TextInputLayout
import com.google.android.material.timepicker.MaterialTimePicker
import com.google.android.material.timepicker.MaterialTimePicker.INPUT_MODE_KEYBOARD
import com.google.android.material.timepicker.TimeFormat
import com.loper7.date_time_picker.DateTimeConfig
import com.loper7.date_time_picker.dialog.CardDatePickerDialog
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.City
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentBirthDataDialogBinding
import com.one.astrology.ui.activity.MapActivity
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.adapter.CitySuggestionsAdapter
import com.one.astrology.ui.fragment.viewmodel.SharedViewModel
import com.one.astrology.util.EphemerisUtil.Companion.getOffset
import com.one.astrology.util.LocationUtil
import com.one.astrology.util.LocationUtil.Companion.searchCitySuggestions
import com.one.astrology.util.NotesUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.core.util.FormatUtils
import com.one.core.util.InputTools
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import io.objectbox.Box
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date


/**
 * 出生資料頁
 */
class BirthDataDialogFragment : DialogFragment {
    // 回調接口，用於通知新增或更新的出生資料
    private var onDismissListener: ((BirthData?) -> Unit)? = null

    // R.layout.fragment_birth_data
    private var mDate: Date? = null
    private lateinit var materialDatePicker: MaterialDatePicker<Long>
    private lateinit var materialTimePicker: MaterialTimePicker
    private lateinit var birthData: BirthData
    private lateinit var itemBoxSignRecord: Box<BirthData?>

    // 出生地座標
    private var birthplaceLatitude = -1.0
    private var birthplaceLongitude = -1.0

    // 居住地座標（新增）
    private var residenceLatitude = -1.0
    private var residenceLongitude = -1.0


    private var placeType = 0
    private lateinit var binding: FragmentBirthDataDialogBinding
    private lateinit var dialog: AlertDialog
    private val calendar = Calendar.getInstance()
    private val sharedViewModel by viewModels<SharedViewModel>()
    private lateinit var citySuggestionsAdapter: CitySuggestionsAdapter
    private lateinit var residenceCitySuggestionsAdapter: CitySuggestionsAdapter

    // Activity Result Launchers for map selection
    private lateinit var birthplaceMapLauncher: ActivityResultLauncher<Intent>
    private lateinit var residenceMapLauncher: ActivityResultLauncher<Intent>

    // Permission launchers for GPS
    private lateinit var birthplaceLocationPermissionLauncher: ActivityResultLauncher<String>
    private lateinit var residenceLocationPermissionLauncher: ActivityResultLauncher<String>

    constructor() {
        birthData = BirthData()
    }

    constructor(signRecord: BirthData) {
        birthData = signRecord
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化出生地地圖選擇 launcher
        birthplaceMapLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val latLng = MapActivity.getLatLngFromResult(result.data)
                    if (latLng != null) {
                        // 更新出生地座標
                        birthplaceLatitude = latLng.latitude
                        birthplaceLongitude = latLng.longitude

                        updateBirthplaceTimezone(latLng)

                        // 更新地址
                        updateLocationAddress(latLng, binding.progressBarMap)

                        LogUtil.d("出生地經緯度：${birthplaceLatitude}, $birthplaceLongitude")
                    }
                }
            }

        // 初始化居住地地圖選擇 launcher
        residenceMapLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val latLng = MapActivity.getLatLngFromResult(result.data)
                    if (latLng != null) {
                        // 更新居住地座標
                        residenceLatitude = latLng.latitude
                        residenceLongitude = latLng.longitude
                        updateResidenceCoordinatesDisplay()
                        updateResidenceTimezone(LatLng(residenceLatitude, residenceLongitude))

                        // 更新居住地地址
                        lifecycleScope.launch {
                            val address = LocationUtil.latLngToAddresses(latLng, listOf("city"))
                            binding.etResidenceCity.setText(address)
                        }

                        LogUtil.d("居住地經緯度：${residenceLatitude}, $residenceLongitude")
                    }
                }
            }

        // 初始化出生地 GPS 權限 launcher
        birthplaceLocationPermissionLauncher =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (isGranted) {
                    LocationUtil.getCurrentLocation(
                        requireActivity(),
                        PermissionsUtil.LOCATION_PERMISSION_REQUEST_CODE
                    ) { location ->
                        // 更新出生地
                        birthplaceLatitude = location.latitude
                        birthplaceLongitude = location.longitude

                        birthData.birthplaceLatitude = birthplaceLatitude
                        birthData.birthplaceLongitude = birthplaceLongitude

                        updateBirthplaceTimezone(LatLng(birthplaceLatitude, birthplaceLongitude))

                        // 更新地址
                        updateLocationAddress(
                            LatLng(birthplaceLatitude, birthplaceLongitude),
                            binding.progressBarGetCurrentLocation
                        )
                    }
                } else {
                    LogUtil.d("定位權限被拒絕")
                    Toast.makeText(
                        requireContext(),
                        "需要定位權限才能使用此功能喔！",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

        // 初始化居住地 GPS 權限 launcher
        residenceLocationPermissionLauncher =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (isGranted) {
                    LocationUtil.getCurrentLocation(
                        requireActivity(),
                        PermissionsUtil.LOCATION_PERMISSION_REQUEST_CODE
                    ) { location ->
                        // 更新居住地
                        residenceLatitude = location.latitude
                        residenceLongitude = location.longitude
                        updateResidenceCoordinatesDisplay()
                        updateResidenceTimezone(LatLng(residenceLatitude, residenceLongitude))

                        // 更新居住地地址
                        lifecycleScope.launch {
                            val address = LocationUtil.latLngToAddresses(
                                LatLng(
                                    residenceLatitude,
                                    residenceLongitude
                                ), listOf("city")
                            )
                            binding.etResidenceCity.setText(address)
                        }
                    }
                } else {
                    LogUtil.d("定位權限被拒絕")
                    Toast.makeText(
                        requireContext(),
                        "需要定位權限才能使用此功能喔！",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(requireActivity())
        val inflater = requireActivity().layoutInflater
        binding = FragmentBirthDataDialogBinding.inflate(inflater, null, false)
        builder.setView(binding.root)
        initView()
        initDatePicker()
        initTimePickerDialog()
        initLocationInput()
        initResidenceLocationInput() // 新增居住地輸入初始化
        dialog = builder.create()
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        return dialog
    }

    override fun onCancel(dialog: android.content.DialogInterface) {
        super.onCancel(dialog)
        // 對話框取消時調用回調函數，參數為null
        onDismissListener?.invoke(null)
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("出生資料頁", this.javaClass.simpleName)
    }

    fun initView() {
        itemBoxSignRecord = ObjectBox.get().boxFor(BirthData::class.java)
        if (birthData.name.isNotEmpty()) {
            initData()
        } else {
            birthData = BirthData()
        }

        binding.tvDate.setOnClickListener {
            val calendar = Calendar.getInstance()
            calendar.year = calendar[Calendar.YEAR] - 20

            if (mDate != null) {
                calendar.time = mDate!!
            }
            val list = intArrayOf(
                DateTimeConfig.YEAR,
                DateTimeConfig.MONTH,
                DateTimeConfig.DAY,
                DateTimeConfig.HOUR,
                DateTimeConfig.MIN
            ).toMutableList()

            CardDatePickerDialog.builder(requireContext())
                .setTitle(getString(R.string.select_a_date_and_time_of_birth))
                .setBackGroundModel(CardDatePickerDialog.CARD)
                .setDefaultTime(calendar.timeInMillis)
                .setDisplayType(list)
                .setLabelText(
                    getString(R.string.year), getString(R.string.month),
                    getString(R.string.day), getString(R.string.time), getString(R.string.minute)
                )
                .showDateLabel(true)
                .showFocusDateInfo(true)
                .showBackNow(true)
                .setOnChoose(getString(R.string.choose)) { millisecond ->
                    val timeString: String =
                        FormatUtils.dateToString(
                            Date(millisecond),
                            getString(R.string.yyyy_mm_dd_hh_mm)
                        )
                    binding.tvDate.setText(timeString)
                    mDate = Date(millisecond)
                }
                .setOnCancel(getString(R.string.cancel)) { }
                .build().show()
        }
        binding.tvTime.setOnClickListener { timePickerDialog() }
        binding.btSave.setOnClickListener {
            addData(it)
        }
        binding.chipGroup.setOnCheckedStateChangeListener { group, _ ->
            // Responds to child chip checked/unchecked
            birthData.tag = when (group.checkedChipId) {
                R.id.chip_1 -> binding.chip1.text.toString()
                R.id.chip_2 -> binding.chip2.text.toString()
                R.id.chip_3 -> binding.chip3.text.toString()
                R.id.chip_4 -> binding.chip4.text.toString()
                R.id.chip_5 -> binding.chip5.text.toString()
                R.id.chip_6 -> binding.chip6.text.toString()
                R.id.chip_7 -> binding.chip7.text.toString()
                else -> binding.chip1.text.toString()
            }
        }

        binding.btnGetCurrentLocation.setOnClickListener {
            birthplaceLocationPermissionLauncher.launch(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        binding.btnMap.setOnClickListener {
            // 使用 Activity Result API 啟動地圖選擇
            val intent = if (birthplaceLatitude != -1.0 && birthplaceLongitude != -1.0) {
                MapActivity.createIntent(requireContext(), birthplaceLatitude, birthplaceLongitude)
            } else {
                MapActivity.createIntent(requireContext())
            }
            birthplaceMapLauncher.launch(intent)
        }

        binding.btnSearch.setOnClickListener {
            if (binding.etCityName.text.toString().isEmpty()) {
                return@setOnClickListener
            }
            lifecycleScope.launch {
                val latLng = LocationUtil.addressToLatLng(
                    requireContext(),
                    binding.etCityName.text.toString()
                )
                birthplaceLatitude = latLng.latitude
                birthplaceLongitude = latLng.longitude
                val coordinatesText =
                    "${formatCoordinate(birthplaceLatitude)}, ${formatCoordinate(birthplaceLongitude)}"
                binding.tvBirthplaceCoordinates.text = coordinatesText

                // 使用 EphemerisUtil 獲取時區
                updateBirthplaceTimezone(latLng)
            }
        }
        binding.btnSearchResidence.setOnClickListener {
            if (binding.etResidenceCity.text.toString().isEmpty()) {
                return@setOnClickListener
            }
            lifecycleScope.launch {
                val latLng = LocationUtil.addressToLatLng(
                    requireContext(),
                    binding.etResidenceCity.text.toString()
                )
                residenceLatitude = latLng.latitude
                residenceLongitude = latLng.longitude
                val coordinatesText =
                    "${formatCoordinate(latLng.latitude)}, ${formatCoordinate(latLng.longitude)}"
                binding.tvResidenceCoordinates.text = coordinatesText

                // 使用 EphemerisUtil 獲取時區
                updateResidenceTimezone(latLng)
            }
        }
    }


    private fun addData(view: View) {
        if (TextUtils.isEmpty(binding.tvDate.text.toString()) || birthplaceLatitude == -1.0 || birthplaceLongitude == -1.0) {
            InputTools.HideKeyboard(view)
            Toast.makeText(
                requireActivity(),
                getString(R.string.please_fill_in_the_information),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        addRecord()
    }

    private fun initData() {
        binding.etName.setText(birthData.name)

        calendar.timeInMillis = birthData.birthday
        mDate = Date(calendar.timeInMillis)
        val timeString: String = FormatUtils.dateToString(mDate, "yyyy/MM/dd HH:mm")
        binding.tvDate.setText(timeString)

//        binding.tvTimezoneOffset.text =
//            "時區偏移：${if (birthData.timezoneOffset >= 0) "+" else ""}${"%.1f".format(birthData.timezoneOffset)} 小時"

        // 初始化出生地資料
        birthplaceLatitude = birthData.birthplaceLatitude
        birthplaceLongitude = birthData.birthplaceLongitude
        LogUtil.d("latLngBirth : $birthplaceLatitude , $birthplaceLongitude")

        if (birthplaceLatitude != -1.0 && birthplaceLongitude != -1.0) {
            // 更新出生地位置資訊卡片
            val coordinatesText =
                "${formatCoordinate(birthplaceLatitude)}, ${formatCoordinate(birthplaceLongitude)}"
            binding.tvBirthplaceCoordinates.text = coordinatesText
            updateBirthplaceTimezone(LatLng(birthplaceLatitude, birthplaceLongitude))
        }
        if (!birthData.birthplaceArea.isNullOrEmpty()) {
            binding.etCityName.setText(birthData.birthplaceArea)
        } else {
            lifecycleScope.launch {
                binding.progressBar.visibility = View.VISIBLE
                val cityName =
                    LocationUtil.latLngToAddresses(
                        LatLng(birthplaceLatitude, birthplaceLongitude),
                        listOf("city")
                    )
                birthData.birthplaceArea = cityName
                binding.etCityName.setText(cityName)
                binding.progressBar.visibility = View.GONE
            }
        }
        if (birthData.tag.isNotEmpty()) {
            when (birthData.tag) {
                binding.chip1.text.toString() -> binding.chip1.isChecked = true
                binding.chip2.text.toString() -> binding.chip2.isChecked = true
                binding.chip3.text.toString() -> binding.chip3.isChecked = true
                binding.chip4.text.toString() -> binding.chip4.isChecked = true
                binding.chip5.text.toString() -> binding.chip5.isChecked = true
                binding.chip6.text.toString() -> binding.chip6.isChecked = true
                binding.chip7.text.toString() -> binding.chip7.isChecked = true
                binding.chip8.text.toString() -> binding.chip8.isChecked = true
                else -> {}
            }
        }

        // 初始化備注欄位
        binding.etNotes.setText(birthData.notes)

        // 初始化居住地資料
        if (!birthData.residenceArea.isNullOrEmpty()) {
            binding.etResidenceCity.setText(birthData.residenceArea ?: "")
            residenceLatitude = birthData.residenceLatitude ?: -1.0
            residenceLongitude = birthData.residenceLongitude ?: -1.0

            if (residenceLatitude != -1.0 && residenceLongitude != -1.0) {
                updateResidenceCoordinatesDisplay()
                updateResidenceTimezone(LatLng(residenceLatitude, residenceLongitude))
            }
        }
    }

    private fun addRecord() {
        birthData.name = binding.etName.text.toString()
        birthData.createTime = System.currentTimeMillis()

        val strTime = binding.tvDate.text.toString()
        val date = FormatUtils.stringToDate(strTime, "yyyy/MM/dd HH:mm")
        calendar.timeInMillis = date.time
        birthData.birthday = calendar.timeInMillis
        birthData.birthdayString = FormatUtils.longToString(birthData.birthday, "yyyy/MM/dd HH:mm")
        birthData.birthplaceLatitude = birthplaceLatitude
        birthData.birthplaceLongitude = birthplaceLongitude

        // 新增居住地資料
        birthData.residenceArea = binding.etResidenceCity.text.toString().takeIf { it.isNotBlank() }
        birthData.residenceLatitude = residenceLatitude
        birthData.residenceLongitude = residenceLongitude

        birthData.notes = NotesUtil.formatNotes(binding.etNotes.text.toString())


        mDate = calendar.time

        val hasValidCoordinates =
            birthData.birthplaceLatitude != -1.0 && birthData.birthplaceLongitude != -1.0
        val userInputCity = binding.etCityName.text.toString()

        when {
            hasValidCoordinates && userInputCity.isNotEmpty() -> {
                birthData.birthplaceArea = userInputCity
                saveAndNavigate()
            }

            hasValidCoordinates -> {
                lifecycleScope.launch {
                    birthData.birthplaceArea = LocationUtil.latLngToArea(
                        LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
                    )
                    saveAndNavigate()
                }
            }

            else -> {
                saveAndNavigate()
            }
        }
    }

    private fun saveAndNavigate() {
        // 記錄備注操作日誌
        NotesUtil.logNotesOperation(
            if (birthData.id == 0L) "新增" else "修改",
            birthData.name,
            birthData.notes
        )

        itemBoxSignRecord.put(birthData)
        sharedViewModel.data.postValue("")
        toSignDetailActivity()

        // 調用回調函數
        onDismissListener?.invoke(birthData)

        dialog.dismiss()
    }


    private fun toSignDetailActivity() {
        val bundle = Bundle()
        bundle.putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
        startActivity(SignDetailActivity::class.java, bundle)
    }

    private fun startActivity(clz: Class<*>?, bundle: Bundle?) {
        val intent = Intent()
        intent.setClass(requireContext(), clz!!)
        if (bundle != null) {
            intent.putExtras(bundle)
        }
        startActivity(intent)
    }

    private fun initDatePicker() {
        val calendar = Calendar.getInstance()
//        calendar.year = calendar[Calendar.YEAR] - 25

        if (mDate != null) {
            calendar.time = mDate!!
        }

        materialDatePicker =
            MaterialDatePicker.Builder.datePicker()
                .setTitleText(getString(R.string.select_a_date_of_birth))
                .setSelection(calendar.timeInMillis)
//                .setInputMode(MaterialDatePicker.INPUT_MODE_TEXT)
                .build()

        materialDatePicker.addOnPositiveButtonClickListener {
            val timeString: String = FormatUtils.dateToString(
                Date(it),
                getString(R.string.yyyy_mm_dd)
            )
            binding.tvDate.setText(timeString)
        }
    }

    private fun initTimePickerDialog() {
        val calendar = Calendar.getInstance()
        if (mDate != null) {
            calendar.time = mDate!!
        }
        materialTimePicker =
            MaterialTimePicker.Builder()
                .setTimeFormat(TimeFormat.CLOCK_12H)
                .setHour(calendar.get(Calendar.HOUR_OF_DAY))
                .setMinute(calendar.get(Calendar.MINUTE))
                .setTitleText(getString(R.string.select_the_time_of_birth))
                .build()
        MaterialTimePicker.Builder().setInputMode(INPUT_MODE_KEYBOARD)

        materialTimePicker.addOnPositiveButtonClickListener {
            val newHour: Int = materialTimePicker.hour
            val newMinute: Int = materialTimePicker.minute
            val hour = String.format("%02d", newHour)
            val minute = String.format("%02d", newMinute)
            binding.tvTime.setText(getString(R.string.time_h_m, hour, minute))
        }
    }

    private fun timePickerDialog() {
        if (!materialTimePicker.isVisible) {
            materialTimePicker.show(requireActivity().supportFragmentManager, "")
        }
    }

    private fun initLocationInput() {
        // 初始化城市建議列表適配器
        citySuggestionsAdapter = CitySuggestionsAdapter { city ->
            updateBirthplace(city)
            binding.rvCitySuggestions.visibility = View.GONE
        }

        binding.rvCitySuggestions.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = citySuggestionsAdapter
        }

        // 設置出生地輸入
        setupBirthplaceInput()

        // 設置GPS按鈕
        binding.etCityName.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val query = binding.etCityName.text.toString()
                if (query.isNotEmpty()) {
                    lifecycleScope.launch {
                        try {
                            val suggestions = searchCitySuggestions(query)
                            citySuggestionsAdapter.submitList(suggestions)
                            binding.rvCitySuggestions.visibility =
                                if (suggestions.isNotEmpty()) View.VISIBLE else View.GONE
                        } catch (e: Exception) {
                            e.message?.let { LogUtil.e("搜尋城市建議失敗", it) }
                            Toast.makeText(context, "搜尋城市建議失敗，請重試", Toast.LENGTH_SHORT)
                                .show()
                        }
                    }
                }
                true
            } else {
                false
            }
        }

        // 設置GPS按鈕點擊事件
        (binding.etCityName.parent as? TextInputLayout)?.setEndIconOnClickListener {
//            getCurrentLocation()
        }
    }

    private fun setupBirthplaceInput() {
        // 設置地點搜尋
        binding.etCityName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                s?.toString()?.let { query ->
                    if (query.length >= 2) {
                        lifecycleScope.launch {
                            try {
                                val suggestions = searchCitySuggestions(query)
                                citySuggestionsAdapter.submitList(suggestions)
                                binding.rvCitySuggestions.visibility =
                                    if (suggestions.isNotEmpty()) View.VISIBLE else View.GONE
                            } catch (e: Exception) {
                                e.message?.let { LogUtil.e("搜尋城市建議失敗", it) }
                                context?.let { safeContext ->
                                    Toast.makeText(
                                        safeContext,
                                        "搜尋城市建議失敗，請重試",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                        }
                    } else {
                        binding.rvCitySuggestions.visibility = View.GONE
                    }
                }
            }
        })
    }

    private fun updateBirthplace(city: City) {
        birthplaceLatitude = city.latitude
        birthplaceLongitude = city.longitude
        birthData.birthplaceLatitude = birthplaceLatitude
        birthData.birthplaceLongitude = birthplaceLongitude

        // 更新地址顯示
        updateLocationAddress(
            LatLng(birthplaceLatitude, birthplaceLongitude),
            binding.progressBarGetCurrentLocation
        )
    }

    private fun updateLocationAddress(latLng: LatLng, view: View) {
        lifecycleScope.launch {
            view.visibility = View.VISIBLE
            try {
                val cityName = LocationUtil.latLngToAddresses(latLng, listOf("city"))
                birthData.birthplaceArea = cityName
                binding.etCityName.setText(cityName)
            } catch (e: Exception) {
                LogUtil.e("獲取地址失敗", e.message ?: "Unknown error")
                if (isAdded) {
                    Toast.makeText(
                        requireContext(),
                        "獲取地址失敗，請重試",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } finally {
                view.visibility = View.GONE
            }
        }
    }

    private fun formatAddress(address: String): String {
        return address.split(",")[1].trim()
    }

    @SuppressLint("DefaultLocale")
    private fun formatCoordinate(value: Double): String {
        return String.format("%.4f", value)
    }

    /**
     * 格式化時區偏移量
     * @param offset 時區偏移量（小時）
     * @return 格式化的時區字串，例如 "UTC+8" 或 "UTC-5"
     */
    @SuppressLint("DefaultLocale")
    private fun formatTimezoneOffset(offset: Double): String {
        val hours = offset.toInt()
        val minutes = ((offset - hours) * 60).toInt()

        return if (minutes == 0) {
            if (hours >= 0) "UTC+$hours" else "UTC$hours"
        } else {
            val sign = if (offset >= 0) "+" else ""
            String.format("UTC%s%d:%02d", sign, hours, kotlin.math.abs(minutes))
        }
    }


    /**
     * 設置對話框關閉後的回調
     * @param listener 回調函數，參數為新增或更新的出生資料，如果為空則表示取消
     */
    fun setOnDismissListener(listener: (BirthData?) -> Unit) {
        this.onDismissListener = listener
    }

    /**
     * 初始化居住地輸入
     */
    private fun initResidenceLocationInput() {
        // 設置居住地城市建議適配器
        residenceCitySuggestionsAdapter = CitySuggestionsAdapter { city ->
            binding.etResidenceCity.setText(city.name)
//            binding.rvResidenceCitySuggestions.visibility = View.GONE
            // 根據城市獲取座標
            searchResidenceLocationByCity(city.name)
        }

//        binding.rvResidenceCitySuggestions.layoutManager = LinearLayoutManager(requireContext())
//        binding.rvResidenceCitySuggestions.adapter = residenceCitySuggestionsAdapter

        // 居住地展開/收合功能
//        binding.llResidenceHeader.setOnClickListener {
//            val isVisible = binding.llResidenceContent.isVisible
//            if (isVisible) {
//                binding.llResidenceContent.visibility = View.GONE
//                binding.ivResidenceExpand.setImageResource(R.drawable.baseline_expand_more_24)
//            } else {
//                binding.llResidenceContent.visibility = View.VISIBLE
//                binding.ivResidenceExpand.setImageResource(R.drawable.baseline_expand_less_24)
//            }
//        }

        // 居住地城市輸入監聽
        binding.etResidenceCity.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val query = s.toString().trim()
                if (query.length >= 2) {
                    searchResidenceCitySuggestions(query)
                } else {
//                    binding.rvResidenceCitySuggestions.visibility = View.GONE
                }
            }
        })

        // 居住地地圖按鈕
        binding.btnResidenceMap.setOnClickListener {
            // 使用 Activity Result API 啟動地圖選擇
            val intent = if (residenceLatitude != -1.0 && residenceLongitude != -1.0) {
                MapActivity.createIntent(requireContext(), residenceLatitude, residenceLongitude)
            } else {
                MapActivity.createIntent(requireContext())
            }
            residenceMapLauncher.launch(intent)
        }

        // 居住地GPS按鈕
        binding.btnResidenceGPS.setOnClickListener {
            residenceLocationPermissionLauncher.launch(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        // 初始化居住地資料
        if (birthData.residenceArea?.isNotEmpty() == true) {
            binding.etResidenceCity.setText(birthData.residenceArea ?: "")
            residenceLatitude = birthData.residenceLatitude ?: -1.0
            residenceLongitude = birthData.residenceLongitude ?: -1.0
            updateResidenceCoordinatesDisplay()
        }
    }

    /**
     * 搜尋居住地城市建議
     */
    private fun searchResidenceCitySuggestions(query: String) {
        lifecycleScope.launch {
            try {
                val suggestions = searchCitySuggestions(query)
                residenceCitySuggestionsAdapter.submitList(suggestions)
                binding.rvResidenceCitySuggestions.visibility =
                    if (suggestions.isNotEmpty()) View.VISIBLE else View.GONE
            } catch (e: Exception) {
                LogUtil.e("搜尋居住地城市失敗", e.message ?: "Unknown error")
                binding.rvResidenceCitySuggestions.visibility = View.GONE
            }
        }
    }

    /**
     * 根據居住地城市名稱搜尋位置
     */
    private fun searchResidenceLocationByCity(cityName: String) {
        lifecycleScope.launch {
            try {
                val latLng = LocationUtil.addressToLatLng(requireContext(), cityName)
                if (latLng != null) {
                    residenceLatitude = latLng.latitude
                    residenceLongitude = latLng.longitude
                    updateResidenceCoordinatesDisplay()
                    updateResidenceTimezone(LatLng(residenceLatitude, residenceLongitude))
                }
            } catch (e: Exception) {
                LogUtil.e("搜尋居住地位置失敗", e.message ?: "Unknown error")
            }
        }
    }

    /**
     * 更新居住地座標顯示
     */
    private fun updateResidenceCoordinatesDisplay() {
        if (residenceLatitude != -1.0 && residenceLongitude != -1.0) {
            // 更新座標顯示（格式：37.7858, -122.4064）
            val coordinatesText =
                "${formatCoordinate(residenceLatitude)}, ${formatCoordinate(residenceLongitude)}"
            binding.tvResidenceCoordinates.text = coordinatesText
            binding.cvResidenceInfo.visibility = View.VISIBLE
        } else {
            binding.cvResidenceInfo.visibility = View.GONE
        }
    }

    /**
     * 更新居住地時區顯示
     */
    private fun updateResidenceTimezone(latLng: LatLng) {
        lifecycleScope.launch {
            try {
                // 使用當前時間來獲取時區偏移量
                val calendar = Calendar.getInstance()
                val year = calendar.get(Calendar.YEAR)
                val month = calendar.get(Calendar.MONTH) + 1
                val day = calendar.get(Calendar.DAY_OF_MONTH)
                val hour = calendar.get(Calendar.HOUR_OF_DAY)
                val minute = calendar.get(Calendar.MINUTE)

                val (offset, _) = getOffset(year, month, day, hour, minute, latLng)

                // 格式化時區顯示
                val timezoneText = formatTimezoneOffset(offset)
                binding.tvResidenceTimezone.text = timezoneText
            } catch (e: Exception) {
                LogUtil.e("獲取居住地時區失敗", e.message ?: "Unknown error")
                binding.tvResidenceTimezone.text = "--"
            }
        }
    }


    /**
     * 更新出生地座標和時區顯示
     */
    private fun updateBirthplaceTimezone(latLng: LatLng) {
        lifecycleScope.launch {
            try {
                // 使用當前時間來獲取時區偏移量
                val calendar = Calendar.getInstance()
                val year = calendar.get(Calendar.YEAR)
                val month = calendar.get(Calendar.MONTH) + 1
                val day = calendar.get(Calendar.DAY_OF_MONTH)
                val hour = calendar.get(Calendar.HOUR_OF_DAY)
                val minute = calendar.get(Calendar.MINUTE)

                val (offset, isDaylightSaving) = getOffset(year, month, day, hour, minute, latLng)

                // 格式化時區顯示
                val timezoneText = formatTimezoneOffset(offset)
                binding.tvBirthplaceTimezone.text = timezoneText

                // 更新座標顯示（格式：37.7858, -122.4064）
                val coordinatesText =
                    "${formatCoordinate(latLng.latitude)}, ${formatCoordinate(latLng.longitude)}"
                binding.tvBirthplaceCoordinates.text = coordinatesText

                binding.cvBirthplaceInf.visibility = View.VISIBLE

                birthData.birthplaceLatitude = latLng.latitude
                birthData.birthplaceLongitude = latLng.longitude
            } catch (e: Exception) {
                LogUtil.e("獲取出生地時區失敗", e.message ?: "Unknown error")
                binding.tvBirthplaceTimezone.text = "--"
            }
        }
    }
}