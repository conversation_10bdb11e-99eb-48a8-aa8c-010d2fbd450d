package com.one.astrology.ui.activity

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.jeremyliao.liveeventbus.LiveEventBus
import com.one.astrology.event.EventKey
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.compose.screens.SettingsScreen
import com.one.astrology.ui.viewmodel.SettingsViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 設定頁面 - Compose版本
 */
@AndroidEntryPoint
class SettingsComposeActivity : ComponentActivity() {

    private val viewModel: SettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化數據
        viewModel.loadChartList(this, SignDetailActivity.chart.nameEng)
        viewModel.loadPlanetList(this)
        viewModel.loadOtherSettings(this)

        setContent {
            AppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    SettingsScreen(
                        viewModel = viewModel,
                        onClose = {
                            // 發送事件通知星盤頁面重新加載數據
                            LiveEventBus.get<Boolean>(EventKey.RefreshChart).post(true)
                            finish()
                        },
                        onReset = {
                            viewModel.resetAllSettings(this)
                            // 發送事件通知星盤頁面重新加載數據
                            LiveEventBus.get<Boolean>(EventKey.RefreshChart).post(true)
                        }
                    )
                }
            }
        }
    }
}
