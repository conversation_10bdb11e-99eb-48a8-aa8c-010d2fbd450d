package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.Category
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.AppTheme
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBarDouble
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil

class MarksAnalyzeFragment : Fragment() {

    private val chatViewModel by viewModels<ChatViewModel>()
    private var chartName = ""
    private var chart: Chart = Chart.Marks

    override fun onResume() {
        super.onResume()
        requireActivity().title = getString(R.string.analysis)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (arguments != null) {
            val category = arguments?.getSerializable("category") as? Category
            if (category != null) {
                chartName = getString(category.chart.type)
            }
        }
        return ComposeView(requireContext()).apply {
            setContent {
                AppTheme {
                    MainAnalyzeScreen(chatViewModel)
                }
            }
        }
    }

    @Composable
    fun MainAnalyzeScreen(viewModel: ChatViewModel) {
        val context = LocalContext.current
        val matchEvent by viewModel.matchEvent.observeAsState(MatchEvent())
        val isLoading by viewModel.isLoading.observeAsState(false)
        val data by viewModel.data.observeAsState(ReadingData("", "", ""))
        val errorMessage by viewModel.error.observeAsState("")

        LaunchedEffect(errorMessage) {
            if (matchEvent.birthDataA.name.isEmpty()) {
                LogUtil.d("errorMessage 更新: $errorMessage")
                viewModel.getTwoBirthData()
                if (matchEvent.birthDataA.name.isEmpty() || matchEvent.birthDataB.name.isEmpty()) {
                    return@LaunchedEffect
                }

                when (chart) {
                    Chart.Marks -> {
                        viewModel.getMarks(
                            context,
                            matchEvent.birthDataA,
                            matchEvent.birthDataB,
                        )
                    }

                    else -> {
                        LogUtil.e(chart.name)
                    }
                }
                viewModel.init()
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {
            // 頂部標題欄
            TopBarDouble(
                matchEvent.birthDataA.name,
                matchEvent.birthDataA.generateBirthdayString(),
                matchEvent.birthDataB.name,
                matchEvent.birthDataB.generateBirthdayString(),
                onBirthDataSelected = { birthDataList ->
                    if (birthDataList.size >= 2) {
                        val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                        val birthDataListSelected =
                            birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
                        birthDataListSelected.forEach {
                            it.isSelected = false
                            birthDataBox.put(it)
                        }
                        matchEvent.birthDataA = birthDataList[0]
                        matchEvent.birthDataA.isSelected = true
                        birthDataBox.put(matchEvent.birthDataA)
                        matchEvent.birthDataB = birthDataList[1]
                        matchEvent.birthDataB.isSelected = true
                        birthDataBox.put(matchEvent.birthDataB)

                        when (chart) {
                            Chart.Marks -> {
                                viewModel.getMarks(
                                    context,
                                    matchEvent.birthDataA,
                                    matchEvent.birthDataB,
                                )
                            }

                            else -> {
                                LogUtil.e(chart.name)
                            }
                        }
                        viewModel.init()
                    }
                },
                onSwap = {
                    // 交換 horoscopeA 和 horoscopeB
                    val tempHoroscope = matchEvent.horoscopeA
                    matchEvent.horoscopeA = matchEvent.horoscopeB
                    matchEvent.horoscopeB = tempHoroscope

                    // 交換 birthDataA 和 birthDataB
                    val tempBirthData = matchEvent.birthDataA
                    matchEvent.birthDataA = matchEvent.birthDataB
                    matchEvent.birthDataB = tempBirthData

                    // 更新 ViewModel
                    viewModel.matchEvent.value = matchEvent

                    // 確保同步資料
                    when (chart) {
                        Chart.Marks -> {
                            viewModel.getMarks(
                                context,
                                matchEvent.birthDataA,
                                matchEvent.birthDataB,
                            )
                        }

                        else -> {
                            LogUtil.e(chart.name)
                        }
                    }
                    viewModel.init()
                }
            )

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel.init()
                    } else {
                        val categories = listOf(
                            "${chartName}整體命運" to "${chartName}：全面解析雙方命盤的能量互動，揭示關係的核心動態與未來發展方向。\n",
                            "${chartName}財富互動" to "${chartName}：探討雙方的金錢觀、財務管理方式及物質需求，分析財運互動與共富潛能。\n",
                            "${chartName}感情與婚姻" to "${chartName}：深入剖析雙方的戀愛風格、情感連結與婚姻穩定度，揭示親密關係的發展趨勢。\n",
                            "${chartName}家庭與親緣" to "${chartName}：探索雙方的家庭背景、親子互動與家族業力，理解家庭對關係的深遠影響。\n",
                            "${chartName}事業與志向" to "${chartName}：解析雙方的職業目標、事業發展方向與合作契合度，發掘事業共創的可能性。\n",
                            "${chartName}人際與社交" to "${chartName}：解讀雙方的社交風格、合作模式與人際影響，分析關係在社交網絡中的定位。\n",
                            "${chartName}健康與體質" to "${chartName}：比較雙方的身心健康特質、生活方式與壓力應對機制，找出相互影響的關鍵因素。\n",
                            "${chartName}挑戰與契機" to "${chartName}：揭示雙方關係中的潛在挑戰與成長機會，提供化解衝突與深化連結的指引。\n"
                        )

                        CategoryList(
                            context,
                            categories,
                            chatViewModel,
                            matchEvent,
                            chart = chart
                        )
                    }
                }
            }
        }
    }
}