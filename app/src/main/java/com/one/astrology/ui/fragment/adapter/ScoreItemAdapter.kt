package com.one.astrology.ui.fragment.adapter


import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.R
import com.one.astrology.data.db.AspectData
import com.one.astrology.databinding.ItemScoreBinding

class ScoreItemAdapter :
    BaseQuickAdapter<AspectData, ScoreItemAdapter.BaseViewHolder>() {

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemScoreBinding = ItemScoreBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)


    override fun onBindViewHolder(
        holder: BaseViewHolder,
        position: Int,
        item: AspectData?
    ) {
        if (item != null) {
            holder.binding.tvPlanetNameA.text = item.planetA
            holder.binding.tvAspectType.text = item.type
            holder.binding.tvPlanetNameB.text = item.planetB
            holder.binding.tvScore.text = context.getString(R.string.score_d, item.score)


            when (item.type) {
                "合" -> holder.binding.tvAspectType.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.blueDark
                    )
                )

                "六合" -> holder.binding.tvAspectType.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.greenLight
                    )
                )

                "刑" -> holder.binding.tvAspectType.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.red_light
                    )
                )

                "拱" -> holder.binding.tvAspectType.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.green
                    )
                )

                "衝" -> holder.binding.tvAspectType.setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.navy_blue
                    )
                )

                "落入" -> {
                    holder.binding.tvAspectType.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.white
                        )
                    )
                    holder.binding.tvPlanetNameA.text = item.planetA + " (" + item.nameA + ")"
                    holder.binding.tvPlanetNameB.text = item.planetB + " (" + item.nameB + ")"
                }
            }

            holder.itemView.tag = item
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }
}