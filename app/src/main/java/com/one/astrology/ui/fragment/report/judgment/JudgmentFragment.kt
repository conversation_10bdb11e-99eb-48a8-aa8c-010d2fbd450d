package com.one.astrology.ui.fragment.report.judgment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayout
import com.one.astrology.R
import com.one.astrology.databinding.FragmentJudgmentBinding
import com.one.astrology.ui.fragment.navigation.FateFragment
import com.one.astrology.ui.view.TabLayoutMediators
import com.one.core.util.LogUtil


/**
 * 論斷
 */
class JudgmentFragment : Fragment() {
    private lateinit var binding: FragmentJudgmentBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentJudgmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initTab()
    }

    private fun initTab() {
        val adapter = RecyclerviewAdapter(this)
        binding.viewPager2.adapter = adapter
        binding.viewPager2.isUserInputEnabled = false

        TabLayoutMediators(
            binding.tabLayout, binding.viewPager2
        ) { tab, position ->
            when (position) {
                0 -> tab.text = getString(R.string.planet_sign)
                1 -> tab.text = getString(R.string.planet_house)
                2 -> tab.text = getString(R.string.planet_aspect)
                3 -> tab.text = getString(R.string.flying_star)
                4 -> tab.text = getString(R.string.career)
                5 -> tab.text = getString(R.string.natal_pattern)
//                6 -> tab.text = getString(R.string.firdaria_list)
//                7 -> tab.text = getString(R.string.feature)
//                8 -> tab.text = getString(R.string.interpretation)
            }
        }.attach()

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    LogUtil.d(tab.text.toString())
                }
            }
        })
        binding.viewPager2.offscreenPageLimit = 1 // 預加載
    }


    class RecyclerviewAdapter(fragment: Fragment?) : FragmentStateAdapter(fragment!!) {
        private var fragments: ArrayList<Fragment> = arrayListOf(
            PlanetSignFragment(),
            PlanetHouseFragment(),
            PlanetAspectFragment(),
            FlyFragment(),
            CareerFragment(),
            FateFragment(),
        )

        override fun getItemCount(): Int {
            return fragments.size
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }
    }
}