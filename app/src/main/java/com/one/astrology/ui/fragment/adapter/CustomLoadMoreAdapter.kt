package com.one.astrology.ui.fragment.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.loadState.LoadState
import com.chad.library.adapter.base.loadState.trailing.TrailingLoadStateAdapter
import com.one.astrology.databinding.ItemLoadMoreBinding

class CustomLoadMoreAdapter : TrailingLoadStateAdapter<CustomLoadMoreAdapter.CustomVH>() {

    override fun onCreateViewHolder(parent: ViewGroup, loadState: LoadState): CustomVH {
        val viewBinding = ItemLoadMoreBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return CustomVH(viewBinding)
    }

    override fun onBindViewHolder(holder: CustomVH, loadState: LoadState) {
        // 根据加载状态，来自定义你的 UI 界面
        when (loadState) {
            is LoadState.NotLoading -> {
                if (loadState.endOfPaginationReached) {
                    holder.viewBinding.loadingProgress.visibility = View.GONE
                } else {
                    holder.viewBinding.loadingProgress.visibility = View.GONE
                }
            }
            is LoadState.Loading -> {
                holder.viewBinding.loadingProgress.visibility = View.VISIBLE
            }
            is LoadState.Error -> {
                holder.viewBinding.loadingProgress.visibility = View.GONE
            }
            is LoadState.None -> {
                holder.viewBinding.loadingProgress.visibility = View.GONE
            }
        }
    }

    class CustomVH(val viewBinding: ItemLoadMoreBinding) : RecyclerView.ViewHolder(viewBinding.root)
}