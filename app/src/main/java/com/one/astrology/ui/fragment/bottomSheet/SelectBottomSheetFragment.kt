package com.one.astrology.ui.fragment.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.databinding.FragmentSelectBottomSheetBinding
import com.one.astrology.ui.fragment.adapter.MultiAdapter
import com.one.core.util.LogUtil


/**
 * SelectBottomSheetFragment
 */
class SelectBottomSheetFragment : BottomSheetDialogFragment(R.layout.fragment_select_bottom_sheet) {

    private var multiAdapter: MultiAdapter = MultiAdapter()
    private lateinit var binding: FragmentSelectBottomSheetBinding

    interface MatchCallBack {
        fun onClick(list: List<BirthData?>)
    }

    companion object {
        private var onClickListener: MatchCallBack? = null
        private var birthDataA: BirthData? = null
        private var birthDataB: BirthData? = null
        fun newInstance(
            onClickListener: MatchCallBack,
            birthDataA: BirthData? = null,
            birthDataB: BirthData? = null
        ) =
            SelectBottomSheetFragment().apply {
                SelectBottomSheetFragment.onClickListener = onClickListener
                SelectBottomSheetFragment.birthDataA = birthDataA
                SelectBottomSheetFragment.birthDataB = birthDataB
            }

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSelectBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("SelectBottomSheetFragment", this.javaClass.simpleName)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initRecycleView()

        binding.tvOK.setOnClickListener {
            val list = multiAdapter.getSelected()
            if (list.size == 2) {
                onClickListener?.onClick(list)
            } else {
                return@setOnClickListener
            }
            dismiss()
        }

        val designBottomSheet: FrameLayout = dialog?.findViewById(R.id.design_bottom_sheet)!!
        designBottomSheet.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
        val behavior = BottomSheetBehavior.from(designBottomSheet)
        behavior.peekHeight = 1200
        behavior.state = BottomSheetBehavior.STATE_COLLAPSED
        behavior.saveFlags = BottomSheetBehavior.SAVE_FIT_TO_CONTENTS

        behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {

            override fun onStateChanged(bottomSheet: View, newState: Int) {
                when (newState) {
                    BottomSheetBehavior.STATE_EXPANDED -> {}
                    BottomSheetBehavior.STATE_COLLAPSED -> {}
                    BottomSheetBehavior.STATE_DRAGGING -> {}
                    BottomSheetBehavior.STATE_SETTLING -> {}
                    BottomSheetBehavior.STATE_HIDDEN -> {}
                    BottomSheetBehavior.STATE_HALF_EXPANDED -> {

                    }
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {

            }

        })

    }

    private fun initRecycleView() {
        val layoutManager = LinearLayoutManager(requireActivity())
        layoutManager.orientation = RecyclerView.VERTICAL
        binding.recyclerView.layoutManager = layoutManager
        val signRecordBox = ObjectBox.get().boxFor(BirthData::class.java)
        val signRecordList = signRecordBox.query().orderDesc(BirthData_.id).build().find()
        if (birthDataA != null) {
            val userA = signRecordList.find { it.id == birthDataA!!.id }
            if (userA != null) {
                signRecordList.find { it.id == birthDataA!!.id }!!.isChecked = true
            }
        }
        if (birthDataB != null) {
            val userB = signRecordList.find { it.id == birthDataB!!.id }
            if (userB != null) {
                signRecordList.find { it.id == birthDataB!!.id }!!.isChecked = true
            }
        }
        multiAdapter.isEmptyViewEnable = true
        multiAdapter.setEmptyViewLayout(requireContext(), R.layout.layout_empty)
        multiAdapter.submitList(signRecordList)
        binding.recyclerView.adapter = multiAdapter
    }
}