package com.one.astrology.ui.fragment.analysis

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.data.Horoscope
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.event.MatchEvent
import com.one.astrology.ui.fragment.analysis.calculator.SolarArcEvent
import com.one.astrology.ui.fragment.analysis.screen.ErrorMessage
import com.one.astrology.ui.fragment.analysis.screen.LoadingContent
import com.one.astrology.ui.fragment.analysis.screen.components.CategoryList
import com.one.astrology.ui.fragment.analysis.screen.components.TopBarSingle
import com.one.astrology.ui.fragment.analysis.screen.copyToClipboard
import com.one.astrology.viewmodel.ChatViewModel
import com.one.core.util.LogUtil
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId

class SolarArcFragment : Fragment() {
    private val chatViewModel by viewModels<ChatViewModel>()
    private val chartName = "太陽弧推運盤"

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                MainAnalyzeScreen(chatViewModel)
            }
        }
    }

    private fun calculateAge(birthTimestamp: Long): Int {
        // 將 timestamp (毫秒) 轉換為 Instant
        val birthDate = Instant.ofEpochMilli(birthTimestamp)
            .atZone(ZoneId.systemDefault()) // 使用系統時區
            .toLocalDate()

        // 取得當前日期
        val currentDate = LocalDate.now()

        // 計算出生日期與當前日期的年齡差
        val period = Period.between(birthDate, currentDate)

        // 回傳年齡
        return period.years
    }

    @Composable
    fun MainAnalyzeScreen(chatViewModel1: ChatViewModel) {

        val context = LocalContext.current
        var horoscope by remember { mutableStateOf(Horoscope()) } // 讓 UI 可以即時更新
        var matchEvent by remember { mutableStateOf(MatchEvent()) }
        var birthData by remember { mutableStateOf(BirthData()) }

        val isLoading by chatViewModel1.isLoading.observeAsState(false)
        val data by chatViewModel1.data.observeAsState(ReadingData("", "",""))
        val errorMessage by chatViewModel1.error.observeAsState("")
        var events by remember { mutableStateOf<List<SolarArcEvent>>(emptyList()) }
        var dataNow by remember { mutableLongStateOf(System.currentTimeMillis()) }

        LaunchedEffect(errorMessage) {
            LogUtil.d("errorMessage 更新: $errorMessage")
            birthData = chatViewModel1.getBirthData()
            if (birthData.name.isEmpty()) {
                return@LaunchedEffect
            }
            matchEvent = chatViewModel1.getSolarArc(context, birthData, dataNow)
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.white))
        ) {

            TopBarSingle(birthData, { birthDataList ->
                val selectedBirthData = birthDataList[0]
                if (selectedBirthData.name != birthData.name) {
                    val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
                    birthData = chatViewModel1.getBirthData()
                    if (birthData.name.isNotEmpty()) {
                        birthData.isSelected = false
                        birthDataBox.put(birthData)
                    }
                    birthData = selectedBirthData
                    birthData.isSelected = true
                    birthDataBox.put(birthData)
                    matchEvent = chatViewModel1.getSolarArc(context, birthData, dataNow)
                }
            }, {
                horoscope = chatViewModel1.calculateHoroscope(context, Chart.SolarArc, birthData)
                val calculatedEvents: ArrayList<SolarArcEvent> = ArrayList()
                val event = chatViewModel1.solarArcCalculator(horoscope.planetList)
                calculatedEvents.addAll(event)
                events = calculatedEvents
                chatViewModel1.init()
            }, { date ->
                dataNow = date.time
            })

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(0.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isLoading) {
                    LoadingContent()
                } else {
                    if (errorMessage.isNotEmpty()) {
                        ErrorMessage(errorMessage)
                    } else if (data.title.isNotEmpty()) {
                        val bundle = Bundle().apply {
                            putParcelable("reading_data", data)
                        }
                        findNavController().navigate(R.id.markdownFragment, bundle)
                        chatViewModel1.init()
                    } else if (events.isNotEmpty()) {
                        HoroscopeEventsScreen(events)
                    } else {
                        val categories = listOf(
                            "${chartName}命運走向" to "${chartName}：解析太陽弧推運中，主要行星與命盤點的運行變化，揭示命運的走向與核心事件 (繁體中文):\n",
                            "${chartName}財運與資源轉變" to "${chartName}：探索太陽弧推運對財運、金錢流動與資源獲取的影響 (繁體中文):\n",
                            "${chartName}感情與人際關係" to "${chartName}：剖析太陽弧推運下，感情狀況、人際互動與親密關係的變化 (繁體中文):\n",
                            "${chartName}家庭與親緣變動" to "${chartName}：解析太陽弧推運對家庭關係、家族動態及親緣影響的揭示 (繁體中文):\n",
                            "${chartName}事業與志向的進展" to "${chartName}：分析太陽弧推運對事業發展、職場機會與個人目標實現的促進作用 (繁體中文):\n",
                            "${chartName}健康與身心變化" to "${chartName}：探索太陽弧推運在健康與身心變化方面的潛在影響 (繁體中文):\n",
                            "${chartName}挑戰與突破的契機" to "${chartName}：揭示太陽弧推運中，當前的挑戰、成長契機與突破點 (繁體中文):\n",
                            "${chartName}自我成長與內在變遷" to "${chartName}：探索太陽弧推運中，個人內心世界的成長與變化過程 (繁體中文):\n"
                        )
                        CategoryList(
                            context,
                            categories,
                            chatViewModel,
                            matchEvent,
                            chart = Chart.SolarArc
                        )
                    }
                }
            }
        }
    }

    @Composable
    fun HoroscopeEventsScreen(events: List<SolarArcEvent>) {
        val context = LocalContext.current

        LazyColumn(
            contentPadding = PaddingValues(vertical = 12.dp, horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(events) { event ->
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA)), // 柔和背景色
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.elevatedCardElevation(defaultElevation = 8.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            copyToClipboard(
                                context,
                                "${event.age}歲 太陽弧${event.planetArc}  ${event.aspect}  本命${event.planetNatal}"
                            )
                            Toast.makeText(context, "已複製到剪貼簿", Toast.LENGTH_SHORT).show()
                        }
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = "${event.age}歲",
                                style = MaterialTheme.typography.headlineSmall.copy(fontWeight = FontWeight.Bold),
                                color = Color(0xFF212121), // 深灰色
                                modifier = Modifier.weight(1f) // 讓標題靠左
                            )
                            IconButton(onClick = {
                                copyToClipboard(
                                    context,
                                    "${event.age}歲 ${event.planetArc} ${event.aspect} ${event.planetNatal}\n${event.importantEvents}"
                                )
                                Toast.makeText(context, "已複製到剪貼簿", Toast.LENGTH_SHORT).show()
                            }) {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = "複製",
                                    tint = Color(0xFFB0BEC5) // 淡灰色
                                )
                            }
                        }
                        Divider(color = Color(0xFFB0BEC5)) // 淡灰色分隔線
                        // 事件標題（藍色超連結風格）
                        Text(
                            text = "太陽弧${event.planetArc}  ${event.aspect}  本命${event.planetNatal}", // 這裡假設 event.planetArc 是標題，例如「事業運勢上升」
                            style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Medium),
                            color = Color(0xFF1E88E5), // 藍色
                            modifier = Modifier.padding(vertical = 4.dp)
                        )

                        // 事件描述
                        Text(
                            text = event.importantEvents,
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color(0xFF616161) // 次要文字顏色
                        )
                    }
                }
            }
        }
    }

}
