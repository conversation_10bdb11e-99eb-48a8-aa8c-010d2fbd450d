package com.one.astrology.ui.fragment.analysis

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.fragment.app.Fragment
import com.one.astrology.R
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.ReadingData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import io.noties.markwon.Markwon
import kotlinx.coroutines.launch

class MarkdownFragment : Fragment() {

    private var readingData = ReadingData("", "", "")

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 取得傳遞過來的參數，避免 NullPointerException
        readingData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable("reading_data", ReadingData::class.java)
        } else {
            arguments?.getParcelable("reading_data")
        } ?: ReadingData("", "", "") // 若為 null，則提供預設值
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                DisplayMarkdownData(
                    readingData
                )
            }
        }
    }

    @Composable
    fun DisplayMarkdownData(readingData: ReadingData) {
        val context = LocalContext.current
        val clipboardManager = LocalClipboardManager.current
        var isDialogOpen by remember { mutableStateOf(false) }

        Column(modifier = Modifier.fillMaxSize()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(colorResource(id = R.color.colorPrimary))
                    .padding(vertical = 12.dp, horizontal = 0.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = readingData.title,
                    fontSize = 20.sp,
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 10.dp),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    ),
                )
                IconButton(
                    onClick = {
                        isDialogOpen = true
                    },
                    modifier = Modifier
                ) {
                    Icon(
                        painterResource(id = R.drawable.ic_detail),
                        contentDescription = "星盤資訊",
                        tint = Color.White
                    )
                }
                IconButton(
                    onClick = {
                        clipboardManager.setText(AnnotatedString(readingData.content))
                        Toast.makeText(context, "已複製到剪貼簿", Toast.LENGTH_SHORT).show()
                    },
                    modifier = Modifier
                ) {
                    Icon(
                        painterResource(id = R.drawable.ic_copy),
                        contentDescription = "複製",
                        tint = Color.White
                    )
                }
                IconButton(
                    onClick = {
                        when (readingData.chart) {
                            Chart.Celestial,
                            Chart.Natal,
                            Chart.SecondaryProgression,
                            Chart.SecondaryProgressionSynastry,
                            Chart.TertiaryProgression,
                            Chart.TertiaryProgressionSynastry,
                            Chart.SolarReturn,
                            Chart.LunarReturn,
                            Chart.Firdaria -> {
                                toBirth(
                                    readingData.chart,
                                    readingData.birthDataA,
                                )
                            }
                            Chart.SolarArc,
                            Chart.Transit,
                            Chart.Synastry,
                            Chart.SynastrySecondaryProgression,
                            Chart.SynastryTertiaryProgression,
                            Chart.Composite,
                            Chart.CompositeSecondaryProgression,
                            Chart.CompositeTertiaryProgression,
                            Chart.Davison,
                            Chart.DavisonSecondaryProgression,
                            Chart.DavisonTertiaryProgression,
                            Chart.Marks,
                            Chart.MarksSecondaryProgression,
                            Chart.MarksTertiaryProgression -> {
                                toBirth(
                                    readingData.chart,
                                    readingData.birthDataA,
                                    readingData.birthDataB
                                )
                            }

                            null -> {
                                toBirth(
                                    readingData.chart,
                                    readingData.birthDataA,
                                )
                            }
                        }

                    },
                    modifier = Modifier
                ) {
                    Icon(
                        painterResource(id = R.drawable.ic_baseline_stars_24),
                        contentDescription = "星盤",
                        tint = Color.White
                    )
                }
            }
            CustomAlertDialog(
                isOpen = isDialogOpen,
                onDismiss = { isDialogOpen = false },
                title = "星盤資訊",
                message = readingData.chartInfo
            )
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 10.dp, vertical = 10.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                item {
                    MarkdownText(markdown = readingData.content, modifier = Modifier.padding(0.dp))
                }
            }
        }
    }

    private fun toBirth(chart: Chart?, birthDataA: BirthData?, birthDataB: BirthData?) {
        val bundle = Bundle()
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthDataA)
        bundle.putParcelable(KeyDefine.UserBirthDataB, birthDataB)
        if (chart != null) {
            bundle.putString(KeyDefine.Chart, chart.toStorageValue())
        }
        val intent = Intent()
        intent.setClass(requireContext(), SignDetailActivity::class.java)
        intent.putExtras(bundle)
        startActivity(intent)
    }


    private fun toBirth(chart: Chart?, birthData: BirthData?) {
        val bundle = Bundle()
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
        if (chart != null) {
            bundle.putString(KeyDefine.Chart, chart.toStorageValue())
        }
        bundle.putBoolean(KeyDefine.IsSystem, true)
        val intent = Intent()
        intent.setClass(requireContext(), SignDetailActivity::class.java)
        intent.putExtras(bundle)
        startActivity(intent)
    }
}

@Composable
fun MarkdownText(
    markdown: String,
    modifier: Modifier = Modifier,
    textColor: Color = Color.Black,
    textSize: Float = 16f
) {
    val context = LocalContext.current
    val markwon = remember { Markwon.create(context) }

    AndroidView(
        factory = {
            TextView(context).apply {
                movementMethod = LinkMovementMethod.getInstance()
                setTextColor(textColor.toArgb())
                setTextSize(TypedValue.COMPLEX_UNIT_SP, textSize)
            }
        },
        update = { textView ->
            textView.setTextColor(textColor.toArgb())
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, textSize)
            markwon.setMarkdown(textView, markdown)
        },
        modifier = modifier
    )
}

@Composable
fun CustomAlertDialog(
    isOpen: Boolean,
    onDismiss: () -> Unit,
    title: String,
    message: String
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()
    if (isOpen) {
        AlertDialog(
            onDismissRequest = onDismiss,
            shape = RoundedCornerShape(12.dp), // 圓角對話框
            title = {
                Text(
                    text = title,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
            },
            text = {
                Box(
                    modifier = Modifier
                        .heightIn(max = 500.dp)
                        .padding(horizontal = 8.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .verticalScroll(rememberScrollState())
                            .padding(0.dp)
                    ) {
                        Text(
                            text = message,
                            fontSize = 16.sp,
                            color = Color.DarkGray,
                            lineHeight = 22.sp
                        )
                    }
                }
            },
            confirmButton = {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    Button(
                        onClick = {
                            clipboardManager.setText(AnnotatedString(message))
                            scope.launch {
                                snackbarHostState.showSnackbar("已複製到剪貼簿")
                            }
                            Toast.makeText(context, "已複製到剪貼簿", Toast.LENGTH_SHORT).show()
                        },
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(id = R.color.colorPrimary))
                    ) {
                        Text("複製", color = Color.White)
                    }
                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(id = R.color.colorPrimary))
                    ) {
                        Text("確定", color = Color.White)
                    }
                }
            }
        )
    }
}



