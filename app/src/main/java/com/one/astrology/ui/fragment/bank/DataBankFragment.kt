package com.one.astrology.ui.fragment.bank

import android.animation.AnimatorInflater
import android.animation.AnimatorSet
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.Lifecycle
import com.google.android.gms.maps.model.LatLng
import com.one.astrology.ObjectBox
import com.one.astrology.R
import com.one.astrology.callback.TranslateCallback
import com.one.astrology.constant.KeyDefine
import com.one.astrology.data.Horoscope
import com.one.astrology.data.db.AstroData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.databinding.FragmentDataBankBinding
import com.one.astrology.db.OtherDBHelper
import com.one.astrology.ui.activity.SignDetailActivity
import com.one.astrology.ui.fragment.AnalyzeFragment.Companion.ARG_DATA_BANK
import com.one.astrology.ui.fragment.BaseFragment
import com.one.astrology.ui.view.DrawView
import com.one.astrology.ui.view.DrawViewPro
import com.one.astrology.util.EncryptedSPUtil
import com.one.astrology.util.EphemerisUtil
import com.one.astrology.util.TranslateUtil
import com.one.astrology.util.astro.ChartUtils.toStorageValue
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import dagger.hilt.android.AndroidEntryPoint


/**
 * 星盤資料頁
 */
@AndroidEntryPoint
class DataBankFragment : BaseFragment(R.layout.fragment_data_bank) {

    private lateinit var item: AstroData
    private lateinit var binding: FragmentDataBankBinding
    private var isAnimator = false
    private lateinit var drawView: DrawView
    private lateinit var drawViewPro: DrawViewPro
    private var isSymbolMode = false

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("星盤資料頁", this.javaClass.simpleName)
    }

    override fun onPause() {
        super.onPause()
        drawView.onPause()
        drawViewPro.onPause()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                item = it.getParcelable(ARG_DATA_BANK, AstroData::class.java) ?: AstroData()
            } else {
                item = it.getParcelable(ARG_DATA_BANK) ?: AstroData()
            }

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (rootView == null) {
            binding = FragmentDataBankBinding.inflate(inflater, container, false)
            rootView = binding.root
        } else {
            (rootView?.parent as? ViewGroup)?.removeView(rootView)
        }
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_data_bank, menu)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_random -> {
                        val data = OtherDBHelper.queryRandomDetailData(requireContext())
                        initData(data)
                        true
                    }

                    R.id.action_add -> {
                        addData()
                        true
                    }

                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)


        isAnimator = EncryptedSPUtil.getChartAnimation(requireContext())
        isSymbolMode = EncryptedSPUtil.getSymbolMode(requireContext())
        drawView = DrawView(requireContext(), null)
        drawViewPro = DrawViewPro(requireContext(), null)
        initView()
    }

    private fun addData() {
        val itemBoxSignRecord = ObjectBox.get().boxFor(BirthData::class.java)
        val data = OtherDBHelper.queryDetailData(requireContext(), item.id.toString())
        val birthData = BirthData()
        if (data != null) {
            birthData.name = data.name.toString()
            birthData.birthday = data.timeInMillis!!
            birthData.birthplaceLatitude = data.latitude!!.toDouble()
            birthData.birthplaceLongitude = data.longitude!!.toDouble()
            itemBoxSignRecord.put(birthData)
            Toast.makeText(requireContext(), "加入成功!", Toast.LENGTH_SHORT).show()
        }
    }

    private fun initView() {
        binding.lltTop.setOnClickListener {
            drawView.onPause()
            drawViewPro.onPause()
        }
        binding.nestedScrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            drawView.onPause()
            drawViewPro.onPause()
        })

        val data = OtherDBHelper.queryDetailData(requireContext(), item.id.toString())
        initData(data)

        // 初始化時讀取保存的模式
        isSymbolMode = EncryptedSPUtil.getSymbolMode(requireContext())


        // 根據保存的模式添加對應的視圖
        binding.drawContainer.removeAllViews()
        if (isSymbolMode) {
            binding.drawContainer.addView(drawViewPro)
        } else {
            binding.drawContainer.addView(drawView)
        }
        val isShowRuler = EncryptedSPUtil.getPlanetRuler(requireContext())
        drawView.init(Chart.Natal, isShowRuler)
        drawViewPro.init(Chart.Natal, isShowRuler)

    }

    private fun initData(data: AstroData?) {
        if (data != null) {
            requireActivity().title = data.name.toString()
            val birthData = BirthData()
            birthData.name = data.name.toString()
            birthData.birthday = data.timeInMillis!!
            birthData.birthplaceLatitude = data.latitude!!.toDouble()
            birthData.birthplaceLongitude = data.longitude!!.toDouble()
            val horoscope = getHoroscope(requireContext(), Chart.Natal, birthData)
            singleChart(horoscope)

            binding.tvName.text = data.name

            when (data.gender) {
                "F" -> {
                    binding.tvGender.text = "女"
                }

                "M" -> {
                    binding.tvGender.text = "男"
                }

                "Mx" -> {
                    binding.tvGender.text = "混合"
                }

                "FM" -> {
                    binding.tvGender.text = "女變男"
                }

                "MF" -> {
                    binding.tvGender.text = "男變女"
                }

                "N/A" -> {
                    binding.tvGender.text = "事件數據"
                }

                "UNK" -> {
                    binding.tvGender.text = "未知"
                }

                else -> {
                    binding.tvGender.text = "未知"
                }
            }
            binding.tvBirthday.text =
                FormatUtils.longToString(data.timeInMillis!!, "yyyy/MM/dd HH:mm")
            binding.tvPlace.text = data.place?.replace(",", "") ?: ""
            binding.tvLatitude.text = data.latitude
            binding.tvLongitude.text = data.longitude

            binding.tvLink.setOnClickListener {
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(data.link)
                startActivity(intent)
            }

            binding.tvWikipedia.setOnClickListener {
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(data.wikipedia)
                startActivity(intent)
            }

            binding.fabChart.setOnClickListener {
                toBirth(birthData)
            }

            if (data.biography != null) {
                val textArray = data.biography?.split('\n')
                translate(
                    ArrayList(textArray!!),
                    0,
                    binding.tvBiography,
                    binding.progressBarBiography
                )
            }
            if (data.categories != null) {
                val textArray = data.categories?.split('\n')
                translate(
                    ArrayList(textArray!!),
                    0,
                    binding.tvCategories,
                    binding.progressBarCategories
                )
            }
        }
    }

    private fun translate(
        textArray: ArrayList<String>,
        i: Int,
        textView: TextView,
        progressBar: ProgressBar
    ) {
        progressBar.visibility = View.VISIBLE
        TranslateUtil.translate(textArray[i], object : TranslateCallback {
            override fun onSuccess(message: String) {
                textArray[i] = message
                val index = i + 1
                if (index < textArray.size) {
                    translate(textArray, index, textView, progressBar)
                } else {
                    var text = ""
                    for (j in 0 until textArray.size) {
                        text += if (j < textArray.size - 1) {
                            textArray[j] + "\n"
                        } else {
                            textArray[j]
                        }
                    }
                    textView.text = text
                    progressBar.visibility = View.GONE
                }
            }

            override fun onFailure(message: String?) {
                if (message != null) {
                    LogUtil.d(message)
                }
                progressBar.visibility = View.VISIBLE
            }
        })

    }

    private fun toBirth(birthData: BirthData) {
        val bundle = Bundle()
        bundle.putParcelable(KeyDefine.UserBirthDataA, birthData)
        bundle.putString(KeyDefine.Chart, Chart.Natal.toStorageValue())
        bundle.putBoolean(KeyDefine.IsSystem, true)
        startActivity(SignDetailActivity::class.java, bundle)
    }

    private fun getHoroscope(context: Context, chart: Chart, birthData: BirthData): Horoscope {
        return EphemerisUtil.calculate(
            context,
            chart,
            birthData.name,
            birthData.birthday,
            LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
        )
    }

    private fun singleChart(horoscopeA: Horoscope) {
        requireActivity().runOnUiThread {
            setAnimator(horoscopeA)
        }
    }


    private fun setAnimator(horoscopeA: Horoscope) {
        binding.drawContainer.removeAllViews()
        isSymbolMode = EncryptedSPUtil.getSymbolMode(requireContext())
        if (isSymbolMode) {
            binding.drawContainer.addView(drawViewPro)
            drawViewPro.setData(horoscopeA)
        } else {
            binding.drawContainer.addView(drawView)
            drawView.setData(horoscopeA)
        }
        binding.loadingProgress.visibility = View.GONE
    }

    private fun flipCard(context: Context, visibleView: View, inVisibleView: View): AnimatorSet {
        val flipOutAnimatorSet =
            AnimatorInflater.loadAnimator(
                context,
                R.animator.flip_out
            ) as AnimatorSet
        val flipInAnimatorSet =
            AnimatorInflater.loadAnimator(
                context,
                R.animator.flip_in
            ) as AnimatorSet
        try {
            inVisibleView.visibility = View.VISIBLE
            val scale = context.resources.displayMetrics.density
            val cameraDist = 8000 * scale
            visibleView.cameraDistance = cameraDist
            inVisibleView.cameraDistance = cameraDist
            flipOutAnimatorSet.setTarget(inVisibleView)
            flipInAnimatorSet.setTarget(visibleView)
            flipOutAnimatorSet.start()
            flipInAnimatorSet.start()
        } catch (e: Exception) {
            e.message?.let { LogUtil.e(it) }
        }
        return flipInAnimatorSet
    }

    private fun translate(string: String, textView: TextView, progressBar: ProgressBar) {
        progressBar.visibility = View.VISIBLE
        TranslateUtil.translate(string, object : TranslateCallback {
            override fun onSuccess(message: String) {
                textView.text = message
                progressBar.visibility = View.GONE
            }

            override fun onFailure(message: String?) {
                if (message != null) {
                    LogUtil.d(message)
                }
                textView.text = string
                progressBar.visibility = View.GONE
            }
        })
    }
}