package com.one.astrology.ui.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.QuickViewHolder
import com.google.android.material.snackbar.Snackbar
import com.one.astrology.R
import com.one.astrology.data.bean.Aspect
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.databinding.LayoutEmptyBinding
import com.one.astrology.databinding.SnackbarHouseViewBinding
import com.one.astrology.databinding.SnackbarPlanetViewBinding
import com.one.astrology.databinding.SnackbarSignViewBinding
import com.one.astrology.util.EphemerisUtil

class SnackBarView {
    companion object {
        fun showPlanet(
            context: Context,
            signBeanList: ArrayList<SignBean>,
            planetBean: PlanetBean,
            container: View
        ): Snackbar {
            val snackView = View.inflate(context, R.layout.snackbar_planet_view, null)
            val snackbarViewBinding = SnackbarPlanetViewBinding.bind(snackView)

            val snackBar = Snackbar.make(container, "", Snackbar.LENGTH_INDEFINITE)
            val layout = snackBar.view
            val textView =
                layout.findViewById<View>(com.google.android.material.R.id.snackbar_text) as TextView
            textView.visibility = View.INVISIBLE
            layout as Snackbar.SnackbarLayout
            layout.addView(snackView)
            val color = ContextCompat.getColor(context, R.color.transparent)
            snackBar.view.setBackgroundColor(color)
            snackbarViewBinding.tvPlanetName.text = planetBean.chName

            val signBeanFind = signBeanList.find {
                val rulers = it.ruler.split("、")
                val name = rulers.find { ruler ->
                    ruler == planetBean.chName
                }
                !name.isNullOrEmpty()
            }
            if (signBeanFind != null) {
                val index = signBeanList.indexOf(signBeanFind)
                snackbarViewBinding.tvRuler.text =
                    context.getString(R.string.lord_of_the_first_house, index + 1)
            } else {
                snackbarViewBinding.tvRuler.visibility = View.GONE
            }
            val colorPlanet = ("#" + planetBean.color).toColorInt()
            snackbarViewBinding.tvPlanetSymbol.setTextColor(colorPlanet)
            snackbarViewBinding.tvPlanetSymbol.text = planetBean.symbol

            val strings: ArrayList<String> = EphemerisUtil.szZodiac(planetBean.longitude)
            val degree = strings[1] + "°" + strings[2]
            snackbarViewBinding.tvSignName.text =
                context.getString(
                    R.string.fall_into_horoscope,
                    planetBean.signBean.chName,
                    degree
                )
            val houseData = planetBean.signBean.houseData
            val houseDegree = houseData.degree
            snackbarViewBinding.tvHouse.text = context.getString(
                R.string.fall_into_palace,
                houseData.index.toString(),
                houseDegree
            )
            initRecycleView(context, snackbarViewBinding.recyclerView, planetBean.aspects)

            snackBar.gravityCenter()
            snackBar.show()
            return snackBar
        }

        fun showPlanetB(
            context: Context,
            planetBean: PlanetBean,
            container: View
        ): Snackbar {
            val snackView = View.inflate(context, R.layout.snackbar_planet_view, null)
            val snackbarViewBinding = SnackbarPlanetViewBinding.bind(snackView)

            val snackBar = Snackbar.make(container, "", Snackbar.LENGTH_INDEFINITE)
            val layout = snackBar.view
            val textView =
                layout.findViewById<View>(com.google.android.material.R.id.snackbar_text) as TextView
            textView.visibility = View.INVISIBLE
            layout as Snackbar.SnackbarLayout
            layout.addView(snackView)
            val color = ContextCompat.getColor(context, R.color.transparent)
            snackBar.view.setBackgroundColor(color)
            snackbarViewBinding.tvPlanetName.text = planetBean.chName
            snackbarViewBinding.tvRuler.visibility = View.GONE

            val colorPlanet = ("#" + planetBean.color).toColorInt()
            snackbarViewBinding.tvPlanetSymbol.setTextColor(colorPlanet)
            snackbarViewBinding.tvPlanetSymbol.text = planetBean.symbol
            val strings: ArrayList<String> = EphemerisUtil.szZodiac(planetBean.longitude)
            val degree = strings[1] + "°" + strings[2]
            snackbarViewBinding.tvSignName.text =
                context.getString(
                    R.string.fall_into_horoscope,
                    planetBean.signBean.chName,
                    degree
                )
            val houseData = planetBean.signBean.houseData
            val houseDegree = houseData.degree
            snackbarViewBinding.tvHouse.text = context.getString(
                R.string.fall_into_palace,
                houseData.index.toString(),
                houseDegree
            )
            initRecycleView(context, snackbarViewBinding.recyclerView, planetBean.aspects)

            snackBar.gravityCenter()
            snackBar.show()
            return snackBar
        }

        fun showSign(
            context: Context,
            signBean: SignBean,
            planetBeans: ArrayList<PlanetBean>,
            container: View
        ): Snackbar {
            val snackView = View.inflate(context, R.layout.snackbar_sign_view, null)
            val snackbarViewBinding = SnackbarSignViewBinding.bind(snackView)

            val snackBar = Snackbar.make(container, "", Snackbar.LENGTH_INDEFINITE)
            val layout = snackBar.view
            val textView =
                layout.findViewById<View>(com.google.android.material.R.id.snackbar_text) as TextView
            textView.visibility = View.INVISIBLE
            layout as Snackbar.SnackbarLayout
            layout.addView(snackView)
            val color = ContextCompat.getColor(context, R.color.transparent)
            snackBar.view.setBackgroundColor(color)
            snackbarViewBinding.tvPlanetName.text = signBean.chName

            val colorPlanet = ("#" + signBean.color).toColorInt()
            snackbarViewBinding.tvPlanetSymbol.setTextColor(colorPlanet)
            snackbarViewBinding.tvPlanetSymbol.text = signBean.unicode

            snackbarViewBinding.tvRuler.text = context.getString(R.string.ruler, signBean.ruler)

            initRecycleSignView(context, snackbarViewBinding.recyclerView, planetBeans)
            snackBar.gravityCenter()
            snackBar.show()
            return snackBar
        }

        fun showHouse(
            context: Context,
            index: Int,
            planetBeans: ArrayList<PlanetBean>,
            signBean: SignBean,
            container: View
        ): Snackbar {
            val snackView = View.inflate(context, R.layout.snackbar_house_view, null)
            val snackbarViewBinding = SnackbarHouseViewBinding.bind(snackView)

            val snackBar = Snackbar.make(container, "", Snackbar.LENGTH_INDEFINITE)
            val layout = snackBar.view
            val textView =
                layout.findViewById<View>(com.google.android.material.R.id.snackbar_text) as TextView
            textView.visibility = View.INVISIBLE
            layout as Snackbar.SnackbarLayout
            layout.addView(snackView)
            val color = ContextCompat.getColor(context, R.color.transparent)
            snackBar.view.setBackgroundColor(color)

            snackbarViewBinding.tvHouseIndex.text = context.getString(R.string.house_index, index)
            snackbarViewBinding.tvCusp.text =
                context.getString(R.string.palace_cusp, signBean.chName, signBean.degree + signBean.minute)
            snackbarViewBinding.tvRuler.text = context.getString(R.string.ruler_s, signBean.ruler)

            initRecycleHouseView(context, snackbarViewBinding.recyclerView, planetBeans)

            snackBar.gravityCenter()
            snackBar.show()
            return snackBar
        }

        @SuppressLint("InflateParams")
        private fun initRecycleView(
            context: Context,
            recyclerView: RecyclerView,
            aspects: ArrayList<Aspect>
        ) {
            val layoutManager = LinearLayoutManager(context)
            layoutManager.orientation = RecyclerView.VERTICAL
            recyclerView.layoutManager = layoutManager

            val aspectItemAdapter = AspectItemAdapter()
            aspectItemAdapter.isEmptyViewEnable = true

            val layoutEmpty = LayoutEmptyBinding.inflate(LayoutInflater.from(context), null, false)
            layoutEmpty.tvEmptyDesc.text = "無相位"
            aspectItemAdapter.emptyView = layoutEmpty.root


            aspectItemAdapter.submitList(aspects)
            recyclerView.adapter = aspectItemAdapter
        }

        @SuppressLint("InflateParams")
        private fun initRecycleSignView(
            context: Context,
            recyclerView: RecyclerView,
            planetBeans: ArrayList<PlanetBean>
        ) {
            val layoutManager = LinearLayoutManager(context)
            layoutManager.orientation = RecyclerView.VERTICAL
            recyclerView.layoutManager = layoutManager

            val itemAdapter = SignItemAdapter()
            itemAdapter.submitList(planetBeans)
            recyclerView.adapter = itemAdapter
        }

        @SuppressLint("InflateParams")
        private fun initRecycleHouseView(
            context: Context,
            recyclerView: RecyclerView,
            planetBeans: ArrayList<PlanetBean>
        ) {
            val layoutManager = LinearLayoutManager(context)
            layoutManager.orientation = RecyclerView.VERTICAL
            recyclerView.layoutManager = layoutManager

            val itemAdapter = HouseItemAdapter()
            itemAdapter.submitList(planetBeans)
            recyclerView.adapter = itemAdapter
        }

        private fun Snackbar.gravityCenter() {
            this.view.layoutParams = (this.view.layoutParams as FrameLayout.LayoutParams).apply {
//            marginEnd = 250
//            marginStart = 250
                width = 700
                gravity = Gravity.CENTER
            }
        }
    }
}

class SignItemAdapter : BaseQuickAdapter<PlanetBean, QuickViewHolder>() {

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): QuickViewHolder {
        return QuickViewHolder(R.layout.item_planet_w, parent)
    }

    override fun onBindViewHolder(
        holder: QuickViewHolder,
        position: Int,
        item: PlanetBean?
    ) {
        if (item != null) {
            val colorPlanet = ("#" + item.color).toColorInt()
            holder.getView<TextView>(R.id.tvPlanetSymbol).setTextColor(colorPlanet)
            holder.getView<TextView>(R.id.tvPlanetSymbol).text = item.symbol
            holder.getView<TextView>(R.id.tvPlanetName).text = item.chName

            val colorSign = ("#" + item.signBean.color).toColorInt()
            holder.getView<TextView>(R.id.tvSignSymbol).setTextColor(colorSign)
            holder.getView<TextView>(R.id.tvSignSymbol).text = item.signBean.unicode
            holder.getView<TextView>(R.id.tvSignName).text = item.signBean.chName
            holder.getView<TextView>(R.id.tvSignDegree).text =
                item.signBean.degree + item.signBean.minute
        }
    }
}

class HouseItemAdapter : BaseQuickAdapter<PlanetBean, QuickViewHolder>() {

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): QuickViewHolder {
        return QuickViewHolder(R.layout.item_house_w, parent)
    }

    override fun onBindViewHolder(
        holder: QuickViewHolder,
        position: Int,
        item: PlanetBean?
    ) {
        if (item != null) {
            val colorPlanet = ("#" + item.color).toColorInt()
            holder.getView<TextView>(R.id.tvPlanetSymbol).setTextColor(colorPlanet)
            holder.getView<TextView>(R.id.tvPlanetSymbol).text = item.symbol
            holder.getView<TextView>(R.id.tvPlanetName).text = item.chName

            holder.getView<TextView>(R.id.tvHouseName).text =
                context.getString(R.string.house_index, item.houseData.index)
            holder.getView<TextView>(R.id.tvHouseDegree).text = item.houseData.degree
        }
    }

}

class AspectItemAdapter : BaseQuickAdapter<Aspect, QuickViewHolder>() {

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): QuickViewHolder {
        return QuickViewHolder(R.layout.item_aspect_w, parent)
    }

    override fun onBindViewHolder(holder: QuickViewHolder, position: Int, item: Aspect?) {
        if (item != null) {
            val colorPlanet = ("#" + item.planet.color).toColorInt()
            holder.getView<TextView>(R.id.tvPlanetSymbol).setTextColor(colorPlanet)
            holder.getView<TextView>(R.id.tvPlanetSymbol).text = item.planet.symbol

            holder.getView<TextView>(R.id.tvPlanetName).text = item.planet.chName
            holder.getView<TextView>(R.id.tvAspectType).text = item.type.nameCh
            when (item.type.nameCh) {
                context.getString(R.string.d_0) -> holder.getView<TextView>(R.id.tvAspectType)
                    .setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.blueDark
                        )
                    )

                context.getString(R.string.d_60) -> holder.getView<TextView>(R.id.tvAspectType)
                    .setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.greenLight
                        )
                    )

                context.getString(R.string.d_90) -> holder.getView<TextView>(R.id.tvAspectType)
                    .setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.red_light
                        )
                    )

                context.getString(R.string.d_120) -> holder.getView<TextView>(R.id.tvAspectType)
                    .setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.green
                        )
                    )

                context.getString(R.string.d_180) -> holder.getView<TextView>(R.id.tvAspectType)
                    .setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.navy_blue
                        )
                    )
            }

            holder.getView<TextView>(R.id.tvDirection).text = item.direction

            if (item.orb != null) {
                val strAngle = EphemerisUtil.szZodiac(item.orb!!)
                holder.getView<TextView>(R.id.tvOrb).text =
                    context.getString(R.string.orb_degree, strAngle[1], strAngle[2])
                holder.itemView.tag = item
            }
        }
    }

}