package com.one.astrology.ui.fragment.adapter


import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.one.astrology.R
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.databinding.ItemHouseBinding

class HouseItemAdapter : BaseQuickAdapter<SignBean, HouseItemAdapter.BaseViewHolder>() {

    var planetList: MutableList<PlanetBean> = ArrayList()

    class BaseViewHolder(
        parent: ViewGroup,
        val binding: ItemHouseBinding = ItemHouseBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(
        holder: <PERSON><PERSON><PERSON>wHolder,
        position: Int,
        item: SignBean?
    ) {
        if (item != null) {
            holder.binding.tvIndex.text = context.getString(R.string.house, position + 1)
            holder.binding.tvSignName.tag = item
            holder.binding.tvSignName.text = item.chName
            holder.binding.tvSignDegree.text = item.degree + item.minute
            var planet = planetList.find { it.chName == item.ruler }

            if (item.ruler.contains("、")) {
                val rulers = item.ruler.split("、")
                var indexString = ""
                for (i in rulers.indices) {
                    val ruler = rulers[i]
                    planet = planetList.find { it.chName == ruler }
                    if (planet != null) {
                        indexString =
                            planet.signBean.chName + " " + planet.signBean.houseData.index.toString() + "宮"
                    }
                    if (i == 0) {
                        holder.binding.tvRuler2.visibility = View.GONE
                        holder.binding.tvFlyInto2.visibility = View.GONE
                        holder.binding.tvRuler.text = ruler
                        holder.binding.tvFlyInto.tag = planet
                        holder.binding.tvFlyInto.text = indexString
                    } else {
                        holder.binding.tvRuler2.visibility = View.VISIBLE
                        holder.binding.tvFlyInto2.visibility = View.VISIBLE
                        holder.binding.tvRuler2.text = ruler
                        holder.binding.tvFlyInto2.tag = planet
                        holder.binding.tvFlyInto2.text = indexString
                    }
                }
            } else {
                holder.binding.tvRuler2.visibility = View.GONE
                holder.binding.tvFlyInto2.visibility = View.GONE
                holder.binding.tvRuler.text = item.ruler
                holder.binding.tvFlyInto.tag = planet
                if (planet != null) {
                    holder.binding.tvFlyInto.text = context.getString(
                        R.string.name_house_index,
                        planet.signBean.chName,
                        planet.signBean.houseData.index
                    )
                }
            }
        }
    }

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup,
        viewType: Int
    ): BaseViewHolder {
        return BaseViewHolder(parent)
    }

}