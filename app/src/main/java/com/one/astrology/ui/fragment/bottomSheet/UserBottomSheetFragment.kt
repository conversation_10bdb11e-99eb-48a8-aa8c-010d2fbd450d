package com.one.astrology.ui.fragment.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.databinding.FragmentUserBottomSheetBinding
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import java.util.Date

/**
 * UserBottomSheetFragment
 */
class UserBottomSheetFragment(
    private var signRecordA: BirthData,
    private var signRecordB: BirthData?,
    private var onClickListener : View.OnClickListener
) : BottomSheetDialogFragment(R.layout.fragment_user_bottom_sheet) {

    private lateinit var binding: FragmentUserBottomSheetBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentUserBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        LogUtil.setCurrentScreen("UserBottomSheetFragment", this.javaClass.simpleName)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUserInfo()
    }

    private fun initUserInfo() {
        binding.tvName.text = signRecordA.name
        val timeString: String =
            FormatUtils.dateToString(Date(signRecordA.birthday), "yyyy/MM/dd HH:mm")
        binding.tvBirthday.text = timeString
        binding.tvLatitude.text = "%.3f".format(signRecordA.birthplaceLatitude)
        binding.tvLongitude.text = "%.3f".format(signRecordA.birthplaceLongitude)
        binding.ibEdit.setOnClickListener {
            val onClickListener = View.OnClickListener {
                signRecordA = it.tag as BirthData
                initUserInfo()
                onClickListener.onClick(it)
                dismiss()
            }
            SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                requireActivity().supportFragmentManager,
                "SelectBottomSheetFragment"
            )
        }
        if (signRecordB == null) {
            binding.lltRight.visibility = View.GONE
            return
        }
        binding.lltRight.visibility = View.VISIBLE
        binding.tvNameB.text = signRecordB!!.name
        val timeStringB: String =
            FormatUtils.dateToString(Date(signRecordB!!.birthday), "yyyy/MM/dd HH:mm")
        binding.tvBirthdayB.text = timeStringB
        binding.tvLatitudeB.text = "%.3f".format(signRecordB!!.birthplaceLatitude)
        binding.tvLongitudeB.text = "%.3f".format(signRecordB!!.birthplaceLongitude)
        binding.lltRight.visibility = View.VISIBLE
        binding.ibEditB.setOnClickListener {
            val onClickListener = View.OnClickListener {
                signRecordB = it.tag as BirthData
                initUserInfo()
                onClickListener.onClick(it)
                dismiss()
            }
            SingleSelectBottomSheetFragment.newInstance(onClickListener).show(
                requireActivity().supportFragmentManager,
                "SelectBottomSheetFragment"
            )
        }
    }

}