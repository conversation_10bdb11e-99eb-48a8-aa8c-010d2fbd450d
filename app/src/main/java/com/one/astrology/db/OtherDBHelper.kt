package com.one.astrology.db

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import com.one.astrology.data.db.AstroData
import com.one.astrology.data.db.Categories
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import java.io.FileOutputStream


const val OTHER_DATABASE_NAME = "other.db" /* the database name */
const val OTHER_ASSET_NAME = "db/other.db"

/* The name of the asset file which could be different if required */
const val OTHER_DATABASE_VERSION = 4
//const val ASSET_COPY_BUFFER_SIZE = 8 * 1024

class OtherDBHelper private constructor(val context: Context) :
    SQLiteOpenHelper(context, OTHER_DATABASE_NAME, null, OTHER_DATABASE_VERSION) {
    override fun onCreate(p0: SQLiteDatabase?) {
        LogUtil.d("onCreate")
    }

    override fun onUpgrade(p0: SQLiteDatabase?, oldVersion: Int, newVersion: Int) {
        LogUtil.d("onUpgrade")
        if (newVersion > oldVersion) {
            getAndCopyAssetDatabase(context)
        }
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        private var instance: OtherDBHelper? = null
        fun getInstance(context: Context): OtherDBHelper {
            if (instance == null) {
                getAndCopyAssetDatabase(context)
                instance = OtherDBHelper(context)
            }
            return instance as OtherDBHelper
        }

        fun queryCategories(
            context: Context,
            id: Long,
            categories: String
        ): ArrayList<AstroData> {
            val db = getInstance(context)
            val whereClause =
                "id > ? and time is NOT NULL and latitude is NOT NULL and longitude is NOT NULL AND categories LIKE ?"
            val whereArgs = arrayOf(
                id.toString(),
                "%$categories%",
            )
            val orderBy = "id"
            val tableColumns = arrayOf(
                "id",
                "name",
                "time",
                "latitude",
                "longitude",
                "biography",
                "wikipedia",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", tableColumns, whereClause, whereArgs,
                null, null, orderBy, "10"
            )
            val list = ArrayList<AstroData>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        id = cursor.getLong(0),
                        name = cursor.getString(1),
                        time = cursor.getString(2),
                        latitude = cursor.getString(3),
                        longitude = cursor.getString(4),
                        biography = cursor.getString(5),
                        wikipedia = cursor.getString(6)
                    )
                    list.add(data)
                    cursor.moveToNext()
                }
            }
            cursor.close()
            return list
        }

        fun queryDetailDataList(
            context: Context,
            id: Long,
            name: String,
            categories: String
        ): ArrayList<AstroData> {
            val db = getInstance(context)
            val whereClause =
                "id > ? and name LIKE ? and time is NOT NULL and latitude is NOT NULL and longitude is NOT NULL AND categories LIKE ?"
            val whereArgs = arrayOf(
                id.toString(),
                "%$name%",
                "%$categories%",
            )
            val orderBy = "id"
            val tableColumns = arrayOf(
                "id",
                "name",
                "time",
                "place",
                "latitude",
                "longitude",
                "biography",
                "wikipedia",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", tableColumns, whereClause, whereArgs,
                null, null, orderBy, "10"
            )

            val list = ArrayList<AstroData>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        id = cursor.getLong(0),
                        name = cursor.getString(1),
                        time = cursor.getString(2),
                        place = cursor.getString(3),
                        latitude = cursor.getString(4),
                        longitude = cursor.getString(5),
                        biography = cursor.getString(6),
                        wikipedia = cursor.getString(7)
                    )
                    val date = FormatUtils.stringToDate(data.time!!, "yyyy/MM/dd HH:mm")
                    LogUtil.d("date ${date.time}")
                    data.timeInMillis = date.time
                    list.add(data)
                    cursor.moveToNext()
                }
                return list
            }

            cursor.close()
            return list
        }

        fun queryAstroDataList(
            context: Context
        ): ArrayList<AstroData> {
            val db = getInstance(context)
            val orderBy = "id"
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data_cn", null, null, null,
                null, null, orderBy
            )
            val list = ArrayList<AstroData>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val synastryAspect = AstroData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                        cursor.getString(4),
                        cursor.getString(5),
                        cursor.getString(6),
                        cursor.getString(7),
                    )
                    list.add(synastryAspect)
                    cursor.moveToNext()
                }
                return list
            }

            cursor.close()
            return list
        }

        fun queryVocationType(
            context: Context
        ): ArrayList<Categories> {
            val db = getInstance(context)
            val whereClause =
                "id is NOT NULL"
            val orderBy = "id"
            val tableColumns = arrayOf(
                "id",
                "name_eng",
                "name_cn",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "Vocation", tableColumns, whereClause, null,
                null, null, orderBy,
            )
            val list = ArrayList<Categories>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = Categories()
                    data.id = cursor.getLong(0)
                    data.name = cursor.getString(1)
                    data.nameCn = cursor.getString(2)
                    list.add(data)
                    cursor.moveToNext()
                }
            }
            cursor.close()
            return list
        }

        fun queryCategoriesType(
            context: Context
        ): ArrayList<Categories> {
            val db = getInstance(context)
            val whereClause =
                "id is NOT NULL"
            val orderBy = "id"
            val tableColumns = arrayOf(
                "id",
                "type",
                "type_cn",
                "name",
                "name_ch",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "categories", tableColumns, whereClause, null,
                null, null, orderBy,
            )
            val list = ArrayList<Categories>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = Categories()
                    data.id = cursor.getLong(0)
                    data.type = cursor.getString(1)
                    data.typeCn = cursor.getString(2)
                    data.name = cursor.getString(3)
                    data.nameCn = cursor.getString(4)
                    list.add(data)
                    cursor.moveToNext()
                }
            }
            cursor.close()
            return list
        }

        fun queryProgrammer(
            context: Context
        ): ArrayList<AstroData> {
            val db = getInstance(context)
            val whereClause =
                "time is NOT NULL and latitude is NOT NULL and longitude is NOT NULL AND categories LIKE '%Programmer%'"
            val orderBy = "RANDOM()"
            val tableColumns = arrayOf(
                "id",
                "name",
                "time",
                "latitude",
                "longitude",
                "biography",
                "wikipedia",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", tableColumns, whereClause, null,
                null, null, orderBy,
            )
            val list = ArrayList<AstroData>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        id = cursor.getLong(0),
                        name = cursor.getString(1),
                        time = cursor.getString(2),
                        latitude = cursor.getString(3),
                        longitude = cursor.getString(4),
                        biography = cursor.getString(5),
                        wikipedia = cursor.getString(6)
                    )
                    list.add(data)
                    cursor.moveToNext()
                }
            }
            cursor.close()
            return list
        }

        fun queryCategories(
            context: Context
        ): ArrayList<AstroData> {
            val db = getInstance(context)
            val whereClause =
                "time is NOT NULL AND categories is NOT NULL and create_time = ''"
            val orderBy = "id"
            val tableColumns = arrayOf(
                "id",
                "name",
                "time",
                "latitude",
                "longitude",
                "biography",
                "categories",
                "wikipedia",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", tableColumns, whereClause, null,
                null, null, orderBy,
            )
            val list = ArrayList<AstroData>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        id = cursor.getLong(0),
                        name = cursor.getString(1),
                        time = cursor.getString(2),
                        latitude = cursor.getString(3),
                        longitude = cursor.getString(4),
                        biography = cursor.getString(5),
                        categories = cursor.getString(6),
                        wikipedia = cursor.getString(7)
                    )
                    if (data.categories != null) {
                        val lines = data.categories!!.split("\n")
                        lines.forEach {
                            val category = it.split(" : ")
                            if (category.size >= 3) {
                                LogUtil.d("[${data.id}] ${category[0]} ${category[1]} ${category[2]}")
                                writeCategories(context, category[0], category[1], category[2])
                            }
                        }
                    }
                    data.createTime = System.currentTimeMillis()
                    update(context, data)
                    list.add(data)
                    cursor.moveToNext()
                }
            }
//            cursor.close()
//            db.close()
            return list
        }

        private fun update(
            context: Context,
            data: AstroData
        ) {
            val db = getInstance(context).writableDatabase

            val values = ContentValues()
            values.put("create_time", data.createTime)

            db.update("detail_data", values, "id=${data.id}", null)
//            db.close()
        }

        private fun writeCategories(
            context: Context,
            c1: String,
            c2: String,
            c3: String,
        ) {
            val db = getInstance(context)
            val writableDatabase = db.writableDatabase
            writableDatabase.execSQL(
                "INSERT OR REPLACE INTO  categories(c1 , c2 , c3)\n" +
                        "VALUES ( '$c1','$c2','$c3' )\n"
            ) // '$c1','$c2','$c3'
//                    "FROM categories\n"+
//                    "WHERE EXISTS (SELECT *\n" +
//                    "FROM categories\n" +
//                    "WHERE categories.c1 = '$c1' and categories.c2 = '$c2'  and categories.c3 = '$c3');")
//            writableDatabase.close()
        }

        fun queryDetailDataList(
            context: Context
        ): ArrayList<AstroData> {
            val db = getInstance(context)
            val whereClause =
                "time is NOT NULL and latitude is NOT NULL and longitude is NOT NULL " +
                        "and wikipedia is NOT NULL and rodden_rating = 'AA' " +
                        "and gender is NOT 'N/A' and gender is NOT 'UNK'"
            val orderBy = "RANDOM()"
            val tableColumns = arrayOf(
                "id",
                "name",
                "time",
                "place",
                "latitude",
                "longitude",
                "biography",
                "wikipedia",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", tableColumns, whereClause, null,
                null, null, orderBy, "10"
            )

            val list = ArrayList<AstroData>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        id = cursor.getLong(0),
                        name = cursor.getString(1),
                        time = cursor.getString(2),
                        place = cursor.getString(3),
                        latitude = cursor.getString(4),
                        longitude = cursor.getString(5),
                        biography = cursor.getString(6),
                        wikipedia = cursor.getString(7)
                    )
                    val date = FormatUtils.stringToDate(data.time!!, "yyyy/MM/dd HH:mm")
                    LogUtil.d("date ${date.time}")
                    data.timeInMillis = date.time
                    list.add(data)
                    cursor.moveToNext()
                }
                return list
            }

            cursor.close()
            return list
        }

        fun queryDetailDataList(
            context: Context,
            id: Long,
            name: String
        ): ArrayList<AstroData> {
            val db = getInstance(context)
            val whereClause =
                "id > ? and  name LIKE ? and time is NOT NULL and latitude is NOT NULL and longitude is NOT NULL and gender is NOT 'N/A'"
            val whereArgs = arrayOf(
                id.toString(),
                "%$name%",
            )
            val orderBy = "id"
            val tableColumns = arrayOf(
                "id",
                "name",
                "time",
                "place",
                "latitude",
                "longitude",
                "biography",
                "wikipedia",
            )
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", tableColumns, whereClause, whereArgs,
                null, null, orderBy, "10"
            )

            val list = ArrayList<AstroData>()
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        id = cursor.getLong(0),
                        name = cursor.getString(1),
                        time = cursor.getString(2),
                        place = cursor.getString(3),
                        latitude = cursor.getString(4),
                        longitude = cursor.getString(5),
                        biography = cursor.getString(6),
                        wikipedia = cursor.getString(7)
                    )
                    val date = FormatUtils.stringToDate(data.time!!, "yyyy/MM/dd HH:mm")
                    LogUtil.d("date ${date.time}")
                    data.timeInMillis = date.time
                    list.add(data)
                    cursor.moveToNext()
                }
                return list
            }

            cursor.close()
            return list
        }

        fun queryDetailData(
            context: Context
        ): AstroData? {
            val db = getInstance(context)
            val whereClause = "create_time is NULL and time is NULL "// create_time is NULL or
            val orderBy = "id"
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", null, whereClause, null,
                null, null, orderBy, "1"
            )

            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                        cursor.getString(4),
                        cursor.getString(5),
                        cursor.getString(6),
                        cursor.getString(7),
                        cursor.getString(8),
                        cursor.getString(9),
                        cursor.getString(10),
                        cursor.getString(11),
                        cursor.getLong(12),
                        cursor.getString(13),
                    )
                    cursor.moveToNext()
                    return data
                }
            }
            cursor.close()
            return null
        }

        fun queryDetailData2(
            context: Context
        ): AstroData? {
            val db = getInstance(context)
            val whereClause = "create_time is ''"
            val orderBy = "id"
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", null, whereClause, null,
                null, null, orderBy, "1"
            )

            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                        cursor.getString(4),
                        cursor.getString(5),
                        cursor.getString(6),
                        cursor.getString(7),
                        cursor.getString(8),
                        cursor.getString(9),
                        cursor.getString(10),
                        cursor.getString(11),
                        cursor.getLong(12),
                        cursor.getString(13),
                    )
                    cursor.moveToNext()
                    return data
                }
            }
            cursor.close()
            return null
        }

        fun queryDetailDataDesc(
            context: Context
        ): AstroData? {
            val db = getInstance(context)
            val whereClause = "time is NULL and create_time is NULL"
            val orderBy = "id"
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", null, whereClause, null,
                null, null, "$orderBy DESC", "1"
            )

            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                        cursor.getString(4),
                        cursor.getString(5),
                        cursor.getString(6),
                        cursor.getString(7),
                        cursor.getString(8),
                        cursor.getString(9),
                        cursor.getString(10),
                        cursor.getString(11),
                        cursor.getLong(12),
                        cursor.getString(13),
                    )
                    cursor.moveToNext()
                    return data
                }
            }
            cursor.close()
            return null
        }

        fun queryDetailData(
            context: Context,
            id: String
        ): AstroData? {
            val db = getInstance(context)
            val whereClause = "id = ?"
            val orderBy = "id"
            val whereArgs = arrayOf(
                id,
            )
            val cursor: Cursor = db.readableDatabase.query(
                "detail_data", null, whereClause, whereArgs,
                null, null, orderBy, "1"
            )

            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    val data = AstroData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                        cursor.getString(4),
                        cursor.getString(5),
                        cursor.getString(6),
                        cursor.getString(7),
                        cursor.getString(8),
                        cursor.getString(9),
                        cursor.getString(10),
                        cursor.getString(11),
                        cursor.getLong(12),
                        cursor.getString(13),
                    )
                    cursor.moveToNext()
                    if (data.time != null) {
                        val date = FormatUtils.stringToDate(data.time!!, "yyyy/MM/dd HH:mm")
                        LogUtil.d("date ${data.time}")
                        data.timeInMillis = date.time
                    }
                    return data
                }
            }
            cursor.close()
            return null
        }


        fun queryRandomDetailData(
            context: Context
        ): AstroData? {
            val db = getInstance(context)

            val cursor: Cursor? = db.readableDatabase.query(
                "detail_data",  // 資料表名稱
                null,           // 選取所有欄位
                null,           // 無需條件
                null,           // 無需條件參數
                null,           // 無需 group by
                null,           // 無需 having
                "RANDOM()",     // 隨機排序
                "1"             // 只取 1 筆
            )

            cursor?.use { // 使用 use 確保游標自動關閉
                if (it.moveToFirst()) { // 確保有資料
                    val data = AstroData(
                        it.getLong(0),
                        it.getString(1),
                        it.getString(2),
                        it.getString(3),
                        it.getString(4),
                        it.getString(5),
                        it.getString(6),
                        it.getString(7),
                        it.getString(8),
                        it.getString(9),
                        it.getString(10),
                        it.getString(11),
                        it.getLong(12),
                        it.getString(13),
                    )

                    data.time?.let { timeStr -> // 安全處理時間轉換
                        val date = FormatUtils.stringToDate(timeStr, "yyyy/MM/dd HH:mm")
                        LogUtil.d("date $timeStr")
                        data.timeInMillis = date.time
                    }
                    return data
                }
            }
            return null // 若無資料，返回 null
        }


        fun writeAstroDataList(
            context: Context,
            name: String,
            link: String
        ) {
            val db = getInstance(context)
            val writableDatabase = db.writableDatabase
            writableDatabase.execSQL("INSERT INTO data_bank(name, link) VALUES('$name','$link')")
            writableDatabase.close()
        }

        fun updateData(
            context: Context,
            data: AstroData
        ) {
            val db = getInstance(context).writableDatabase

            val values = ContentValues()
            values.put("name", data.name)
            values.put("link", data.link)
            values.put("birth_name", data.birthName)
            values.put("time", data.time)
            values.put("place", data.place)
            values.put("latitude", data.latitude)
            values.put("longitude", data.longitude)
            values.put("rodden_rating", data.roddenRating)
            values.put("biography", data.biography)
            values.put("categories", data.categories)
            values.put("gender", data.gender)
            values.put("create_time", data.createTime)
            values.put("wikipedia", data.wikipedia)

            db.update("detail_data", values, "id=${data.id}", null)
            db.close()
        }

        private fun ifDatabaseExists(context: Context): Boolean {
            val dbFile = context.getDatabasePath(OTHER_DATABASE_NAME)
            if (dbFile.exists()) return true
            else if (!dbFile.parentFile?.exists()!!) {
                dbFile.parentFile?.mkdirs()
            }
            return false
        }

        private fun getAndCopyAssetDatabase(context: Context) {
            val path = context.getDatabasePath(OTHER_DATABASE_NAME)
            context.assets.open(OTHER_ASSET_NAME).copyTo(
                FileOutputStream(path), ASSET_COPY_BUFFER_SIZE
            )
        }
    }

}
