package com.one.astrology.db

import com.google.firebase.Firebase
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.ValueEventListener
import com.google.firebase.database.database
import com.one.astrology.data.db.SignDescData
import com.one.astrology.data.type.Chart
import com.one.core.util.LogUtil

interface ValueListener {
    fun onDataChange(signDescData: SignDescData?)
}

class RealTimeDBHelp {
    companion object {

        fun getSign(
            chart: Chart,
            planetName: String,
            signName: String,
            valueListener: ValueListener
        ) {
            val database = Firebase.database("https://astrology-78f27-d1593.firebaseio.com/")
            val myRef = database.reference.child(chart.nameEng)
            val orderStatusQuery = myRef.child("sign").orderByChild("planet").equalTo(planetName)
            val valueEventListener: ValueEventListener = object : ValueEventListener {
                override fun onDataChange(dataSnapshot: DataSnapshot) {
                    val sign = dataSnapshot.children.filter { it.child("sign").value == signName }
                    if (sign.isNotEmpty()) {
                        val signDescData = SignDescData()
                        signDescData.sign = signName
                        signDescData.planet = planetName
                        var desc = sign[0].child("desc").getValue(String::class.java)
                        if (!desc.isNullOrEmpty()) {
                            signDescData.descList.add(desc)
                        }
                        desc = sign[0].child("desc_魯道夫").getValue(String::class.java)
                        if (!desc.isNullOrEmpty()) {
                            signDescData.descList.add(desc)
                        }
                        val desc1 = sign[0].child("desc_1").getValue(String::class.java)
                        if (!desc1.isNullOrEmpty()) {
                            signDescData.descList.add(desc1)
                        }
                        val desc2 = sign[0].child("desc_2").getValue(String::class.java)
                        if (!desc2.isNullOrEmpty()) {
                            signDescData.descList.add(desc2)
                        }
                        val descAll = sign[0].child("desc_all").getValue(String::class.java)
                        if (!descAll.isNullOrEmpty()) {
                            signDescData.descList.add(descAll)
                        }
                        valueListener.onDataChange(signDescData)
                    } else {
                        valueListener.onDataChange(null)
                    }
                }

                override fun onCancelled(databaseError: DatabaseError) {
                    LogUtil.d(databaseError.message)
                    valueListener.onDataChange(null)
                }
            }
            orderStatusQuery.addListenerForSingleValueEvent(valueEventListener)
        }

//        fun removeValue() {
//            val database = Firebase.database
//            val myRef = database.reference
//            for (i in 408 until 465) {
//                myRef.child(i.toString()).removeValue()
//                myRef.child(i.toString()).removeEventListener(object : ValueEventListener {
//                    override fun onDataChange(snapshot: DataSnapshot) {
//                        for (ds in snapshot.children) {
//                            val userName = ds.child("desc_魯道夫").getValue(String::class.java)
//                            Log.d("TAG", userName!!)
//                        }
//                    }
//
//                    override fun onCancelled(error: DatabaseError) {
//                        Log.d("TAG", error.message) //Don't ignore potential errors!
//                    }
//                })
//            }
//        }
    }
}