package com.one.astrology.db

import android.annotation.SuppressLint
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.db.FlyData
import com.one.astrology.data.db.HouseDescData
import com.one.astrology.data.db.RetrogradeData
import com.one.astrology.data.db.SignDescData
import com.one.astrology.data.type.Chart
import com.one.core.util.LogUtil
import java.io.FileOutputStream


const val DATABASE_NAME = "database.db" /* the database name */
const val ASSET_NAME = "db/database.db"

/* The name of the asset file which could be different if required */
const val DATABASE_VERSION = 18
const val ASSET_COPY_BUFFER_SIZE = 8 * 1024

class DBHelper private constructor(val context: Context) :
    SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {
    override fun onCreate(p0: SQLiteDatabase?) {
        LogUtil.d("onCreate")
    }

    override fun onUpgrade(p0: SQLiteDatabase?, oldVersion: Int, newVersion: Int) {
        LogUtil.d("onUpgrade")
        if (newVersion > oldVersion) {
            getAndCopyAssetDatabase(context)
        }
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        private var instance: DBHelper? = null
        fun getInstance(context: Context): DBHelper {
            if (instance == null) {
                getAndCopyAssetDatabase(context)
                instance = DBHelper(context)
            }
            return instance as DBHelper
        }


        fun querySign(
            context: Context,
            chart: Chart,
            planet: String,
            sign: String,
        ): SignDescData? {
            val db = getInstance(context)
            val whereClause = "planet = ? and sign = ?"
            val whereArgs = arrayOf(
                planet,
                sign,
            )
            val orderBy = "id"
            val table = when (chart) {
                Chart.Natal -> {
                    "natal_sign"
                }

                Chart.Transit -> {
                    "transit_sign"
                }
                Chart.Composite -> {
                    "composite_sign"
                }

                else -> {
                    return null
                }
            }
            val database = db.readableDatabase
            val cursor: Cursor = database.query(
                table, null, whereClause, whereArgs,
                null, null, orderBy
            )
            var data: SignDescData? = null
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                data = SignDescData(
                    cursor.getLong(0),
                    cursor.getString(1),
                    cursor.getString(2),
                    cursor.getString(3),
                )
                for (i in 3 until cursor.columnCount) {
                    if (cursor.getString(i).isNullOrEmpty()) {
                        continue
                    }
                    if (data.desc?.isEmpty() == true) {
                        data.desc = cursor.getString(i)
                    }
                    data.descList.add(cursor.getString(i))
                }
            }
            cursor.close()
            db.readableDatabase.close()
            return data
        }

        fun queryHouse(
            context: Context,
            chart: Chart,
            house: Int,
            planet: String,
        ): HouseDescData? {
            val db = getInstance(context)
            val whereClause = "house = ? AND planet = ?"
            val whereArgs = arrayOf(
                house.toString(),
                planet,
            )
            val orderBy = "id"
            val table = when (chart) {
                Chart.Natal -> {
                    "natal_house"
                }

                Chart.Synastry -> {
                    "synastry_house"
                }

                Chart.Composite -> {
                    "composite_house"
                }

                Chart.Transit -> {
                    "transit_house"
                }

                Chart.SecondaryProgression -> {
                    "secondary_progression_house"
                }

                Chart.TertiaryProgression -> {
                    "secondary_progression_house"
                }

                else -> {
                    return null
                }
            }
            val cursor: Cursor = db.readableDatabase.query(
                table, null, whereClause, whereArgs,
                null, null, orderBy
            )
            var data: HouseDescData? = null
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                data = HouseDescData(
                    cursor.getLong(0),
                    cursor.getInt(1),
                    cursor.getString(2),
                    cursor.getString(3),
                )
                for (i in 3 until cursor.columnCount) {
                    if (cursor.getString(i).isNullOrEmpty()) {
                        continue
                    }
                    data.descList.add(cursor.getString(i))
                }
            }
            cursor.close()
            return data
        }

        fun queryHouseSign(
            context: Context,
            chart: Chart,
            house: Int,
            sign: String,
        ): HouseDescData? {
            val db = getInstance(context)
            val whereClause = "house = ? AND sign = ?"
            val whereArgs = arrayOf(
                house.toString(),
                sign,
            )
            val orderBy = "id"
            val table = when (chart) {
                Chart.Natal -> {
                    "natal_house_sign"
                }

                else -> {
                    return null
                }
            }
            val cursor: Cursor = db.readableDatabase.query(
                table, null, whereClause, whereArgs,
                null, null, orderBy
            )
            var data: HouseDescData? = null
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                data = HouseDescData(
                    cursor.getLong(0),
                    cursor.getInt(1),
                    cursor.getString(2),
                    cursor.getString(3),
                )
                for (i in 3 until cursor.columnCount) {
                    if (cursor.getString(i).isNullOrEmpty()) {
                        continue
                    }
                    data.descList.add(cursor.getString(i))
                }
            }
            cursor.close()
            return data
        }

        fun queryAspect(
            context: Context,
            chart: Chart,
            planetA: String,
            degree: Int,
            planetB: String,
        ): AspectData? {
            val db = getInstance(context)
            val whereClause =
                "(planet_a = ? AND degree like ? AND planet_b = ?) OR (planet_a = ? AND degree like ? AND planet_b = ?)"
            val whereArgs = arrayOf(
                planetA,
                "%$degree%",
                planetB,
                planetB,
                "%$degree%",
                planetA,
            )
            val orderBy = "id"

            val table = when (chart) {
                Chart.Natal -> {
                    "natal_aspect"
                }

                Chart.Synastry -> {
                    "synastry_aspect"
                }

                Chart.Composite -> {
                    "composite_aspect"
                }

                Chart.Transit -> {
                    "transit_aspect"
                }

                Chart.SecondaryProgression -> {
                    "secondary_progression_aspect"
                }

                Chart.TertiaryProgression -> {
                    "secondary_progression_aspect"
                }

                else -> {
                    return null
                }
            }

            val cursor: Cursor = db.readableDatabase.query(
                table, null, whereClause, whereArgs,
                null, null, orderBy
            )
            var aspectData: AspectData? = null
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                when (chart) {
                    Chart.Natal -> {
                        aspectData = AspectData(
                            cursor.getLong(0),
                            planetA,
                            "",
                            degree,
                            planetB,
                            0
                        )
                        aspectData.desc = cursor.getString(4)
                        for (i in 4 until cursor.columnCount) {
                            if (cursor.getString(i).isNullOrEmpty()) {
                                continue
                            }
                            aspectData.descList.add(cursor.getString(i))
                        }
                    }

                    Chart.Synastry -> {
                        aspectData = AspectData(
                            cursor.getLong(0),
                            cursor.getString(1),
                            "",
                            cursor.getInt(2),
                            cursor.getString(3),
                            cursor.getInt(4),
                            cursor.getString(5),
                        )
                        for (i in 5 until cursor.columnCount) {
                            if (cursor.getString(i).isNullOrEmpty()) {
                                continue
                            }
                            aspectData.descList.add(cursor.getString(i))
                        }
                    }

                    Chart.Composite,
                    Chart.SecondaryProgression,
                    Chart.TertiaryProgression,
                    Chart.Transit -> {
                        aspectData = AspectData(
                            cursor.getLong(0),
                            cursor.getString(1),
                            "",
                            cursor.getInt(2),
                            cursor.getString(3),
                            0,
                            cursor.getString(4),
                        )
                        for (i in 4 until cursor.columnCount) {
                            if (cursor.getString(i).isNullOrEmpty()) {
                                continue
                            }
                            aspectData.descList.add(cursor.getString(i))
                        }
                    }

                    else -> {
                        return null
                    }
                }
            }
            cursor.close()
            return aspectData
        }

        fun queryFly(
            context: Context,
            chart: Chart,
            houseIndex: Int,
            flyIndex: Int,
        ): FlyData? {
            val db = getInstance(context)
            val whereClause =
                "(house_index = ? AND fly_index = ? )"
            val whereArgs = arrayOf(
                houseIndex.toString(),
                flyIndex.toString(),
            )
            val orderBy = "id"

            val table = when (chart) {
                Chart.Natal -> {
                    "natal_fly"
                }

                else -> {
                    return null
                }
            }

            val cursor: Cursor = db.readableDatabase.query(
                table, null, whereClause, whereArgs,
                null, null, orderBy
            )
            var flyData: FlyData? = null
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                when (chart) {
                    Chart.Natal -> {
                        flyData = FlyData(
                            cursor.getLong(0),
                            cursor.getInt(1),
                            cursor.getInt(2),
                            cursor.getString(3),
                        )
                        for (i in 3 until cursor.columnCount) {
                            if (cursor.getString(i).isNullOrEmpty()) {
                                continue
                            }
                            flyData.descList.add(cursor.getString(i))
                        }
                    }

                    else -> {
                        return null
                    }
                }
            }
            cursor.close()
            return flyData
        }

        fun queryRetrograde(
            context: Context,
            planet: String,
        ): RetrogradeData? {
            val db = getInstance(context)
            val orderBy = "id"
            val whereClause = "planet = ?"
            val whereArgs = arrayOf(
                planet
            )
            val cursor: Cursor = db.readableDatabase.query(
                "retrograde", null, whereClause, whereArgs,
                null, null, orderBy
            )
            var data: RetrogradeData? = null
            if (cursor.count > 0 && cursor.columnCount > 0) {
                cursor.moveToFirst()
                for (it in 0 until cursor.count) {
                    data = RetrogradeData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        cursor.getString(2),
                    )
                    for (i in 2 until cursor.columnCount) {
                        if (cursor.getString(i).isNullOrEmpty()) {
                            continue
                        }
                        data.descList.add(cursor.getString(i))
                    }
                    cursor.moveToNext()
                }
                return data
            }

            cursor.close()
            return data
        }

        private fun ifDatabaseExists(context: Context): Boolean {
            val dbFile = context.getDatabasePath(DATABASE_NAME)
            if (dbFile.exists()) return true
            else if (!dbFile.parentFile?.exists()!!) {
                dbFile.parentFile?.mkdirs()
            }
            return false
        }

        private fun getAndCopyAssetDatabase(context: Context) {
            val path = context.getDatabasePath(DATABASE_NAME)
            context.assets.open(ASSET_NAME).copyTo(
                FileOutputStream(path), ASSET_COPY_BUFFER_SIZE
            )
        }
    }

}
