package com.one.astrology.db

import android.annotation.SuppressLint
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import com.one.astrology.data.SignData
import com.one.astrology.data.bean.SignDescBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.db.ChartData
import com.one.astrology.data.db.HouseDefine
import com.one.astrology.data.db.PlanetData
import com.one.astrology.data.db.SettingAspectData
import com.one.core.util.LogUtil
import java.io.FileOutputStream


const val BASIC_DATABASE_NAME = "basic.db" /* the database name */
const val BASIC_ASSET_NAME = "db/basic.db"

/* The name of the asset file which could be different if required */
const val BASIC_DATABASE_VERSION = 3
//const val ASSET_COPY_BUFFER_SIZE = 8 * 1024

class BasicDBHelper private constructor(val context: Context) :
    SQLiteOpenHelper(context, BASIC_DATABASE_NAME, null, BASIC_DATABASE_VERSION) {
    override fun onCreate(p0: SQLiteDatabase?) {
        LogUtil.d("onCreate")
    }

    override fun onUpgrade(p0: SQLiteDatabase?, oldVersion: Int, newVersion: Int) {
        LogUtil.d("onUpgrade")
        if (newVersion > oldVersion) {
            getAndCopyAssetDatabase(context)
        }
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        private var instance: BasicDBHelper? = null
        @Synchronized
        fun getInstance(context: Context): BasicDBHelper {
            if (instance == null) {
                getAndCopyAssetDatabase(context)
                instance = BasicDBHelper(context)
            }
            return instance as BasicDBHelper
        }

        fun querySignDesc(context: Context, sign: String?): SignDescBean? {
            val dbHelper = getInstance(context)
            val db = dbHelper.readableDatabase
            val whereClause = "sign = ?" //OR column1 = ?"
            val whereArgs = arrayOf(
                sign,
            )
            val orderBy = "id"

            var cursor: Cursor? = null
            var signDescBean: SignDescBean? = null

            try {
                cursor = db.query(
                    "sign_desc", null, whereClause, whereArgs,
                    null, null, orderBy
                )

                if (cursor != null && cursor.moveToFirst()) {
                    signDescBean = SignDescBean()
                    signDescBean.id = cursor.getInt(0)
                    signDescBean.sign = cursor.getString(1)
                    signDescBean.ruler = cursor.getString(2)
                    signDescBean.exalt = cursor.getString(3)
                    signDescBean.triplicityDay = cursor.getString(4)
                    signDescBean.triplicityNight = cursor.getString(5)
                    signDescBean.triplicityMix = cursor.getString(6)
                    signDescBean.terms1 = cursor.getString(7)
                    signDescBean.terms1d = cursor.getString(8)
                    signDescBean.terms2 = cursor.getString(9)
                    signDescBean.terms2d = cursor.getString(10)
                    signDescBean.terms3 = cursor.getString(11)
                    signDescBean.terms3d = cursor.getString(12)
                    signDescBean.terms4 = cursor.getString(13)
                    signDescBean.terms4d = cursor.getString(14)
                    signDescBean.terms5 = cursor.getString(15)
                    signDescBean.terms5d = cursor.getString(16)
                    signDescBean.face1 = cursor.getString(17)
                    signDescBean.face2 = cursor.getString(18)
                    signDescBean.face3 = cursor.getString(19)
                    signDescBean.detriment = cursor.getString(20)
                    signDescBean.fall = cursor.getString(21)
                    signDescBean.theme = cursor.getString(22)
                    signDescBean.properties = cursor.getString(23)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                cursor?.close()
            }

            return signDescBean
        }

        fun querySignData(context: Context, chName: String?): SignData? {
            val dbHelper = getInstance(context) // 確保使用單例管理資料庫
            val db = dbHelper.readableDatabase // 使用資料庫連線
            val whereClause = "chName = ?"
            val whereArgs = arrayOf(chName)
            val orderBy = "angle"

            var cursor: Cursor? = null
            var signData: SignData? = null

            try {
                // 查詢資料
                cursor = db.query(
                    "sign", null, whereClause, whereArgs,
                    null, null, orderBy
                )

                // 如果有結果，提取數據
                if (cursor != null && cursor.moveToFirst()) {
                    signData = SignData(
                        cursor.getString(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                        cursor.getString(4),
                        cursor.getInt(5),
                        cursor.getString(6),
                        cursor.getString(7),
                        cursor.getString(8),
                        cursor.getString(9),
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace() // 打印異常日誌
            } finally {
                // 確保 Cursor 被關閉
                cursor?.close()
            }

            return signData
        }


        fun queryPlanet(context: Context, chName: String?): PlanetData? {
            val dbHelper = getInstance(context)
            val db = dbHelper.readableDatabase
            val whereClause = "chName = ?"
            val whereArgs = arrayOf(
                chName,
            )
            val orderBy = "id"

            var cursor: Cursor? = null
            var planetData: PlanetData? = null

            try {
                cursor = db.query(
                    "planet", null, whereClause, whereArgs,
                    null, null, orderBy
                )

                if (cursor != null && cursor.moveToFirst()) {
                    planetData = PlanetData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                        cursor.getString(4),
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                cursor?.close()
            }

            return planetData
        }
        fun queryChartList(
            context: Context,
        ): ArrayList<ChartData> {
            val dbHelper = getInstance(context)
            val db = dbHelper.readableDatabase
            val orderBy = "id"
            val list = ArrayList<ChartData>()

            var cursor: Cursor? = null
            try {
                cursor = db.query(
                    "chart", null, null, null,
                    null, null, orderBy
                )

                if (cursor != null && cursor.count > 0 && cursor.columnCount > 0) {
                    cursor.moveToFirst()
                    for (it in 0 until cursor.count) {
                        val item = ChartData(
                            cursor.getLong(0),
                            cursor.getString(1),
                            cursor.getString(2),
                            cursor.getString(3),
                        )
                        list.add(item)
                        cursor.moveToNext()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                cursor?.close()
            }

            return list
        }

        fun querySettingAspectList(
            context: Context,
        ): ArrayList<SettingAspectData> {
            val dbHelper = getInstance(context)
            val db = dbHelper.readableDatabase
            val orderBy = "id"
            val list = ArrayList<SettingAspectData>()

            var cursor: Cursor? = null
            try {
                cursor = db.query(
                    "setting_aspect", null, null, null,
                    null, null, orderBy
                )

                if (cursor != null && cursor.count > 0 && cursor.columnCount > 0) {
                    cursor.moveToFirst()
                    for (it in 0 until cursor.count) {
                        val item = SettingAspectData(
                            cursor.getLong(0),
                            cursor.getString(1),
                            cursor.getString(2),
                            cursor.getInt(3),
                            cursor.getInt(4),
                            cursor.getString(5).toBoolean(),
                        )
                        list.add(item)
                        cursor.moveToNext()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                cursor?.close()
            }

            return list
        }

        fun queryHouse(
            context: Context,
            house: String,
        ): HouseDefine? {
            val dbHelper = getInstance(context)
            val db = dbHelper.readableDatabase
            val orderBy = "house"
            val whereClause = "house = ?"
            val whereArgs = arrayOf(
                house
            )

            var cursor: Cursor? = null
            var data: HouseDefine? = null

            try {
                cursor = db.query(
                    "house", null, whereClause, whereArgs,
                    null, null, orderBy
                )

                if (cursor != null && cursor.count > 0 && cursor.columnCount > 0) {
                    cursor.moveToFirst()
                    data = HouseDefine(
                        cursor.getInt(0),
                        cursor.getString(1),
                        cursor.getString(2),
                        cursor.getString(3),
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                cursor?.close()
            }

            return data
        }

        fun querySynastryAspect(
            context: Context,
            planetNameA: String,
            degree: String,
            planetNameB: String,
        ): AspectData? {
            val dbHelper = getInstance(context)
            val db = dbHelper.readableDatabase
            val whereClause =
                "(planet_a = ? AND degree = ? AND planet_b = ?) OR (planet_a = ? AND degree = ? AND planet_b = ?)"
            val whereArgs = arrayOf(
                planetNameA,
                degree,
                planetNameB,
                planetNameB,
                degree,
                planetNameA,
            )
            val orderBy = "id"

            var cursor: Cursor? = null
            var synastryAspect: AspectData? = null

            try {
                cursor = db.query(
                    "synastry_aspect", null, whereClause, whereArgs,
                    null, null, orderBy
                )

                if (cursor != null && cursor.count > 0 && cursor.columnCount > 0) {
                    cursor.moveToFirst()
                    synastryAspect = AspectData(
                        cursor.getLong(0),
                        cursor.getString(1),
                        "",
                        cursor.getInt(2),
                        cursor.getString(3),
                        cursor.getInt(4),
                        cursor.getString(5),
                    )
                    for (i in 5 until cursor.columnCount) {
                        if (cursor.getString(i).isNullOrEmpty()) {
                            continue
                        }
                        synastryAspect.descList.add(cursor.getString(i))
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                cursor?.close()
            }

            return synastryAspect
        }

        fun querySynastryAspectList(
            context: Context
        ): ArrayList<AspectData> {
            val dbHelper = getInstance(context)
            val db = dbHelper.readableDatabase
            val orderBy = "id"
            val list = ArrayList<AspectData>()

            var cursor: Cursor? = null
            try {
                cursor = db.query(
                    "synastry_aspect", null, null, null,
                    null, null, orderBy
                )

                if (cursor != null && cursor.count > 0 && cursor.columnCount > 0) {
                    cursor.moveToFirst()
                    for (it in 0 until cursor.count) {
                        val synastryAspect = AspectData(
                            cursor.getLong(0),
                            cursor.getString(1),
                            "",
                            cursor.getInt(2),
                            cursor.getString(3),
                            cursor.getInt(4),
                            cursor.getString(5),
                        )
                        list.add(synastryAspect)
                        cursor.moveToNext()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                cursor?.close()
            }

            return list
        }

        private fun ifDatabaseExists(context: Context): Boolean {
            val dbFile = context.getDatabasePath(BASIC_DATABASE_NAME)
            if (dbFile.exists()) return true
            else if (!dbFile.parentFile?.exists()!!) {
                dbFile.parentFile?.mkdirs()
            }
            return false
        }

        private fun getAndCopyAssetDatabase(context: Context) {
            val path = context.getDatabasePath(BASIC_DATABASE_NAME)
            context.assets.open(BASIC_ASSET_NAME).copyTo(
                FileOutputStream(path), ASSET_COPY_BUFFER_SIZE
            )
        }
    }

}
