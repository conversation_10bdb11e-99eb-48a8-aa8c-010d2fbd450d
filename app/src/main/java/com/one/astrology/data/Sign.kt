package com.one.astrology.data

class Sign(
    var planetName: String,
    var name: String,
    var signDegree: String?,
    var dignity: String?,
    var quadruplicities: String,
    var triplicities: String,
    var house: Int,
    var houseDegree: String,
    var isRetrograde: Boolean
)

data class SignData(
    var symbol: String,
    var unicode: String,
    var enName: String,
    var cnName: String,
    var color: String,
    var angle: Int?,
    var yinYang: String,
    var triplicities: String,
    var quadruplicities: String,
    var keyword: String,
)