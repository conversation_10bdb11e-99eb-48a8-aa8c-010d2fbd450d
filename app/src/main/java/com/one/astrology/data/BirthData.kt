package com.one.astrology.data

import java.util.Calendar

/**
 * 出生資料類
 */
data class BirthData(
    val name: String,
    val birthTime: Long,
    val latitude: Double,
    val longitude: Double,
    val isDaylightSavingTime: Boolean = false
) {
    /**
     * 獲取格式化的出生時間
     */
    fun getFormattedBirthTime(): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = birthTime
        
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH) + 1
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        
        return "$year/$month/$day $hour:$minute"
    }
    
    /**
     * 獲取格式化的地理位置
     */
    fun getFormattedLocation(): String {
        return "緯度: $latitude, 經度: $longitude"
    }
} 