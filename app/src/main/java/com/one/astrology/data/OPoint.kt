package com.one.astrology.data

import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator

class OPoint() : Parcelable {
    var x = 0.0F
    var y = 0.0F

    constructor(parcel: Parcel) : this() {
        x = parcel.readFloat()
        y = parcel.readFloat()
    }

    constructor(x: Float, y: Float) : this() {
        this.x = x
        this.y = y
    }

    /**
     * Set the point's x and y coordinates
     */
    operator fun set(x: Float, y: Float) {
        this.x = x
        this.y = y
    }

    /**
     * Set the point's x and y coordinates to the coordinates of p
     */
    fun set(p: OPoint) {
        x = p.x
        y = p.y
    }


    /**
     * Returns true if the point's coordinates equal (x,y)
     */
    fun equals(x: Float, y: Float): Boolean {
        return this.x == x && this.y == y
    }

    /**
     * Parcelable interface methods
     */
    override fun describeContents(): Int {
        return 0
    }

    /**
     * Write this point to the specified parcel. To restore a point from
     * a parcel, use readFromParcel()
     * @param out The parcel to write the point's coordinates into
     */
    override fun writeToParcel(out: Parcel, flags: Int) {
        out.writeFloat(x)
        out.writeFloat(y)
    }

    /**
     * Set the point's coordinates from the data stored in the specified
     * parcel. To write a point to a parcel, call writeToParcel().
     *
     * @param in The parcel to read the point's coordinates from
     */
    fun readFromParcel(`in`: Parcel) {
        x = `in`.readFloat()
        y = `in`.readFloat()
    }


    companion object CREATOR : Creator<OPoint> {
        override fun createFromParcel(parcel: Parcel): OPoint {
            return OPoint(parcel)
        }

        override fun newArray(size: Int): Array<OPoint?> {
            return arrayOfNulls(size)
        }
    }
}