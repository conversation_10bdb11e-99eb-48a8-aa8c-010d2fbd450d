package com.one.astrology.data.db

import com.google.gson.annotations.SerializedName
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

@Entity
class SettingAspectData (
    @Id(assignable = true)
    var id: Long = 0,
    @SerializedName("chName")
    var chName: String? = "",
    @SerializedName("enName")
    var enName: String? = "",
    @SerializedName("degree")
    var degree: Int? = 0,
    @SerializedName("orb")
    var orb: Int? = 0,
    @SerializedName("is_open")
    var isOpen: Boolean? = false,
)