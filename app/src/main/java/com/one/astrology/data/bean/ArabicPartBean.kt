package com.one.astrology.data.bean

import com.google.gson.annotations.SerializedName
import com.one.astrology.data.HouseData
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import io.objectbox.annotation.Transient
import java.io.Serializable

/**
 * 阿拉伯點數據模型
 * 阿拉伯點是古典占星學中的重要概念，通過特定公式計算得出的敏感點
 */
@Entity
class ArabicPartBean : Serializable {
    @Id(assignable = true)
    var idL: Long = 0

    @SerializedName("id")
    var id = 0

    @SerializedName("symbol")
    var symbol: String = ""

    @SerializedName("enName")
    var enName: String = ""

    @SerializedName("chName")
    var chName: String = ""

    @SerializedName("formula")
    var formula: String = ""  // 計算公式的描述

    @SerializedName("description")
    var description: String = ""  // 阿拉伯點的含義描述

    @SerializedName("category")
    var category: String = ""  // 分類（如：財富、健康、關係等）

    @SerializedName("angle")
    var angle = 0.0  // 在星盤中的角度位置

    @SerializedName("isDay")
    var isDay: Boolean = true  // 是否使用白天公式（部分阿拉伯點在白天和夜晚有不同公式）

    @SerializedName("isEnabled")
    var isEnabled: Boolean = true  // 是否啟用此阿拉伯點

    // 計算所需的行星或點位
    @SerializedName("point1")
    var point1: Int = 0  // 第一個點（通常是上升點）

    @SerializedName("point2")
    var point2: Int = 0  // 第二個點

    @SerializedName("point3")
    var point3: Int = 0  // 第三個點

    // 獲取阿拉伯點所在的星座和宮位信息
    @Transient
    var signBean = SignBean()

    @Transient
    var houseData = HouseData()

    override fun toString(): String {
        return "ArabicPartBean(chName='$chName', angle=$angle, formula='$formula')"
    }
} 