package com.one.astrology.data.entity

import com.google.gson.annotations.SerializedName
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.io.Serializable

@Entity
class SynastryAspect : Serializable {
    /**
     * degree : 0
     * description : 太陽合相在一般關係中，在同性間熟悉感頗強，男性很少跟同星座者成為朋友，否則相互爭輝，形成勢力抗衡。太陽合相因主要性質太像，像是同帶負電的電子與電子沒有吸引，反而相互排斥。太陽系只要一顆恆星，不需要伴星，無法容許兩個發光體。如太陽射手男性，常跟太陽牡羊及獅子的男性結交為友，卻只有異性友人會是太陽射手，女性則沒有差別性。太陽本身有挑戰相時，對合相者也莫名產生敵意，但在非家庭親人間，為關係帶來挑戰與刺激，較有建設性。
     *
     *
     * 在職場關係中，太陽是主要行事作風，在外人看來太相像，除非年齡差距夠大，否則不易共事。管理階級可能找相同太陽作未來接班人，但不會挑來當現任副手。太陽競相發光的特質，如在火象星座更強勢，男性會強烈感受同星座者的對方意志，彼此較勁明顯，但女性較少察覺到威脅性。太陽是個人理解、控制、想表達的人格，力量強烈外顯，合相代表價值觀及人生態度的認同，強化彼此的陽剛屬性。
     *
     *
     * 在兩性關係中，太陽以對相力量最大，合相性質太相似，雖有自然的知心，但生命觀點相同流於狹隘。異性的太陽合相時，多了不同的性別吸引，可以談得來，異性間易成為朋友，比同性好相處，但不足以成為情侶。太陽合相的男女很少成為情感配偶，除非其他有力相位促成，關係本質不純粹是男女朋友，雙方可能是因工作結合，但一山不容二虎，相處有排斥性。除非本命宮位在對宮，關係方有互補可能，如一方太陽在獅子座一宮，另一方在獅子座七宮，代表環境有機緣長期發展，雙方有默契地各行其是，相敬如賓。
     * planet_name_a : 太陽
     * planet_name_b : 太陽
     * score : 1
     * type : 合
     *
     * Separating Aspect出相位
     * Applying Aspect入相位
     */
    @Id
    @SerializedName("id")
    var id: Long = 0

    @SerializedName("degree")
    var degree = 0

    @SerializedName("description")
    var description: String = ""

    @SerializedName("planet_name_a")
    var planetNameA: String = ""

    @SerializedName("planet_name_b")
    var planetNameB: String = ""

    @SerializedName("score")
    var score = 0

    @SerializedName("type")
    var type: String = ""
    var nameA: String = ""
    var nameB: String = ""
    var orb = 0.0

    var isApplying = true // 入相位（Applying Aspect）出相位（Separating Aspect）
    var direction = ""
}