package com.one.astrology.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Announcement(
    val id: String,
    val title: String,
    val content: String,
    val publishTime: String,
    val type: AnnouncementType,
    val link: String? = null,
    val isVisible: Boolean = false
) : Parcelable

enum class AnnouncementType {
    IMPORTANT,
    ACTIVITY,
    SYSTEM,
    NORMAL
} 