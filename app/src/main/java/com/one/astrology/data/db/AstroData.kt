package com.one.astrology.data.db

import android.os.Parcelable
import kotlinx.parcelize.Parcelize


@Parcelize
data class AstroData(
    var id: Long? = 0,
    var name: String? = "",
    var link: String? = "",
    var birthName: String? = "",
    var time: String? = null,
    var place: String? = "",
    var latitude: String? = "",
    var longitude: String? = "",
    var roddenRating: String? = "",
    var biography: String? = "",
    var categories: String? = "",
    var gender: String? = "",
    var createTime: Long? = null,
    var wikipedia: String? = "",
    var timeInMillis: Long? = 0,
    var biographyCn: String? = null,
) : Parcelable