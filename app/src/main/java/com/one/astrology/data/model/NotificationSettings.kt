package com.one.astrology.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * 推播通知設定資料模型
 */
@Entity
@Parcelize
data class NotificationSettings(
    @Id
    @SerializedName("id")
    var id: Long = 0,

    /**
     * 是否啟用每日行運推播
     */
    @SerializedName("isDailyTransitEnabled")
    var isDailyTransitEnabled: Boolean = true,

    /**
     * 推播時間 - 小時 (0-23)
     */
    @SerializedName("notificationHour")
    var notificationHour: Int = 10,

    /**
     * 推播時間 - 分鐘 (0-59)
     */
    @SerializedName("notificationMinute")
    var notificationMinute: Int = 0,

    /**
     * 是否啟用重要事件推播
     */
    @SerializedName("isImportantEventsEnabled")
    var isImportantEventsEnabled: Boolean = true,

    /**
     * 是否啟用相位事件推播
     */
    @SerializedName("isAspectEventsEnabled")
    var isAspectEventsEnabled: Boolean = true,

    /**
     * 是否啟用宮位變化推播
     */
    @SerializedName("isHouseChangeEnabled")
    var isHouseChangeEnabled: Boolean = true,

    /**
     * 是否啟用星座切換推播
     */
    @SerializedName("isSignChangeEnabled")
    var isSignChangeEnabled: Boolean = true,

    /**
     * 是否啟用逆行事件推播
     */
    @SerializedName("isRetrogradeEnabled")
    var isRetrogradeEnabled: Boolean = false,

    /**
     * 最小重要性等級（存儲為 Int）
     */
    @SerializedName("minimumImportance")
    var minimumImportanceValue: Int = TransitImportance.MEDIUM.ordinal,

    /**
     * 相位容許度設定（度數）
     */
    @SerializedName("aspectOrb")
    var aspectOrb: Double = 3.0,

    /**
     * 提前通知天數（在事件發生前幾天開始推播）
     */
    @SerializedName("advanceNoticeDays")
    var advanceNoticeDays: Int = 3,

    /**
     * 是否啟用聲音
     */
    @SerializedName("isSoundEnabled")
    var isSoundEnabled: Boolean = true,

    /**
     * 是否啟用震動
     */
    @SerializedName("isVibrationEnabled")
    var isVibrationEnabled: Boolean = true,

    /**
     * 最後更新時間
     */
    @SerializedName("lastUpdateTime")
    var lastUpdateTime: Long = System.currentTimeMillis(),

    /**
     * 關聯的出生資料ID（如果為空則使用當前選中的出生資料）
     */
    @SerializedName("birthDataId")
    var birthDataId: Long? = null

) : Parcelable, Serializable {

    /**
     * 最小重要性等級（enum 屬性）
     */
    var minimumImportance: TransitImportance
        get() = TransitImportance.values().getOrElse(minimumImportanceValue) { TransitImportance.MEDIUM }
        set(value) { minimumImportanceValue = value.ordinal }

    /**
     * 獲取推播時間字串 (HH:mm 格式)
     */
    fun getNotificationTimeString(): String {
        return String.format("%02d:%02d", notificationHour, notificationMinute)
    }

    /**
     * 設定推播時間
     */
    fun setNotificationTime(hour: Int, minute: Int) {
        this.notificationHour = hour.coerceIn(0, 23)
        this.notificationMinute = minute.coerceIn(0, 59)
        this.lastUpdateTime = System.currentTimeMillis()
    }

    /**
     * 檢查是否應該推播指定的事件
     */
    fun shouldNotifyEvent(event: TransitEvent): Boolean {
        // 檢查總開關
        if (!isDailyTransitEnabled) return false

        // 檢查重要性等級
        if (event.importance.level < minimumImportance.level) return false

        // 檢查事件類型開關
        return when {
            event.isAspectEvent() -> isAspectEventsEnabled
            event.isHouseChangeEvent() -> isHouseChangeEnabled
            event.isSignChangeEvent() -> isSignChangeEnabled
            event.isRetrogradeEvent() -> isRetrogradeEnabled
            else -> isImportantEventsEnabled
        }
    }

    /**
     * 檢查相位是否在容許度範圍內
     */
    fun isWithinOrb(orb: Double): Boolean {
        return orb <= aspectOrb
    }

    companion object {
        /**
         * 獲取預設設定
         */
        fun getDefault(): NotificationSettings {
            return NotificationSettings().apply {
                isDailyTransitEnabled = true
                notificationHour = 10
                notificationMinute = 0
                isImportantEventsEnabled = true
                isAspectEventsEnabled = true
                isHouseChangeEnabled = true
                isSignChangeEnabled = true
                isRetrogradeEnabled = false
                minimumImportance = TransitImportance.MEDIUM
                aspectOrb = 3.0
                advanceNoticeDays = 3
                isSoundEnabled = true
                isVibrationEnabled = true
            }
        }
    }
}
