package com.one.astrology.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map


val Context.settingsDataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

object SettingsPreferencesDataStore {

    private val fcmToken = stringPreferencesKey("fcm_token")
    private val filterSort = stringPreferencesKey("filter_sort")

    suspend fun putFcmToken(context: Context, name: String) {
        context.settingsDataStore.edit { setting ->
            setting[fcmToken] = name
        }
    }

    suspend fun getFcmToken(context: Context): String {
        val nameFlow = context.settingsDataStore.data.map { settings ->
            settings[fcmToken] ?: ""
        }
        return nameFlow.first()
    }

    suspend fun putFilterSort(context: Context, data: Int) {
        context.settingsDataStore.edit { setting ->
            setting[filterSort] = data.toString()
        }
    }

    suspend fun getFilterSort(context: Context): Int {
        val nameFlow = context.settingsDataStore.data.map { settings ->
            settings[filterSort] ?: ""
        }
        val number = nameFlow.first()
        if (number.isEmpty()) {
            return 0
        }
        return nameFlow.first().toInt()
    }
}