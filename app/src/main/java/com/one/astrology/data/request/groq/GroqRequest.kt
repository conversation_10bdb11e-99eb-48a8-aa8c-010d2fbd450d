package com.one.astrology.data.request.groq

data class GroqRequest(
    val model: String = "deepseek-r1-distill-llama-70b", // llama3-8b-8192
    val messages: List<Message>,
    val temperature: Double = 0.7
)

data class Message(
    val role: String, // "user" 或 "system" 或 "assistant"
    val content: String
)

data class GroqResponse(
    val choices: List<Choice>
)

data class Choice(
    val message: Message
)
