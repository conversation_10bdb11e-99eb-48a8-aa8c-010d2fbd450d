package com.one.astrology.data.request.openai


import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class OpenResponse(
    @SerializedName("choices")
    val choices: List<Choice>,
    @SerializedName("created")
    val created: Int, // 1586839808
    @SerializedName("id")
    val id: String, // cmpl-GERzeJQ4lvqPk8SkZu4XMIuR
    @SerializedName("model")
    val model: String, // text-davinci:003
    @SerializedName("object")
    val objectX: String, // text_completion
    @SerializedName("usage")
    val usage: Usage
) {
    @Keep
    data class Choice(
        @SerializedName("finish_reason")
        val finishReason: String, // length
        @SerializedName("index")
        val index: Int, // 0
        @SerializedName("logprobs")
        val logprobs: Any, // null
        @SerializedName("text")
        val text: String // This is indeed a test
    )

    @Keep
    data class Usage(
        @SerializedName("completion_tokens")
        val completionTokens: Int, // 7
        @SerializedName("prompt_tokens")
        val promptTokens: Int, // 5
        @SerializedName("total_tokens")
        val totalTokens: Int // 12
    )
}