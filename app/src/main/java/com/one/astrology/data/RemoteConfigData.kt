package com.one.astrology.data

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class RemoteConfigData(
    @SerializedName("adPosition")
    val adPosition: Int, // 0
    @SerializedName("adType")
    val adType: Int, // 0
    @SerializedName("updateType")
    val updateType: Int, // 0
    @SerializedName("apkUrl")
    val apkUrl: String, // 系統維護中。
    @SerializedName("isForceUpdate")
    val isForceUpdate: Boolean, // false
    @SerializedName("isMaintenance")
    val isMaintenance: Boolean, // false
    @SerializedName("isOpenAd")
    val isOpenAd: Boolean, // true
    @SerializedName("isOpenArticle")
    val isOpenArticle: Boolean, // true
    @SerializedName("isOpenBilling")
    val isOpenBilling: Boolean, // true
    @SerializedName("isOpenLogin")
    val isOpenLogin: <PERSON><PERSON><PERSON>, // false
    @SerializedName("isOpenFeedback")
    val isOpenFeedback: Boolean, // false
    @SerializedName("maintenanceMessage")
    val maintenanceMessage: String, // 系統維護中。
    @SerializedName("versionCode")
    val versionCode: Int // 30
)