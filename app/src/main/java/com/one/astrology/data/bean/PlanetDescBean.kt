package com.one.astrology.data.bean

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import java.io.Serializable


@Keep
data class PlanetDescBean(
    @SerializedName("id")
    var id: Int? = null,

    @SerializedName("name")
    var name: String? = null,

    @SerializedName("ruler")
    var ruler: String? = null,

    @SerializedName("exalt")
    var exalt: String? = null,

    @SerializedName("detriment")
    var detriment: String? = null,

    @SerializedName("fall")
    var fall: String? = null,

    @SerializedName("desc")
    var desc: String? = null,

    @SerializedName("positive")
    var positive: String? = null,

    @SerializedName("negative")
    var negative: String? = null,

    ) : Serializable