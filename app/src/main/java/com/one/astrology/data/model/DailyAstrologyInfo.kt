package com.one.astrology.data.model

import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.db.AspectData
import java.io.Serializable

/**
 * 每日星象信息
 */
data class DailyAstrologyInfo(
    val date: String, // 日期，例如：2023-10-28
    val moonPhase: String? = null, // 月相，例如：上弦月、滿月等
    val planets: List<PlanetBean>, // 行星位置列表
    val importantAspects: List<AspectData>, // 重要相位列表
    var address: String,
    var hasPermissions: Boolean
) : Serializable

/**
 * 行星每日狀態
 */
data class PlanetDailyStatus(
    val id: Int, // 行星ID
    val name: String, // 行星名稱，例如：太陽、月亮
    val symbol: String, // 行星符號
    val color: String, // 行星顏色 (Hex值不含#)
    val signName: String, // 所在星座名稱，例如：白羊座
    val degree: Double, // 所在星座度數
    val isRetrograde: Boolean // 是否逆行
)