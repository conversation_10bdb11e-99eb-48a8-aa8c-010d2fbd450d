package com.one.astrology.data

import com.one.astrology.data.bean.SignBean
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import swisseph.SweConst
import java.io.Serializable


class Houses : Serializable {

    var cusps: MutableList<Double> = ArrayList()
    var ascmc: MutableList<Double> = ArrayList()

    var signBeanList = ArrayList<SignBean>()

    constructor()

    constructor(cusps: MutableList<Double>, ascmc: MutableList<Double>) {
        this.cusps = cusps
        this.ascmc = ascmc
        initSign()
    }

    fun initSign() {
        signBeanList.clear()
        for (i in 1 until cusps.size) {
            val angle = cusps[i]
            val strings: ArrayList<String> = EphemerisUtil.szZodiac(angle)
            val signBean: SignBean = EphemerisUtil.signList[strings[0].toInt()]
            val sign = SignBean(signBean)
            sign.angle = angle
            val degree = strings[1] + "°" + strings[2]
            sign.degree = "${strings[1]}°"
            sign.minute = strings[2]
            LogUtil.i("宮位 $i 宮 ${sign.chName}  angle : $angle degree : $degree")
            signBeanList.add(sign)
        }
    }

    // 宮頭
    fun getHouseCusps(index: Int): Double {
        return cusps[index]
    }

    /**
    cusp - (double[13]) The house cusps are returned here in cusp[1...12] for the houses 1 to 12.
    ascmc - (double[10]) The special points like ascendant etc. are returned here. See the list above.

    ascmc[0] = ascendant
    ascmc[1] = mc
    ascmc[2] = armc (= sidereal time)
    ascmc[3] = vertex
    ascmc[4] = equatorial ascendant
    ascmc[5] = co-ascendant (Walter Koch)
    ascmc[6] = co-ascendant (Michael Munkasey)
    ascmc[7] = polar ascendant (Michael Munkasey)
    ascmc[8] = reserved for future use
    ascmc[9] = reserved for future use
     */
    fun getAsc(): Double {
        if (ascmc.size <= SweConst.SE_ASC) {
            return 0.0
        }
        return ascmc[SweConst.SE_ASC]
    }

    fun getDes(): Double {
        return if (ascmc[SweConst.SE_ASC] + 180 > 360) {
            ascmc[SweConst.SE_ASC] - 180
        } else {
            ascmc[SweConst.SE_ASC] + 180
        }
    }

    fun getMC(): Double {
        return ascmc[SweConst.SE_MC]
    }

    fun getIc(): Double {
        return if (ascmc[SweConst.SE_MC] + 180 > 360) {
            ascmc[SweConst.SE_MC] - 180
        } else {
            ascmc[SweConst.SE_MC] + 180
        }
    }

    fun getVertex(): Double {
        return ascmc[SweConst.SE_VERTEX]
    }
}