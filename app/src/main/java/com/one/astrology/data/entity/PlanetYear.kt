package com.one.astrology.data.entity

data class PlanetYear(
    var primary: String? = "",
    var year: Int? = 0,
    var second: String? = "",
    var startTime: Long? = 0L,
)

/**
 *
 * 法達星限法是一種時間主星系統 (Time-lord system)，以 75 年為一個循環，分為 9 個大運（七曜、北交點與南交點），
 * 七曜大運之中，又再均分為 7 個副運。
 * 計算Firdaria，第一步要先知道星盤是屬於日間盤或是夜間盤。判斷方法是依據太陽的位置而定，
 * 若太陽在第一宮到第六宮之間（地平面下）是夜間盤；反之，若是太陽在第七宮到第十二宮之間（地平面上）是日間盤。
 * 知道星盤是日間盤或夜間盤之後，你就可以按照下列Firdaria 順序，得知從出生開始每個「大運」的主星，以及掌管的年數：
 *
 * 白天盤：太陽（10年）→ 金星（8年）→ 水星（13年）→ 月亮（9年）→ 土星（11年）→ 木星（12年）→ 火星（7年）→ 北交點（3年）→ 南交點（2年）
 * 夜間盤：月亮（9年）→ 土星（11年）→ 木星（12年）→ 火星（7年）→ 太陽（10年）→ 金星（8年）→ 水星（13年）→ 北交點（3年）→ 南交點（2年）
 * 除了北交點與南交點以外，每一顆行星所掌管的大運年數，都會被平均切分為七個等份，分別再指派一顆行星，作為「副運」主星。
 * 副運主星的分配方式很簡單，第一個都是從「大運」主星開始，然後依照上述行星順序分配。
 *
 */
val dayArray = arrayListOf(
    PlanetYear("太陽", 10),
    PlanetYear("金星", 8),
    PlanetYear("水星", 13),
    PlanetYear("月亮", 9),
    PlanetYear("土星", 11),
    PlanetYear("木星", 12),
    PlanetYear("火星", 7),
    PlanetYear("北交點", 3),
    PlanetYear("南交點", 2),
)

val nightArray = arrayListOf(
    PlanetYear("月亮", 9),
    PlanetYear("土星", 11),
    PlanetYear("木星", 12),
    PlanetYear("火星", 7),
    PlanetYear("太陽", 10),
    PlanetYear("金星", 8),
    PlanetYear("水星", 13),
    PlanetYear("北交點", 3),
    PlanetYear("南交點", 2),
)