package com.one.astrology.data.model

import androidx.compose.ui.graphics.Color
import java.util.Date

/**
 * 星象事件數據模型
 */
data class AstrologyEvent(
    val id: String,
    val date: Date,
    val type: EventType,
    val title: String,
    val description: String,
    val importance: EventImportance,
    val planetIds: List<Int> = emptyList(),
    val aspectType: Int? = null,
    val fromSign: Int? = null,
    val toSign: Int? = null,
    val isExact: Boolean = false,
    val orb: Double = 0.0
) {
    /**
     * 獲取事件顏色
     */
    fun getEventColor(): Color {
        return when (type) {
            EventType.PLANET_ASPECT -> Color(0xFF2196F3) // 藍色
            EventType.PLANET_SIGN_CHANGE -> Color(0xFF4CAF50) // 綠色
            EventType.MOON_PHASE -> Color(0xFF9C27B0) // 紫色
            EventType.PLANET_RETROGRADE -> Color(0xFFFF9800) // 橙色
            EventType.SOLAR_TERM -> Color(0xFFFFEB3B) // 黃色
            EventType.ECLIPSE -> Color(0xFFF44336) // 紅色
        }
    }

    /**
     * 獲取事件圖標
     */
    fun getEventIcon(): String {
        return when (type) {
            EventType.PLANET_ASPECT -> "⚹"
            EventType.PLANET_SIGN_CHANGE -> "→"
            EventType.MOON_PHASE -> "☽"
            EventType.PLANET_RETROGRADE -> "℞"
            EventType.SOLAR_TERM -> "☀"
            EventType.ECLIPSE -> "🌑"
        }
    }
}

/**
 * 星象事件類型
 */
enum class EventType(val displayName: String) {
    PLANET_ASPECT("行星相位"),
    PLANET_SIGN_CHANGE("行星換座"),
    MOON_PHASE("月相變化"),
    PLANET_RETROGRADE("行星逆行"),
    SOLAR_TERM("節氣變化"),
    ECLIPSE("日月蝕")
}

/**
 * 事件重要性
 */
enum class EventImportance(val level: Int, val displayName: String) {
    LOW(1, "一般"),
    MEDIUM(2, "重要"),
    HIGH(3, "非常重要"),
    CRITICAL(4, "極其重要")
}

/**
 * 月相類型
 */
enum class MoonPhase(val displayName: String, val symbol: String) {
    NEW_MOON("新月", "🌑"),
    WAXING_CRESCENT("娥眉月", "🌒"),
    FIRST_QUARTER("上弦月", "🌓"),
    WAXING_GIBBOUS("盈凸月", "🌔"),
    FULL_MOON("滿月", "🌕"),
    WANING_GIBBOUS("虧凸月", "🌖"),
    LAST_QUARTER("下弦月", "🌗"),
    WANING_CRESCENT("殘月", "🌘")
}

/**
 * 相位類型
 */
enum class AspectType(val angle: Double, val displayName: String, val symbol: String) {
    CONJUNCTION(0.0, "合相", "☌"),
    SEXTILE(60.0, "六分相", "⚹"),
    SQUARE(90.0, "四分相", "□"),
    TRINE(120.0, "三分相", "△"),
    OPPOSITION(180.0, "對分相", "☍"),
//    SEMI_SEXTILE(30.0, "半六分相", "⚺"),
//    SEMI_SQUARE(45.0, "半四分相", "∠"),
//    SESQUIQUADRATE(135.0, "倍半四分相", "⚼"),
//    QUINCUNX(150.0, "補十二分相", "⚻")
}

/**
 * 節氣類型
 */
enum class SolarTerm(val displayName: String, val longitude: Double) {
    SPRING_EQUINOX("春分", 0.0),
    QINGMING("清明", 15.0),
    GRAIN_RAIN("穀雨", 30.0),
    SUMMER_BEGINS("立夏", 45.0),
    GRAIN_BUDS("小滿", 60.0),
    GRAIN_IN_EAR("芒種", 75.0),
    SUMMER_SOLSTICE("夏至", 90.0),
    MINOR_HEAT("小暑", 105.0),
    MAJOR_HEAT("大暑", 120.0),
    AUTUMN_BEGINS("立秋", 135.0),
    STOPPING_HEAT("處暑", 150.0),
    WHITE_DEWS("白露", 165.0),
    AUTUMN_EQUINOX("秋分", 180.0),
    COLD_DEWS("寒露", 195.0),
    FROSTS_DESCENT("霜降", 210.0),
    WINTER_BEGINS("立冬", 225.0),
    MINOR_SNOW("小雪", 240.0),
    MAJOR_SNOW("大雪", 255.0),
    WINTER_SOLSTICE("冬至", 270.0),
    MINOR_COLD("小寒", 285.0),
    MAJOR_COLD("大寒", 300.0),
    SPRING_BEGINS("立春", 315.0),
    RAIN_WATER("雨水", 330.0),
    INSECTS_AWAKEN("驚蟄", 345.0)
}

/**
 * 蝕相類型
 */
enum class EclipseType(val displayName: String) {
    SOLAR_ECLIPSE("日蝕"),
    LUNAR_ECLIPSE("月蝕"),
    PARTIAL_SOLAR("日偏蝕"),
    TOTAL_SOLAR("日全蝕"),
    ANNULAR_SOLAR("日環蝕"),
    PARTIAL_LUNAR("月偏蝕"),
    TOTAL_LUNAR("月全蝕"),
    PENUMBRAL_LUNAR("半影月蝕")
}

/**
 * 行星逆行狀態
 */
data class RetrogradeStatus(
    val planetId: Int,
    val planetName: String,
    val isRetrograde: Boolean,
    val startDate: Date?,
    val endDate: Date?
)

/**
 * 星象事件工廠類
 */
object AstrologyEventFactory {
    
    /**
     * 創建行星相位事件
     */
    fun createAspectEvent(
        date: Date,
        planet1Id: Int,
        planet1Name: String,
        planet2Id: Int,
        planet2Name: String,
        aspectType: AspectType,
        orb: Double,
        isExact: Boolean = false
    ): AstrologyEvent {
        val importance = when {
            isExact -> EventImportance.HIGH
            orb <= 1.0 -> EventImportance.MEDIUM
            else -> EventImportance.LOW
        }
        
        return AstrologyEvent(
            id = "aspect_${planet1Id}_${planet2Id}_${aspectType.name}_${date.time}",
            date = date,
            type = EventType.PLANET_ASPECT,
            title = "$planet1Name ${aspectType.displayName} $planet2Name",
            description = "${planet1Name}與${planet2Name}形成${aspectType.displayName}，容許度${String.format("%.1f", orb)}°",
            importance = importance,
            planetIds = listOf(planet1Id, planet2Id),
            aspectType = aspectType.ordinal,
            orb = orb,
            isExact = isExact
        )
    }
    
    /**
     * 創建行星換座事件
     */
    fun createSignChangeEvent(
        date: Date,
        planetId: Int,
        planetName: String,
        fromSign: Int,
        toSign: Int,
        fromSignName: String,
        toSignName: String
    ): AstrologyEvent {
        val importance = when (planetId) {
            0, 1 -> EventImportance.HIGH // 太陽、月亮
            2, 3, 4 -> EventImportance.MEDIUM // 水星、金星、火星
            else -> EventImportance.LOW
        }
        
        return AstrologyEvent(
            id = "sign_change_${planetId}_${fromSign}_${toSign}_${date.time}",
            date = date,
            type = EventType.PLANET_SIGN_CHANGE,
            title = "$planetName 進入 $toSignName",
            description = "${planetName}從${fromSignName}進入${toSignName}",
            importance = importance,
            planetIds = listOf(planetId),
            fromSign = fromSign,
            toSign = toSign
        )
    }
    
    /**
     * 創建月相事件
     */
    fun createMoonPhaseEvent(
        date: Date,
        moonPhase: MoonPhase
    ): AstrologyEvent {
        val importance = when (moonPhase) {
            MoonPhase.NEW_MOON, MoonPhase.FULL_MOON -> EventImportance.HIGH
            MoonPhase.FIRST_QUARTER, MoonPhase.LAST_QUARTER -> EventImportance.MEDIUM
            else -> EventImportance.LOW
        }
        
        return AstrologyEvent(
            id = "moon_phase_${moonPhase.name}_${date.time}",
            date = date,
            type = EventType.MOON_PHASE,
            title = moonPhase.displayName,
            description = "月相變化：${moonPhase.displayName}",
            importance = importance,
            planetIds = listOf(1) // 月亮
        )
    }
    
    /**
     * 創建行星逆行事件
     */
    fun createRetrogradeEvent(
        date: Date,
        planetId: Int,
        planetName: String,
        isStarting: Boolean
    ): AstrologyEvent {
        val action = if (isStarting) "開始" else "結束"
        val importance = when (planetId) {
            2 -> EventImportance.HIGH // 水星逆行
            3, 4 -> EventImportance.MEDIUM // 金星、火星逆行
            else -> EventImportance.LOW
        }
        
        return AstrologyEvent(
            id = "retrograde_${planetId}_${if (isStarting) "start" else "end"}_${date.time}",
            date = date,
            type = EventType.PLANET_RETROGRADE,
            title = "$planetName ${action}逆行",
            description = "${planetName}${action}逆行運動",
            importance = importance,
            planetIds = listOf(planetId)
        )
    }
    
    /**
     * 創建節氣事件
     */
    fun createSolarTermEvent(
        date: Date,
        solarTerm: SolarTerm
    ): AstrologyEvent {
        val importance = when (solarTerm) {
            SolarTerm.SPRING_EQUINOX, SolarTerm.SUMMER_SOLSTICE,
            SolarTerm.AUTUMN_EQUINOX, SolarTerm.WINTER_SOLSTICE -> EventImportance.HIGH
            else -> EventImportance.MEDIUM
        }
        
        return AstrologyEvent(
            id = "solar_term_${solarTerm.name}_${date.time}",
            date = date,
            type = EventType.SOLAR_TERM,
            title = solarTerm.displayName,
            description = "節氣：${solarTerm.displayName}",
            importance = importance,
            planetIds = listOf(0) // 太陽
        )
    }
    
    /**
     * 創建日月蝕事件
     */
    fun createEclipseEvent(
        date: Date,
        eclipseType: EclipseType,
        magnitude: Double = 0.0
    ): AstrologyEvent {
        return AstrologyEvent(
            id = "eclipse_${eclipseType.name}_${date.time}",
            date = date,
            type = EventType.ECLIPSE,
            title = eclipseType.displayName,
            description = "${eclipseType.displayName}，食分：${String.format("%.2f", magnitude)}",
            importance = EventImportance.CRITICAL,
            planetIds = if (eclipseType.displayName.contains("日")) listOf(0, 1) else listOf(1) // 太陽月亮或月亮
        )
    }
}
