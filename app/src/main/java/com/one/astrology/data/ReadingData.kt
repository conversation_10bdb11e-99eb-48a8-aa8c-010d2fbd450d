package com.one.astrology.data

import android.os.Parcelable
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import kotlinx.parcelize.Parcelize

@Parcelize
data class ReadingData(
    var title: String,
    var content: String,
    var chartInfo: String,
    var chart: Chart? = null,
    var birthDataA: BirthData? = null,
    var birthDataB: BirthData? = null,
) : Parcelable