package com.one.astrology.data.entity

import com.google.gson.annotations.SerializedName
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.io.Serializable

@Entity
class TransitData : Serializable {
    @Id(assignable = true)
    var id: Long = 0

    @SerializedName("createTime")
    var createTime: Long = 0

    @SerializedName("house")
    var house: String = ""

    @SerializedName("timeString")
    var timeString: String = ""

    @SerializedName("name")
    var name: String = ""
}