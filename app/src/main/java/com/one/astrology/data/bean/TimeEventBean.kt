package com.one.astrology.data.bean

/**
 * 時間事件數據模型
 * 用於表示年表中的事件
 */
data class TimeEventBean(
    /**
     * 事件發生的日期（毫秒時間戳）
     */
    val date: Long,
    
    /**
     * 事件類型
     * 可以是行運、推運、太陽返照等
     */
    val type: Int,
    
    /**
     * 第一個行星ID
     * 例如：行運行星ID或推運行星ID
     */
    val firstPlanetId: Int = -1,
    
    /**
     * 第二個行星ID
     * 例如：本命行星ID
     */
    val secondPlanetId: Int = -1,
    
    /**
     * 宮位ID
     * 當事件涉及宮位時使用
     */
    val houseId: Int = -1,
    
    /**
     * 相位類型
     * 例如：合相、刑相、拱相等
     */
    val aspectType: Int = -1,
    
    /**
     * 事件描述
     */
    val description: String = "",
    
    /**
     * 事件強度（0-10）
     * 數值越高表示影響越強
     */
    val strength: Int = calculateStrength(type, firstPlanetId, secondPlanetId, aspectType),
    
    /**
     * 事件性質
     * 正面、負面或中性
     */
    val nature: Int = calculateNature(firstPlanetId, secondPlanetId, aspectType),
    
    /**
     * 事件持續時間（毫秒）
     * 0表示瞬時事件
     */
    val duration: Long = calculateDuration(type, firstPlanetId, aspectType)
) {
    companion object {
        // 事件類型常量
        const val TYPE_TRANSIT = 1           // 行運
        const val TYPE_PROGRESSION = 2       // 推運
        const val TYPE_SOLAR_RETURN = 3      // 太陽返照
        const val TYPE_LUNAR_RETURN = 4      // 月亮返照
        const val TYPE_ECLIPSE = 5           // 日食/月食
        const val TYPE_TRANSIT_TO_HOUSE = 6  // 行運對宮位
        const val TYPE_PROGRESSION_TO_HOUSE = 7 // 推運對宮位
        
        // 事件性質常量
        const val NATURE_POSITIVE = 1        // 正面
        const val NATURE_NEUTRAL = 0         // 中性
        const val NATURE_NEGATIVE = -1       // 負面
        
        /**
         * 計算事件強度
         * @param type 事件類型
         * @param firstPlanetId 第一個行星ID
         * @param secondPlanetId 第二個行星ID
         * @param aspectType 相位類型
         * @return 事件強度（0-10）
         */
        private fun calculateStrength(
            type: Int,
            firstPlanetId: Int,
            secondPlanetId: Int,
            aspectType: Int
        ): Int {
            var strength = 5 // 默認中等強度
            
            // 根據事件類型調整強度
            when (type) {
                TYPE_TRANSIT -> strength += 0
                TYPE_PROGRESSION -> strength += 1
                TYPE_SOLAR_RETURN -> strength += 2
                TYPE_LUNAR_RETURN -> strength += 1
                TYPE_ECLIPSE -> strength += 3
                TYPE_TRANSIT_TO_HOUSE -> strength -= 1
                TYPE_PROGRESSION_TO_HOUSE -> strength -= 1
            }
            
            // 根據行星重要性調整強度
            if (isPrimaryPlanet(firstPlanetId)) strength += 1
            if (isPrimaryPlanet(secondPlanetId)) strength += 1
            
            // 根據相位類型調整強度
            when (aspectType) {
                1 -> strength += 2  // 合相
                2 -> strength += 0  // 六分相
                3 -> strength += 1  // 刑相
                4 -> strength += 1  // 拱相
                5 -> strength += 2  // 沖相
            }
            
            // 確保強度在0-10範圍內
            return when {
                strength < 0 -> 0
                strength > 10 -> 10
                else -> strength
            }
        }
        
        /**
         * 計算事件性質
         * @param firstPlanetId 第一個行星ID
         * @param secondPlanetId 第二個行星ID
         * @param aspectType 相位類型
         * @return 事件性質
         */
        private fun calculateNature(
            firstPlanetId: Int,
            secondPlanetId: Int,
            aspectType: Int
        ): Int {
            // 根據相位類型判斷基本性質
            val baseNature = when (aspectType) {
                1 -> NATURE_NEUTRAL  // 合相（取決於行星）
                2 -> NATURE_POSITIVE // 六分相
                3 -> NATURE_NEGATIVE // 刑相
                4 -> NATURE_POSITIVE // 拱相
                5 -> NATURE_NEGATIVE // 沖相
                else -> NATURE_NEUTRAL
            }
            
            // 如果是合相，根據行星組合判斷性質
            if (aspectType == 1) {
                if (isBeneficPlanet(firstPlanetId) && isBeneficPlanet(secondPlanetId)) {
                    return NATURE_POSITIVE
                }
                if (isMaleficPlanet(firstPlanetId) && isMaleficPlanet(secondPlanetId)) {
                    return NATURE_NEGATIVE
                }
            }
            
            return baseNature
        }
        
        /**
         * 計算事件持續時間
         * @param type 事件類型
         * @param firstPlanetId 第一個行星ID
         * @param aspectType 相位類型
         * @return 持續時間（毫秒）
         */
        private fun calculateDuration(
            type: Int,
            firstPlanetId: Int,
            aspectType: Int
        ): Long {
            // 默認持續時間（1天）
            var durationDays = 1L
            
            // 根據事件類型調整持續時間
            when (type) {
                TYPE_TRANSIT -> {
                    // 根據行星速度調整持續時間
                    durationDays = when (firstPlanetId) {
                        0 -> 1L  // 太陽
                        1 -> 1L  // 月亮
                        2 -> 1L  // 水星
                        3 -> 2L  // 金星
                        4 -> 3L  // 火星
                        5 -> 7L  // 木星
                        6 -> 10L // 土星
                        7 -> 14L // 天王星
                        8 -> 14L // 海王星
                        9 -> 14L // 冥王星
                        else -> 1L
                    }
                }
                TYPE_PROGRESSION -> durationDays = 30L // 推運影響約一個月
                TYPE_SOLAR_RETURN -> durationDays = 365L // 太陽返照影響一年
                TYPE_LUNAR_RETURN -> durationDays = 30L // 月亮返照影響一個月
                TYPE_ECLIPSE -> durationDays = 90L // 日食/月食影響約三個月
            }
            
            // 轉換為毫秒
            return durationDays * 24 * 60 * 60 * 1000
        }
        
        /**
         * 判斷是否為主要行星（太陽、月亮、上升點）
         * @param planetId 行星ID
         * @return 是否為主要行星
         */
        private fun isPrimaryPlanet(planetId: Int): Boolean {
            return planetId in listOf(0, 1, 10) // 太陽、月亮、上升點
        }
        
        /**
         * 判斷是否為吉星（金星、木星）
         * @param planetId 行星ID
         * @return 是否為吉星
         */
        private fun isBeneficPlanet(planetId: Int): Boolean {
            return planetId in listOf(3, 5) // 金星、木星
        }
        
        /**
         * 判斷是否為凶星（土星、火星）
         * @param planetId 行星ID
         * @return 是否為凶星
         */
        private fun isMaleficPlanet(planetId: Int): Boolean {
            return planetId in listOf(4, 6) // 火星、土星
        }
    }
} 