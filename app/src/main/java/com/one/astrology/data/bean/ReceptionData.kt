package com.one.astrology.data.bean

/**
 * 接納 Reception
 * 互容 Mutual reception
 */
data class ReceptionData(
    var planetNameA: String = "",
    var planetNameB: String = "",
    var typeA: Dignities = Dignities.NONE,
    var typeB: Dignities = Dignities.NONE,
    var type: String = "",
    var isFind: Boolean = false,
    var hasAspect: Boolean = false,
    var isReception: Boolean = false,
    var isMutual: Boolean = false
)

/**
 * 廟(own sign)、旺(exaltation)、三分(triplicity)、界(term)和面(face)，陷(debilitation)，弱(fall)所包含的星座度數
 */
enum class Dignities {
    Ruler,
    Exalt,
    Triplicity,
    Term,
    Face,
    Detriment,
    Fall,
    NONE
}