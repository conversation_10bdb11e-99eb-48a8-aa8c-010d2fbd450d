package com.one.astrology.data.type

import android.os.Parcelable
import com.one.astrology.R
import kotlinx.parcelize.Parcelize

@Parcelize
enum class Chart(val type: Int, val nameEng: String): Parcelable {
    Celestial(R.string.celestial, "Celestial"),
    Natal(R.string.natal, "Natal"), // 本命盤 / 出生盤 / Natal chart / Birth chart
    Transit(
        R.string.transits,
        "Transit"
    ), // 行運盤 西方占星學稱為「Transit」/「Transit Chart」。比較短時期或近日的。建基於出生圖，把需要問事當日星體位置以同心圓方式排成的複式命盤。
    SecondaryProgression(
        R.string.secondary_progression,
        "SecondaryProgression"
    ), // 月亮次限推運法 人出生後每日的行運就是代表每年的行運 365.25
    SecondaryProgressionSynastry(
        R.string.secondary_progression_synastry,
        "SecondaryProgressionSynastry"
    ),
    TertiaryProgression(
        R.string.tertiary_progression,
        "TertiaryProgression"
    ), // 月亮三限推運法 人出生後一天代表一月來進行推算 27.321661
    TertiaryProgressionSynastry(
        R.string.tertiary_progression_synastry,
        "TertiaryProgressionSynastry"
    ),
    SolarReturn(
        R.string.solar_return,
        "SolarReturn"
    ), // 日返 太陽返照盤 Solar Return（Solar Revolution）太陽回歸法

    //  日返比1 日返比2
    SolarArc(R.string.solar_arc, "SolarArc"),//  日弧 太陽弧推運法 太陽弧推運法(Solar Arc)
    LunarReturn(R.string.lunar_return, "LunarReturn"),//  Lunar Return Chart 月亮回歸圖

    // 月返 月亮返照法 月返比1 月返比2
    // 法達盤
    Firdaria(R.string.firdaria, "Firdaria"),
    Synastry(R.string.synastry, "Synastry"), // 比較盤 Synastry
    SynastrySecondaryProgression(
        R.string.synastry_secondary_progression,
        "SynastrySecondaryProgression"
    ),
    SynastryTertiaryProgression(
        R.string.synastry_tertiary_progression,
        "SynastryTertiaryProgression"
    ),
    Composite(
        R.string.composite,
        "Composite"
    ), // 組合盤 Composite 組合中點盤就是用一定的數學方法找到兩個人命盤同行星的中點位置作為新的行星位置並以次畫出的新的星盤。
    CompositeSecondaryProgression(
        R.string.composite_secondary_progression,
        "CompositeSecondaryProgression"
    ),
    CompositeTertiaryProgression(
        R.string.composite_tertiary_progression,
        "CompositeTertiaryProgression"
    ),
    Davison(R.string.davison, "Davison"), // 時空盤 TimeSpaceMidpoint / Davison Relationship Chart
    DavisonSecondaryProgression(
        R.string.davison_secondary_progression,
        "DavisonSecondaryProgression"
    ),
    DavisonTertiaryProgression(R.string.davison_tertiary_progression, "DavisonTertiaryProgression"),
    Marks(R.string.marks, "Marks"),// Marks 馬克思盤
    MarksSecondaryProgression(R.string.marks_secondary_progression, "MarksSecondaryProgression"),
    MarksTertiaryProgression(R.string.marks_tertiary_progression, "MarksTertiaryProgression"),
}
