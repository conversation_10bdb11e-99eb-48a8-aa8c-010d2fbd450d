package com.one.astrology.data.entity

import com.google.gson.annotations.SerializedName
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

@Entity
class SignTable {
    @JvmField
    @Id
    var id: Long = 0

    /**
     * name : 牡羊座
     * num : 1
     * quadruplicities : 1
     * quadruplicities_text : 啟動
     * triplicities : 2
     * triplicities_text : 火象
     */
    @JvmField
    @SerializedName("name")
    var name: String? = null

    @JvmField
    @SerializedName("num")
    var num = 0

    @JvmField
    @SerializedName("quadruplicities")
    var quadruplicities = 0

    @JvmField
    @SerializedName("quadruplicities_text")
    var quadruplicitiesText: String? = null

    @JvmField
    @SerializedName("triplicities")
    var triplicities = 0

    @JvmField
    @SerializedName("triplicities_text")
    var triplicitiesText: String? = null
}