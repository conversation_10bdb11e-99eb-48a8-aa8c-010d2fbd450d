package com.one.astrology.data.bean

import androidx.compose.ui.graphics.Color
import androidx.core.graphics.toColorInt
import com.google.gson.annotations.SerializedName
import com.one.astrology.data.HouseData
import com.one.astrology.util.astro.Dignity
import com.one.astrology.util.astro.SolarProximity
import com.one.core.util.LogUtil
import io.objectbox.BoxStore
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import io.objectbox.annotation.Transient
import java.io.Serializable

@Entity
class PlanetBean : Serializable {
    @Id(assignable = true)
    var idL: Long = 0

    @SerializedName("id")
    var id = 0

    @SerializedName("symbol")
    var symbol: String = ""

    @SerializedName("enName")
    var enName: String = ""

    @SerializedName("enSimpleName")
    var enSimpleName: String = ""

    @SerializedName("chName")
    var chName: String = ""

    @SerializedName("color")
    var color: String = ""

    @SerializedName("angle")
    var longitude = 0.0

    var isChecked = false

    @SerializedName("isFortune")
    var isFortune = false

    var isClassic = false


    var speed = 0.0 // xx[3]:   speed in longitude (degree / day) 若小於0則代表 行星逆行

    var isRetrograde: Boolean = false
    val isSectInFavor: Boolean = true
    val isHighInSky: Boolean = false
    val isRulerOfHouse: Boolean = false
    var planetNature: String? = null


    @Transient
    var solarProximity: SolarProximity? = SolarProximity.NONE

    @Transient
    var dignity: Dignity? = Dignity.NONE

    @Transient
    var signBean = SignBean()

    @Transient
    var houseData = HouseData()

    @Transient
    var aspects = ArrayList<Aspect>()

    var description: String = ""

    // Add BoxStore field
    @JvmField
    @kotlin.jvm.Transient
    @Suppress("PropertyName")
    var __boxStore: BoxStore? = null

    private fun isValidHexColor(color: String): Boolean {
        return color.matches(Regex("^[0-9A-Fa-f]{6}([0-9A-Fa-f]{2})?$"))
    }

    fun getSafeColor(): Color {
        return if (isValidHexColor(color)) {
            try {
                Color(("#$color").toColorInt())
            } catch (e: IllegalArgumentException) {
                LogUtil.e("Invalid color format for $chName: $color $e")
                Color.Gray
            }
        } else {
            LogUtil.e("Fallback color used for $chName due to invalid hex: $color")
            Color.Gray
        }
    }

    constructor()
    constructor(
        id: Int,
        symbol: String,
        enName: String,
        enSimpleName: String,
        chName: String,
        color: String
    ) {
        this.id = id
        this.symbol = symbol
        this.enName = enName
        this.enSimpleName = enSimpleName
        this.chName = chName
        this.color = color
    }

    constructor(
        id: Int,
        symbol: String,
        enName: String,
        enSimpleName: String,
        chName: String,
        color: String,
        angle: Double
    ) {
        this.id = id
        this.symbol = symbol
        this.enName = enName
        this.enSimpleName = enSimpleName
        this.chName = chName
        this.color = color
        this.longitude = angle
    }

    constructor(
        id: Int,
        symbol: String,
        enName: String,
        enSimpleName: String,
        chName: String,
        color: String,
        angle: Double,
        isChecked: Boolean = false,
        isFortune: Boolean = false,
        isClassic: Boolean = false,
        isSpecialPoint: Boolean = false
    ) {
        this.id = id
        this.symbol = symbol
        this.enName = enName
        this.enSimpleName = enSimpleName
        this.chName = chName
        this.color = color
        this.longitude = angle
        this.isChecked = isChecked
        this.isFortune = isFortune
        this.isClassic = isClassic
    }

    constructor(
        id: Int,
        chName: String,
        enName: String,
        symbol: String,
        description: String,
    ) {
        this.id = id
        this.chName = chName
        this.enName = enName
        this.symbol = symbol
        this.description = description
    }
}