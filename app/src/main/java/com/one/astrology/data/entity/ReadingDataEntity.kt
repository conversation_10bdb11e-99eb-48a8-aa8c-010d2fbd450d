package com.one.astrology.data.entity

import com.one.astrology.data.type.Chart
import io.objectbox.annotation.Convert
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import io.objectbox.converter.PropertyConverter

@Entity
class ReadingDataEntity : java.io.Serializable {
    @Id(assignable = true)
    var id: Long = 0
    
    var title: String = ""
    var content: String = ""
    var chartInfo: String = ""
    var createTime: Long = System.currentTimeMillis()
    
    @Convert(converter = ChartConverter::class, dbType = String::class)
    var chart: Chart? = null
    
    @Convert(converter = BirthDataConverter::class, dbType = String::class)
    var birthDataA: BirthData? = null
    
    @Convert(converter = BirthDataConverter::class, dbType = String::class)
    var birthDataB: BirthData? = null

    constructor() // 無參數建構函數

    constructor(
        title: String,
        content: String,
        chartInfo: String,
        chart: Chart? = null,
        birthDataA: BirthData? = null,
        birthDataB: BirthData? = null
    ) {
        this.title = title
        this.content = content
        this.chartInfo = chartInfo
        this.chart = chart
        this.birthDataA = birthDataA
        this.birthDataB = birthDataB
        this.createTime = System.currentTimeMillis()
    }
}

class ChartConverter : PropertyConverter<Chart?, String> {
    override fun convertToDatabaseValue(entityProperty: Chart?): String? {
        return entityProperty?.name
    }

    override fun convertToEntityProperty(databaseValue: String?): Chart? {
        return databaseValue?.let { Chart.valueOf(it) }
    }
}

class BirthDataConverter : PropertyConverter<BirthData?, String> {
    override fun convertToDatabaseValue(entityProperty: BirthData?): String? {
        return entityProperty?.let { 
            // 將 BirthData 轉換為 JSON 字符串
            com.google.gson.Gson().toJson(it)
        }
    }

    override fun convertToEntityProperty(databaseValue: String?): BirthData? {
        return databaseValue?.let {
            // 將 JSON 字符串轉換回 BirthData 對象
            com.google.gson.Gson().fromJson(it, BirthData::class.java)
        }
    }
}