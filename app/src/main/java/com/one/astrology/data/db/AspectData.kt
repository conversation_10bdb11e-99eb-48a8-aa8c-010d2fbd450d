package com.one.astrology.data.db

import com.google.gson.annotations.SerializedName
import com.one.astrology.data.bean.AspectType
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.io.Serializable

@Entity
data class AspectData(
    @Id
    @SerializedName("id")
    var id: Long = 0,

    var planetA: String = "",
    var type: String = "",
    var degree: Int = 0,
    var planetB: String = "",
    var score: Int = 0,
    var desc: String? = null,

    @Transient
    var descList: ArrayList<String> = ArrayList(),

    var nameA: String? = "",
    var nameB: String? = "",
    var orb: Double? = 0.0,
    var isApplying: Boolean? = true,// 入相位（Applying Aspect）出相位（Separating Aspect）
    var direction: String? = "",

    @Transient
    var aspectType: AspectType? = null,
) : Serializable