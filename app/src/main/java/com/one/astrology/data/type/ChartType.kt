package com.one.astrology.data.type


import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

class ChartType : ArrayList<ChartType.ChartTypeItem>() {
    @Keep
    data class ChartTypeItem(
        @SerializedName("content")
        val content: List<Content>,
        @SerializedName("title")
        val title: String // 基本盤
    ) {
        @Keep
        data class Content(
            @SerializedName("description")
            val description: String, // 出生時的命盤，從本命盤上可以看到一生的運勢概況和性格特徵，是各個人生領域的基本情況。
            @SerializedName("name")
            val name: String, // 天象盤
            @SerializedName("type")
            val type: String, // Celestial
            @SerializedName("visible")
            val visible: Boolean? = true
        )
    }
}