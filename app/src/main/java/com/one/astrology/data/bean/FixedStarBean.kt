package com.one.astrology.data.bean

/**
 * 固定星資料模型
 * 用於表示固定星的位置和特性
 */
data class FixedStarBean(
    /**
     * 固定星ID
     */
    val id: Int,
    
    /**
     * 固定星名稱（中文）
     */
    val chineseName: String,
    
    /**
     * 固定星名稱（英文）
     */
    val englishName: String,
    
    /**
     * 固定星所在星座
     */
    val constellation: String,
    
    /**
     * 固定星在黃道上的經度（角度）
     */
    val longitude: Double,
    
    /**
     * 固定星在黃道上的緯度（角度）
     */
    val latitude: Double,
    
    /**
     * 固定星的視星等（亮度）
     * 數值越小，亮度越高
     */
    val magnitude: Double,
    
    /**
     * 固定星的性質
     * 例如：木星性、土星性、火星性等
     */
    val nature: String,
    
    /**
     * 固定星的象徵意義
     */
    val symbolism: String,
    
    /**
     * 固定星的影響力
     * 通常與星等相關，星等越小影響力越大
     */
    val influence: Double = calculateInfluence(magnitude),
    
    /**
     * 固定星的關鍵詞
     */
    val keywords: List<String> = emptyList(),
    
    /**
     * 固定星的詳細描述
     */
    val description: String = ""
) {
    /**
     * 獲取固定星所在的黃道星座
     * @return 星座ID (1-12)
     */
    fun getZodiacSign(): Int {
        return (longitude / 30.0).toInt() + 1
    }
    
    /**
     * 獲取固定星在黃道星座中的度數
     * @return 度數 (0-30)
     */
    fun getZodiacDegree(): Double {
        return longitude % 30.0
    }
    
    /**
     * 檢查固定星是否與指定角度形成合相
     * @param angle 角度
     * @param orb 容許度（允許的誤差範圍）
     * @return 是否形成合相
     */
    fun isConjunct(angle: Double, orb: Double = getDefaultOrb()): Boolean {
        val distance = calculateAngleDistance(longitude, angle)
        return distance <= orb
    }
    
    /**
     * 檢查固定星是否與指定角度形成對分相
     * @param angle 角度
     * @param orb 容許度（允許的誤差範圍）
     * @return 是否形成對分相
     */
    fun isOpposite(angle: Double, orb: Double = getDefaultOrb()): Boolean {
        val distance = calculateAngleDistance(longitude, angle)
        return Math.abs(distance - 180.0) <= orb
    }
    
    /**
     * 獲取默認的容許度
     * 根據固定星的亮度（星等）決定容許度
     * 亮星（低星等）有更大的容許度
     */
    private fun getDefaultOrb(): Double {
        return when {
            magnitude <= 1.0 -> 2.0  // 一等星及更亮的星
            magnitude <= 2.0 -> 1.5  // 二等星
            magnitude <= 3.0 -> 1.0  // 三等星
            else -> 0.5             // 四等星及更暗的星
        }
    }
    
    /**
     * 計算兩個角度之間的最小距離
     * @param angle1 角度1
     * @param angle2 角度2
     * @return 最小距離（0-180度）
     */
    private fun calculateAngleDistance(angle1: Double, angle2: Double): Double {
        val diff = Math.abs(angle1 - angle2) % 360.0
        return if (diff > 180.0) 360.0 - diff else diff
    }
    
    companion object {
        /**
         * 根據星等計算固定星的影響力
         * 星等越小（越亮），影響力越大
         * @param magnitude 視星等
         * @return 影響力（0-10，10為最大）
         */
        fun calculateInfluence(magnitude: Double): Double {
            // 轉換公式：影響力 = 10 - 星等，但不小於0且不大於10
            val influence = 10.0 - magnitude
            return when {
                influence < 0.0 -> 0.0
                influence > 10.0 -> 10.0
                else -> influence
            }
        }
    }
} 