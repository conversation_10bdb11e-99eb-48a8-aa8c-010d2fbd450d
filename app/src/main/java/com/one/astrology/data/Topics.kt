package com.one.astrology.data


import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import java.io.Serializable

class Topics : ArrayList<Topics.TopicItem>() {
    @Keep
    data class TopicItem(
        @SerializedName("content")
        val contents: ArrayList<Content>,
        @SerializedName("id")
        val id: Int, // 1
        @SerializedName("title")
        val title: String, // 高智商的星盤配置
        @SerializedName("header")
        val header: String?,
        @SerializedName("url")
        val url: String?, // https://developer.android.com/guide/fragments/dialogs
    ) : Serializable {
        @Keep
        data class Content(
            @SerializedName("id")
            val id: Int?, // 1
            @SerializedName("title")
            val title: String, // 行星星座
            @SerializedName("condition")
            val condition: String, // 金星牡羊、狮子、天蠍、射手、金牛座
            @SerializedName("description")
            val description: String, // 美女金星落在強勢星座
            @SerializedName("planet_name_list_a")
            var planetNameListA: ArrayList<String>?,
            @SerializedName("planet_name_list_b")
            var planetNameListB: ArrayList<String>?,
            @SerializedName("planet_name_a")
            var planetNameA: String?, // 金星
            @SerializedName("planet_name_b")
            var planetNameB: String?,
            @SerializedName("signNameList")
            var signNameList: ArrayList<String>?, // 牡羊座、狮子座、天蠍座、射手座、金牛座
            @SerializedName("type_name")
            val typeName: Int?,// 1
            @SerializedName("score")
            val score: Int?,// 1
            @SerializedName("house")
            val house: ArrayList<Int>?,
            @SerializedName("house_a")
            val houseA: ArrayList<Int>?,// 1
            @SerializedName("house_b")
            val houseB: ArrayList<Int>?,// 1
            @SerializedName("angle")
            val angle: ArrayList<Double>?,
            @SerializedName("is_retrograde")
            val isRetrograde: Boolean?,
            @SerializedName("content")
            val subContents: ArrayList<Content>?,

            var resultList: ArrayList<String>?,
            var isChecked: Boolean = false
        ) : Serializable
    }
}