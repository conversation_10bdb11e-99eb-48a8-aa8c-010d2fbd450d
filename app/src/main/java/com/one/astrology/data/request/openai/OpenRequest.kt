package com.one.astrology.data.request.openai


import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName


@Keep
data class OpenRequest(
    @SerializedName("max_tokens")
    val maxTokens: Int, // 7
    @SerializedName("model")
    val model: String, // text-davinci-003
    @SerializedName("prompt")
    val prompt: String, // Say this is a test
    @SerializedName("temperature")
    val temperature: Int // 0
)

