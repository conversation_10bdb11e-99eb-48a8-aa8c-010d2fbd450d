package com.one.astrology.data.bean

import androidx.compose.ui.graphics.Color
import androidx.core.graphics.toColorInt
import com.google.gson.annotations.SerializedName
import com.one.astrology.data.HouseData
import com.one.core.util.LogUtil
import io.objectbox.BoxStore
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.io.Serializable

/**
 * 星座 /assets/files/sign.json
 *
 * <AUTHOR>
 */
@Entity
class SignBean : Serializable {
    @Id(assignable = true)
    var id: Long = 0

    @SerializedName("symbol")
    var symbol: String = ""

    @SerializedName("unicode")
    var unicode: String = ""

    @SerializedName("enName")
    var enName: String = ""

    @SerializedName("enShortName")
    var enShortName: String = ""

    @SerializedName("chName")
    var chName: String = ""

    @SerializedName("color")
    var color: String = ""

    @SerializedName("angle")
    var angle = 0.0

    var degree = ""
    var minute = ""
    var dignity = ""
    var triplicities: String = ""
    var quadruplicities: String = ""
    var yinYang: String = ""

    @SerializedName("ruler")
    var ruler: String = ""

    @SerializedName("exaltation")
    var exaltation: String = "" // 廟（守護星） 入廟

    @SerializedName("rulership")
    var rulership: String = "" // 旺（擢升 旺相

    @SerializedName("detriment")
    var detriment: String = "" // 落 失勢 落弱

    @SerializedName("fall")
    var fall: String = "" // 陷 落陷

    var energy = ""

    @Transient
    var houseData : HouseData = HouseData() //= ToOne<HouseData>(this, SignBean_.houseData) //: HouseData = HouseData()

    // Add BoxStore field
    @JvmField
    @Transient
    @Suppress("PropertyName")
    var __boxStore: BoxStore? = null

    constructor()

    private fun isValidHexColor(color: String): Boolean {
        return color.matches(Regex("^[0-9A-Fa-f]{6}([0-9A-Fa-f]{2})?$"))
    }

    fun getSafeColor(): Color {
        return if (isValidHexColor(color)) {
            try {
                Color(("#$color").toColorInt())
            } catch (e: IllegalArgumentException) {
                LogUtil.e("Invalid color format for $chName: $color $e")
                Color.Gray
            }
        } else {
            LogUtil.e("Fallback color used for $chName due to invalid hex: $color")
            Color.Gray
        }
    }

    constructor(
        signBean: SignBean
    ) {
        this.symbol = signBean.symbol
        this.unicode = signBean.unicode
        this.enName = signBean.enName
        this.enShortName = signBean.enShortName
        this.chName = signBean.chName
        this.color = signBean.color
        this.angle = signBean.angle
        this.ruler = signBean.ruler
    }

//    fun init(signBean: SignBean) {
//        this.symbol = signBean.symbol
//        this.enName = signBean.enName
//        this.enShortName = signBean.enShortName
//        this.chName = signBean.chName
//        this.color = signBean.color
//        this.angle = signBean.angle
//        this.ruler = signBean.ruler
//    }
}