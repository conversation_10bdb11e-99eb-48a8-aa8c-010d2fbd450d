package com.one.astrology.data

import com.one.astrology.data.type.Chart


data class PdfData(
    var chart: Chart = Chart.Natal,
    var name: String = "",
    var pdfItemData: ArrayList<PdfItemData> = ArrayList()
)

data class PdfItemData(
    var type: InterpretationType = InterpretationType.sign,
    var subTitle: String? = "",
    var desc: String? = "",
    var descList: ArrayList<String> = ArrayList(),
)

enum class InterpretationType {
    sign,
    house,
    aspect,
    fly
}