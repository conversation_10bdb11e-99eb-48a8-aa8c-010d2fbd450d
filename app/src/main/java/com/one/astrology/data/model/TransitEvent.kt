package com.one.astrology.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * 行運事件類型
 */
enum class TransitEventType(val displayName: String) {
    ASPECT_FORMING("相位形成"),
    ASPECT_EXACT("相位精確"),
    HOUSE_CHANGE("宮位變化"),
    SIGN_CHANGE("星座切換"),
    RETROGRADE_START("開始逆行"),
    RETROGRADE_END("結束逆行"),
    STATION_DIRECT("順行停滯"),
    STATION_RETROGRADE("逆行停滯")
}

/**
 * 相位方向（入相位/出相位）
 */
enum class AspectDirection(val displayName: String) {
    APPLYING("入相位"),
    SEPARATING("出相位"),
    EXACT("精確相位")
}

/**
 * 行運事件重要性等級
 */
enum class TransitImportance(val level: Int, val displayName: String) {
    LOW(1, "輕微"),
    MEDIUM(2, "中等"),
    HIGH(3, "重要"),
    CRITICAL(4, "關鍵")
}

/**
 * 行運事件資料模型
 */
@Entity
@Parcelize
data class TransitEvent(
    @Id
    @SerializedName("id")
    var id: Long = 0,

    /**
     * 事件類型（存儲為 Int）
     */
    @SerializedName("eventType")
    var eventTypeValue: Int = TransitEventType.ASPECT_FORMING.ordinal,

    /**
     * 事件發生時間（時間戳）
     */
    @SerializedName("eventTime")
    var eventTime: Long = 0,

    /**
     * 行運行星ID
     */
    @SerializedName("transitPlanetId")
    var transitPlanetId: Int = 0,

    /**
     * 行運行星名稱
     */
    @SerializedName("transitPlanetName")
    var transitPlanetName: String = "",

    /**
     * 本命行星ID（如果是相位事件）
     */
    @SerializedName("natalPlanetId")
    var natalPlanetId: Int? = null,

    /**
     * 本命行星名稱（如果是相位事件）
     */
    @SerializedName("natalPlanetName")
    var natalPlanetName: String? = null,

    /**
     * 相位類型（如果是相位事件）
     */
    @SerializedName("aspectType")
    var aspectType: Int? = null,

    /**
     * 相位名稱（如果是相位事件）
     */
    @SerializedName("aspectName")
    var aspectName: String? = null,

    /**
     * 相位方向
     */
    @SerializedName("aspectDirection")
    var aspectDirection: String? = null,

    /**
     * 容許度（度數）
     */
    @SerializedName("orb")
    var orb: Double = 0.0,

    /**
     * 精確度（距離精確相位的度數）
     */
    @SerializedName("exactness")
    var exactness: Double = 0.0,

    /**
     * 來源宮位（如果是宮位變化事件）
     */
    @SerializedName("fromHouse")
    var fromHouse: Int? = null,

    /**
     * 目標宮位（如果是宮位變化事件）
     */
    @SerializedName("toHouse")
    var toHouse: Int? = null,

    /**
     * 來源星座（如果是星座切換事件）
     */
    @SerializedName("fromSign")
    var fromSign: Int? = null,

    /**
     * 目標星座（如果是星座切換事件）
     */
    @SerializedName("toSign")
    var toSign: Int? = null,

    /**
     * 來源星座名稱
     */
    @SerializedName("fromSignName")
    var fromSignName: String? = null,

    /**
     * 目標星座名稱
     */
    @SerializedName("toSignName")
    var toSignName: String? = null,

    /**
     * 事件重要性等級（存儲為 Int）
     */
    @SerializedName("importance")
    var importanceValue: Int = TransitImportance.MEDIUM.ordinal,

    /**
     * 事件描述
     */
    @SerializedName("description")
    var description: String = "",

    /**
     * 詳細解釋
     */
    @SerializedName("interpretation")
    var interpretation: String = "",

    /**
     * 建議
     */
    @SerializedName("advice")
    var advice: String = "",

    /**
     * 關聯的出生資料名稱
     */
    @SerializedName("birthDataName")
    var birthDataName: String = "",

    /**
     * 創建時間
     */
    @SerializedName("createTime")
    var createTime: Long = System.currentTimeMillis(),

    /**
     * 是否已讀
     */
    @SerializedName("isRead")
    var isRead: Boolean = false,

    /**
     * 是否已推播
     */
    @SerializedName("isNotified")
    var isNotified: Boolean = false

) : Parcelable, Serializable {

    /**
     * 事件類型（enum 屬性）
     */
    var eventType: TransitEventType
        get() = TransitEventType.values().getOrElse(eventTypeValue) { TransitEventType.ASPECT_FORMING }
        set(value) { eventTypeValue = value.ordinal }



    /**
     * 事件重要性等級（enum 屬性）
     */
    var importance: TransitImportance
        get() = TransitImportance.values().getOrElse(importanceValue) { TransitImportance.MEDIUM }
        set(value) { importanceValue = value.ordinal }

    /**
     * 獲取事件標題
     */
    fun getEventTitle(): String {
        return when (eventType) {
            TransitEventType.ASPECT_FORMING, TransitEventType.ASPECT_EXACT -> {
                if (natalPlanetName != null && aspectName != null) {
                    "$transitPlanetName ${aspectName} $natalPlanetName"
                } else {
                    "$transitPlanetName 相位事件"
                }
            }
            TransitEventType.HOUSE_CHANGE -> {
                if (fromHouse != null && toHouse != null) {
                    "$transitPlanetName 從第${fromHouse}宮進入第${toHouse}宮"
                } else {
                    "$transitPlanetName 宮位變化"
                }
            }
            TransitEventType.SIGN_CHANGE -> {
                if (fromSignName != null && toSignName != null) {
                    "$transitPlanetName 從${fromSignName}進入${toSignName}"
                } else {
                    "$transitPlanetName 星座切換"
                }
            }
            TransitEventType.RETROGRADE_START -> "$transitPlanetName 開始逆行"
            TransitEventType.RETROGRADE_END -> "$transitPlanetName 結束逆行"
            TransitEventType.STATION_DIRECT -> "$transitPlanetName 順行停滯"
            TransitEventType.STATION_RETROGRADE -> "$transitPlanetName 逆行停滯"
        }
    }

    /**
     * 獲取事件簡短描述
     */
    fun getShortDescription(): String {
        return when (eventType) {
            TransitEventType.ASPECT_FORMING -> {
                val directionText = aspectDirection ?: ""
                "正在形成${directionText}，容許度 ${String.format("%.1f", orb)}°"
            }
            TransitEventType.ASPECT_EXACT -> "精確相位"
            TransitEventType.HOUSE_CHANGE -> "宮位轉換"
            TransitEventType.SIGN_CHANGE -> "星座轉換"
            TransitEventType.RETROGRADE_START, TransitEventType.RETROGRADE_END,
            TransitEventType.STATION_DIRECT, TransitEventType.STATION_RETROGRADE -> "運行狀態變化"
        }
    }

    /**
     * 獲取重要性顏色資源ID
     */
    fun getImportanceColorRes(): Int {
        return when (importance) {
            TransitImportance.LOW -> android.R.color.holo_green_light
            TransitImportance.MEDIUM -> android.R.color.holo_orange_light
            TransitImportance.HIGH -> android.R.color.holo_red_light
            TransitImportance.CRITICAL -> android.R.color.holo_red_dark
        }
    }

    /**
     * 是否為相位事件
     */
    fun isAspectEvent(): Boolean {
        return eventType == TransitEventType.ASPECT_FORMING || eventType == TransitEventType.ASPECT_EXACT
    }

    /**
     * 是否為宮位變化事件
     */
    fun isHouseChangeEvent(): Boolean {
        return eventType == TransitEventType.HOUSE_CHANGE
    }

    /**
     * 是否為星座切換事件
     */
    fun isSignChangeEvent(): Boolean {
        return eventType == TransitEventType.SIGN_CHANGE
    }

    /**
     * 是否為逆行相關事件
     */
    fun isRetrogradeEvent(): Boolean {
        return eventType == TransitEventType.RETROGRADE_START ||
                eventType == TransitEventType.RETROGRADE_END ||
                eventType == TransitEventType.STATION_DIRECT ||
                eventType == TransitEventType.STATION_RETROGRADE
    }
}
