package com.one.astrology.data.bean

/**
 * 中點數據模型
 * 用於表示兩個行星或點位之間的中點
 */
data class MidpointBean(
    /**
     * 中點ID，由兩個行星或點位ID組合而成
     * 例如：太陽-月亮中點的ID可以是 "1_2"
     */
    val id: String,
    
    /**
     * 第一個行星或點位的ID
     */
    val firstPointId: Int,
    
    /**
     * 第二個行星或點位的ID
     */
    val secondPointId: Int,
    
    /**
     * 中點的角度（0-360度）
     */
    val angle: Double,
    
    /**
     * 中點所在的星座ID（1-12）
     */
    val signId: Int,
    
    /**
     * 中點所在的宮位（1-12）
     */
    val houseId: Int,
    
    /**
     * 中點與其他行星或點位形成的相位列表
     */
    val aspects: List<Aspect> = emptyList(),
    
    /**
     * 中點的解釋
     */
    val interpretation: String = ""
) {
    /**
     * 獲取中點的名稱
     * 例如：太陽/月亮
     */
    fun getName(planetNameProvider: (Int) -> String): String {
        return "${planetNameProvider(firstPointId)}/${planetNameProvider(secondPointId)}"
    }
    
    /**
     * 獲取中點的符號
     * 例如：☉/☽
     */
    fun getSymbol(planetSymbolProvider: (Int) -> String): String {
        return "${planetSymbolProvider(firstPointId)}/${planetSymbolProvider(secondPointId)}"
    }
    
    /**
     * 檢查此中點是否與指定行星或點位形成特定相位
     * @param planetId 行星或點位ID
     * @param aspectType 相位類型（合相、刑相、拱相等）
     * @return 是否形成指定相位
     */
    fun hasAspectWith(planetId: Int, aspectType: AspectType): Boolean {
        return aspects.any { it.planet.id == planetId && it.type == aspectType }
    }
    
    /**
     * 獲取與指定行星或點位形成的所有相位
     * @param planetId 行星或點位ID
     * @return 相位列表
     */
//    fun getAspectsWith(planetId: Int): List<Aspect> {
//        return aspects.filter { it.secondPointId == planetId }
//    }
    
    companion object {
        /**
         * 創建中點ID
         * @param firstPointId 第一個行星或點位ID
         * @param secondPointId 第二個行星或點位ID
         * @return 中點ID
         */
        fun createMidpointId(firstPointId: Int, secondPointId: Int): String {
            // 確保ID順序一致，較小的ID在前
            val sortedIds = listOf(firstPointId, secondPointId).sorted()
            return "${sortedIds[0]}_${sortedIds[1]}"
        }
    }
} 