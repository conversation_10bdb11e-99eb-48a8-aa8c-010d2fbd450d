package com.one.astrology.data


data class AstrologyReading(
//    val userId: String,         // 使用者的唯一 ID
//    val userData: UserData,     // 使用者基本資料
//    val astrologyChart: AstrologyChart, // 占星運勢的解讀
    val aiModelResponses: List<AIModelResponse>? = null // 不同 AI 模型的回應資料
)

data class UserData(
    val birthDate: String,      // 出生日期
    val timeOfBirth: String,    // 出生時間
    val placeOfBirth: String    // 出生地點
)


data class AIModelResponse(
//    val name: String,
//    val birthDate: String,      // 出生日期
//    val placeOfBirth: String,    // 出生地點
    val response: String,         // AI 模型的回應內容（解讀文本）
    val tokens: Int,
//    val timestamp: FieldValue? = null  // 使用 Firestore 的 Timestamp 類型
){
    // 顯式定義無參數構造函數
    constructor() : this("",0)
}


data class AstrologyChart(
    val birthChart: String,      // 本命格的解讀
    val financialLuck: String,   // 財帛運的解讀
    val loveLuck: String,        // 愛情運的解讀
    val familyLuck: String,      // 六親運的解讀
    val careerLuck: String,      // 事業運的解讀
    val interpersonalLuck: String, // 人際運的解讀
    val healthLuck: String,      // 健康運的解讀
    val fortuneLuck: String      // 禍福運的解讀
)