package com.one.astrology.data


import com.google.gson.annotations.SerializedName
import androidx.annotation.Keep

@Keep
data class SunSignInfo(
    @SerializedName("color")
    val color: String, //  Spring Green
    @SerializedName("compatibility")
    val compatibility: String, //  Cancer
    @SerializedName("current_date")
    val currentDate: String, // June 23, 2017
    @SerializedName("date_range")
    val dateRange: String, // Mar 21 - Apr 20
    @SerializedName("description")
    val description: String, // It's finally time for you to think about just one thing: what makes you happy. Fortunately, that happens to be a person who feels the same way. Give yourself the evening off. Refuse to be put in charge of anything.
    @SerializedName("lucky_number")
    val luckyNumber: String, //  64
    @SerializedName("lucky_time")
    val luckyTime: String, //  7am
    @SerializedName("mood")
    val mood: String //  Relaxed
)