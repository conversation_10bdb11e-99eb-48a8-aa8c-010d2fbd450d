package com.one.astrology.data.bean

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class SignDescBean(
    @SerializedName("id")
    var id: Int? = null, // 1

    @SerializedName("sign")
    var sign: String? = null, // 牡羊

    @SerializedName("ruler")
    var ruler: String? = null, // 廟 本垣(Rulership)
    @SerializedName("exalt")
    var exalt: String? = null, // 旺 擢升(Exaltation)

    @SerializedName("triplicity_day")
    var triplicityDay: String? = null, // 太陽
    @SerializedName("triplicity_night")
    var triplicityNight: String? = null, // 木星
    @SerializedName("triplicity_mix")
    var triplicityMix: String? = null, // 木星

    @SerializedName("terms_1")
    var terms1: String? = null, // 木星
    @SerializedName("terms_1_d")
    var terms1d: String? = null, // 木星
    @SerializedName("terms_2")
    var terms2: String? = null, // 金星
    @SerializedName("terms_2_d")
    var terms2d: String? = null, // 木星
    @SerializedName("terms_3")
    var terms3: String? = null, // 水星
    @SerializedName("terms_3_d")
    var terms3d: String? = null, // 木星
    @SerializedName("terms_4")
    var terms4: String? = null, // 火星
    @SerializedName("terms_4_d")
    var terms4d: String? = null, // 木星
    @SerializedName("terms_5")
    var terms5: String? = null, // 土星
    @SerializedName("terms_5_d")
    var terms5d: String? = null, // 木星

    @SerializedName("face_1")
    var face1: String? = null, // 火星
    @SerializedName("face_2")
    var face2: String? = null, // 太陽
    @SerializedName("face_3")
    var face3: String? = null, // 金星

    @SerializedName("detriment")
    var detriment: String? = null, // 陷
    @SerializedName("fall")
    var fall: String? = null, // 弱

    @SerializedName("theme")
    var theme: String? = null, // 個人
    @SerializedName("properties")
    var properties: String? = null, // 性能力、企圖心、行動、爭鬥、受傷

)
