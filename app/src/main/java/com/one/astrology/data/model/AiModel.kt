package com.one.astrology.data.model

data class AiModel(
    val id: String,
    val developer: String?,
    val inputPricePerMillion: Double?,
    val cachedInputPricePerMillion: Double?,
    val outputPricePerMillion: Double?,
    val apiKey: String? = null
)

//val aiModules = arrayOf(
//    "deepseek-r1-distill-llama-70b", // 7 簡體字有錯字
//    "gemma2-9b-it",
//    "llama-3.2-90b-vision-preview", // 6.5
//    "llama-3.3-70b-versatile", // 6
//    "llama-3.3-70b-specdec", // 6 回答跟健康較無關係
//    "gpt-4o",               // gpt-4o-2024-08-06
//    "gpt-4o-2024-11-20",
//    "chatgpt-4o-latest", // ChatGPT 中最新使用的內容
//    "gpt-3.5-turbo",     // Currently points to gpt-3.5-turbo-0125.
//    "gpt-4o-mini"       // gpt-4o-mini-2024-07-18
//)

// AIzaSyDpVZFSFlXs4XsGl1T1FHpg3JdLh8dduQE
val aiModels = arrayOf(
    AiModel("deepseek-r1-distill-llama-70b", "DeepSeek", null, null, null),
//    AiModel("distil-whisper-large-v3-en", "HuggingFace", null, null, null),
//    AiModel("whisper-large-v3", "GroqOpenAI", null, null, null),
//    AiModel("whisper-large-v3-turbo", "groqOpenAI", null, null, null),
    AiModel("gemma2-9b-it", "Google", null, null, null),
//    AiModel("llama-3.3-70b-versatile", "Meta", null, null, null),
//    AiModel("llama-3.1-8b-instant", "Meta", null, null, null),
//    AiModel("llama-guard-3-8b", "Meta", null, null, null),
    AiModel("llama-3.2-90b-vision-preview", "Meta", null, null, null),
    AiModel("llama-3.3-70b-versatile", "Meta", null, null, null),
    AiModel("llama-3.3-70b-specdec", "Meta", null, null, null),
    AiModel("llama3-70b-8192", "Meta", null, null, null),
    AiModel("llama3-8b-8192", "Meta", null, null, null),
//    AiModel("mixtral-8x7b-32768", "Mistral", null, null, null),
    AiModel("gpt-4.5-preview", "OpenAI", 75.00, 37.50, 150.00),
    AiModel("gpt-4o", "OpenAI", 2.50, 1.25, 10.00),
    AiModel("gpt-4o-mini", "OpenAI", 0.15, 0.075, 0.60),
    AiModel("o1", "OpenAI", 15.00, 7.50, 60.00),
    AiModel("o1-mini", "OpenAI", 1.10, 0.55, 4.40),
    AiModel("o3-mini", "OpenAI", 1.10, 0.55, 4.40),
    AiModel("gpt-3.5-turbo", "OpenAI", 0.50, null, 1.50)
)