package com.one.astrology.data


import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import com.one.astrology.data.entity.BirthData
import java.io.Serializable

class News : ArrayList<News.NewsItem>() {
    @Keep
    data class NewsItem(
        @SerializedName("content")
        val content: MutableList<Content>,
        @SerializedName("id")
        val id: Int, // 0
        @SerializedName("title")
        val title: String, // 精選內容
        @SerializedName("isVisible")
        val isVisible: Boolean? = false
    ) {
        @Keep
        data class Content(
            @SerializedName("id")
            val id: Int?,
            var typeTitle: String?, // 精選內容
            @SerializedName("icon")
            val icon: String?, // https://developer.android.com/guide/fragments/dialogs
            @SerializedName("source")
            val source: String?, // 維基百科
            @SerializedName("text")
            val text: String, // text
            @SerializedName("title")
            val title: String, // title
            @SerializedName("type")
            val type: String, // text
            @SerializedName("url")
            val url: String?, // https://developer.android.com/guide/fragments/dialogs
            @SerializedName("isVisible")
            val isVisible: Boolean? = false, // 精選內容

            @SerializedName("birthday_data")
            val birthdayData: BirthData? = null // 精選內容
        ) : Serializable
    }
}