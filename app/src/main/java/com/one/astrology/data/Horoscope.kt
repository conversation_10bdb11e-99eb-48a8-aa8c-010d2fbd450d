package com.one.astrology.data

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.google.gson.annotations.SerializedName
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import com.one.astrology.util.AstrologyUtils
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil
import io.objectbox.BoxStore
import io.objectbox.annotation.Id
import swisseph.SweConst
import java.io.Serializable
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * 天宮圖
 */

class Horoscope : Serializable {
    @JvmField
    @Id
    var id: Long = 0

    @SerializedName("createTime")
    var createTime: Long = 0

    @SerializedName("name")
    var name: String = ""

    @SerializedName("time")
    var birthdayTime: Long = 0L

    @Transient
    var latLng: LatLng = LatLng(0.0, 0.0)

    @SerializedName("houses")
    var houses = Houses()

    @SerializedName("planetList")
    var planetList = ArrayList<PlanetBean>()

    @SerializedName("aspectList")
    var aspectList = ArrayList<AspectData>()


    // Add BoxStore field
    @JvmField
    @Transient
    @Suppress("PropertyName")
    var __boxStore: BoxStore? = null

    constructor()

    constructor(
        birthData: BirthData,
    ) : this() {
        this.name = birthData.name
        this.birthdayTime = birthData.birthday
        this.latLng = LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude)
    }

    constructor(
        name: String,
        time: Long,
        latLng: LatLng
    ) : this() {
        this.name = name
        this.birthdayTime = time
        this.latLng = latLng
    }

    fun getPlanetBeanList(): MutableList<PlanetBean> {
        return planetList
    }

    fun findPlantSign(planetName: String): SignBean {
        val planetBean = planetList.find {
            it.chName == planetName
        }
        return planetBean?.signBean ?: SignBean()
    }

    fun getBirthLatLng(): String {
        if (birthdayTime == 0L) {
            return ""
        }
        return "${latLng.latitude} , ${latLng.longitude}"
    }

    fun getBirthdayString(): String {
        if (birthdayTime == 0L) {
            return ""
        }
        // 轉換為 LocalDateTime（需要指定時區）
        val dateTimeMillis =
            LocalDateTime.ofInstant(Instant.ofEpochMilli(birthdayTime), ZoneId.systemDefault())
        // 格式化為 "yyyy-MM-dd HH:mm:ss"
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        LogUtil.d("時間戳轉換: ${dateTimeMillis.format(formatter)}")
        return dateTimeMillis.format(formatter)
    }

    /**
     * **檢查星盤的可判讀性**
     * 有些星盤可能顯示「不可解讀」的狀態，例如：
     * - **上升點 0°-3° 或 27°-29°**：問題可能過早或太晚，不適合解讀。
     * - **月亮空亡（Void of Course Moon）**：代表事情不會有進一步發展或結果已定。
     * - **土星在第七宮**：占星師可能有誤判風險，需特別謹慎解釋。
     * - **水星逆行**：可能需要重新審視問題，或代表誤解與變數。
     */
    fun findNotInterpretable(context: Context): List<Pair<Boolean, String>> {
        val list = ArrayList<Pair<Boolean, String>>()
        val asc = houses.getAsc()
        if ((asc in 0.0..3.0) || (asc in 27.0..29.0)) {
            list.add(Pair(true, "上升點 0°-3° 或 27°-29°**：問題可能過早或太晚，不適合解讀。"))
        }
        val julDay = EphemerisUtil.getJulDay(birthdayTime, latLng)
        val vocTimes = EphemerisUtil.calculateVoidOfCourseMoon(context, julDay)

        vocTimes?.let { (start, end) ->
            val startTime = EphemerisUtil.julianToUnixTimestamp(start)
            val endTime = EphemerisUtil.julianToUnixTimestamp(end)
            if (startTime < birthdayTime && birthdayTime > endTime) {
                Pair(
                    true,
                    "月亮空亡（Void of Course Moon）**：代表事情不會有進一步發展或結果已定。"
                )
            }
            EphemerisUtil.julianToDate(julDay)
            LogUtil.d("月亮空亡開始時間（儒略日）: $start ${EphemerisUtil.julianToDate(start)}")
            LogUtil.d("月亮空亡結束時間（儒略日）: $end ${EphemerisUtil.julianToDate(end)}")
        }

        val saturn = planetList.find { it.chName == "土星" }
        if (saturn != null) {
            if (saturn.houseData.index == 7) {
                list.add(Pair(true, "土星在第七宮：占星師可能有誤判風險，需特別謹慎解釋。"))
            }
        }

        val mercury = planetList.find { it.chName == "水星" }
        if (mercury != null) {
            if (mercury.isRetrograde) {
                list.add(Pair(true, "水星逆行：可能需要重新審視問題，或代表誤解與變數。"))
            }
        }

        return list
    }

    /**
     * 本命格的論斷（占星學分析）
     *
     * 本命格（Natal Chart）即本命盤，是根據你的出生時間、地點與日期繪製出的個人星盤。它反映你的天生性格、潛力、事業發展、人際關係等。
     * 請根據以下的星盤資訊，提供一份詳細的占星解讀：
     * - 太陽：天蠍座 12°（12 宮）
     * - 月亮：天蠍座 18°（12 宮）
     * - 上升：射手座 5°
     * - 水星：天蠍座 10°（12 宮）
     * - 金星：射手座 15°（1 宮）
     * - 火星：摩羯座 8°（1 宮）
     * - 木星：雙子座 20°（7 宮）
     *
     * 請從個性、情感、事業發展等角度進行分析，並提供詳細解釋。
     */
    fun toAllString(context: Context, chart: Chart? = Chart.Natal): String {
        if (name.isEmpty()) return ""
        val text = StringBuilder()
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")
        text.append("宮位系統: ${AstrologyUtils.getHouseSystemName(context)}\n")

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_MARS,
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
            SweConst.SE_URANUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_PLUTO,
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }
        if (chart != Chart.Firdaria) {
            getHouseInfo(1, houses, planetList, text)
            getHouseInfo(2, houses, planetList, text)
            getHouseInfo(3, houses, planetList, text)
            getHouseInfo(4, houses, planetList, text)
            getHouseInfo(5, houses, planetList, text)
            getHouseInfo(6, houses, planetList, text)
            getHouseInfo(7, houses, planetList, text)
            getHouseInfo(8, houses, planetList, text)
            getHouseInfo(9, houses, planetList, text)
            getHouseInfo(10, houses, planetList, text)
            getHouseInfo(11, houses, planetList, text)
            getHouseInfo(12, houses, planetList, text)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }

    /**
     * 財帛運的論斷 → 重點看第2宮（財富）、第8宮（他人財富）、行星配置及流年影響。
     * 總結：判斷財運的必要資訊
     * ✅ 第二宮（個人財富）+ 宮內行星 → 收入來源與財務穩定性
     * ✅ 第八宮（投資財富）+ 宮內行星 → 投資、遺產、被動收入機會
     * ✅ 第十一宮（偏財）+ 木星 / 金星 → 偏財運（如獎金、股票、投資）
     * ✅ 金星（財富小吉星）+ 位置 / 相位 → 財務能力與收入模式
     * ✅ 木星（土星 / 冥王星） → 影響財富成長或財務風險
     * ✅ 行運影響 → 當下財運趨勢
     */
    fun toFinancialLuckString(): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用占星學判斷財帛運(繁體中文):\n")
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")
        // 取得上升星座
        text.append("上升星座: ${houses.signBeanList[0].chName}\n")

        getHouseInfo(2, houses, planetList, text)
        getHouseInfo(8, houses, planetList, text)
        getHouseInfo(11, houses, planetList, text)

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_VENUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_SATURN,
            SweConst.SE_PLUTO,
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }

    /**
     * 總結
     * 第十宮（MC） 決定你的事業方向與社會地位。
     * 第十宮內的行星 影響你的職業發展模式。
     * 太陽 代表你的核心志向，落在哪個宮位會影響你在哪裡發光發熱。
     * 土星 代表事業穩定度與需要努力的方向。
     * 木星 代表事業機會，影響是否容易獲得貴人或發展順遂。
     * 行運星體 影響短期的事業發展，例如木星帶來機會，土星帶來挑戰。
     */
    fun toCareerLuckString(): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用占星學判斷事業方向(繁體中文):\n")
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")

        getHouseInfo(1, houses, planetList, text)
        getHouseInfo(10, houses, planetList, text)

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_SUN,
            SweConst.SE_SATURN,
            SweConst.SE_JUPITER,
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }

    /**
     * 健康的占星特徵
     * 1. 六宮（House 6）— 健康、疾病與日常狀況
     * 掌管日常健康、疾病、生活習慣、工作環境。
     * 健康的強弱與六宮的星座、行星以及宮主星位置密切相關。
     * 六宮宮主星：如果宮主星與其他行星有關聯，能顯示身體容易出現的問題或易受影響的部位。
     * 六宮內行星：影響身體的具體部分或可能的疾病類型。
     * 六宮星座：顯示健康的易受影響部位（例如，雙魚座掌管免疫系統、慢性病）。
     * 2. 第一宮（House 1）— 體質與健康狀況
     * 第一宮掌管個人健康體質、遺傳因素以及身體的整體狀態。
     * 上升星座：顯示你的天生體質與易受影響的健康領域。
     * 例如，天秤座（控制腎臟、泌尿系統）可能面臨這些區域的健康問題。
     * 3. 太陽與月亮
     * 太陽：代表整體的生命力、健康狀態、活力來源。位置與相位可揭示能量的強弱。
     * 月亮：影響身體的適應能力，特別是情緒對健康的影響。月亮的相位可顯示壓力如何影響身體。
     * 4. 木星與土星
     * 木星：影響擴張，健康問題通常與過度、肥胖、發炎相關。
     * 例如，木星在六宮可能顯示你容易過度飲食或不注意健康習慣，導致體重問題。
     * 土星：掌管結構與限制，通常與骨骼、牙齒、慢性病、疲勞等問題有關。
     * 土星在六宮顯示可能有慢性疾病或需要長期治療的健康問題。
     * 5. 凶相（刑、沖、對分）
     * 刑（Square）、**沖（Opposition）**等凶相可能表示健康方面的挑戰，尤其是對個人行星（如太陽、月亮、水星等）的刑沖，可能會造成身體的問題或慢性疾病。
     *
     */
    fun toHealthString(): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用占星學判斷健康狀況(繁體中文):\n")
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")

        getHouseInfo(1, houses, planetList, text)
        getHouseInfo(6, houses, planetList, text)

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_SATURN,
            SweConst.SE_JUPITER,
            SweConst.SE_MERCURY,
            SweConst.SE_MARS,
            SweConst.SE_CHIRON
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }

    /**
     * 🌟 個人星盤（Natal Chart）— 愛情模式與需求
     * 1. 戀愛傾向（吸引力與愛情需求）
     *    金星所在星座、宮位 → 代表戀愛風格、愛的表達方式
     *    火星所在星座、宮位 → 代表吸引力、激情表達
     *    月亮的星座、宮位 → 代表內心的情感需求，影響戀愛中的安全感
     *    太陽 & 上升星座 → 影響戀愛中的自我呈現方式
     * 2. 戀愛&婚姻宮位
     *    第5宮（戀愛） → 是否容易墜入愛河，戀愛風格
     *    第7宮（婚姻伴侶） → 適合的長期伴侶類型、關係特質
     *    第8宮（親密關係） → 性吸引力、深度連結
     *    南北交點 → 與前世因緣、命定戀情的關聯
     * 3. 關鍵相位
     *    金星 & 火星的相位 → 表示戀愛吸引力、性化學反應
     *    金星 & 土星合相／對分 → 穩定但有責任感，或關係有壓力
     *    金星 & 冥王星合相／對分 → 強烈吸引力、佔有慾、情感控制
     *    月亮 & 金星相位 → 是否能溫柔地表達愛意
     *    月亮 & 土星相位 → 情感壓抑或有長遠承諾
     *    海王星強影響（金星、月亮、7宮） → 容易陷入夢幻、理想化愛情，甚至遭遇曖昧或欺騙
     */
    fun toLoveLuckString(): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用占星學判斷感情狀況(繁體中文):\n")
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")
        // 取得上升星座
        text.append("上升星座: ${houses.signBeanList[0].chName}\n")

        getHouseInfo(5, houses, planetList, text)
        getHouseInfo(7, houses, planetList, text)
        getHouseInfo(8, houses, planetList, text)

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_VENUS,
            SweConst.SE_MARS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_MEAN_NODE, // 北交點
            60 // 南交點
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }

    /**
     * 六親運的論斷
     * 1. 出生星盤（本命盤）
     * 你的本命盤已提供，可以用來分析六親運。重點關鍵宮位如下：
     *
     * 父母運：4宮（家庭）、10宮（事業）、太陽（父親）、月亮（母親）
     * 兄弟姊妹：3宮（手足宮）、宮主星的位置與相位
     * 配偶運：7宮（婚姻宮）、宮主星的位置與相位、金星（戀愛）、火星（男性宮位分析）、月亮（女性宮位分析）
     * 子女運：5宮（子女宮）、宮主星的位置與相位
     * 祖先家族：4宮（祖業、家族根基）、8宮（遺產、祖產）、12宮（隱藏的家族因果）
     *
     * 2. 需要的補充資料
     * 完整出生時間（時、分）：這影響宮位位置，確保準確性。（目前你的資料應該已經有正確的時間，因為有宮位）
     * 有無已知的家族影響：如家庭背景、遺產糾紛、婚姻狀況、兄弟姊妹關係等，可幫助更準確解讀。
     * 🔹 3. 如何分析六親運？
     * 查看關鍵宮位與行星
     *
     * 宮位本身的星座特質
     * 宮內行星影響（如4宮有海王星，家庭可能較混亂或隱藏秘密）
     * 宮主星落點（如4宮宮主星落12宮，家庭可能有隱藏問題）
     * 相位影響（如太陽刑土星，代表與父親關係較嚴格或有壓力）
     * 吉凶影響
     *
     * 吉相（六合、三分相）：代表較和諧，與該親屬關係較佳
     * 凶相（對分、四分相）：代表困難、挑戰、疏離、摩擦
     * 結合整體盤勢
     *
     * 例如：7宮（婚姻宮）如果有土星，可能婚姻較遲緩或有責任壓力，但若有吉星輔助（如金星、木星），則可改善。
     */
    fun toSixRelativesLuckString(): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用占星學判斷六親運(繁體中文):\n")
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")


        getHouseInfo(3, houses, planetList, text)
        getHouseInfo(4, houses, planetList, text)
        getHouseInfo(5, houses, planetList, text)
        getHouseInfo(8, houses, planetList, text)
        getHouseInfo(12, houses, planetList, text)

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_MARS,
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
            SweConst.SE_URANUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_PLUTO,
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }


    /**
     * 人際運的論斷
     * 📌 總結
     *
     * 第七宮 代表人際互動模式與合作關係。
     * 第十一宮 代表朋友、社交圈與貴人運。
     * 水星 影響溝通能力與社交風格。
     * 金星 影響人際吸引力與關係中的和諧度。
     * 木星與土星 影響貴人運與長期人際關係的穩定性。
     */
    fun toInterpersonalLuckString(): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用占星學判斷人際運(繁體中文):\n")
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")

        getHouseInfo(7, houses, planetList, text)
        getHouseInfo(11, houses, planetList, text)

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }

    /**
     * 📌 意外風險的占星特徵
     * 1. 火星（Mars）— 意外、事故與傷害
     *    火星掌管行動、運動、事故、急躁行為、受傷等。
     *    火星的相位與位置：例如，火星與冥王星或天王星有強相位（如刑、沖），可能表示意外或突發事件的風險。
     *    火星在某些宮位：例如，火星落在第一宮（容易受傷）或第六宮（容易因過度勞累受傷）可能增加受傷風險。
     * 2. 天王星（Uranus）— 突發性事件與意外
     *    天王星代表突如其來的事件、變化與不穩定性，它與意外、不可預測的事故、科技相關的危險有關。
     *    天王星的相位：例如，天王星與火星或冥王星形成刑或沖，可能表現為交通事故或突發的健康危機。
     * 3. 第八宮（House 8）— 危機、重大變故、深層衝擊
     *    第八宮掌管死亡、危機、重大變故，包括因為手術、暴力或事故引起的身體傷害或疾病。
     *    冥王星是第八宮的主星，會強烈影響這些領域。冥王星的不良相位，特別是與火星、天王星等行星的刑沖，可能增加受重傷或死亡風險。
     * 4. 第十二宮（House 12）— 隱藏的危險與潛在風險
     *    第十二宮掌管隱秘、隱藏的敵人與疾病。它也與長期健康問題、慢性病或無法立即發現的健康風險有關。
     *    如果有行星落在第十二宮，可能會有潛在的健康問題，難以察覺或被忽略，可能導致突然爆發的疾病。
     * 5. 刑沖相位（Square & Opposition）與意外
     *    火星刑天王星：常與突發事故、車禍或運動傷害有關。
     *    火星刑冥王星：強烈的身體暴力或重傷風險，可能在極端情況下出現。
     *    水星刑木星：注意力不集中，可能因分心或過度自信造成事故。
     *    📝 總結
     *    意外風險的占星特徵：
     *    火星：行動、運動、受傷
     *    天王星：突發事件、不可預測的意外
     *    第八宮：重大變故、危機
     *    第十二宮：隱藏的危險、潛在的健康問題
     *    刑沖相位：增加意外風險的因素
     */
    fun toBlessingsDisastersString(): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用占星學判斷意外風險(繁體中文):\n")
        text.append("名稱: $name\n")
//        text.append("出生日期: ${getBirthdayString()}\n")
        // 取得上升星座
        text.append("上升星座: ${houses.signBeanList[0].chName}\n")

        getHouseInfo(8, houses, planetList, text)
        getHouseInfo(12, houses, planetList, text)

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_MARS,
            SweConst.SE_URANUS,
        ).forEach { id ->
            appendPlanetInfo(text, id)
        }

        LogUtil.d(text.toString())
        return text.toString()
    }

    /**
     * 卜卦占星 Horary Astrology
     * 1.提出問題的時間地點
     * 2021年12月31日 12:00:00
     * 2.問題的具體內容
     */
    fun toHoraryString(question: String): String {
        if (name.isEmpty()) return ""

        val text = StringBuilder("\n利用卜卦占星解讀(繁體中文):\n")
        text.append("名稱: $name\n")
        text.append("日期: ${getBirthdayString()}\n")
        text.append("地點: 緯度:${latLng.latitude}, 經度:${latLng.longitude}\n")
        text.append("問題: ${question}\n")


        LogUtil.d(text.toString())
        return text.toString()
    }

    private fun getHouseInfo(
        houseIndex: Int,
        houses: Houses,
        planetList: List<PlanetBean>,
        text: StringBuilder
    ) {
        val sign = houses.signBeanList[houseIndex - 1]
        text.append("${houseIndex}宮星座: ${sign.chName}\n${houseIndex}宮宮主星: ${sign.ruler}\n")

        // 取得宮主星的行星資訊
        val rulerPlanet = planetList.find { it.chName == sign.ruler }
        rulerPlanet?.let {
            text.append("${houseIndex}宮宮主星飛到${it.houseData.index}宮${it.signBean.chName}\n")
            appendAspects(text, it.chName)
            text.append("${houseIndex}宮的度數: ${it.signBean.degree}${it.signBean.minute}\n")
        }

        // 列出宮內的行星
        val inHousePlanets = planetList.filter { it.houseData.index == houseIndex }
        if (inHousePlanets.isNotEmpty()) {
            inHousePlanets.forEach { text.append("${houseIndex}宮內行星: ${it.chName} ${it.signBean.chName}\n") }
        } else {
            text.append("${houseIndex}宮內無行星\n")
        }
    }

    // 附加行星的相位資訊
    private fun appendAspects(text: StringBuilder, planetName: String) {
        aspectList.filter { it.planetA == planetName }.forEach {
            text.append("${it.planetA} ${it.type} ${it.planetB}\n")
        }
    }

    /**
     * 本命格（Natal Chart）即本命盤，是根據你的出生時間、地點與日期繪製出的個人星盤。它反映你的天生性格、潛力、事業發展、人際關係等。
     * 請根據以下的星盤資訊，提供一份詳細的占星解讀：
     * - 太陽：天蠍座 12°（12 宮）
     * - 月亮：天蠍座 18°（12 宮）
     * - 上升：射手座 5°
     * - 水星：天蠍座 10°（12 宮）
     * - 金星：射手座 15°（1 宮）
     * - 火星：摩羯座 8°（1 宮）
     * - 木星：雙子座 20°（7 宮）
     */
    // 取得行星的宮位、星座與相位資訊
    private fun appendPlanetInfo(text: StringBuilder, planetId: Int) {
        planetList.find { it.id == planetId }?.let {
            val isRetrograde = if (it.isRetrograde) {
                "逆行"
            } else {
                ""
            }
            text.append("${it.chName}：${it.signBean.chName} ${it.signBean.degree} ${it.signBean.minute} (${it.houseData.index} 宮) $isRetrograde\n")
            appendAspects(text, it.chName)
        }
    }


}