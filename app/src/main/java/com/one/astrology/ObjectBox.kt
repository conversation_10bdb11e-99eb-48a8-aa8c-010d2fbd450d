package com.one.astrology

import android.content.Context
import android.util.Log
import com.one.astrology.data.MyObjectBox
import io.objectbox.BoxStore
import io.objectbox.android.Admin


object ObjectBox {
    private lateinit var store: BoxStore

    fun init(context: Context) {
        store = MyObjectBox.builder()
            .androidContext(context.applicationContext)
            .build()

//        boxStore = MyObjectBox.builder().androidContext(this).build()
        if (BuildConfig.DEBUG) {
            val started = Admin(store).start(context.applicationContext)
            Log.i("ObjectBoxAdmin", "Started: $started")
        }
    }

    fun get():BoxStore{
        return store
    }
}