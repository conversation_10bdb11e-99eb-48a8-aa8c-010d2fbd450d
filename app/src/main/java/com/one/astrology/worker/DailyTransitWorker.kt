package com.one.astrology.worker

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.one.astrology.ObjectBox
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.entity.BirthData_
import com.one.astrology.data.model.NotificationSettings
import com.one.astrology.data.model.TransitEvent
import com.one.astrology.data.model.TransitEventType
import com.one.astrology.data.model.TransitImportance
import com.one.astrology.util.NotificationUtil
import com.one.astrology.util.TransitCalculator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.one.core.util.LogUtil as CoreLogUtil

/**
 * 每日行運推播 Worker
 * 負責計算當日的行運事件並發送推播通知
 */
class DailyTransitWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        const val WORK_NAME = "daily_transit_notification"
        const val TAG = "DailyTransitWorker"
    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            CoreLogUtil.d("$TAG: 開始執行每日行運推播任務")

            // 檢查推播設定
            val notificationSettings = getNotificationSettings()
            CoreLogUtil.d("$TAG: 推播設定 - 開關: ${notificationSettings.isDailyTransitEnabled}, 時間: ${notificationSettings.getNotificationTimeString()}")

            if (!notificationSettings.isDailyTransitEnabled) {
                CoreLogUtil.d("$TAG: 推播功能已關閉，跳過執行")
                return@withContext Result.success()
            }

            // 獲取選中的出生資料
            val selectedBirthData = getSelectedBirthData()
            if (selectedBirthData == null) {
                CoreLogUtil.e("$TAG: 未找到選中的出生資料，跳過執行")
                return@withContext Result.success()
            }

            CoreLogUtil.d("$TAG: 找到出生資料: ${selectedBirthData.name}")

            // 計算今日的行運事件
            val transitEvents = calculateTodayTransitEvents(selectedBirthData, notificationSettings)
            CoreLogUtil.d("$TAG: 計算到 ${transitEvents.size} 個行運事件")

            // 篩選重要事件
            val importantEvents = filterImportantEvents(transitEvents, notificationSettings)
            CoreLogUtil.d("$TAG: 篩選出 ${importantEvents.size} 個重要事件")

            if (importantEvents.isNotEmpty()) {
                // 發送推播通知
                sendNotification(selectedBirthData, importantEvents, notificationSettings)

                // 保存事件到資料庫
                saveTransitEvents(importantEvents)

                CoreLogUtil.d("$TAG: 成功發送 ${importantEvents.size} 個行運事件推播")
            } else {
                CoreLogUtil.d("$TAG: 今日無重要行運事件，發送測試推播")
                // 如果沒有重要事件，發送一個測試推播
                sendTestNotification(selectedBirthData, notificationSettings)
            }

            Result.success()
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 執行每日行運推播任務時發生錯誤: ${e.message}")
            Result.failure()
        }
    }

    /**
     * 獲取推播設定
     */
    private fun getNotificationSettings(): NotificationSettings {
        return try {
            val box = ObjectBox.get().boxFor(NotificationSettings::class.java)
            box.query().build().findFirst() ?: NotificationSettings.getDefault()
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 獲取推播設定失敗: ${e.message}")
            NotificationSettings.getDefault()
        }
    }

    /**
     * 獲取選中的出生資料
     */
    private fun getSelectedBirthData(): BirthData? {
        return try {
            val birthDataBox = ObjectBox.get().boxFor(BirthData::class.java)
            val selectedData = birthDataBox.query(BirthData_.isSelected.equal(true)).build().find()
            if (selectedData.isNotEmpty()) {
                selectedData[0]
            } else {
                // 如果沒有選中的，取第一個
                birthDataBox.query().order(BirthData_.id).build().findFirst()
            }
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 獲取出生資料失敗: ${e.message}")
            null
        }
    }

    /**
     * 計算今日的行運事件
     */
    private fun calculateTodayTransitEvents(
        birthData: BirthData,
        settings: NotificationSettings
    ): List<TransitEvent> {
        return try {
            val calculator = TransitCalculator()
            val currentTime = System.currentTimeMillis()
            
            // 計算未來幾天的事件（根據設定的提前通知天數）
            calculator.calculateTransitEvents(
                context = applicationContext,
                birthData = birthData,
                targetDate = currentTime,
                daysRange = settings.advanceNoticeDays * 2 + 1
            )
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 計算行運事件失敗: ${e.message}")
            emptyList()
        }
    }

    /**
     * 篩選重要事件
     */
    private fun filterImportantEvents(
        events: List<TransitEvent>,
        settings: NotificationSettings
    ): List<TransitEvent> {
        return events.filter { event ->
            // 檢查是否應該推播此事件
            settings.shouldNotifyEvent(event) &&
            // 檢查是否在容許度範圍內（對於相位事件）
            (!event.isAspectEvent() || settings.isWithinOrb(event.orb)) &&
            // 檢查是否未推播過
            !event.isNotified
        }.sortedByDescending { it.importance.level }
            .take(5) // 最多推播5個最重要的事件
    }

    /**
     * 發送推播通知
     */
    private fun sendNotification(
        birthData: BirthData,
        events: List<TransitEvent>,
        settings: NotificationSettings
    ) {
        try {
            val title = "今日行運提醒 - ${birthData.name}"
            val content = when (events.size) {
                1 -> events[0].getEventTitle()
                else -> "${events[0].getEventTitle()} 等 ${events.size} 個事件"
            }

            NotificationUtil.showTransitNotification(
                context = applicationContext,
                title = title,
                content = content,
                events = events,
                birthData = birthData,
                enableSound = settings.isSoundEnabled,
                enableVibration = settings.isVibrationEnabled
            )
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 發送推播通知失敗: ${e.message}")
        }
    }

    /**
     * 保存行運事件到資料庫
     */
    private fun saveTransitEvents(events: List<TransitEvent>) {
        try {
            val box = ObjectBox.get().boxFor(TransitEvent::class.java)
            events.forEach { event ->
                event.isNotified = true
                event.createTime = System.currentTimeMillis()
            }
            box.put(events)
            CoreLogUtil.d("$TAG: 已保存 ${events.size} 個行運事件到資料庫")
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 保存行運事件失敗: ${e.message}")
        }
    }

    /**
     * 發送測試推播通知
     */
    private fun sendTestNotification(
        birthData: BirthData,
        settings: NotificationSettings
    ) {
        try {
            val testEvent = TransitEvent().apply {
                eventType = TransitEventType.ASPECT_FORMING
                transitPlanetName = ""
                natalPlanetName = ""
                aspectName = ""
                orb = 0.0
                importance = TransitImportance.HIGH
                description = "每日推播"
                birthDataName = birthData.name
            }

            val title = "今日行運提醒 - ${birthData.name}"
            val content = "無特殊事件"

            NotificationUtil.showTransitNotification(
                context = applicationContext,
                title = title,
                content = content,
                events = listOf(testEvent),
                birthData = birthData,
                enableSound = settings.isSoundEnabled,
                enableVibration = settings.isVibrationEnabled
            )

            CoreLogUtil.d("$TAG: 已發送測試推播")
        } catch (e: Exception) {
            CoreLogUtil.e("$TAG: 發送測試推播失敗: ${e.message}")
        }
    }
}
