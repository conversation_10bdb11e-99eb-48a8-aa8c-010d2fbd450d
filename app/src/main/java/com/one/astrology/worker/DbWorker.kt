package com.one.astrology.worker

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.one.astrology.R
import com.one.astrology.data.entity.BirthData
import com.one.astrology.db.OtherDBHelper
import com.one.astrology.ui.activity.SplashActivity

class DbWorker (private val appContext: Context, workerParams: WorkerParameters) :
    Worker(appContext, workerParams) {

    private fun autoTransitCalculate(
        context: Context,
        signRecordA: BirthData,
        signRecordB: BirthData,
        index: Int
    ) {
//        LiveEventBus.get<String>(EventKey.AutoTransitCalculate).postDelay(json, 0)
        showNotification()
    }


    override fun doWork(): Result {
        OtherDBHelper.queryCategories(appContext)
        return Result.success()
    }

    private fun showNotification() {
        val channelLove = NotificationChannel(
            "auto",
            "Channel",
            NotificationManager.IMPORTANCE_HIGH
        )
        channelLove.description = "自動推運"
        channelLove.enableLights(true)
        channelLove.enableVibration(true)

        val notificationIntent = Intent(appContext, SplashActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        notificationIntent.action = "auto_calculation"

        val contentIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivity(
                appContext,
                0,
                notificationIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
            )
        } else {
            PendingIntent.getActivity(
                appContext,
                0,
                notificationIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        }
        // 發送通知
        val notificationManager =
            appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channelLove)
        // 設置通知的標題、文本和圖標
        val builder = NotificationCompat.Builder(appContext, "default")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("處理完成")
            .setContentText("數據處理已完成！")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(contentIntent)
            .setChannelId("auto")
            .setAutoCancel(true)

        // 設置通知的聲音、震動和燈光等效果
        builder.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
        builder.setVibrate(longArrayOf(1000, 1000, 1000, 1000, 1000))
        builder.setLights(Color.RED, 3000, 3000)

        // 發送通知
        notificationManager.notify("tag", 100, builder.build())

//        with(NotificationManagerCompat.from(appContext)) {
//            if (ActivityCompat.checkSelfPermission(appContext, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
//                return
//            }
//            notify("tag", 100, builder.build())
//        }
    }
}