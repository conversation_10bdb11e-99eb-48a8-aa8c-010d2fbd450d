package com.one.astrology.analyzer

import com.one.astrology.model.Dignity
import com.one.astrology.model.PlanetAnalysisResult
import com.one.astrology.model.PlanetCondition

object PlanetStrengthAnalyzer {
    fun analyze(condition: PlanetCondition): PlanetAnalysisResult {
        var score = 0
        val notes = mutableListOf<String>()

        // 尊貴狀態
        when (condition.dignity) {
            Dignity.DOMICILE -> { score += 5; notes += "守護宮，力量穩定強大" }
            Dignity.EXALTATION -> { score += 4; notes += "旺地，表現出色" }
            Dignity.DETRIMENT -> { score -= 4; notes += "弱地，表現困難" }
            Dignity.FALL -> { score -= 5; notes += "陷地，力量極弱" }
            else -> {}
        }

        // 特殊狀態
        if (condition.isRetrograde) notes += "逆行，內省多於外在表現".also { score -= 3 }
        if (condition.isCombust) notes += "燃燒，被太陽吞噬".also { score -= 5 }
        if (condition.isUnderSunBeams) notes += "灼傷，表現模糊".also { score -= 2 }
        if (condition.isCazimi) notes += "日核內，獲神聖加持".also { score += 5 }
        if (condition.isVoidOfCourse) notes += "空亡，行動無果".also { score -= 3 }
        if (condition.isInFeralSign) notes += "野性星座，難以控制".also { score -= 2 }
        if (condition.hasGoodAspects) notes += "吉相支援，正能量加持".also { score += 3 }
        if (condition.hasBadAspects) notes += "凶相挑戰，外在受阻".also { score -= 3 }
        if (condition.isInAngularHouse) notes += "角宮落點，力量明顯".also { score += 2 }
        if (condition.conjunctNorthNode) notes += "合北交，順勢發展".also { score += 1 }
        if (condition.conjunctSouthNode) notes += "合南交，宿命課題重現".also { score -= 1 }

        return PlanetAnalysisResult(condition.name, score, notes.joinToString("；"))
    }
}
