package com.one.astrology.event

import com.one.astrology.data.Horoscope
import com.one.astrology.data.Houses
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.type.Chart
import swisseph.SweConst

class MatchEvent {
    var chartType: Chart = Chart.Natal

    var horoscope: Horoscope = Horoscope()
    var horoscopeA: Horoscope = Horoscope()
    var horoscopeB: Horoscope = Horoscope()
    var timeStamp: Long = 0

    var birthDataA: BirthData = BirthData()
    var birthDataB: BirthData = BirthData()

    var isSingle = true

    var planetListA = ArrayList<PlanetBean>()
    var planetListB = ArrayList<PlanetBean>()
    var houses = Houses()
    var aspectList = ArrayList<AspectData>()

    constructor()

    constructor(birthDataA: BirthData, birthDataB: BirthData, chartType: Chart) {
        this.birthDataA = birthDataA
        this.birthDataB = birthDataB
        this.chartType = chartType
    }

    constructor(birthDataA: BirthData, chartType: Chart) {
        this.birthDataA = birthDataA
        this.chartType = chartType
    }

    constructor(horoscopeA: Horoscope, chartType: Chart) {
        this.horoscopeA = horoscopeA
        this.chartType = chartType
    }

    constructor(horoscopeA: Horoscope, horoscopeB: Horoscope, chartType: Chart) {
        this.horoscopeA = horoscopeA
        this.horoscopeB = horoscopeB
        this.chartType = chartType
    }

    fun toAllStringA(chartName: String): String {
        if (horoscopeA.name.isEmpty()) return ""
        val text = StringBuilder()
        when (chartType) {
            Chart.Synastry -> {
                text.append("名稱: ${birthDataB.name}(行星方) 在 ${birthDataA.name}(宮位方) 的星盤中\n")
                text.append("${chartName}的資訊\n")
            }

            Chart.Davison -> {
                text.append("名稱: ${birthDataB.name} vs ${birthDataA.name}\n")
                text.append("雙方${chartName} (Davison Chart) 中的資訊\n")
            }

            Chart.Composite -> {
                text.append("名稱: ${birthDataB.name} vs ${birthDataA.name}\n")
                text.append("雙方${chartName}中的資訊\n")
            }

            Chart.Marks -> {
                text.append("名稱: ${birthDataA.name}(主盤) 在 ${birthDataB.name}(副盤)\n")
                text.append("${chartName}的資訊\n")
            }

            else -> {
                text.append("名稱: ${birthDataA.name}\n")
                text.append("以下是${chartName}中的資訊\n")
            }
        }

        //        text.append("出生日期: ${horoscopeA.getBirthdayString()}\n")

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_MARS,
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
            SweConst.SE_URANUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_PLUTO,
        ).forEach { id ->
            appendPlanetInfo(text, planetListA, id)
        }

        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_MARS,
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
            SweConst.SE_URANUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_PLUTO,
        ).forEach { id ->
            appendAspectsInfo(text, planetListA, id)
        }
        return text.toString()
    }

    fun toAllStringB(chartName: String): String {
        if (horoscopeB.name.isEmpty()) return ""
        val text = StringBuilder()
        when (chartType) {
            Chart.Synastry -> {
                text.append("名稱: ${birthDataA.name}(行星方) 在 ${birthDataB.name}(宮位方) 的星盤中\n")
                text.append("${chartName}的資訊\n")
            }

            Chart.Davison -> {
                text.append("名稱: ${birthDataA.name} vs ${birthDataB.name}\n")
                text.append("雙方${chartName} (Davison Chart) 中的資訊\n")
            }

            Chart.CompositeSecondaryProgression,
            Chart.Composite -> {
                text.append("名稱: ${birthDataA.name} vs ${birthDataB.name}\n")
                text.append("雙方${chartName}中的資訊\n")
            }

            else -> {
                text.append("名稱: ${birthDataB.name}\n")
                text.append("以下是${chartName}中的資訊\n")
            }
        }

        //        text.append("出生日期: ${horoscopeA.getBirthdayString()}\n")

        // 特定行星的詳細資訊
        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_MARS,
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
            SweConst.SE_URANUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_PLUTO,
        ).forEach { id ->
            appendPlanetInfo(text, planetListB, id)
        }

        listOf(
            SweConst.SE_SUN,
            SweConst.SE_MOON,
            SweConst.SE_MERCURY,
            SweConst.SE_VENUS,
            SweConst.SE_MARS,
            SweConst.SE_JUPITER,
            SweConst.SE_SATURN,
            SweConst.SE_URANUS,
            SweConst.SE_NEPTUNE,
            SweConst.SE_PLUTO,
        ).forEach { id ->
            appendAspectsInfo(text, planetListB, id)
        }
        return text.toString()
    }

    // 取得行星的宮位、星座與相位資訊
    private fun appendPlanetInfo(
        text: StringBuilder,
        planetList: ArrayList<PlanetBean>,
        planetId: Int
    ) {
        planetList.find { it.id == planetId }?.let {
            text.append("${it.chName}：${it.signBean.chName} ${it.signBean.degree} ${it.signBean.minute} (${it.signBean.houseData.index} 宮)\n")
        }
    }

    private fun appendAspectsInfo(
        text: StringBuilder,
        planetList: ArrayList<PlanetBean>,
        planetId: Int
    ) {
        planetList.find { it.id == planetId }?.let {
            appendAspects(text, it.chName)
        }
    }

    // 附加行星的相位資訊
    private fun appendAspects(text: StringBuilder, planetName: String) {
        aspectList.filter { it.planetA == planetName }.forEach {
            text.append("${it.planetA} ${it.type} ${it.planetB}\n")
        }
    }
}