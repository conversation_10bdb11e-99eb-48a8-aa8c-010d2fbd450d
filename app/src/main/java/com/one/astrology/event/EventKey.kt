package com.one.astrology.event

object EventKey {
    const val MatchEvent = "MatchEvent"
    const val AddUserBirthData = "AddUserBirthData"
    const val LatLng = "LatLng"
    const val UpdateUserBirthData = "UpdateUserBirthData"
    const val UpdateChart = "UpdateChart"
    const val UpdateChartA = "UpdateChartA"
    const val UpdateChartB = "UpdateChartB"
    const val AutoTransitCalculate = "autoTransitCalculate"
    const val UserBirthData = "UserBirthData"
    const val CloseSnackbar = "CloseSnackbar"
    const val RefreshChart = "RefreshChart"

    const val FilterSort = "FilterSort"
    const val Default = "Default"
    const val AstroFilter = "AstroFilter"
}