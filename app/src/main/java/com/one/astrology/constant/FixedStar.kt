package com.one.astrology.constant

/**
 * 固定星常量定義
 * 包含常用固定星的ID和名稱
 */
object FixedStar {
    // 固定星ID定義（從1000開始，避免與行星和阿拉伯點ID衝突）
    const val SIRIUS = 1000       // 天狼星
    const val CANOPUS = 1001      // 老人星
    const val ARCTURUS = 1002     // 大角星
    const val VEGA = 1003         // 織女星
    const val CAPELLA = 1004      // 五車二
    const val RIGEL = 1005        // 參宿七
    const val PROCYON = 1006      // 南河三
    const val BETELGEUSE = 1007   // 參宿四
    const val ACHERNAR = 1008     // 水委一
    const val HADAR = 1009        // 半人馬座β星
    const val ALTAIR = 1010       // 牛郎星
    const val ACRUX = 1011        // 南十字座α星
    const val ALDEBARAN = 1012    // 畢宿五
    const val ANTARES = 1013      // 心宿二
    const val SPICA = 1014        // 角宿一
    const val POLLUX = 1015       // 北河二
    const val FOMALHAUT = 1016    // 北落師門
    const val DENEB = 1017        // 天津四
    const val MIMOSA = 1018       // 南十字座β星
    const val REGULUS = 1019      // 軒轅十四
    const val CASTOR = 1020       // 北河三
    const val SHAULA = 1021       // 尾宿八
    const val BELLATRIX = 1022    // 參宿五
    const val ELNATH = 1023       // 五車五
    const val MIAPLACIDUS = 1024  // 船底座β星
    const val ALNILAM = 1025      // 參宿三
    const val ALNAIR = 1026       // 水委四
    const val ALNITAK = 1027      // 參宿一
    const val ALIOTH = 1028       // 北斗五
    const val DUBHE = 1029        // 北斗一
    
    // 重要的皇家星（Royal Stars）
    const val REGULUS_ROYAL = REGULUS    // 軒轅十四（獅子座的心臟）
    const val ALDEBARAN_ROYAL = ALDEBARAN // 畢宿五（金牛座的眼睛）
    const val ANTARES_ROYAL = ANTARES    // 心宿二（天蠍座的心臟）
    const val FOMALHAUT_ROYAL = FOMALHAUT // 北落師門（南魚座的嘴）
    
    // 固定星列表，用於UI顯示和選擇
    val FIXED_STARS = listOf(
        SIRIUS,
        CANOPUS,
        ARCTURUS,
        VEGA,
        CAPELLA,
        RIGEL,
        PROCYON,
        BETELGEUSE,
        ACHERNAR,
        HADAR,
        ALTAIR,
        ACRUX,
        ALDEBARAN,
        ANTARES,
        SPICA,
        POLLUX,
        FOMALHAUT,
        DENEB,
        MIMOSA,
        REGULUS,
        CASTOR,
        SHAULA,
        BELLATRIX,
        ELNATH,
        MIAPLACIDUS,
        ALNILAM,
        ALNAIR,
        ALNITAK,
        ALIOTH,
        DUBHE
    )
    
    // 皇家星列表
    val ROYAL_STARS = listOf(
        REGULUS_ROYAL,
        ALDEBARAN_ROYAL,
        ANTARES_ROYAL,
        FOMALHAUT_ROYAL
    )
    
    // 獲取固定星中文名稱
    fun getChineseName(id: Int): String {
        return when (id) {
            SIRIUS -> "天狼星"
            CANOPUS -> "老人星"
            ARCTURUS -> "大角星"
            VEGA -> "織女星"
            CAPELLA -> "五車二"
            RIGEL -> "參宿七"
            PROCYON -> "南河三"
            BETELGEUSE -> "參宿四"
            ACHERNAR -> "水委一"
            HADAR -> "半人馬座β星"
            ALTAIR -> "牛郎星"
            ACRUX -> "南十字座α星"
            ALDEBARAN -> "畢宿五"
            ANTARES -> "心宿二"
            SPICA -> "角宿一"
            POLLUX -> "北河二"
            FOMALHAUT -> "北落師門"
            DENEB -> "天津四"
            MIMOSA -> "南十字座β星"
            REGULUS -> "軒轅十四"
            CASTOR -> "北河三"
            SHAULA -> "尾宿八"
            BELLATRIX -> "參宿五"
            ELNATH -> "五車五"
            MIAPLACIDUS -> "船底座β星"
            ALNILAM -> "參宿三"
            ALNAIR -> "水委四"
            ALNITAK -> "參宿一"
            ALIOTH -> "北斗五"
            DUBHE -> "北斗一"
            else -> "未知固定星"
        }
    }
    
    // 獲取固定星英文名稱
    fun getEnglishName(id: Int): String {
        return when (id) {
            SIRIUS -> "Sirius"
            CANOPUS -> "Canopus"
            ARCTURUS -> "Arcturus"
            VEGA -> "Vega"
            CAPELLA -> "Capella"
            RIGEL -> "Rigel"
            PROCYON -> "Procyon"
            BETELGEUSE -> "Betelgeuse"
            ACHERNAR -> "Achernar"
            HADAR -> "Hadar"
            ALTAIR -> "Altair"
            ACRUX -> "Acrux"
            ALDEBARAN -> "Aldebaran"
            ANTARES -> "Antares"
            SPICA -> "Spica"
            POLLUX -> "Pollux"
            FOMALHAUT -> "Fomalhaut"
            DENEB -> "Deneb"
            MIMOSA -> "Mimosa"
            REGULUS -> "Regulus"
            CASTOR -> "Castor"
            SHAULA -> "Shaula"
            BELLATRIX -> "Bellatrix"
            ELNATH -> "Elnath"
            MIAPLACIDUS -> "Miaplacidus"
            ALNILAM -> "Alnilam"
            ALNAIR -> "Alnair"
            ALNITAK -> "Alnitak"
            ALIOTH -> "Alioth"
            DUBHE -> "Dubhe"
            else -> "Unknown Fixed Star"
        }
    }
    
    // 獲取固定星所在星座
    fun getConstellation(id: Int): String {
        return when (id) {
            SIRIUS -> "大犬座"
            CANOPUS -> "船底座"
            ARCTURUS -> "牧夫座"
            VEGA -> "天琴座"
            CAPELLA -> "御夫座"
            RIGEL -> "獵戶座"
            PROCYON -> "小犬座"
            BETELGEUSE -> "獵戶座"
            ACHERNAR -> "波江座"
            HADAR -> "半人馬座"
            ALTAIR -> "天鷹座"
            ACRUX -> "南十字座"
            ALDEBARAN -> "金牛座"
            ANTARES -> "天蠍座"
            SPICA -> "室女座"
            POLLUX -> "雙子座"
            FOMALHAUT -> "南魚座"
            DENEB -> "天鵝座"
            MIMOSA -> "南十字座"
            REGULUS -> "獅子座"
            CASTOR -> "雙子座"
            SHAULA -> "天蠍座"
            BELLATRIX -> "獵戶座"
            ELNATH -> "金牛座"
            MIAPLACIDUS -> "船底座"
            ALNILAM -> "獵戶座"
            ALNAIR -> "天鶴座"
            ALNITAK -> "獵戶座"
            ALIOTH -> "大熊座"
            DUBHE -> "大熊座"
            else -> "未知星座"
        }
    }
    
    // 獲取固定星性質
    fun getNature(id: Int): String {
        return when (id) {
            SIRIUS -> "木星性、火星性"
            CANOPUS -> "土星性、木星性"
            ARCTURUS -> "火星性、木星性"
            VEGA -> "金星性、水星性"
            CAPELLA -> "水星性、火星性"
            RIGEL -> "木星性、土星性"
            PROCYON -> "水星性、火星性"
            BETELGEUSE -> "火星性、水星性"
            ACHERNAR -> "木星性"
            HADAR -> "金星性、水星性"
            ALTAIR -> "火星性、木星性"
            ACRUX -> "木星性、火星性"
            ALDEBARAN -> "火星性"
            ANTARES -> "火星性、木星性"
            SPICA -> "金星性、火星性"
            POLLUX -> "火星性"
            FOMALHAUT -> "金星性、水星性"
            DENEB -> "金星性、水星性"
            MIMOSA -> "土星性、金星性"
            REGULUS -> "火星性、木星性"
            CASTOR -> "水星性"
            SHAULA -> "水星性、火星性"
            BELLATRIX -> "火星性、水星性"
            ELNATH -> "火星性"
            MIAPLACIDUS -> "金星性、木星性"
            ALNILAM -> "木星性、土星性"
            ALNAIR -> "土星性、水星性"
            ALNITAK -> "火星性、水星性"
            ALIOTH -> "土星性、金星性"
            DUBHE -> "火星性"
            else -> "未知性質"
        }
    }
    
    // 獲取固定星象徵意義
    fun getSymbolism(id: Int): String {
        return when (id) {
            SIRIUS -> "光明、成功、榮譽、財富"
            CANOPUS -> "航海、旅行、探索"
            ARCTURUS -> "守護、保護、財富"
            VEGA -> "藝術、音樂、創造力"
            CAPELLA -> "知識、學習、教育"
            RIGEL -> "勇氣、冒險、探索"
            PROCYON -> "機智、敏捷、警覺"
            BETELGEUSE -> "力量、勇氣、成功"
            ACHERNAR -> "轉變、結束、新開始"
            HADAR -> "領導力、權威、決斷"
            ALTAIR -> "勇氣、決心、冒險"
            ACRUX -> "精神力量、信仰、靈性"
            ALDEBARAN -> "誠實、勇氣、正直"
            ANTARES -> "熱情、激情、競爭"
            SPICA -> "豐收、財富、藝術"
            POLLUX -> "勇氣、兄弟情誼、保護"
            FOMALHAUT -> "靈性、神秘、藝術"
            DENEB -> "慷慨、正義、善良"
            MIMOSA -> "犧牲、奉獻、精神"
            REGULUS -> "權力、領導、成功"
            CASTOR -> "智慧、敏捷、適應"
            SHAULA -> "危險、挑戰、轉變"
            BELLATRIX -> "軍事成就、榮譽、勝利"
            ELNATH -> "勇氣、力量、決心"
            MIAPLACIDUS -> "航海、旅行、探索"
            ALNILAM -> "力量、決心、成功"
            ALNAIR -> "轉變、結束、新開始"
            ALNITAK -> "力量、勇氣、決心"
            ALIOTH -> "智慧、判斷、決策"
            DUBHE -> "領導、指引、方向"
            else -> "未知象徵"
        }
    }
    
    // 獲取固定星關鍵詞
    fun getKeywords(id: Int): List<String> {
        return when (id) {
            SIRIUS -> listOf("光明", "成功", "榮譽", "財富", "名聲")
            CANOPUS -> listOf("航海", "旅行", "探索", "指引", "遠方")
            ARCTURUS -> listOf("守護", "保護", "財富", "收穫", "豐盛")
            VEGA -> listOf("藝術", "音樂", "創造力", "靈感", "美麗")
            CAPELLA -> listOf("知識", "學習", "教育", "智慧", "理解")
            RIGEL -> listOf("勇氣", "冒險", "探索", "遠見", "擴張")
            PROCYON -> listOf("機智", "敏捷", "警覺", "準備", "預見")
            BETELGEUSE -> listOf("力量", "勇氣", "成功", "榮譽", "勝利")
            ACHERNAR -> listOf("轉變", "結束", "新開始", "流動", "變化")
            HADAR -> listOf("領導力", "權威", "決斷", "力量", "影響")
            ALTAIR -> listOf("勇氣", "決心", "冒險", "速度", "行動")
            ACRUX -> listOf("精神力量", "信仰", "靈性", "超越", "神聖")
            ALDEBARAN -> listOf("誠實", "勇氣", "正直", "堅定", "決心")
            ANTARES -> listOf("熱情", "激情", "競爭", "挑戰", "力量")
            SPICA -> listOf("豐收", "財富", "藝術", "創造", "繁榮")
            POLLUX -> listOf("勇氣", "兄弟情誼", "保護", "支持", "合作")
            FOMALHAUT -> listOf("靈性", "神秘", "藝術", "夢想", "理想")
            DENEB -> listOf("慷慨", "正義", "善良", "高尚", "理想")
            MIMOSA -> listOf("犧牲", "奉獻", "精神", "超越", "信仰")
            REGULUS -> listOf("權力", "領導", "成功", "榮譽", "王者")
            CASTOR -> listOf("智慧", "敏捷", "適應", "學習", "溝通")
            SHAULA -> listOf("危險", "挑戰", "轉變", "力量", "勇氣")
            BELLATRIX -> listOf("軍事成就", "榮譽", "勝利", "力量", "保護")
            ELNATH -> listOf("勇氣", "力量", "決心", "堅持", "突破")
            MIAPLACIDUS -> listOf("航海", "旅行", "探索", "發現", "冒險")
            ALNILAM -> listOf("力量", "決心", "成功", "目標", "方向")
            ALNAIR -> listOf("轉變", "結束", "新開始", "變化", "流動")
            ALNITAK -> listOf("力量", "勇氣", "決心", "堅持", "成就")
            ALIOTH -> listOf("智慧", "判斷", "決策", "分析", "洞察")
            DUBHE -> listOf("領導", "指引", "方向", "穩定", "可靠")
            else -> listOf("未知關鍵詞")
        }
    }
    
    // 判斷是否為皇家星
    fun isRoyalStar(id: Int): Boolean {
        return ROYAL_STARS.contains(id)
    }
} 