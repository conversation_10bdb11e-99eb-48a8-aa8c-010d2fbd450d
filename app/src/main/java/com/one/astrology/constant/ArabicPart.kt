package com.one.astrology.constant

import com.one.astrology.data.bean.PlanetBean

/**
 * 阿拉伯點常量定義
 * 包含常用阿拉伯點的ID和名稱
 * | 名稱       | 英文名稱                        | 日生公式                        | 夜生公式                        |
 * |------------|---------------------------------|----------------------------------|----------------------------------|
 * | 幸運點     | Lot of Fortune（Lot of the Moon） | ASC + 月亮 - 太陽                | ASC + 太陽 - 月亮                |
 * | 精神點     | Lot of Spirit（Lot of the Sun）  | ASC + 太陽 - 月亮                | ASC + 月亮 - 太陽                |
 * | 金星點     | Lot of Venus（外貌、美感、愛情）   | 幸運點 + 精神點 - ASC            | 精神點 + 幸運點 - ASC            |
 * | 水星點     | Lot of Mercury（智力、批判、爭論） | 精神點 + 幸運點 - ASC            | 幸運點 + 精神點 - ASC            |
 * | → 基礎點   | Lot of Basis（堅忍、支持）        | 金星點或水星點中落於地平線下者   | 金星點或水星點中落於地平線下者   |
 * | 勇氣點     | Lot of Courage（火星點）          | 幸運點 + 火星 - ASC              | 火星 + 幸運點 - ASC              |
 * | 勝利點     | Lot of Victory（木星點）          | 精神點 + 木星 - ASC              | 木星 + 精神點 - ASC              |
 * | 報應點     | Lot of Nemesis（土星點）          | 土星 + 幸運點 - ASC              | 幸運點 + 土星 - ASC
 */
object ArabicPart {
    // 基本阿拉伯點ID定義（從100開始，避免與行星ID衝突）
    const val FORTUNE = 100       // 福點 (Part of Fortune)
    const val SPIRIT = 101        // 精神點 (Part of Spirit)
    const val BASIS = 102         // 基礎點 (Part of Basis)
    const val VENUS_LOT = 103     // 金星點 (Lot of Venus)
    const val MERCURY_LOT = 104   // 水星點 (Lot of Mercury)
    const val COURAGE = 105       // 勇氣點 (Part of Courage)
    const val VICTORY = 106       // 勝利點 (Part of Victory)
    const val NEMESIS = 107       // 報應點 / 復仇點 (Part of Nemesis)
    const val EXALTATION = 108    // 旺點 (Lot of Exaltation)
    const val FATHER = 109        // 父親點 (Part of Father)
    const val MOTHER = 110        // 母親點 (Part of Mother)
    const val MARRIAGE = 111        // 婚姻點 (通用)
    const val MARRIAGE_MEN = 124    // 婚姻點男
    const val MARRIAGE_WOMEN = 125  // 婚姻點女
    const val CHILDREN = 112      // 子女點 (Part of Children)
    const val EROS = 113          // 愛情點 (Part of Eros)
    const val BROTHERS = 114      // 兄弟點 (Part of Brothers)
    const val DEATH = 115         // 死亡點 (Part of Death)
    const val ILLNESS = 116       // 疾病點 (Part of Illness)
    const val CAREER = 117        // 事業點 (Part of Career)
    const val COMMERCE = 118      // 商業點 (Part of Commerce)
    const val TRAVEL = 119        // 旅行點 (Part of Travel)
    const val NECESSITY = 120     // 必然點 (Part of Necessity)
    const val FAITH = 121         // 信仰點 (Part of Faith)
    const val DESTINY = 122       // 命運點 (Part of Destiny)
    const val INHERITANCE = 123   // 遺產點 (Part of Inheritance)

    val arabicParts = listOf(
        PlanetBean(
            FORTUNE,
            "福點",
            "Part of Fortune",
            "⊕",
            "代表物質財富和幸運，是最常用的阿拉伯點。它顯示了一個人在物質層面上的幸運和成功的潛力。"
        ),
        PlanetBean(
            SPIRIT,
            "精神點",
            "Part of Spirit",
            "⊗",
            "代表精神財富和內在幸福，與福點相對。它顯示了一個人在精神層面上的幸福和成功的潛力。"
        ),
//        PlanetBean(BASIS, "基礎點", "Part of Basis", "◇", "代表一個人內在的核心與出發點，顯示了命運與行動的根本驅力。"),
//        PlanetBean(VENUS_LOT, "金星點", "Lot of Venus", "♀", "代表與金星相關的愉悅與吸引，顯示了一個人在享樂與美感方面的特質。"),
//        PlanetBean(MERCURY_LOT, "水星點", "Lot of Mercury", "☿", "代表與水星相關的思考與溝通，顯示了智力與表達的傾向。"),
        PlanetBean(
            COURAGE,
            "勇氣點",
            "Part of Courage",
            "⚡",
            "代表勇氣和決斷力，顯示了一個人面對挑戰和困難時的勇氣和決斷力。"
        ),
        PlanetBean(
            VICTORY,
            "勝利點",
            "Part of Victory",
            "⚔",
            "代表成功和勝利，顯示了一個人在競爭和挑戰中取得勝利的能力。"
        ),
        PlanetBean(
            NEMESIS,
            "報應點",
            "Part of Nemesis",
            "⚔",
            "代表敵人和對手，顯示了一個人可能面臨的敵對關係和挑戰。"
        ),
        PlanetBean(
            EXALTATION,
            "旺點",
            "Lot of Exaltation",
            "⬆",
            "代表榮耀與提升的潛力，顯示了一個人在某些領域中能達到顛峰的能力。"
        ),
        PlanetBean(
            FATHER,
            "父親點",
            "Part of Father",
            "♂",
            "代表與父親的關係，顯示了一個人與父親的關係和父親對其的影響。"
        ),
        PlanetBean(
            MOTHER,
            "母親點",
            "Part of Mother",
            "♀",
            "代表與母親的關係，顯示了一個人與母親的關係和母親對其的影響。"
        ),
//        PlanetBean(
//            MARRIAGE,
//            "婚姻點",
//            "Part of Marriage",
//            "⚭",
//            "代表婚姻關係，顯示了一個人在婚姻方面的特質和潛力。"
//        ),
        PlanetBean(
            MARRIAGE_MEN,
            "婚姻點(男)",
            "Part of Marriage",
            "⚭",
            "代表婚姻關係，顯示了一個人在婚姻方面的特質和潛力。"
        ),
        PlanetBean(
            MARRIAGE_WOMEN,
            "婚姻點(女)",
            "Part of Marriage",
            "⚭",
            "代表婚姻關係，顯示了一個人在婚姻方面的特質和潛力。"
        ),
        PlanetBean(
            CHILDREN,
            "子女點",
            "Part of Children",
            "⚇",
            "代表子女關係，顯示了一個人與子女的關係和養育子女的能力。"
        ),
//        PlanetBean(EROS, "愛情點", "Part of Eros", "♥", "代表愛情和性吸引力，顯示了一個人在愛情和性方面的特質和潛力。"),
//        PlanetBean(BROTHERS, "兄弟點", "Part of Brothers", "⚆", "代表與兄弟姐妹的關係，顯示了一個人與兄弟姐妹的關係和互動。"),
//        PlanetBean(DEATH, "死亡點", "Part of Death", "☠", "代表生命的終結和轉變，顯示了一個人對死亡的態度和可能的死亡方式。"),
//        PlanetBean(ILLNESS, "疾病點", "Part of Illness", "⚕", "代表健康問題，顯示了一個人可能面臨的健康挑戰和疾病傾向。"),
//        PlanetBean(CAREER, "事業點", "Part of Career", "⚒", "代表職業和事業發展，顯示了一個人在職業和事業方面的潛力和方向。"),
//        PlanetBean(COMMERCE, "商業點", "Part of Commerce", "⚖", "代表商業和交易能力，顯示了一個人在商業和交易方面的潛力和特質。"),
//        PlanetBean(TRAVEL, "旅行點", "Part of Travel", "✈", "代表旅行和遠行，顯示了一個人在旅行和遠行方面的經驗和傾向。"),
//        PlanetBean(NECESSITY, "必然點", "Part of Necessity", "⚓", "代表必然性和命運，顯示了一個人生命中不可避免的事件和經歷。"),
//        PlanetBean(FAITH, "信仰點", "Part of Faith", "✝", "代表信仰和精神追求，顯示了一個人在信仰和精神層面的特質和追求。"),
//        PlanetBean(DESTINY, "命運點", "Part of Destiny", "⚝", "代表命運和宿命，顯示了一個人生命中的命運和宿命。"),
//        PlanetBean(INHERITANCE, "遺產點", "Part of Inheritance", "⚱", "代表遺產和繼承，顯示了一個人在遺產和繼承方面的潛力和特質。")
    )


    // 獲取阿拉伯點中文名稱
    fun getChineseName(id: Int): String {
        return when (id) {
            FORTUNE -> "福點"
            SPIRIT -> "精神點"
            BASIS -> "基礎點"
            VENUS_LOT -> "金星點"
            MERCURY_LOT -> "水星點"
            COURAGE -> "勇氣點"
            VICTORY -> "勝利點"
            NEMESIS -> "報應點"
            EXALTATION -> "旺點"
            FATHER -> "父親點"
            MOTHER -> "母親點"
            MARRIAGE -> "婚姻點"
            MARRIAGE_MEN -> "婚姻點(男)"
            MARRIAGE_WOMEN -> "婚姻點(女)"
            CHILDREN -> "子女點"
            EROS -> "愛情點"
            BROTHERS -> "兄弟點"
            DEATH -> "死亡點"
            ILLNESS -> "疾病點"
            CAREER -> "事業點"
            COMMERCE -> "商業點"
            TRAVEL -> "旅行點"
            NECESSITY -> "必然點"
            FAITH -> "信仰點"
            DESTINY -> "命運點"
            INHERITANCE -> "遺產點"
            else -> "未知阿拉伯點"
        }
    }

    // 獲取阿拉伯點英文名稱
    fun getEnglishName(id: Int): String {
        return when (id) {
            FORTUNE -> "Lot of Fortune"
            SPIRIT -> "Lot of Spirit"
            BASIS -> "Lot of Basis"
            VENUS_LOT -> "Lot of Venus"
            MERCURY_LOT -> "Lot of Mercury"
            COURAGE -> "Lot of Courage"
            VICTORY -> "Lot of Victory"
            NEMESIS -> "Lot of Nemesis"
            EXALTATION -> "Lot of Exaltation"
            FATHER -> "Lot of the Father"
            MOTHER -> "Lot of the Mother"
            MARRIAGE -> "Lot of Marriage"
            CHILDREN -> "Lot of Children"
            EROS -> "Lot of Eros"
            BROTHERS -> "Lot of Siblings"
            DEATH -> "Lot of Death"
            ILLNESS -> "Lot of Illness"
            CAREER -> "Lot of Career"
            COMMERCE -> "Lot of Commerce"
            TRAVEL -> "Lot of Travel"
            NECESSITY -> "Lot of Necessity"
            FAITH -> "Lot of Faith"
            DESTINY -> "Lot of Destiny"
            INHERITANCE -> "Lot of Inheritance"
            else -> "Unknown Arabic Lot"
        }
    }


    // 獲取阿拉伯點符號
    fun getSymbol(id: Int): String {
        return when (id) {
            FORTUNE -> "⊕"
            SPIRIT -> "⊗"
            BASIS -> "◇"
            VENUS_LOT -> "♀"
            MERCURY_LOT -> "☿"
            COURAGE -> "⚡"
            VICTORY -> "⚔"
            NEMESIS -> "⚔"
            EXALTATION -> "⬆"
            FATHER -> "♂"
            MOTHER -> "♀"
            MARRIAGE -> "⚭"
            CHILDREN -> "⚇"
            EROS -> "♥"
            BROTHERS -> "⚆"
            DEATH -> "☠"
            ILLNESS -> "⚕"
            CAREER -> "⚒"
            COMMERCE -> "⚖"
            TRAVEL -> "✈"
            NECESSITY -> "⚓"
            FAITH -> "✝"
            DESTINY -> "⚝"
            INHERITANCE -> "⚱"
            else -> "?"
        }
    }

    // 獲取阿拉伯點描述
    fun getDescription(id: Int): String {
        return when (id) {
            FORTUNE -> "代表物質財富和幸運，是最常用的阿拉伯點。它顯示了一個人在物質層面上的幸運和成功的潛力。"
            SPIRIT -> "代表精神財富和內在幸福，與福點相對。它顯示了一個人在精神層面上的幸福和成功的潛力。"
            BASIS -> "代表一個人內在的核心與出發點，顯示了命運與行動的根本驅力。"
            VENUS_LOT -> "代表與金星相關的愉悅與吸引，顯示了一個人在享樂與美感方面的特質。"
            MERCURY_LOT -> "代表與水星相關的思考與溝通，顯示了智力與表達的傾向。"
            COURAGE -> "代表勇氣和決斷力，顯示了一個人面對挑戰和困難時的勇氣和決斷力。"
            VICTORY -> "代表成功和勝利，顯示了一個人在競爭和挑戰中取得勝利的能力。"
            NEMESIS -> "代表敵人和對手，顯示了一個人可能面臨的敵對關係和挑戰。"
            EXALTATION -> "代表榮耀與提升的潛力，顯示了一個人在某些領域中能達到顛峰的能力。"
            FATHER -> "代表與父親的關係，顯示了一個人與父親的關係和父親對其的影響。"
            MOTHER -> "代表與母親的關係，顯示了一個人與母親的關係和母親對其的影響。"
            MARRIAGE -> "代表婚姻關係，顯示了一個人在婚姻方面的特質和潛力。"
            CHILDREN -> "代表子女關係，顯示了一個人與子女的關係和養育子女的能力。"
            EROS -> "代表愛情和性吸引力，顯示了一個人在愛情和性方面的特質和潛力。"
            BROTHERS -> "代表與兄弟姐妹的關係，顯示了一個人與兄弟姐妹的關係和互動。"
            DEATH -> "代表生命的終結和轉變，顯示了一個人對死亡的態度和可能的死亡方式。"
            ILLNESS -> "代表健康問題，顯示了一個人可能面臨的健康挑戰和疾病傾向。"
            CAREER -> "代表職業和事業發展，顯示了一個人在職業和事業方面的潛力和方向。"
            COMMERCE -> "代表商業和交易能力，顯示了一個人在商業和交易方面的潛力和特質。"
            TRAVEL -> "代表旅行和遠行，顯示了一個人在旅行和遠行方面的經驗和傾向。"
            NECESSITY -> "代表必然性和命運，顯示了一個人生命中不可避免的事件和經歷。"
            FAITH -> "代表信仰和精神追求，顯示了一個人在信仰和精神層面的特質和追求。"
            DESTINY -> "代表命運和宿命，顯示了一個人生命中的命運和宿命。"
            INHERITANCE -> "代表遺產和繼承，顯示了一個人在遺產和繼承方面的潛力和特質。"
            else -> "未知阿拉伯點的描述。"
        }
    }

//    // 獲取阿拉伯點計算公式
//    fun getFormula(id: Int, isDay: Boolean): String {
//        return when (id) {
//            FORTUNE -> if (isDay) "ASC + Moon - Sun" else "ASC + Sun - Moon"
//            SPIRIT -> if (isDay) "ASC + Sun - Moon" else "ASC + Moon - Sun"
//            EROS -> if (isDay) "ASC + Venus - Sun" else "ASC + Venus - Moon"
//            MARRIAGE -> "ASC + Descendant - Venus"
//            CHILDREN -> "ASC + Jupiter - Saturn"
//            FATHER -> "ASC + Saturn - Sun"
//            MOTHER -> "ASC + Venus - Moon"
//            BROTHERS -> "ASC + Jupiter - Saturn"
//            DEATH -> "ASC + 8th House Cusp - Moon"
//            ILLNESS -> "ASC + Mars - Saturn"
//            CAREER -> "ASC + MC - Sun"
//            COMMERCE -> "ASC + Mercury - Sun"
//            TRAVEL -> "ASC + 9th House Cusp - Moon"
//            VICTORY -> "ASC + Jupiter - Sun"
//            NEMESIS -> "ASC + Saturn - Mars"
//            NECESSITY -> "ASC + Saturn - Sun"
//            COURAGE -> "ASC + Mars - Sun"
//            FAITH -> "ASC + Jupiter - Sun"
//            DESTINY -> "ASC + Part of Fortune - Moon"
//            INHERITANCE -> "ASC + Part of Fortune - Saturn"
//            else -> "未知公式"
//        }
//    }
//
//    // 獲取阿拉伯點分類
//    fun getCategory(id: Int): String {
//        return when (id) {
//            FORTUNE, SPIRIT, COMMERCE, INHERITANCE -> "財富"
//            EROS, MARRIAGE -> "愛情與關係"
//            CHILDREN, FATHER, MOTHER, BROTHERS -> "家庭"
//            DEATH, ILLNESS -> "健康"
//            CAREER, VICTORY, NEMESIS, COURAGE -> "事業"
//            TRAVEL -> "旅行"
//            NECESSITY, FAITH, DESTINY -> "命運"
//            else -> "其他"
//        }
//    }
} 