package com.one.astrology.constant

import swisseph.SweConst

class Planet {
    /**
    行星 (Std Planets)
    |  太陽  |   Sun   |      ☉      |   SE_SUN   |
    |  月亮  |  Moon  |      ☽      |  SE_MOON  |
    |  水星  | Mercury |      ☿      | SE_MERCURY |
    |  金星  |  Venus  |      ♀      |  SE_VENUS  |
    |  火星  |  Mars  |      ♂      |  SE_MARS  |
    |  木星  | Jupiter |      ♃      | SE_JUPITER |
    |  土星  | Saturn |      ♄      | SE_SATURN |
    | 天王星 | Uranus |      ♅      | SE_URANUS |
    | 海王星 | Neptune |      ♆      | SE_NEPTUNE |
    | 冥王星 |  Pluto  |      ♇      |  SE_PLUTO  |
    |  地球  |  Earth  |    🜨、♁    |  SE_EARTH  |
     */

    companion object {
        // 添加行星常量，使用整數值
        const val SUN = 0
        const val MOON = 1
        const val MERCURY = 2
        const val VENUS = 3
        const val MARS = 4
        const val JUPITER = 5
        const val SATURN = 6
        const val URANUS = 7
        const val NEPTUNE = 8
        const val PLUTO = 9
        const val EARTH = 14
        
        const val MOON_S_NODE = 60
        const val SUN_MOON_MIDPOINT = 61
        const val VERTEX = 62
        const val FORTUNE = 63

        const val Asc = 100
        const val MC = 101
        const val Des = 102
        const val IC = 103

        // 行星
        val planets = intArrayOf(
            SweConst.SE_SUN,  // = 0; // 太陽（Sun）
            SweConst.SE_MOON,  // = 1; // 月亮（Moon）
            SweConst.SE_MERCURY,  // = 2; // 水星（Mercury）
            SweConst.SE_VENUS,  // = 3; // 金星（Venus）
            SweConst.SE_MARS,  // = 4; // 火星（Mars）
            SweConst.SE_JUPITER,  // = 5; // 木星（Jupiter）
            SweConst.SE_SATURN,  // = 6; // 土星（Saturn）
            SweConst.SE_URANUS,  // = 7; // 天王星（Uranus）
            SweConst.SE_NEPTUNE,  // = 8; // 海王星（Neptune）
            SweConst.SE_PLUTO,  // = 9; // 冥王星（Pluto）
            SweConst.SE_MEAN_NODE,  // = 10; // 北(升)交點 (Moon N. Node ☊)
            SweConst.SE_MEAN_APOG,  // = 12; // 莉莉斯 (Lilith)
//                SE_EARTH, // = 14;  // 地球（Earth）
            SweConst.SE_CHIRON,  // = 15; // 凱龍星（Chiron）
            SweConst.SE_PHOLUS,  // = 16; // 人龍星 | Pholus
            SweConst.SE_CERES,  // = 17; // 穀神星 | Ceres
            SweConst.SE_PALLAS,  // = 18; // 智神星| Pallas
            SweConst.SE_JUNO,  // = 19; // 婚神星 | Juno
            SweConst.SE_VESTA, // = 20; // 灶神星 | Vesta

            SweConst.SE_CUPIDO,  // = 40; // 邱比特	| Cupido
            SweConst.SE_HADES,  // = 41; // 黑帝斯	| Hades
            SweConst.SE_ZEUS,  // = 42; // 宙斯	| Zeus
            SweConst.SE_KRONOS,  // = 43; // 克洛諾斯	| Kronos
            SweConst.SE_APOLLON,  // = 44; // 阿波羅	| Apollon
            SweConst.SE_ADMETOS,  // = 45; // 阿德墨托斯	| Admetos
            SweConst.SE_VULKANUS,  // = 46; // 武爾坎努斯	| Vulcanus
            SweConst.SE_POSEIDON, // = 47; // 波塞冬   | Poseidon

            SUN_MOON_MIDPOINT, // 日月中點
            VERTEX, // 宿命點
            MOON_S_NODE, // 南交
        )


        const val Sun = "太陽"
        const val Moon = "月亮"
        const val Mercury = "水星"
        const val Venus = "金星"
        const val Mars = "火星"
        const val Jupiter = "木星"
        const val Saturn = "土星"
        const val Uranus = "天王星"
        const val Neptune = "海王星"
        const val Pluto = "冥王星"
        const val Earth = "地球"

        const val Ascendant = "上升"
        const val Midheaven  = "中天" // 天頂
        const val Descendant  = "下降"
        const val ImumCoeli = "天底"
/*
Body numbers of other asteroids are above SE_AST_OFFSET (= 10000) and have to be constructed as follows:
ipl = SE_AST_OFFSET + minor_planet_catalogue_number;
e.g. Eros : ipl = SE_AST_OFFSET + 433 (= 10433)
The names of the asteroids and their catalogue numbers can be found in seasnam.txt.
Examples are:

5          Astraea 義神星（5 Astraea）
6          Hebe
7          Iris
8          Flora
9          Metis
10        Hygiea
30        Urania
42        Isis                not identical with "Isis-Transpluto"
153      Hilda              has an own asteroid belt at 4 AU
227      Philosophia
251      Sophia
259      Aletheia
275      Sapientia
279      Thule             asteroid close to Jupiter
375      Ursula
433      Eros
763      Cupido          different from Witte's Cupido
944      Hidalgo
1181    Lilith              not identical with Dark Moon 'Lilith'
1221    Amor
1387    Kama
1388    Aphrodite
1862    Apollo            different from Witte's Apollon
3553    Damocles      highly eccentric orbit between Mars and Uranus
3753    Cruithne        "second moon" of Earth
4341    Poseidon       Greek Neptune - different from Witte's Poseidon
4464    Vulcano         fire god - different from Witte's Vulkanus and intramercurian Vulcan
5731    Zeus              Greek Jupiter - different from Witte's Zeus
7066    Nessus       毒龍星   third named Centaur - between Saturn and Pluto
There are two ephemeris files for each asteroid (except the main asteroids), a long one and a short one:
se09999.se1    long-term ephemeris of asteroid number 9999, 3000 BCE – 3000 CE
se09999s.se1   short ephemeris of asteroid number 9999, 1500 – 2100 CE
 */

    }
}