package com.one.astrology.firebase

import android.content.Context
import com.google.firebase.ktx.Firebase
import com.google.firebase.storage.StreamDownloadTask
import com.google.firebase.storage.ktx.storage
import com.one.astrology.data.db.AstroData
import com.one.astrology.util.LocationUtil
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader


class StorageUtil {
    companion object {
        fun get(context: Context) {
            val storage = Firebase.storage
            // Create a storage reference from our app
            val storageRef = storage.reference

            // Create a reference with an initial file path and name
            val pathReference = storageRef.child("chart/celebrity - natal_charts.csv")
            pathReference.getStream()
                .addOnSuccessListener { stream ->
                    // Parse CSV data from the stream
//                    parseCSVFromStream(context, stream)
                }
                .addOnFailureListener { exception: Exception? ->
                    LogUtil.e(exception.toString())
                }
        }

        // Function to parse CSV data from an InputStream
        private suspend fun parseCSVFromStream(
            context: Context,
            inputStream: StreamDownloadTask.TaskSnapshot
        ) {
            val reader = BufferedReader(InputStreamReader(inputStream.stream))
            var line: String
            try {
                // Read each line of the CSV file
                while (reader.readLine().also { line = it } != null) {
                    // Split the line by comma (assuming CSV format)
                    val list = line.split(
                        ',',
                        ignoreCase = false,
                        limit = 7
                    )
                    // 1988年2月13日
                    val date = FormatUtils.stringToDate(list[1], "yyyy年MM月dd日 HH:mm")

                    val latLng = LocationUtil.addressToLatLng(context, list[2])

                    AstroData(
                        name = list[0],
                        time = date.time.toString(),
                        place = list[2],
                        latitude = latLng.latitude.toString(),
                        longitude = latLng.longitude.toString(),
                        biography = list[4],
                        wikipedia = list[5]
                    )
                    val data = line.split(",".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()

                    // Process the CSV data as needed
                    // For example, you can print it or store it in a data structure
                    // You can also map the data to a custom object if necessary
                    for (datum in data) {
                        println(datum)
                    }
                }
            } catch (e: IOException) {
                // Handle any IO errors
            } finally {
                try {
                    // Close the reader when done
                    reader.close()
                } catch (e: IOException) {
                    // Handle any IO errors
                }
            }
        }
    }
}