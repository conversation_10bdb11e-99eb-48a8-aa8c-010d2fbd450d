package com.one.astrology.engine

import com.one.astrology.analyzer.PlanetStrengthAnalyzer
import com.one.astrology.model.PlanetAnalysisResult
import com.one.astrology.model.PlanetCondition

object AstroAnalysisEngine {
    fun analyzeAll(planets: List<PlanetCondition>): List<PlanetAnalysisResult> {
        return planets.map { PlanetStrengthAnalyzer.analyze(it) }
    }

    fun summarize(results: List<PlanetAnalysisResult>): String {
        val total = results.sumOf { it.score }
        return when {
            total >= 20 -> "整體星盤行星狀態非常有力"
            total >= 5 -> "行星整體表現尚可"
            total >= -5 -> "行星整體偏弱，建議注意內在平衡"
            else -> "多數行星無力，容易感到阻礙，建議內省與修練"
        }
    }
}
