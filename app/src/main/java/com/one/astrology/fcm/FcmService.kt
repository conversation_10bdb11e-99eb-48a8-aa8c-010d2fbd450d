package com.one.astrology.fcm

import android.Manifest
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.GsonBuilder
import com.one.astrology.R
import com.one.astrology.ui.activity.SplashActivity
import com.one.core.util.LogUtil

class FcmService : FirebaseMessagingService() {
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        LogUtil.d("onNewToken $token")
    }

    override fun onMessageReceived(message: RemoteMessage) {
        super.onMessageReceived(message)
        val gson = GsonBuilder().setPrettyPrinting().create()
        LogUtil.d("remoteMessage : \n" + gson.toJson(message))
        if (message.data.isNotEmpty()) {
            showNotification(this, message)
        }
    }

    private fun showNotification(context: Context, remoteMessage: RemoteMessage) {
        val title = remoteMessage.data["title"]
        val body = remoteMessage.data["body"]
        val type = remoteMessage.data["type"]

        if (title != null && body != null && type != null) {
            val contentIntent = getContentIntent(context, type)
            showNotification(context, title, body, contentIntent)
        }
    }

    private fun getContentIntent(
        context: Context,
        type: String
    ): PendingIntent {
        val notificationIntent = Intent(context, SplashActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
//        notificationIntent.putExtra(PushKeyType.PUSH_KEY, pushKey)
//        notificationIntent.putExtra(PushKeyType.LINK, link)
        notificationIntent.action = type

        // flags 需使用 FLAG_MUTABLE (可變的) 否則點擊推播無法進入聊天室
        return PendingIntent.getActivity(
            context,
            0,
            notificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        )
    }

    private fun showNotification(
        context: Context,
        title: String,
        body: String,
        contentIntent: PendingIntent
    ) {
        val builder = NotificationCompat.Builder(
            context,
            "1000"
        )
            .setSmallIcon(R.drawable.ic_baseline_stars_24)
            .setContentTitle(title)
            .setContentText(body)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(contentIntent)
            .setAutoCancel(true)

        with(NotificationManagerCompat.from(context)) {
            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }
            notify(
                "1",
                1000,
                builder.build()
            )
        }
    }
}