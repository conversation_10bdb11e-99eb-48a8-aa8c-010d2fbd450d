package com.one.astrology.api;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.one.core.util.LogUtil;

import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

import okhttp3.Connection;
import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.http.HttpHeaders;
import okio.Buffer;
import okio.BufferedSource;

/**
 * API Log
 */
public final class OInterceptor implements Interceptor {
    private static final Charset UTF8 = StandardCharsets.UTF_8;

    private final Gson gson = new GsonBuilder()
            .setPrettyPrinting()
            .create();

    public enum Level {
        /**
         * No logs.
         */
        NONE,
        /**
         * Logs request and response lines.
         *
         * <p>Example:
         * <pre>{@code
         * --> POST /greeting http/1.1 (3-byte body)
         *
         * <-- 200 OK (22ms, 6-byte body)
         * }</pre>
         */
        BASIC,
        /**
         * Logs request and response lines and their respective headers.
         *
         * <p>Example:
         * <pre>{@code
         * --> POST /greeting http/1.1
         * Host: example.com
         * Content-Type: plain/text
         * Content-Length: 3
         * --> END POST
         *
         * <-- 200 OK (22ms)
         * Content-Type: plain/text
         * Content-Length: 6
         * <-- END HTTP
         * }</pre>
         */
        HEADERS,
        /**
         * Logs request and response lines and their respective headers and bodies (if present).
         *
         * <p>Example:
         * <pre>{@code
         * --> POST /greeting http/1.1
         * Host: example.com
         * Content-Type: plain/text
         * Content-Length: 3
         *
         * Hi?
         * --> END POST
         *
         * <-- 200 OK (22ms)
         * Content-Type: plain/text
         * Content-Length: 6
         *
         * Hello!
         * <-- END HTTP
         * }</pre>
         */
        BODY
    }

    public OInterceptor() {
    }

    private volatile Level level = Level.NONE;

    /**
     * Change the level at which this interceptor logs.
     */
    public OInterceptor setLevel(Level level) {
        if (level == null) throw new NullPointerException("level == null. Use Level.NONE instead.");
        this.level = level;
        return this;
    }

    public Level getLevel() {
        return level;
    }


    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        Level level = this.level;

        // 避免發生 IOException: unexpected end of stream on Connection
        Request request = chain.request().newBuilder().addHeader("Connection", "close").build();
        if (level == Level.NONE) {
            return chain.proceed(request);
        }

        boolean logBody = level == Level.BODY;
        boolean logHeaders = logBody || level == Level.HEADERS;

        RequestBody requestBody = request.body();
        boolean hasRequestBody = requestBody != null;

        Connection connection = chain.connection();
        Protocol protocol = connection != null ? connection.protocol() : Protocol.HTTP_1_1;
        String requestStartMessage = "--> " + request.method() + ' ' + request.url() + ' ' + protocol;
        if (!logHeaders && hasRequestBody) {
            requestStartMessage += " (" + requestBody.contentLength() + "-byte body)";
        }
        LogUtil.INSTANCE.d(requestStartMessage);

        if (logHeaders) {
            if (hasRequestBody) {
                // Request body headers are only present when installed as a network interceptor. Force
                // them to be included (when available) so there values are known.
                if (requestBody.contentType() != null) {
                    LogUtil.INSTANCE.d("Content-Type: " + requestBody.contentType());
                }
                if (requestBody.contentLength() != -1) {
                    LogUtil.INSTANCE.d("Content-Length: " + requestBody.contentLength());
                }
            }

            Headers headers = request.headers();
            for (int i = 0, count = headers.size(); i < count; i++) {
                String name = headers.name(i);
                // Skip headers from the request body as they are explicitly logged above.
                if (!"Content-Type".equalsIgnoreCase(name) && !"Content-Length".equalsIgnoreCase(name)) {
                    LogUtil.INSTANCE.d(name + ": " + headers.value(i));
                }
            }

            if (!logBody || !hasRequestBody) {
                LogUtil.INSTANCE.d("--> END " + request.method());
            } else if (bodyEncoded(request.headers())) {
                LogUtil.INSTANCE.d("--> END " + request.method() + " (encoded body omitted)");
            } else {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);

                Charset charset = UTF8;
                MediaType contentType = requestBody.contentType();
                if (contentType != null) {
                    charset = contentType.charset(UTF8);
                }

                if (isPlaintext(buffer)) {
                    if (charset != null) {
                        String jsonString = buffer.readString(charset);
                        try {
                            if (!TextUtils.isEmpty(jsonString) && JsonParser.parseString(jsonString).isJsonObject()) {
                                JsonObject jsonObject = JsonParser.parseString(jsonString).getAsJsonObject();
                                String jsonOutput = gson.toJson(jsonObject);
                                LogUtil.INSTANCE.i("input : \n" + jsonOutput);
                            } else {
                                LogUtil.INSTANCE.i("input : \n" + jsonString);
                            }
                        } catch (Exception e) {
                            LogUtil.INSTANCE.i("input : \n" + jsonString);
                        }
                    } else {
                        LogUtil.INSTANCE.d("input : \n" + "null");
                    }
                    LogUtil.INSTANCE.i("--> END " + request.method()
                            + " (" + requestBody.contentLength() + "-byte body)");
                } else {
                    LogUtil.INSTANCE.d("--> END " + request.method() + " (binary "
                            + requestBody.contentLength() + "-byte body omitted)");
                }
            }
        }

        long startNs = System.nanoTime();
        Response response;
        try {
            response = chain.proceed(request);
        } catch (Exception e) {
            LogUtil.INSTANCE.d("<-- HTTP FAILED: " + e);
            throw e;
        }
        long tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs);

        ResponseBody responseBody = response.body();
        long contentLength = 0;
        if (responseBody != null) {
            contentLength = responseBody.contentLength();
        }
        String bodySize = contentLength != -1 ? contentLength + "-byte" : "unknown-length";
        LogUtil.INSTANCE.i("<-- " + response.code() + ' ' + response.message() + ' '
                + response.request().url() + " (" + tookMs + "ms" + (!logHeaders ? ", "
                + bodySize + " body" : "") + ')');

        if (logHeaders) {
            Headers headers = response.headers();
            for (int i = 0, count = headers.size(); i < count; i++) {
                LogUtil.INSTANCE.d(headers.name(i) + ": " + headers.value(i));
            }

            if (!logBody || !HttpHeaders.hasBody(response)) {
                LogUtil.INSTANCE.d("<-- END HTTP");
            } else if (bodyEncoded(response.headers())) {
                LogUtil.INSTANCE.d("<-- END HTTP (encoded body omitted)");
            } else {
                BufferedSource source = null;
                if (responseBody != null) {
                    source = responseBody.source();
                }
                if (source != null) {
                    source.request(Long.MAX_VALUE); // Buffer the entire body.
                }
                Buffer buffer = null;
                if (source != null) {
                    buffer = source.buffer();
                }

                Charset charset = UTF8;
                MediaType contentType = null;
                if (responseBody != null) {
                    contentType = responseBody.contentType();
                }
                if (contentType != null) {
                    charset = contentType.charset(UTF8);
                }

                if (buffer != null && !isPlaintext(buffer)) {
                    LogUtil.INSTANCE.d("<-- END HTTP (binary " + buffer.size() + "-byte body omitted)");
                    return response;
                }

                if (contentLength != 0) {
                    if (charset != null) {
                        String jsonString = buffer.clone().readString(charset);
                        try {
                            if (!TextUtils.isEmpty(jsonString) && JsonParser.parseString(jsonString).isJsonObject()) {
                                JsonObject jsonObject = JsonParser.parseString(jsonString).getAsJsonObject();
                                String jsonOutput = gson.toJson(jsonObject);
                                LogUtil.INSTANCE.i("response : \n" + jsonOutput);
                            } else {
                                LogUtil.INSTANCE.i("response : \n" + jsonString);
                            }
                        } catch (Exception e) {
                            LogUtil.INSTANCE.i("response : \n" + jsonString);
                        }
                    }
                }

                if (buffer != null) {
                    LogUtil.INSTANCE.d("<-- END HTTP (" + buffer.size() + "-byte body)");
                }
            }
        }

        return response;
    }

    /**
     * Returns true if the body in question probably contains human readable text. Uses a small sample
     * of code points to detect unicode control characters commonly used in binary file signatures.
     */
    static boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }

    private boolean bodyEncoded(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return contentEncoding != null && !contentEncoding.equalsIgnoreCase("identity");
    }
}
