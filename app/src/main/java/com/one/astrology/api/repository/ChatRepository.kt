package com.one.astrology.api.repository


import com.one.astrology.api.RetrofitClient
import com.one.astrology.data.request.openai.ChatRequest
import com.one.astrology.data.request.openai.ChatResponse
import com.one.core.util.LogUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ChatRepository {
    private val apiService = RetrofitClient.instance

    suspend fun fetchChatResponse(apiKey: String, request: ChatRequest): ChatResponse? {
        return withContext(Dispatchers.IO) {
            try {
                apiService.getChatResponse("Bearer $apiKey", request)
            } catch (e: Exception) {
                LogUtil.e("fetchChatResponse error: ${e.message}")
                null
            }
        }
    }
}
