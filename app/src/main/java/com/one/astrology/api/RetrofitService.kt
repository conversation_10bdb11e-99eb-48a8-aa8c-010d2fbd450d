package com.one.astrology.api

import com.one.astrology.data.SunSignInfo
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.POST
import retrofit2.http.Query

interface RetrofitService {
    //POST: https://aztro.sameerkumar.website
    //POST: https://aztro.sameerkumar.website?sign= <sign> &day= <day>
    @POST("/")
    suspend fun getSignInfo(
        @Query("sign") sign: String,
        @Query("day") day: String
    ): Response<SunSignInfo>


    companion object {
//        private var logging = OInterceptor()
        var retrofitService: RetrofitService? = null
        fun getInstance(): RetrofitService {
            if (retrofitService == null) {
//                logging.level = OInterceptor.Level.BODY
                val retrofit = Retrofit.Builder()
                    .baseUrl("https://aztro.sameerkumar.website/")
                    .addConverterFactory(GsonConverterFactory.create())
                    .build()
                retrofitService = retrofit.create(RetrofitService::class.java)
            }
            return retrofitService!!
        }

    }
}