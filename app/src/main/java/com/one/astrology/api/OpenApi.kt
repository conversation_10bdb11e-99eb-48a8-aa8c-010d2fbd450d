package com.one.astrology.api

import com.one.astrology.data.request.openai.ChatRequest
import com.one.astrology.data.request.openai.ChatResponse
import com.one.astrology.data.request.openai.OpenRequest
import com.one.astrology.data.request.openai.OpenResponse
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST

interface OpenApi {

    @Headers(
        "Content-Type: application/json",
        "Authorization: Bearer ********************************************************************************************************************************************************************"
    )
    @POST("v1/chat/completions")
    fun getChatResponse(@Body request: ChatRequest): Call<ChatResponse>

    @Headers("Content-Type: application/json")
    @POST("v1/chat/completions")
    suspend fun getChatResponse(
        @Header("Authorization") authorization: String,
        @Body request: ChatRequest
    ): ChatResponse


    /**
     *
    curl https://api.openai.com/v1/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer YOUR_API_KEY" \
    -d '{"model": "text-davinci-003", "prompt": "Say this is a test", "temperature": 0, "max_tokens": 7}'
    ***************************************************
    ***************************************************

    ***************************************************
     */
    @Headers(
//        "Accept: application/json",
        "Content-Type: application/json",
        "Authorization: Bearer ********************************************************************************************************************************************************************",
//        "User-Agent: Your-App-Name",
//        "Cache-Control: max-age=640000"
    )
    @POST("v1/completions")
    suspend fun completions(
        @Body body: OpenRequest
    ): Response<OpenResponse>


    @Headers(
        "Content-Type: application/json",
        "Authorization: Bearer ********************************************************************************************************************************************************************",
    )
    @POST("v1/chat/completions")
    suspend fun chatCompletions(
        @Body body: OpenRequest
    ): Response<OpenResponse>
}