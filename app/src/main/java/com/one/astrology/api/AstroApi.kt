package com.one.astrology.api

import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface AstroApi {

    // https://www.astro.com/astro-databank/Special:AllPages
    @GET("/astro-databank/Special:AllPages")
    suspend fun getAllPages(
    ): Response<ResponseBody>


    // https://www.astro.com/wiki/astro-databank/index.php?title=Special:AllPages&from=Afro-X
    @GET("/wiki/astro-databank/index.php?title=Special:AllPages")
    suspend fun getAstroWiki(
//        @Query("title") title: String,
        @Query(value = "from", encoded = true) from: String,
    ): Response<ResponseBody>


    @GET("{path}")
    suspend fun getAstroData(@Path(value ="path", encoded = true) path: String): Response<ResponseBody>
}