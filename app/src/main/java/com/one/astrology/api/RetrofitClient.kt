package com.one.astrology.api

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object RetrofitClient {
    private const val BASE_URL = "https://api.openai.com/"
    private const val TIMEOUT = 30L // 設定超時時間為 30 秒

    val instance: OpenApi by lazy {
        // 創建 HttpLoggingInterceptor 來記錄請求與響應
        val loggingInterceptor = HttpLoggingInterceptor()
        loggingInterceptor.level = HttpLoggingInterceptor.Level.BODY // 設定記錄級別，BODY 會記錄請求和響應的詳細內容

        // 創建 OkHttpClient 並添加 loggingInterceptor 和超時設置
        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(TIMEOUT, TimeUnit.SECONDS) // 設定連接超時時間
            .readTimeout(TIMEOUT, TimeUnit.SECONDS) // 設定讀取超時時間
            .writeTimeout(TIMEOUT, TimeUnit.SECONDS) // 設定寫入超時時間
            .build()

        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(OpenApi::class.java)
    }
}