package com.one.astrology.api.groq

import com.one.astrology.BuildConfig
import com.one.astrology.api.OInterceptor
import com.one.astrology.data.request.groq.GroqRequest
import com.one.astrology.data.request.groq.GroqResponse
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Call
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST
import java.util.concurrent.TimeUnit

interface GroqApiService {

    @Headers("Content-Type: application/json")
    @POST("openai/v1/chat/completions")
    fun getChatCompletion(@Body request: GroqRequest): Call<GroqResponse>

    companion object {
        fun create(apiKey: String): GroqApiService {
            val loggingInterceptor = OInterceptor()
            loggingInterceptor.level = if (BuildConfig.DEBUG) {
                OInterceptor.Level.BODY
            } else {
                OInterceptor.Level.NONE
            }
            val client = OkHttpClient.Builder().build()
            val newOkHttpClient = client.newBuilder()
            newOkHttpClient.addInterceptor(loggingInterceptor)
            newOkHttpClient.connectTimeout(30, TimeUnit.SECONDS)
            newOkHttpClient.writeTimeout(30, TimeUnit.SECONDS)
            newOkHttpClient.readTimeout(30, TimeUnit.SECONDS)

            newOkHttpClient.addInterceptor(Interceptor { chain ->
                val original = chain.request()
                val builder = original.newBuilder()
                val request = builder.method(original.method, original.body).build()
                chain.proceed(request)
            })
            val retrofit = Retrofit.Builder()
                .baseUrl("https://api.groq.com/")
                .client(newOkHttpClient.build())
                .addConverterFactory(GsonConverterFactory.create())
                .client(
                    OkHttpClient.Builder()
                        .addInterceptor { chain ->
                            val request = chain.request().newBuilder()
                                .addHeader("Authorization", "Bearer $apiKey")
                                .addHeader("Content-Type", "application/json")
                                .build()
                            chain.proceed(request)
                        }
                        .build()
                )
                .build()

            return retrofit.create(GroqApiService::class.java)
        }
    }
}
