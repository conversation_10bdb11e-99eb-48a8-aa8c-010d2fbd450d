package com.one.astrology.api

import com.one.astrology.data.request.openai.ChatRequest
import com.one.astrology.data.request.openai.ChatResponse
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

//data class ChatRequest(
//    val model: String,
//    val messages: List<Message>
//)
//
//data class Message(
//    val role: String,
//    val content: String
//)
//
//data class ChatResponse(
//    val choices: List<Choice>
//)
//
//data class Choice(
//    val message: Message
//)

interface OpenAIService {
    @Headers("Authorization: Bearer ********************************************************************************************************************************************************************")
    @POST("v1/chat/completions")
    fun getChatResponse(@Body request: ChatRequest): Call<ChatResponse>
}