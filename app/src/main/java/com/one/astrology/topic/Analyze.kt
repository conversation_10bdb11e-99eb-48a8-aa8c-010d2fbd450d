package com.one.astrology.topic

import android.content.Context
import com.one.astrology.data.Horoscope
import com.one.astrology.data.Topics
import com.one.astrology.data.bean.AspectType
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.type.Chart
import com.one.astrology.util.AssetsToObjectUtil
import com.one.astrology.util.EphemerisUtil
import com.one.core.util.LogUtil

class Analyze {
    // 行星、星座、宮位、相位
    companion object {
        // 吸引力大的女生星盤配置 美貌驚人
        // https://zhuanlan.zhihu.com/p/161617554
        // https://zhanxingzhan.com/zh-hant/xingzuoyunshi/xingpan/ggv8kg.html

        // 高智商的星盤配置 https://kknews.cc/zh-tw/astrology/e5nlxzr.html

        fun topicItem(
            context: Context,
            chart: Chart,
            topic: Topics.TopicItem,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ): ArrayList<Topics.TopicItem.Content> {

            topic.contents.forEach { content ->
                getContent(context, chart, content, horoscopeA, horoscopeB)
            }
            return topic.contents
        }

        fun topicItem(
            topic: Topics.TopicItem,
            horoscope: Horoscope
        ): ArrayList<Topics.TopicItem.Content> {
            topic.contents.forEach { content ->
                getContent(content, horoscope)
            }
            return topic.contents
        }

        private fun getContent(
            context: Context,
            chart: Chart,
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            content.resultList = ArrayList()
            content.isChecked = false
            when (content.title) {
                "行星星座" -> {
                    planetAndSign(content, horoscopeA)
                }

                "相位" -> {
                    EphemerisUtil.aspects(
                        context,
                        chart,
                        horoscopeA.planetList,
                        horoscopeB.planetList,
                        isMatch = true, isGetScore = false
                    )
                    aspects(context, chart, content, horoscopeA, horoscopeB)
                }

                "宮位" -> {
                    house(context, content, horoscopeA, horoscopeB)
                    house(context, content, horoscopeB, horoscopeA)
                }

                "逆行" -> {
                    content.planetNameA?.let {
                        isRetrograde(horoscopeA.planetList, it, content)
                    }
                }

                "行星星座、相位" -> {
                    EphemerisUtil.aspects(
                        context,
                        chart,
                        horoscopeA.planetList,
                        horoscopeB.planetList,
                        isMatch = true,
                        isGetScore = false
                    )
                    planetAndSign(content, horoscopeA)
                    if (content.isChecked) {
                        aspects(content, horoscopeA)
                    }
                }

                "相位、宮位" -> {
                    EphemerisUtil.aspects(
                        context,
                        chart,
                        horoscopeA.planetList,
                        horoscopeB.planetList,
                        isMatch = true, isGetScore = false
                    )
                    aspects(content, horoscopeA)
                    if (content.isChecked) {
                        house(content, horoscopeA)
                    }
                }

                "行星星座、宮位" -> {
                    planetAndSign(content, horoscopeA)
                    if (content.isChecked) {
                        house(content, horoscopeA)
                    }
                }

                "同星座" -> {
                    sameSign(content, horoscopeA, horoscopeB)
                    sameSign(content, horoscopeB, horoscopeA)
                }

                "主星相同" -> {
                    sameRuler(content, horoscopeA, horoscopeB)
                }

                "主星星座" -> {
                    rulerSign(content, horoscopeA, horoscopeB)
                    rulerSign(content, horoscopeB, horoscopeA)
                }

                "主星位於同星座" -> {
                    rulerSameSign(content, horoscopeA, horoscopeB)
                    rulerSameSign(content, horoscopeB, horoscopeA)
                }

                "主星落在相同或相反的星座" -> {
                    rulerSameOppositeSign(content, horoscopeA, horoscopeB)
                    rulerSameOppositeSign(content, horoscopeB, horoscopeA)
                }

                "外觀主星相同" -> {
                    sameDecanicSign(content, horoscopeA, horoscopeB)
                }

                else -> {
                }
            }
        }

        private fun getContent(content: Topics.TopicItem.Content, horoscope: Horoscope) {
            content.resultList = ArrayList()
            content.isChecked = false
            when (content.title) {
                "行星星座" -> {
                    planetAndSign(content, horoscope)
                }

                "相位" -> {
                    aspects(content, horoscope)
                }

                "宮位" -> {
                    house(content, horoscope)
                }

                "逆行" -> {
                    content.planetNameA?.let {
                        isRetrograde(horoscope.planetList, it, content)
                    }
                }

                "行星星座、相位" -> {
                    planetAndSign(content, horoscope)
                    if (content.isChecked) {
                        aspects(content, horoscope)
                    }
                }

                "相位、宮位" -> {
                    aspects(content, horoscope)
                    if (content.isChecked) {
                        house(content, horoscope)
                    }
                }

                "行星星座、宮位" -> {
                    planetAndSign(content, horoscope)
                    if (content.isChecked) {
                        house(content, horoscope)
                    }
                }

                else -> {
                }
            }
        }

        private fun sameSign(
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            content.planetNameListA?.let {
                content.planetNameListA!!.forEach { planetNameA ->
                    content.planetNameListB!!.forEach { planetNameB ->
                        val planetA = horoscopeA.planetList.find { it.chName == planetNameA }
                        val planetB = horoscopeB.planetList.find { it.chName == planetNameB }
                        if (planetA != null) {
                            if (planetB != null) {
                                if (planetA.signBean.chName == planetB.signBean.chName) {
                                    content.isChecked = true
                                    val result =
                                        "${horoscopeA.name} 的${planetNameA} 與 ${horoscopeB.name} 的${planetNameB}相同為" + planetA.signBean.chName
                                    if (content.resultList?.contains(result) != true) {
                                        content.resultList?.add(result)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        private fun sameRuler(
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            content.houseA?.forEach { itA ->
                content.houseB?.forEach { itB ->
                    val rulersA = horoscopeA.houses.signBeanList[itA - 1].ruler.split("、")
                    val rulersB = horoscopeB.houses.signBeanList[itB - 1].ruler.split("、")
                    rulersA.forEach { rulerA ->
                        rulersB.forEach { rulerB ->
                            if (rulerA.contains(rulerB)) {
                                content.isChecked = true
                                val result =
                                    "${horoscopeA.name} 的 $itA 宮主星與${horoscopeB.name} 的 $itB 宮主星相同為$rulerA"
                                if (content.resultList?.contains(result) != true) {
                                    content.resultList?.add(result)
                                }
                            }
                        }
                    }
                }
            }
        }

        // 主星位於X的星座内
        private fun rulerSign(
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            content.houseA?.forEach { itA ->
                content.houseB?.forEach { itB ->
                    val rulerA = horoscopeA.houses.signBeanList[itA - 1].ruler.split("、")[0]
                    val planetA = horoscopeB.planetList.find { it.chName == rulerA }
                    if (planetA != null) {
                        if (planetA.houseData.index == itB) {
                            content.isChecked = true
                            val result =
                                "${horoscopeA.name} 的 $itA 宮主星是${rulerA}而${horoscopeB.name} 的${rulerA}也落在${itB}宮"
                            if (content.resultList?.contains(result) != true) {
                                content.resultList?.add(result)
                            }
                        }
                    }
                }
            }
        }

        private fun rulerSameSign(
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            content.houseA?.forEach { itA ->
                content.houseB?.forEach { itB ->
                    val rulerA = horoscopeA.houses.signBeanList[itA - 1].ruler.split("、")[0]
                    val planetA = horoscopeA.planetList.find { it.chName == rulerA }
                    if (planetA != null) {
                        val signA = planetA.signBean.chName
                        val rulerB = horoscopeB.houses.signBeanList[itB - 1].ruler.split("、")[0]
                        val planetB = horoscopeB.planetList.find { it.chName == rulerB }
                        if (planetB != null) {
                            val signB = planetB.signBean.chName
                            if (signA.contains(signB)) {
                                content.isChecked = true
                                val result =
                                    "${horoscopeA.name} 的 $itA 宮主星${rulerA}落${signA}與 ${horoscopeB.name} 的 $itB 宮主星${rulerB}落${signA}相同"
                                if (content.resultList?.contains(result) != true) {
                                    content.resultList?.add(result)
                                }
                            }
                        }
                    }

                }
            }
        }

        private fun rulerSameOppositeSign(
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            content.houseA?.forEach { itA ->
                val rulerA = horoscopeA.houses.signBeanList[itA - 1].ruler.split("、")[0]
                val planetA = horoscopeA.planetList.find { it.chName == rulerA }
                if (planetA != null) {
                    val signA = planetA.signBean.chName
                    val planetB = horoscopeB.planetList.find { it.chName == planetA.chName }
                    if (planetB != null) {
                        val signB = planetB.signBean.chName
                        val oppositeSign = getOppositeSign(signB)
                        if (signA == signB) {
                            content.isChecked = true
                            val result =
                                "${horoscopeA.name} 的 $itA 宮主星${rulerA}在${signA} 與 ${horoscopeB.name} 的${rulerA}在$signB 相同"
                            if (content.resultList?.contains(result) != true) {
                                content.resultList?.add(result)
                            }
                        } else if (signA == oppositeSign) {
                            content.isChecked = true
                            val result =
                                "${horoscopeA.name} 的 $itA 宮主星${rulerA}在${signA} 與 ${horoscopeB.name} 的${rulerA}在$signB 相反"
                            if (content.resultList?.contains(result) != true) {
                                content.resultList?.add(result)
                            }
                        }
                    }
                }
            }
        }

        private fun sameDecanicSign(
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            content.houseA?.forEach { itA ->
                content.houseB?.forEach { itB ->
                    val signA = horoscopeA.houses.signBeanList[itA - 1].chName
                    val degreeA = horoscopeA.houses.signBeanList[itA - 1].degree.split("°")[0]
                    val planetNameA = getDecanicPlanet(signA, degreeA.toInt())

                    val signB = horoscopeB.houses.signBeanList[itB - 1].chName
                    val degreeB = horoscopeB.houses.signBeanList[itB - 1].degree.split("°")[0]
                    val planetNameB = getDecanicPlanet(signB, degreeB.toInt())
                    if (planetNameA == planetNameB) {
                        content.isChecked = true
                        val result =
                            "${horoscopeA.name} 的 $itA 宮外觀主星${planetNameA} 與 ${horoscopeB.name} 的 $itB 宮外觀主星${planetNameA} 相同"
                        if (content.resultList?.contains(result) != true) {
                            content.resultList?.add(result)
                        }
                    }
                }
            }
        }

        private fun getOppositeSign(sign: String): String {
            val signs = listOf(
                "牡羊座", "金牛座", "雙子座", "巨蟹座",
                "獅子座", "處女座", "天秤座", "天蠍座",
                "射手座", "摩羯座", "水瓶座", "雙魚座"
            )

            val index = signs.indexOf(sign)
            if (index == -1) {
                return "無效的星座名稱"
            }

            // 計算對宮星座的位置
            val oppositeIndex = (index + 6) % 12
            return signs[oppositeIndex]
        }

        private fun getDecanicPlanet(sign: String, degree: Int): String {
            // 檢查有效的度數範圍
            if (degree < 0 || degree >= 360) {
                return "請輸入有效的度數（0到359之間）"
            }

            // 星座與其對應的行星對應表
            val decans = mapOf(
                "牡羊座" to listOf("火星", "太陽", "金星"),
                "金牛座" to listOf("水星", "月亮", "土星"),
                "雙子座" to listOf("木星", "火星", "太陽"),
                "巨蟹座" to listOf("金星", "水星", "月亮"),
                "獅子座" to listOf("土星", "木星", "火星"),
                "處女座" to listOf("太陽", "金星", "水星"),
                "天秤座" to listOf("月亮", "土星", "木星"),
                "天蠍座" to listOf("火星", "太陽", "金星"),
                "射手座" to listOf("水星", "月亮", "土星"),
                "摩羯座" to listOf("木星", "火星", "太陽"),
                "水瓶座" to listOf("金星", "水星", "月亮"),
                "雙魚座" to listOf("土星", "木星", "火星")
            )

            // 如果星座無效，返回錯誤訊息
            val planetList = decans[sign] ?: return "無效的星座名稱"

            // 根據度數選擇行星
            val planetIndex = degree / 10
            return planetList.getOrNull(planetIndex) ?: "無效的度數範圍"
        }

        private fun aspects(
            context: Context,
            chart: Chart,
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            if (content.subContents != null) {
                getContent(context, chart, content, horoscopeA, horoscopeB)
                return
            }

            content.angle?.forEach { angle ->
                content.planetNameListA?.forEach {
                    content.planetNameListB?.forEach { it1 ->
                        aspects(horoscopeA, horoscopeB, it, angle, it1, content)
                        aspects(horoscopeB, horoscopeA, it, angle, it1, content)
                    }
                }
            }
        }

        private fun aspects(
            content: Topics.TopicItem.Content,
            horoscope: Horoscope
        ) {
            if (content.subContents != null) {
                content.subContents.forEach {
                    getContent(it, horoscope)
                }
                return
            }
            content.angle?.forEach { angle ->
                content.planetNameA?.let {
                    content.planetNameB?.let { it1 ->
                        aspects(horoscope.planetList, it, angle, it1, content)
                    }
                }
            }
        }

        private fun aspects(
            planetList: MutableList<PlanetBean>,
            planetNameA: String,
            angle: Double,
            planetNameB: String,
            content: Topics.TopicItem.Content
        ) {
            val planet = planetList.find { it.chName == planetNameA }
            if (planet != null) {
                AspectType.getType(angle)
                if (planet.aspects.find { it.type == AspectType.getType(angle) && it.planet.chName == planetNameB } != null) {
                    content.isChecked = true
                    content.resultList?.add(
                        planetNameA + (AspectType.getType(angle)?.nameCh ?: "") + planetNameB
                    )
                }
            }
        }

        private fun planetAndSign(
            content: Topics.TopicItem.Content,
            horoscope: Horoscope
        ) {
            content.signNameList?.forEach { signName ->
                if (content.planetNameA.isNullOrEmpty()) {
                    content.planetNameListA?.let {
                        content.planetNameListA!!.forEach { name ->
                            planet(horoscope.planetList, name, signName, content)
                        }
                    }
                } else {
                    content.planetNameA?.let {
                        planet(horoscope.planetList, it, signName, content)
                    }
                }
            }
        }

        fun house(
            content: Topics.TopicItem.Content,
            horoscope: Horoscope
        ) {
            content.house?.forEach { house ->
                if (content.planetNameA.isNullOrEmpty()) {
                    content.planetNameListA?.let {
                        content.planetNameListA!!.forEach { name ->
                            house(horoscope.planetList, name, house, content)
                        }
                    }
                } else {
                    content.planetNameA?.let {
                        house(horoscope.planetList, it, house, content)
                    }
                }
            }
        }

        fun house(
            context: Context,
            content: Topics.TopicItem.Content,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ) {
            val planetList = getPlanetList(context, horoscopeA, horoscopeB)
            content.planetNameListA?.forEach { planetName ->
                content.house?.forEach { house ->
                    val planet = planetList.find { it.chName == planetName }
                    if (planet != null) {
                        if (planet.signBean.houseData.index == house) {
                            content.isChecked = true
                            val result =
                                "${horoscopeA.name} 的${planetName}落 ${horoscopeB.name} 的${house}宮"
                            content.resultList?.add(result)
                        }
                    }
                }
            }
        }

        private fun getPlanetList(
            context: Context,
            horoscopeA: Horoscope,
            horoscopeB: Horoscope
        ): ArrayList<PlanetBean> {
            val list = ArrayList<PlanetBean>()
            try {
                val signList = AssetsToObjectUtil.getSignList(context)
                val planetBeanList =
                    horoscopeA.getPlanetBeanList().sortedWith(compareBy {
                        it.id
                    })
                val planetList = planetBeanList.filter { it.isChecked }
                for (planet in planetList) {
                    val strings: ArrayList<String> = EphemerisUtil.szZodiac(planet.longitude)
                    planet.signBean = SignBean(signList[strings[0].toInt()])
                    planet.signBean.degree = "${strings[1]}°"
                    planet.signBean.minute = strings[2]
                    planet.signBean.houseData =
                        EphemerisUtil.house(planet.longitude, horoscopeB.houses.cusps)
                    LogUtil.i("星位 ${planet.chName} ${planet.signBean.chName} ${strings[1]}°${strings[2]} ${planet.signBean.houseData.index}宮")
                    list.add(planet)
                }
            } catch (ex: Exception) {
                ex.message?.let { LogUtil.e(it) }
            }
            return list
        }


        private fun isRetrograde(
            planetList: MutableList<PlanetBean>,
            planetName: String,
            content: Topics.TopicItem.Content
        ) {
            val planet = planetList.find { it.chName == planetName }
            if (planet != null) {
                if (planet.isRetrograde == content.isRetrograde) {
                    content.isChecked = true
                    if (planet.isRetrograde) {
                        content.resultList?.add(planetName + "逆行")
                    } else {
                        content.resultList?.add(planetName + "順行")
                    }
                }
            }
        }

        private fun house(
            planetList: MutableList<PlanetBean>,
            planetName: String,
            index: Int,
            content: Topics.TopicItem.Content
        ) {
            val planet = planetList.find { it.chName == planetName }
            if (planet != null) {
                if (planet.signBean.houseData.index == index) {
                    content.isChecked = true
                    content.resultList?.add(planetName + "落" + index + "宮")
                }
            }
        }

        private fun planet(
            planetList: MutableList<PlanetBean>,
            planetName: String,
            signName: String,
            content: Topics.TopicItem.Content
        ) {
            val planet = planetList.find { it.chName == planetName }
            if (planet != null) {
                if (planet.signBean.chName == signName) {
                    content.isChecked = true
                    content.resultList?.add(planetName + planet.signBean.chName)
                }
            }
        }

        private fun aspects(
            horoscopeA: Horoscope,
            horoscopeB: Horoscope,
            planetNameA: String,
            angle: Double,
            planetNameB: String,
            content: Topics.TopicItem.Content
        ) {
            val planet = horoscopeA.planetList.find { it.chName == planetNameA }
            if (planet != null) {
                if (planet.aspects.find { it.type == AspectType.getType(angle) && it.planet.chName == planetNameB } != null) {
                    content.isChecked = true
                    content.resultList?.add(
                        "${horoscopeA.name} 的 $planetNameA ${(AspectType.getType(angle)?.nameCh ?: "")} ${horoscopeB.name} 的 $planetNameB"
                    )
                }
            }
        }
    }
}