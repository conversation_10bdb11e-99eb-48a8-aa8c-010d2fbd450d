package com.one.astrology.topic

import android.content.Context
import com.one.astrology.R
import com.one.astrology.data.DescData
import com.one.astrology.data.Horoscope
import com.one.astrology.db.BasicDBHelper

class CareerAnalyze {
    companion object {
        fun analyze(
            context: Context,
            horoscope: Horoscope
        ): ArrayList<DescData> {
            val result = ArrayList<DescData>()
            val sun = horoscope.planetList.find { it.chName == "太陽" }
            if (sun != null) {
                val sunHouse = sun.signBean.houseData.index

                val house = BasicDBHelper.queryHouse(context, sunHouse.toString())
                if (house != null) {
                    val descData = DescData()
                    descData.aspect =
                        sun.aspects.find { it.type.value == 90 || it.type.value == 180 }
                    descData.title =
                        context.getString(R.string.house_where_the_sun_located, sunHouse)
                    descData.desc = context.getString(R.string.suitable_job, house.career)
                    result.add(descData)
                }
            }

            val jupiter = horoscope.planetList.find { it.chName == "木星" }
            if (jupiter != null) {
                val jupiterHouse = jupiter.signBean.houseData.index
                val house = BasicDBHelper.queryHouse(context, jupiterHouse.toString())
                if (house != null) {
                    val descData = DescData()
                    descData.aspect =
                        jupiter.aspects.find { it.type.value == 90 || it.type.value == 180 }
                    descData.title =
                        context.getString(R.string.jupiter_house_position, jupiterHouse)
                    descData.desc =
                        context.getString(
                            R.string.suitable_job,
                            house.career
                        )
                    result.add(descData)
                }
            }

            val house9 = horoscope.houses.signBeanList[8]
            val rulers9 = house9.ruler.split("、")
            for (element in rulers9) {
                val descData = analyzeRuler(context, R.string.house_of_9_house, horoscope, element)
                result.add(descData)
            }

            val house2 = horoscope.houses.signBeanList[1]
            val rulers2 = house2.ruler.split("、")
            for (element in rulers2) {
                val descData =
                    analyzeRuler(context, R.string.house_which_2nd_house, horoscope, element)
                result.add(descData)
            }
            return result
        }

        private fun analyzeRuler(
            context: Context,
            stringId: Int,
            horoscope: Horoscope,
            ruler: String
        ): DescData {
            val descData = DescData()
            val rulerPlanet = horoscope.planetList.find { it.chName == ruler }
            if (rulerPlanet != null) {
                descData.aspect =
                    rulerPlanet.aspects.find { it.type.value == 90 || it.type.value == 180 }
                val rulerHouse = rulerPlanet.signBean.houseData.index
                descData.title = context.getString(stringId, rulerPlanet.chName, rulerHouse)
                val house = BasicDBHelper.queryHouse(context, rulerHouse.toString())
                if (house != null) {
                    descData.desc = context.getString(
                        R.string.suitable_job,
                        house.career
                    )
                    return descData
                }
            }
            return descData
        }


        fun analyzeFate(
            context: Context,
            horoscope: Horoscope
        ): ArrayList<DescData> {
            val result = ArrayList<DescData>()
            horoscope.houses.signBeanList
            val sun = horoscope.planetList.find { it.chName == "太陽" }
            if (sun != null) {
                val sunHouse = sun.signBean.houseData.index

                val house = BasicDBHelper.queryHouse(context, sunHouse.toString())
                if (house != null) {
                    val descData = DescData()
                    descData.aspect =
                        sun.aspects.find { it.type.value == 90 || it.type.value == 180 }
                    descData.title =
                        context.getString(R.string.house_where_the_sun_located, sunHouse)
                    descData.desc = context.getString(R.string.suitable_job, house.career)
                    result.add(descData)
                }
            }

            val jupiter = horoscope.planetList.find { it.chName == "木星" }
            if (jupiter != null) {
                val jupiterHouse = jupiter.signBean.houseData.index
                val house = BasicDBHelper.queryHouse(context, jupiterHouse.toString())
                if (house != null) {
                    val descData = DescData()
                    descData.aspect =
                        jupiter.aspects.find { it.type.value == 90 || it.type.value == 180 }
                    descData.title =
                        context.getString(R.string.jupiter_house_position, jupiterHouse)
                    descData.desc =
                        context.getString(
                            R.string.suitable_job,
                            house.career
                        )
                    result.add(descData)
                }
            }

            val house9 = horoscope.houses.signBeanList[8]
            val rulers9 = house9.ruler.split("、")
            for (element in rulers9) {
                val descData = analyzeRuler(context, R.string.house_of_9_house, horoscope, element)
                result.add(descData)
            }

            val house2 = horoscope.houses.signBeanList[1]
            val rulers2 = house2.ruler.split("、")
            for (element in rulers2) {
                val descData =
                    analyzeRuler(context, R.string.house_which_2nd_house, horoscope, element)
                result.add(descData)
            }
            return result
        }
    }
}