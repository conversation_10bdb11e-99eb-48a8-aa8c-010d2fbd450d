package com.one.astrology.topic

import android.content.Context
import com.one.astrology.data.Horoscope

class AnalyzeHealth {
    companion object{
        fun topicItem(
            context: Context,
            horoscopeA: Horoscope
        ) {
//            EphemerisUtil.aspects(
//                context,
//                Chart.Natal,
//                horoscopeA.planetList,
//                horoscopeB.planetList,
//                isMatch = true, isGetScore = false
//            )


        }

    }
}