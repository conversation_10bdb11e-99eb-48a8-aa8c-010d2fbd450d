package com.one.astrology.di



import com.google.gson.Gson
import com.one.astrology.BuildConfig
import com.one.astrology.api.AstroApi
import com.one.astrology.api.OInterceptor
import dagger.Module
import dagger.Provides
import dagger.Reusable
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

@Module
@InstallIn(SingletonComponent::class)
object AstroApiModule {

    @Reusable
    @Provides
    fun provideAstroApi(
        gson: Gson
    ): AstroApi {
        val loggingInterceptor = OInterceptor()
        loggingInterceptor.level = if (BuildConfig.DEBUG) {
            OInterceptor.Level.BODY
        } else {
            OInterceptor.Level.NONE
        }
        val client = OkHttpClient.Builder().build()
        val newOkHttpClient = client.newBuilder()
        newOkHttpClient.addInterceptor(loggingInterceptor)
        newOkHttpClient.connectTimeout(10, TimeUnit.SECONDS)
        newOkHttpClient.writeTimeout(10, TimeUnit.SECONDS)
        newOkHttpClient.readTimeout(10, TimeUnit.SECONDS)

        newOkHttpClient.addInterceptor(Interceptor { chain ->
            val original = chain.request()
            val builder = original.newBuilder()
            val request = builder.method(original.method, original.body).build()
            chain.proceed(request)
        })

        val retrofit = Retrofit.Builder()
            .baseUrl("https://www.astro.com")
            .client(newOkHttpClient.build())
//            .addCallAdapterFactory(ApiResultCallAdapterFactory)
//            .addConverterFactory(ApiResultConverterFactory)
            .addConverterFactory(GsonConverterFactory.create())
//            .addConverterFactory(MoshiConverterFactory.create(moshi))
            .build()

        return retrofit.create(AstroApi::class.java)
    }

}


