package com.one.astrology.di

import com.one.astrology.api.FcmApi
import com.one.astrology.api.OpenApi
import dagger.Module
import dagger.Provides
import dagger.Reusable
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
object ApiModule {

    @Reusable
    @Provides
    fun provideFcmApi(retrofit: Retrofit): FcmApi =
        retrofit.create(FcmApi::class.java)

    @Reusable
    @Provides
    fun provideOpenApi(retrofit: Retrofit): OpenApi =
        retrofit.create(OpenApi::class.java)

}