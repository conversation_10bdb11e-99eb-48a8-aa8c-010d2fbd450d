package com.one.astrology.di


import com.google.gson.Gson
import com.google.gson.GsonBuilder
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetModule {


    @Singleton
    @Provides
    fun provideGson(): Gson {
        return GsonBuilder()
            .setPrettyPrinting()
            .serializeNulls()
            .create()
    }

//    @Singleton
//    @Provides
//    fun provideFCMRetrofit(
//        gson: Gson
//    ): Retrofit {
//        return Retrofit.Builder()
//            .baseUrl("https://fcm.googleapis.com/")
//            .addConverterFactory(GsonConverterFactory.create(gson))
//            .build()
//    }

    @Singleton
    @Provides
    fun provideRetrofit(
        gson: Gson
    ): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://api.openai.com/")
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }

}