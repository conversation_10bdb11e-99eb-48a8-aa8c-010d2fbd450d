package com.one.astrology.model

data class PlanetCondition(
    val name: String,
    val dignity: Dignity = Dignity.NONE,
    val isRetrograde: <PERSON><PERSON><PERSON> = false,
    val isCombust: <PERSON>ole<PERSON> = false,
    val isCazimi: <PERSON><PERSON><PERSON> = false,
    val isUnderSunBeams: <PERSON><PERSON><PERSON> = false,
    val isVoidOfCourse: <PERSON>olean = false,
    val isInFeralSign: <PERSON>olean = false,
    val hasGoodAspects: <PERSON>olean = false,
    val hasBadAspects: <PERSON><PERSON><PERSON> = false,
    val isInAngularHouse: <PERSON><PERSON><PERSON> = false,
    val conjunctNorthNode: Boolean = false,
    val conjunctSouthNode: Boolean = false
)

enum class Dignity {
    DOMICILE, EXALTATION, DETRIMENT, FALL, NONE
}

data class PlanetAnalysisResult(
    val name: String,
    val score: Int,
    val interpretation: String
)
