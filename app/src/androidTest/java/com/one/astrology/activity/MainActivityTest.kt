package com.one.astrology.activity

import androidx.test.core.app.ActivityScenario
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.one.astrology.ui.activity.MainActivity
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * MainActivity的UI測試
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {
    
    @Before
    fun setup() {
        // 測試前的準備工作
    }
    
    @Test
    fun testBottomNavigationDisplay() {
        // 啟動MainActivity
        ActivityScenario.launch(MainActivity::class.java)
        
        // 檢查底部導航欄是否顯示
//        onView(withId(R.id.bottom_navigation)).check(matches(isDisplayed()))
    }
    
    @Test
    fun testNavigationToAnalyzeFragment() {
        // 啟動MainActivity
        ActivityScenario.launch(MainActivity::class.java)
        
        // 點擊分析標籤（假設分析標籤的ID是R.id.navigation_analyze）
//        onView(withId(R.id.navigation_analyze)).perform(click())
        
        // 驗證分析頁面已顯示（假設分析頁面的容器ID是R.id.analyze_fragment_container）
//        onView(withId(R.id.analyze_fragment_container)).check(matches(isDisplayed()))
    }
    
    @Test
    fun testNavigationToHomeFragment() {
        // 啟動MainActivity
        ActivityScenario.launch(MainActivity::class.java)
        
        // 點擊首頁標籤（假設首頁標籤的ID是R.id.navigation_home）
//        onView(withId(R.id.navigation_home)).perform(click())
        
        // 驗證首頁已顯示（假設首頁的容器ID是R.id.home_fragment_container）
//        onView(withId(R.id.home_fragment_container)).check(matches(isDisplayed()))
    }
    
    @Test
    fun testNavigationToSettingsFragment() {
        // 啟動MainActivity
        ActivityScenario.launch(MainActivity::class.java)
        
        // 點擊設置標籤（假設設置標籤的ID是R.id.navigation_settings）
//        onView(withId(R.id.navigation_settings)).perform(click())
        
        // 驗證設置頁面已顯示（假設設置頁面的容器ID是R.id.settings_fragment_container）
//        onView(withId(R.id.settings_fragment_container)).check(matches(isDisplayed()))
    }
} 