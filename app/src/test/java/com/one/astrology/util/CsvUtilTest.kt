package com.one.astrology.util

import com.google.common.truth.Truth.assertThat
import org.junit.Test

/**
 * CsvUtil 單元測試
 * 測試 CSV 格式處理功能，特別是包含逗號、雙引號等特殊字符的處理
 */
class CsvUtilTest {

    @Test
    fun `escapeField should handle normal text without quotes`() {
        val result = CsvUtil.escapeField("normal text")
        assertThat(result).isEqualTo("normal text")
    }

    @Test
    fun `escapeField should wrap text with commas in quotes`() {
        val result = CsvUtil.escapeField("text, with comma")
        assertThat(result).isEqualTo("\"text, with comma\"")
    }

    @Test
    fun `escapeField should escape quotes and wrap in quotes`() {
        val result = CsvUtil.escapeField("text with \"quotes\"")
        assertThat(result).isEqualTo("\"text with \"\"quotes\"\"\"")
    }

    @Test
    fun `escapeField should handle newlines`() {
        val result = CsvUtil.escapeField("text\nwith\nnewlines")
        assertThat(result).isEqualTo("\"text\nwith\nnewlines\"")
    }

    @Test
    fun `escapeField should handle null values`() {
        val result = CsvUtil.escapeField(null)
        assertThat(result).isEqualTo("")
    }

    @Test
    fun `escapeField should handle empty string`() {
        val result = CsvUtil.escapeField("")
        assertThat(result).isEqualTo("")
    }

    @Test
    fun `createCsvRow should create proper CSV row`() {
        val result = CsvUtil.createCsvRow("name", "value, with comma", "normal")
        assertThat(result).isEqualTo("name,\"value, with comma\",normal")
    }

    @Test
    fun `createCsvHeader should create header with newline`() {
        val result = CsvUtil.createCsvHeader("Name", "Value", "Tag")
        assertThat(result).isEqualTo("Name,Value,Tag\n")
    }

    @Test
    fun `parseCsvLine should parse simple CSV line`() {
        val result = CsvUtil.parseCsvLine("name,value,tag")
        assertThat(result).containsExactly("name", "value", "tag").inOrder()
    }

    @Test
    fun `parseCsvLine should parse CSV line with quoted fields`() {
        val result = CsvUtil.parseCsvLine("name,\"value, with comma\",tag")
        assertThat(result).containsExactly("name", "value, with comma", "tag").inOrder()
    }

    @Test
    fun `parseCsvLine should parse CSV line with escaped quotes`() {
        val result = CsvUtil.parseCsvLine("name,\"value with \"\"quotes\"\"\",tag")
        assertThat(result).containsExactly("name", "value with \"quotes\"", "tag").inOrder()
    }

    @Test
    fun `parseCsvLine should handle empty fields`() {
        val result = CsvUtil.parseCsvLine("name,,tag")
        assertThat(result).containsExactly("name", "", "tag").inOrder()
    }

    @Test
    fun `parseCsvLine should handle quoted empty fields`() {
        val result = CsvUtil.parseCsvLine("name,\"\",tag")
        assertThat(result).containsExactly("name", "", "tag").inOrder()
    }

    @Test
    fun `validateFieldCount should return true for sufficient fields`() {
        val fields = listOf("a", "b", "c")
        val result = CsvUtil.validateFieldCount(fields, 3)
        assertThat(result).isTrue()
    }

    @Test
    fun `validateFieldCount should return false for insufficient fields`() {
        val fields = listOf("a", "b")
        val result = CsvUtil.validateFieldCount(fields, 3)
        assertThat(result).isFalse()
    }

    @Test
    fun `getFieldSafely should return field value when index is valid`() {
        val fields = listOf("a", "b", "c")
        val result = CsvUtil.getFieldSafely(fields, 1)
        assertThat(result).isEqualTo("b")
    }

    @Test
    fun `getFieldSafely should return default value when index is out of bounds`() {
        val fields = listOf("a", "b")
        val result = CsvUtil.getFieldSafely(fields, 5, "default")
        assertThat(result).isEqualTo("default")
    }

    @Test
    fun `getFieldSafely should trim whitespace`() {
        val fields = listOf("  a  ", "  b  ")
        val result = CsvUtil.getFieldSafely(fields, 0)
        assertThat(result).isEqualTo("a")
    }

    @Test
    fun `parseDoubleSafely should parse valid double`() {
        val result = CsvUtil.parseDoubleSafely("123.45")
        assertThat(result).isEqualTo(123.45)
    }

    @Test
    fun `parseDoubleSafely should return default for invalid double`() {
        val result = CsvUtil.parseDoubleSafely("invalid", -1.0)
        assertThat(result).isEqualTo(-1.0)
    }

    @Test
    fun `parseBooleanSafely should parse valid boolean`() {
        val result1 = CsvUtil.parseBooleanSafely("true")
        val result2 = CsvUtil.parseBooleanSafely("false")
        assertThat(result1).isTrue()
        assertThat(result2).isFalse()
    }

    @Test
    fun `parseBooleanSafely should return default for invalid boolean`() {
        val result = CsvUtil.parseBooleanSafely("invalid", false)
        assertThat(result).isFalse()
    }

    @Test
    fun `integration test - export and import cycle`() {
        // 模擬包含特殊字符的資料
        val originalData = listOf(
            listOf("張三", "台北市, 信義區", "25.0", "121.5", "true"),
            listOf("李四", "高雄市\"港都\"", "22.6", "120.3", "false"),
            listOf("王五", "台中市\n西區", "24.1", "120.7", "true")
        )

        // 匯出為 CSV
        val csvContent = StringBuilder()
        csvContent.append(CsvUtil.createCsvHeader("姓名", "地址", "緯度", "經度", "啟用"))
        
        originalData.forEach { row ->
            csvContent.append(CsvUtil.createCsvRow(*row.toTypedArray()) + "\n")
        }

        // 模擬匯入過程
        val lines = csvContent.toString().split("\n").filter { it.isNotBlank() }
        val header = lines[0]
        val dataLines = lines.drop(1)

        // 解析資料
        val parsedData = dataLines.map { line ->
            CsvUtil.parseCsvLine(line)
        }

        // 驗證資料完整性
        assertThat(parsedData).hasSize(3)
        assertThat(parsedData[0]).containsExactly("張三", "台北市, 信義區", "25.0", "121.5", "true").inOrder()
        assertThat(parsedData[1]).containsExactly("李四", "高雄市\"港都\"", "22.6", "120.3", "false").inOrder()
        assertThat(parsedData[2]).containsExactly("王五", "台中市\n西區", "24.1", "120.7", "true").inOrder()
    }
}
