package com.one.astrology.util

import com.one.astrology.data.entity.BirthData
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Test

/**
 * NotesUtil工具類的單元測試
 */
class NotesUtilTest {

    @Test
    fun testIsValidNotes() {
        // 測試有效備注
        assertTrue(NotesUtil.isValidNotes("這是一個有效的備注"))
        assertTrue(NotesUtil.isValidNotes("  有效備注  "))
        
        // 測試無效備注
        assertFalse(NotesUtil.isValidNotes(null))
        assertFalse(NotesUtil.isValidNotes(""))
        assertFalse(NotesUtil.isValidNotes("   "))
        assertFalse(NotesUtil.isValidNotes("\n\n"))
    }

    @Test
    fun testFormatNotes() {
        // 測試基本格式化
        assertEquals("測試備注", NotesUtil.formatNotes("  測試備注  "))
        
        // 測試換行符統一
        assertEquals("第一行\n第二行", NotesUtil.formatNotes("第一行\r\n第二行"))
        assertEquals("第一行\n第二行", NotesUtil.formatNotes("第一行\r第二行"))
        
        // 測試多餘換行符移除
        assertEquals("第一行\n\n第二行", NotesUtil.formatNotes("第一行\n\n\n\n第二行"))
        
        // 測試空值處理
        assertEquals("", NotesUtil.formatNotes(null))
        assertEquals("", NotesUtil.formatNotes(""))
    }

    @Test
    fun testGetNotesPreview() {
        val longNotes = "這是一個很長的備注內容，用來測試摘要功能是否正常工作"
        
        // 測試正常摘要
        assertEquals("這是一個很長的備注內容，用來測試摘要功能是否正常工作", NotesUtil.getNotesPreview(longNotes, 100))
        assertEquals("這是一個很長的備注內容，用來測試摘要功能是否正常工作...", NotesUtil.getNotesPreview(longNotes, 30))
        
        // 測試多行備注
        val multiLineNotes = "第一行\n第二行\n第三行"
        assertEquals("第一行", NotesUtil.getNotesPreview(multiLineNotes, 100))
        
        // 測試空值
        assertEquals("", NotesUtil.getNotesPreview(null))
        assertEquals("", NotesUtil.getNotesPreview(""))
    }

    @Test
    fun testAddTimestampedNotes() {
        val birthData = BirthData().apply {
            name = "測試用戶"
            notes = "原有備注"
        }
        
        val result = NotesUtil.addTimestampedNotes(birthData, "新增備注")
        
        // 檢查是否包含原有備注
        assertTrue(result.contains("原有備注"))
        
        // 檢查是否包含新增備注
        assertTrue(result.contains("新增備注"))
        
        // 檢查是否包含時間戳格式
        assertTrue(result.contains("["))
        assertTrue(result.contains("]"))
    }

    @Test
    fun testContainsKeywords() {
        val notes = "這是一個包含關鍵字的備注內容"
        val keywords = listOf("關鍵字", "測試")
        
        // 測試包含關鍵字
        assertTrue(NotesUtil.containsKeywords(notes, listOf("關鍵字")))
        assertTrue(NotesUtil.containsKeywords(notes, keywords))
        
        // 測試不包含關鍵字
        assertFalse(NotesUtil.containsKeywords(notes, listOf("不存在")))
        
        // 測試大小寫忽略
        assertTrue(NotesUtil.containsKeywords(notes, listOf("關鍵字"), true))
        assertTrue(NotesUtil.containsKeywords(notes, listOf("關鍵字"), false))
        
        // 測試空值
        assertFalse(NotesUtil.containsKeywords(null, keywords))
        assertFalse(NotesUtil.containsKeywords(notes, emptyList()))
    }

    @Test
    fun testGetNotesStats() {
        val notes = "這是測試\n第二行\n第三行"
        val stats = NotesUtil.getNotesStats(notes)
        
        // 檢查統計結果
        assertTrue(stats.characterCount > 0)
        assertTrue(stats.wordCount > 0)
        assertEquals(3, stats.lineCount)
        
        // 測試空值
        val emptyStats = NotesUtil.getNotesStats(null)
        assertEquals(0, emptyStats.characterCount)
        assertEquals(0, emptyStats.wordCount)
        assertEquals(0, emptyStats.lineCount)
    }

    @Test
    fun testExportNotesToText() {
        val birthData = BirthData().apply {
            name = "測試用戶"
            birthdayString = "1990-01-01 12:00"
            birthplaceArea = "台北市"
            tag = "自己"
            notes = "這是測試備注"
            createTime = System.currentTimeMillis()
        }
        
        val exportText = NotesUtil.exportNotesToText(birthData)
        
        // 檢查是否包含所有必要信息
        assertTrue(exportText.contains("測試用戶"))
        assertTrue(exportText.contains("1990-01-01 12:00"))
        assertTrue(exportText.contains("台北市"))
        assertTrue(exportText.contains("自己"))
        assertTrue(exportText.contains("這是測試備注"))
        assertTrue(exportText.contains("=== 出生資料備注 ==="))
        assertTrue(exportText.contains("=== 結束 ==="))
    }
}
