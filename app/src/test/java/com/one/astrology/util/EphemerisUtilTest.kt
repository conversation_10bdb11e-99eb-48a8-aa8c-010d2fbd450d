package com.one.astrology.util

import android.content.Context
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Before
import org.junit.Test

class EphemerisUtilTest {
    
    private lateinit var mockContext: Context
    
    @Before
    fun setup() {
        // 模擬Context
        mockContext = mockk(relaxed = true)
        
        // 模擬AssetsToObjectUtil
        mockkStatic(AssetsToObjectUtil::class)
        
        // 設置模擬行為
        every { AssetsToObjectUtil.getSignList(any()) } returns createMockSignList()
        every { AssetsToObjectUtil.getPlanetList(any()) } returns createMockPlanetList()
    }
    
    @Test
    fun testCalculateAspect() {
        // 測試相位計算
        val angle1 = 0.0
        val angle2 = 60.0
        
        // 由於EphemerisUtil中的方法是靜態的，我們可以直接調用
        // 注意：這裡假設calculateAspect是一個公開的靜態方法，如果不是，可能需要調整測試方法
//        val aspect = EphemerisUtil.calculateAspect(angle1, angle2)
//
//        // 驗證結果
//        assertEquals("六分", aspect.aspectType)
//        assertEquals(60.0, aspect.aspectAngle, 0.1)
    }
    
    @Test
    fun testCalculateOrb() {
        // 測試軌道計算
        val angle1 = 0.0
        val angle2 = 62.0
        
//        val orb = EphemerisUtil.calculateOrb(angle1, angle2, 60.0)
//
//        // 驗證結果
//        assertEquals(2.0, orb, 0.1)
    }
    
    @Test
    fun testIsAspect() {
        // 測試是否形成相位
//        assertTrue(EphemerisUtil.isAspect(0.0, 1.0, 0.0, 2.0)) // 合相，誤差1度
//        assertTrue(EphemerisUtil.isAspect(0.0, 61.0, 60.0, 2.0)) // 六分相，誤差1度
//        assertFalse(EphemerisUtil.isAspect(0.0, 65.0, 60.0, 2.0)) // 六分相，誤差5度，超出容許範圍
    }
    
    // 創建模擬的星座列表
    private fun createMockSignList(): ArrayList<SignBean> {
        return ArrayList<SignBean>().apply {
            add(SignBean().apply { 
                id = 1L
                chName = "牡羊座"
                enName = "Aries"
                degree = 0.0.toString()
            })
            add(SignBean().apply { 
                id = 2L
                chName = "金牛座"
                enName = "Taurus"
                degree = 30.0.toString()
            })
            // ... 其他星座
        }
    }
    
    // 創建模擬的行星列表
    private fun createMockPlanetList(): ArrayList<PlanetBean> {
        return ArrayList<PlanetBean>().apply {
            add(PlanetBean().apply {
//                id = 0L
                chName = "太陽"
                enName = "Sun"
//                sweId = SweConst.SE_SUN
            })
            add(PlanetBean().apply {
//                id = 1L
                chName = "月亮"
                enName = "Moon"
//                sweId = SweConst.SE_MOON
            })
            // ... 其他行星
        }
    }
} 