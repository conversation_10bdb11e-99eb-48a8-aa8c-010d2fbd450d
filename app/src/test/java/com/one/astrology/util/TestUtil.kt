package com.one.astrology.util

import com.one.astrology.data.Horoscope
import com.one.astrology.data.Houses
import com.one.astrology.data.bean.PlanetBean
import com.one.astrology.data.bean.SignBean
import com.one.astrology.data.db.AspectData
import com.one.astrology.data.entity.BirthData
import java.util.Calendar

/**
 * 測試工具類，提供測試數據和幫助函數
 */
object TestUtil {
    
    /**
     * 創建測試用的出生數據
     */
    fun createTestBirthData(
        name: String = "測試用戶",
        year: Int = 1990,
        month: Int = 0, // 0-based month (0 = January)
        day: Int = 1,
        hour: Int = 12,
        minute: Int = 0,
        latitude: Double = 25.0,
        longitude: Double = 121.5
    ): BirthData {
        return BirthData().apply {
            this.name = name
            this.birthday = Calendar.getInstance().apply {
                set(year, month, day, hour, minute, 0)
            }.timeInMillis
            this.birthplaceLatitude = latitude
            this.birthplaceLongitude = longitude
        }
    }
    
    /**
     * 創建測試用的星盤數據
     */
    fun createTestHoroscope(
        name: String = "測試用戶",
        birthData: BirthData? = null
    ): Horoscope {
        val horoscope = if (birthData != null) {
            Horoscope(birthData)
        } else {
            val testBirthData = createTestBirthData(name)
            Horoscope(testBirthData)
        }
        
        // 添加測試用的宮位數據
        horoscope.houses = createTestHouses()
        
        // 添加測試用的行星數據
        horoscope.planetList = createTestPlanetList()
        
        // 添加測試用的相位數據
        horoscope.aspectList = createTestAspectList()
        
        return horoscope
    }
    
    /**
     * 創建測試用的宮位數據
     */
    private fun createTestHouses(): Houses {
        val houses = Houses()
        houses.signBeanList = ArrayList<SignBean>().apply {
            // 添加12個宮位的星座
            add(SignBean().apply { 
                id = 1L
                chName = "牡羊座"
                enName = "Aries"
                degree = 0.0.toString()
//                houseNumber = 1
            })
            add(SignBean().apply { 
                id = 2L
                chName = "金牛座"
                enName = "Taurus"
                degree = 30.0.toString()
//                houseNumber = 2
            })
            // ... 其他宮位
        }
        return houses
    }
    
    /**
     * 創建測試用的行星列表
     */
    private fun createTestPlanetList(): ArrayList<PlanetBean> {
        return ArrayList<PlanetBean>().apply {
            // 太陽
//            add(PlanetBean().apply {
//                id = 0L
//                chName = "太陽"
//                enName = "Sun"
//                degree = 10.5
//                signBean = SignBean().apply {
//                    chName = "摩羯座"
//                    enName = "Capricorn"
//                }
//                houseNumber = 10
//                isRetrograde = false
//            })
//
//            // 月亮
//            add(PlanetBean().apply {
//                id = 1L
//                chName = "月亮"
//                enName = "Moon"
//                degree = 120.3
//                signBean = SignBean().apply {
//                    chName = "獅子座"
//                    enName = "Leo"
//                }
//                houseNumber = 5
//                isRetrograde = false
//            })
            
            // 其他行星...
        }
    }
    
    /**
     * 創建測試用的相位列表
     */
    private fun createTestAspectList(): ArrayList<AspectData> {
        return ArrayList<AspectData>().apply {
            // 太陽與月亮的三分相
//            add(AspectData().apply {
//                planetA = "太陽"
//                planetB = "月亮"
//                aspectType = "三分"
//                aspectAngle = 120.0
//                orb = 0.3
//            })
            
            // 其他相位...
        }
    }
} 