package com.one.astrology.util

/**
 * ZodiacCalculator的單元測試
 */
class ZodiacCalculatorTest {

//    private lateinit var zodiacCalculator: ZodiacCalculator
//
//    @Before
//    fun setup() {
//        zodiacCalculator = ZodiacCalculator()
//    }
//
//    @Test
//    fun testGetWesternZodiac() {
//        // 測試白羊座 (3月21日 - 4月19日)
//        assertEquals("白羊座", zodiacCalculator.getWesternZodiac(Calendar.getInstance().apply {
//            set(2023, Calendar.MARCH, 21)
//        }))
//        assertEquals("白羊座", zodiacCalculator.getWesternZodiac(Calendar.getInstance().apply {
//            set(2023, Calendar.APRIL, 19)
//        }))
//
//        // 測試金牛座 (4月20日 - 5月20日)
//        assertEquals("金牛座", zodiacCalculator.getWesternZodiac(Calendar.getInstance().apply {
//            set(2023, Calendar.APRIL, 20)
//        }))
//        assertEquals("金牛座", zodiacCalculator.getWesternZodiac(Calendar.getInstance().apply {
//            set(2023, Calendar.MAY, 20)
//        }))
//
//        // 測試雙子座 (5月21日 - 6月21日)
//        assertEquals("雙子座", zodiacCalculator.getWesternZodiac(Calendar.getInstance().apply {
//            set(2023, Calendar.MAY, 21)
//        }))
//    }
//
//    @Test
//    fun testGetChineseZodiac() {
//        // 測試鼠年 (2020)
//        assertEquals("鼠", zodiacCalculator.getChineseZodiac(Calendar.getInstance().apply {
//            set(2020, Calendar.JANUARY, 25)
//        }))
//
//        // 測試牛年 (2021)
//        assertEquals("牛", zodiacCalculator.getChineseZodiac(Calendar.getInstance().apply {
//            set(2021, Calendar.FEBRUARY, 12)
//        }))
//
//        // 測試虎年 (2022)
//        assertEquals("虎", zodiacCalculator.getChineseZodiac(Calendar.getInstance().apply {
//            set(2022, Calendar.FEBRUARY, 1)
//        }))
//    }
//
//    @Test
//    fun testGetZodiacElement() {
//        // 測試火象星座
//        assertEquals("火", zodiacCalculator.getZodiacElement("白羊座"))
//        assertEquals("火", zodiacCalculator.getZodiacElement("獅子座"))
//        assertEquals("火", zodiacCalculator.getZodiacElement("射手座"))
//
//        // 測試土象星座
//        assertEquals("土", zodiacCalculator.getZodiacElement("金牛座"))
//        assertEquals("土", zodiacCalculator.getZodiacElement("處女座"))
//        assertEquals("土", zodiacCalculator.getZodiacElement("摩羯座"))
//
//        // 測試風象星座
//        assertEquals("風", zodiacCalculator.getZodiacElement("雙子座"))
//        assertEquals("風", zodiacCalculator.getZodiacElement("天秤座"))
//        assertEquals("風", zodiacCalculator.getZodiacElement("水瓶座"))
//
//        // 測試水象星座
//        assertEquals("水", zodiacCalculator.getZodiacElement("巨蟹座"))
//        assertEquals("水", zodiacCalculator.getZodiacElement("天蠍座"))
//        assertEquals("水", zodiacCalculator.getZodiacElement("雙魚座"))
//    }
//
//    @Test
//    fun testGetZodiacCompatibility() {
//        // 測試相同元素的星座相容性
//        assertEquals(0.9, zodiacCalculator.getZodiacCompatibility("白羊座", "獅子座"), 0.01)
//        assertEquals(0.9, zodiacCalculator.getZodiacCompatibility("金牛座", "處女座"), 0.01)
//
//        // 測試互補元素的星座相容性
//        assertEquals(0.8, zodiacCalculator.getZodiacCompatibility("白羊座", "雙子座"), 0.01)
//        assertEquals(0.8, zodiacCalculator.getZodiacCompatibility("巨蟹座", "摩羯座"), 0.01)
//
//        // 測試對立元素的星座相容性
//        assertEquals(0.4, zodiacCalculator.getZodiacCompatibility("白羊座", "巨蟹座"), 0.01)
//        assertEquals(0.4, zodiacCalculator.getZodiacCompatibility("金牛座", "水瓶座"), 0.01)
//    }
} 