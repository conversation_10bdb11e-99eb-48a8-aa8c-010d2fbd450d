package com.one.astrology.util

import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

/**
 * LiveData測試工具類，用於在測試中觀察LiveData的變化
 */
object LiveDataTestUtil {
    
    /**
     * 獲取LiveData的值，帶超時
     */
    @Throws(InterruptedException::class)
    fun <T> getValue(liveData: LiveData<T>, timeoutInMillis: Long = 2000): T {
        val data = arrayOfNulls<Any>(1)
        val latch = CountDownLatch(1)
        
        val observer = object : Observer<T> {
            override fun onChanged(value: T) {
                data[0] = value
                latch.countDown()
                liveData.removeObserver(this)
            }
        }
        
        liveData.observeForever(observer)
        
        // 等待數據變化或超時
        if (!latch.await(timeoutInMillis, TimeUnit.MILLISECONDS)) {
            liveData.removeObserver(observer)
            throw TimeoutException("LiveData value was never set.")
        }
        
        @Suppress("UNCHECKED_CAST")
        return data[0] as T
    }
    
    /**
     * 觀察LiveData直到滿足條件
     */
    @Throws(InterruptedException::class)
    fun <T> observeUntil(
        liveData: LiveData<T>,
        condition: (T) -> Boolean,
        timeoutInMillis: Long = 2000
    ): T {
        val data = arrayOfNulls<Any>(1)
        val latch = CountDownLatch(1)
        
        val observer = object : Observer<T> {
            override fun onChanged(value: T) {
                if (condition(value)) {
                    data[0] = value
                    latch.countDown()
                    liveData.removeObserver(this)
                }
            }
        }
        
        liveData.observeForever(observer)
        
        // 等待條件滿足或超時
        if (!latch.await(timeoutInMillis, TimeUnit.MILLISECONDS)) {
            liveData.removeObserver(observer)
            throw TimeoutException("Condition was never met within timeout.")
        }
        
        @Suppress("UNCHECKED_CAST")
        return data[0] as T
    }
} 