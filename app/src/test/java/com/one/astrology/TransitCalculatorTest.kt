package com.one.astrology

import com.one.astrology.data.entity.BirthData
import com.one.astrology.data.model.TransitEventType
import com.one.astrology.data.model.TransitImportance
import com.one.astrology.util.TransitCalculator
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.mockito.MockitoAnnotations
import java.util.*

/**
 * 行運計算器測試
 */
class TransitCalculatorTest {

    private lateinit var transitCalculator: TransitCalculator
    private lateinit var testBirthData: BirthData

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        transitCalculator = TransitCalculator()
        
        // 創建測試用的出生資料
        testBirthData = BirthData().apply {
            name = "測試用戶"
            birthday = Calendar.getInstance().apply {
                set(1990, Calendar.JANUARY, 1, 12, 0, 0)
            }.timeInMillis
            birthplaceLatitude = 25.0330 // 台北
            birthplaceLongitude = 121.5654
            birthplaceName = "台北市"
            isSelected = true
        }
    }

    @Test
    fun testCalculateTransitEvents_ReturnsNonEmptyList() {
        // 測試計算行運事件是否返回非空列表
        val currentTime = System.currentTimeMillis()
        val events = transitCalculator.calculateTransitEvents(
            context = null, // 在單元測試中可能為 null
            birthData = testBirthData,
            targetDate = currentTime,
            daysRange = 7
        )

        // 驗證結果
        assertNotNull("事件列表不應為 null", events)
        // 注意：由於需要 Android Context，實際測試可能返回空列表
        // 這裡主要測試方法不會拋出異常
    }

    @Test
    fun testTransitEventProperties() {
        // 測試 TransitEvent 的基本屬性
        val event = com.one.astrology.data.model.TransitEvent().apply {
            eventType = TransitEventType.ASPECT_FORMING
            eventTime = System.currentTimeMillis()
            transitPlanetId = 0 // 太陽
            transitPlanetName = "太陽"
            natalPlanetId = 1 // 月亮
            natalPlanetName = "月亮"
            aspectType = 90 // 四分相
            aspectName = "四分相"
            orb = 2.5
            importance = TransitImportance.HIGH
            description = "太陽與月亮形成四分相"
            birthDataName = testBirthData.name
        }

        // 驗證事件屬性
        assertEquals("事件類型應為相位形成", TransitEventType.ASPECT_FORMING, event.eventType)
        assertEquals("行運行星應為太陽", "太陽", event.transitPlanetName)
        assertEquals("本命行星應為月亮", "月亮", event.natalPlanetName)
        assertEquals("相位類型應為四分相", 90, event.aspectType)
        assertEquals("容許度應為2.5", 2.5, event.orb, 0.01)
        assertEquals("重要性應為高", TransitImportance.HIGH, event.importance)
        assertTrue("應為相位事件", event.isAspectEvent())
        assertFalse("不應為宮位變化事件", event.isHouseChangeEvent())
    }

    @Test
    fun testEventTitleGeneration() {
        // 測試事件標題生成
        val aspectEvent = com.one.astrology.data.model.TransitEvent().apply {
            eventType = TransitEventType.ASPECT_FORMING
            transitPlanetName = "火星"
            natalPlanetName = "金星"
            aspectName = "三分相"
        }

        val houseChangeEvent = com.one.astrology.data.model.TransitEvent().apply {
            eventType = TransitEventType.HOUSE_CHANGE
            transitPlanetName = "木星"
            fromHouse = 1
            toHouse = 2
        }

        val signChangeEvent = com.one.astrology.data.model.TransitEvent().apply {
            eventType = TransitEventType.SIGN_CHANGE
            transitPlanetName = "土星"
            fromSignName = "摩羯座"
            toSignName = "水瓶座"
        }

        // 驗證標題生成
        assertEquals("相位事件標題", "火星 三分相 金星", aspectEvent.getEventTitle())
        assertEquals("宮位變化標題", "木星 從第1宮進入第2宮", houseChangeEvent.getEventTitle())
        assertEquals("星座切換標題", "土星 從摩羯座進入水瓶座", signChangeEvent.getEventTitle())
    }

    @Test
    fun testImportanceColorMapping() {
        // 測試重要性等級的顏色映射
        val lowEvent = com.one.astrology.data.model.TransitEvent().apply { importance = TransitImportance.LOW }
        val mediumEvent = com.one.astrology.data.model.TransitEvent().apply { importance = TransitImportance.MEDIUM }
        val highEvent = com.one.astrology.data.model.TransitEvent().apply { importance = TransitImportance.HIGH }
        val criticalEvent = com.one.astrology.data.model.TransitEvent().apply { importance = TransitImportance.CRITICAL }

        // 驗證顏色資源ID不同
        assertNotEquals("不同重要性應有不同顏色", lowEvent.getImportanceColorRes(), criticalEvent.getImportanceColorRes())
        assertTrue("重要性等級應有對應的顏色資源", lowEvent.getImportanceColorRes() > 0)
    }

    @Test
    fun testNotificationSettings() {
        // 測試推播設定
        val settings = com.one.astrology.data.model.NotificationSettings.getDefault()

        // 驗證預設設定
        assertTrue("預設應啟用推播", settings.isDailyTransitEnabled)
        assertEquals("預設推播時間應為10:00", 10, settings.notificationHour)
        assertEquals("預設推播分鐘應為0", 0, settings.notificationMinute)
        assertEquals("預設時間字串", "10:00", settings.getNotificationTimeString())

        // 測試時間設定
        settings.setNotificationTime(14, 30)
        assertEquals("設定後小時應為14", 14, settings.notificationHour)
        assertEquals("設定後分鐘應為30", 30, settings.notificationMinute)
        assertEquals("設定後時間字串", "14:30", settings.getNotificationTimeString())

        // 測試事件篩選
        val testEvent = com.one.astrology.data.model.TransitEvent().apply {
            eventType = TransitEventType.ASPECT_FORMING
            importance = TransitImportance.HIGH
            orb = 2.0
        }

        assertTrue("高重要性事件應被推播", settings.shouldNotifyEvent(testEvent))

        // 關閉相位事件推播
        settings.isAspectEventsEnabled = false
        assertFalse("關閉相位事件後不應推播", settings.shouldNotifyEvent(testEvent))
    }

    @Test
    fun testOrbCalculation() {
        // 測試容許度計算
        val settings = com.one.astrology.data.model.NotificationSettings.getDefault()
        
        assertTrue("3度內應在容許度範圍", settings.isWithinOrb(2.5))
        assertTrue("剛好3度應在容許度範圍", settings.isWithinOrb(3.0))
        assertFalse("超過3度不應在容許度範圍", settings.isWithinOrb(3.5))
    }

    @Test
    fun testEventTypeClassification() {
        // 測試事件類型分類
        val aspectEvent = com.one.astrology.data.model.TransitEvent().apply { eventType = TransitEventType.ASPECT_FORMING }
        val houseEvent = com.one.astrology.data.model.TransitEvent().apply { eventType = TransitEventType.HOUSE_CHANGE }
        val signEvent = com.one.astrology.data.model.TransitEvent().apply { eventType = TransitEventType.SIGN_CHANGE }
        val retrogradeEvent = com.one.astrology.data.model.TransitEvent().apply { eventType = TransitEventType.RETROGRADE_START }

        assertTrue("應識別為相位事件", aspectEvent.isAspectEvent())
        assertFalse("不應識別為宮位事件", aspectEvent.isHouseChangeEvent())

        assertTrue("應識別為宮位事件", houseEvent.isHouseChangeEvent())
        assertFalse("不應識別為相位事件", houseEvent.isAspectEvent())

        assertTrue("應識別為星座事件", signEvent.isSignChangeEvent())
        assertFalse("不應識別為逆行事件", signEvent.isRetrogradeEvent())

        assertTrue("應識別為逆行事件", retrogradeEvent.isRetrogradeEvent())
        assertFalse("不應識別為星座事件", retrogradeEvent.isSignChangeEvent())
    }
}
