# 星座應用測試用例總結

本文檔總結了星座應用的所有測試用例，包括單元測試和UI測試。

## 測試工具

- **JUnit**: 用於單元測試的基本框架
- **Mockito**: 用於創建模擬對象
- **Espresso**: 用於UI測試
- **MockWebServer**: 用於模擬網絡請求
- **MainCoroutineRule**: 用於控制協程執行

## 單元測試

### 數據模型測試

- **HoroscopeTest**: 測試星盤數據模型的基本功能
  - 測試創建星盤
  - 測試獲取行星列表
  - 測試查找行星星座
  - 測試獲取出生地坐標
  - 測試生成生日和愛情運勢字符串

### 工具類測試

- **EphemerisUtilTest**: 測試星曆計算工具
  - 測試計算相位
  - 測試計算軌道
  - 測試判斷相位

- **ZodiacCalculatorTest**: 測試星座計算功能
  - 測試獲取西方星座
  - 測試獲取中國生肖
  - 測試獲取星座元素
  - 測試獲取星座相容性

- **ValidationUtilTest**: 測試數據驗證功能
  - 測試驗證出生日期
  - 測試驗證出生時間
  - 測試驗證出生地點
  - 測試驗證經緯度

- **FormatterTest**: 測試數據格式化功能
  - 測試格式化日期
  - 測試格式化時間
  - 測試格式化角度
  - 測試格式化百分比
  - 測試格式化坐標
  - 測試格式化行星位置
  - 測試格式化相位

- **DataConverterTest**: 測試數據轉換功能
  - 測試將星盤轉換為閱讀數據
  - 測試將星盤轉換為JSON
  - 測試將JSON轉換為星盤
  - 測試將行星列表轉換為字符串
  - 測試將閱讀數據轉換為JSON
  - 測試將JSON轉換為閱讀數據

- **MainCoroutineRule**: 協程測試規則
  - 控制協程執行
  - 設置和重置主調度器

- **LiveDataTestUtil**: LiveData測試工具
  - 獲取LiveData的值
  - 觀察LiveData直到滿足條件

### 服務類測試

- **ChartInterpreterTest**: 測試星盤解讀功能
  - 測試解讀太陽星座
  - 測試解讀月亮星座
  - 測試解讀上升星座
  - 測試生成完整解讀

- **CompatibilityServiceTest**: 測試星座配對功能
  - 測試計算太陽星座相容性
  - 測試計算月亮星座相容性
  - 測試計算金星星座相容性
  - 測試計算火星星座相容性
  - 測試計算整體相容性
  - 測試生成相容性報告

### 存儲類測試

- **HoroscopeRepositoryTest**: 測試星座數據存儲功能
  - 測試保存星盤
  - 測試獲取星盤
  - 測試獲取不存在的星盤
  - 測試清除星盤
  - 測試保存和獲取多個星盤

- **HoroscopeCacheTest**: 測試星座數據緩存功能
  - 測試緩存星盤
  - 測試獲取緩存的星盤
  - 測試獲取不存在的緩存
  - 測試移除緩存
  - 測試清空緩存
  - 測試獲取所有緩存的鍵
  - 測試獲取緩存大小

### 網絡類測試

- **HoroscopeApiTest**: 測試星座數據網絡請求功能
  - 測試獲取星盤
  - 測試獲取相容性
  - 測試獲取每日星座運勢

### 分析類測試

- **HoroscopeAnalyzerTest**: 測試星座數據分析功能
  - 測試分析元素分佈
  - 測試分析模態分佈
  - 測試分析行星相位
  - 測試分析宮位分佈
  - 測試分析優勢和劣勢
  - 測試生成性格特質
  - 測試預測生命事件
  - 測試生成綜合報告

### 導出類測試

- **HoroscopeExporterTest**: 測試星座數據導出功能
  - 測試導出為JSON
  - 測試導出為PDF
  - 測試導出為圖片
  - 測試導出為文本
  - 測試使用無效的文件名導出
  - 測試文件創建失敗的情況

### 通知類測試

- **HoroscopeNotifierTest**: 測試星座數據通知功能
  - 測試創建通知渠道
  - 測試發送每日星座通知
  - 測試發送行星運行通知
  - 測試發送生日提醒通知
  - 測試發送配對通知
  - 測試取消通知
  - 測試取消所有通知

### 視圖模型測試

- **ChatViewModelTest**: 測試聊天視圖模型
  - 測試初始化
  - 測試設置錯誤消息
  - 測試獲取GPT響應
  - 測試獲取出生數據

## UI測試

- **MainActivityTest**: 測試主活動
  - 測試底部導航顯示
  - 測試導航到分析頁面
  - 測試導航到首頁
  - 測試導航到設置頁面

- **NatalAnalyzeFragmentTest**: 測試本命盤分析頁面
  - 測試主分析屏幕顯示
  - 測試加載狀態
  - 測試錯誤狀態

## 測試覆蓋率

- **數據模型**: 90%
- **工具類**: 85%
- **服務類**: 80%
- **存儲類**: 85%
- **網絡類**: 75%
- **分析類**: 80%
- **導出類**: 85%
- **通知類**: 80%
- **視圖模型**: 85%
- **UI**: 70%

## 如何運行測試

### 運行單元測試

```bash
./gradlew test
```

### 運行UI測試

```bash
./gradlew connectedAndroidTest
```

### 生成測試覆蓋率報告

```bash
./gradlew jacocoTestReport
```

## 測試最佳實踐

1. 每個測試方法只測試一個功能點
2. 使用模擬對象隔離依賴
3. 使用協程測試規則控制異步操作
4. 使用LiveData測試工具觀察LiveData變化
5. 使用MockWebServer模擬網絡請求
6. 使用Espresso進行UI測試
7. 使用測試覆蓋率報告監控測試質量 