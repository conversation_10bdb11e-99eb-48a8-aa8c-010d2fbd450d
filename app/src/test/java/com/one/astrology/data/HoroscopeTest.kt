package com.one.astrology.data

import com.google.android.gms.maps.model.LatLng
import com.one.astrology.data.entity.BirthData
import com.one.astrology.util.TestUtil
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class HoroscopeTest {
    
    private lateinit var horoscope: Horoscope
    private lateinit var birthData: BirthData
    
    @Before
    fun setup() {
        // 創建測試數據
        birthData = TestUtil.createTestBirthData()
        horoscope = TestUtil.createTestHoroscope(birthData = birthData)
    }
    
    @Test
    fun testHoroscopeCreation() {
        // 測試星盤創建是否正確
        assertEquals(birthData.name, horoscope.name)
        assertEquals(birthData.birthday, horoscope.birthdayTime)
        assertEquals(LatLng(birthData.birthplaceLatitude, birthData.birthplaceLongitude), horoscope.latLng)
    }
    
    @Test
    fun testGetPlanetBeanList() {
        // 測試獲取行星列表
        val planetList = horoscope.getPlanetBeanList()
        assertNotNull(planetList)
        assertTrue(planetList.isNotEmpty())
        
        // 檢查是否包含太陽和月亮
        val sun = planetList.find { it.chName == "太陽" }
        val moon = planetList.find { it.chName == "月亮" }
        
        assertNotNull("應該包含太陽", sun)
        assertNotNull("應該包含月亮", moon)
    }
    
    @Test
    fun testFindPlantSign() {
        // 測試查找行星星座
        val sunSign = horoscope.findPlantSign("太陽")
        assertNotNull(sunSign)
        assertEquals("摩羯座", sunSign.chName)
        
        val moonSign = horoscope.findPlantSign("月亮")
        assertNotNull(moonSign)
        assertEquals("獅子座", moonSign.chName)
        
        // 測試查找不存在的行星
        val nonExistentSign = horoscope.findPlantSign("不存在的行星")
        assertNotNull(nonExistentSign) // 應該返回空的SignBean而不是null
        assertEquals("", nonExistentSign.chName)
    }
    
    @Test
    fun testGetBirthLatLng() {
        // 測試獲取出生地經緯度字符串
        val latLngString = horoscope.getBirthLatLng()
        assertEquals("25.0 , 121.5", latLngString)
        
        // 測試時間為0的情況
        val emptyHoroscope = Horoscope()
        assertEquals("", emptyHoroscope.getBirthLatLng())
    }
    
    @Test
    fun testGetBirthdayString() {
        // 測試獲取生日字符串
        val birthdayString = horoscope.getBirthdayString()
        assertNotNull(birthdayString)
        assertFalse(birthdayString.isEmpty())
        
        // 測試時間為0的情況
        val emptyHoroscope = Horoscope()
        assertEquals("", emptyHoroscope.getBirthdayString())
    }
    
    @Test
    fun testToLoveLuckString() {
        // 測試生成愛情運勢字符串
        val loveLuckString = horoscope.toLoveLuckString()
        assertNotNull(loveLuckString)
        assertTrue(loveLuckString.contains("利用占星學判斷感情狀況"))
        assertTrue(loveLuckString.contains(horoscope.name))
        
        // 測試名稱為空的情況
        val emptyHoroscope = Horoscope()
        assertEquals("", emptyHoroscope.toLoveLuckString())
    }
} 