# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable

#註解此行，可以自動上傳 mapping 檔到 Firebase
#-printmapping mapping.txt

# If you keep the line number information, uncomment this to
# hide the original source file name.
-renamesourcefileattribute SourceFile

##---------------Begin: proguard astrology  ----------

-keep class com.one.astrology.data.** { *; }
-keep class com.one.core.data.** { *; }
-keep class com.stackzhang.astro.bean.** { *; }

##---------------Begin: proguard firebase  ----------

-keepclassmembers class com.google.firebase.database.GenericTypeIndicator { *; }

##---------------Begin: proguard liveeventbus  ----------

-dontwarn com.jeremyliao.liveeventbus.**
-keep class com.jeremyliao.liveeventbus.** { *; }
-keep class androidx.lifecycle.** { *; }
-keep class androidx.arch.core.** { *; }

##---------------Begin: proguard date_time_picker  ----------
-dontwarn com.loper7.date_time_picker.**
-keep class com.loper7.date_time_picker.**{*;}

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

##---------------End: proguard configuration for Gson  ----------

# 保持 Activity 及 Fragment 不被刪除
-keep class * extends androidx.fragment.app.Fragment { *; }
#-keep class * extends androidx.appcompat.app.AppCompatActivity { *; }


-keep class android.util.Log { *; }
#-keepattributes SourceFile,LineNumberTable

# 保護 Retrofit 介面，避免方法名稱或參數被移除
-keep interface * { *; }
-keep class retrofit2.** { *; }
-keep class com.google.gson.** { *; }

-keepattributes Signature
-keepattributes *Annotation*

-keep class java.awt.** { *; }
-dontwarn java.awt.**
-keep class com.itextpdf.** { *; }
-dontwarn com.itextpdf.**

-keep class com.one.astrology.data.AIModelResponse { *; }