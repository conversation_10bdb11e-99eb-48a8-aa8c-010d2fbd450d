# CSV 匯出功能修復文件

## 問題描述
原本的 CSV 匯出功能在處理包含逗號（,）的資料時會導致 CSV 格式異常，因為逗號是 CSV 格式的分隔符，當資料內容本身包含逗號時，會被錯誤地解析為多個欄位。

## 解決方案
建立了 `CsvUtil` 工具類來正確處理 CSV 格式的匯出和匯入，遵循 RFC 4180 CSV 標準：

### 主要功能
1. **欄位轉義處理** (`escapeField`)
   - 自動檢測需要轉義的字符（逗號、雙引號、換行符）
   - 用雙引號包圍包含特殊字符的欄位
   - 將雙引號轉義為兩個雙引號

2. **CSV 行建立** (`createCsvRow`)
   - 將多個欄位組合成正確的 CSV 格式行
   - 自動處理每個欄位的轉義

3. **CSV 解析** (`parseCsvLine`)
   - 正確解析包含引號的 CSV 行
   - 處理轉義的雙引號
   - 支援引號內的逗號

4. **安全的資料轉換**
   - `getFieldSafely`: 安全地獲取欄位值
   - `parseDoubleSafely`: 安全地轉換 Double 值
   - `parseBooleanSafely`: 安全地轉換 Boolean 值

## 修改的檔案

### 新增檔案
- `app/src/main/java/com/one/astrology/util/CsvUtil.kt` - CSV 處理工具類
- `app/src/test/java/com/one/astrology/util/CsvUtilTest.kt` - 單元測試
- `docs/CSV_Export_Fix.md` - 此文件

### 修改的檔案
1. **SettingViewModel.kt**
   - 更新 `exportToCsv()` 方法使用 `CsvUtil.createCsvRow()`
   - 更新 `importCsvFile()` 方法使用 `CsvUtil.parseCsvLine()`

2. **SettingFragment.kt**
   - 更新 `exportToCsv()` 方法使用 `CsvUtil.createCsvRow()`

3. **SynastryAnalyzeViewModel.kt**
   - 更新 `exportBirthDataToCsv()` 方法使用 `CsvUtil.createCsvRow()`
   - 更新 `exportToCsv()` 方法使用 `CsvUtil.createCsvRow()`

## 使用範例

### 匯出 CSV
```kotlin
// 建立標題行
val header = CsvUtil.createCsvHeader("姓名", "地址", "備註")

// 建立資料行
val row = CsvUtil.createCsvRow(
    "張三",
    "台北市, 信義區",  // 包含逗號的資料會被正確處理
    "備註內容"
)
```

### 匯入 CSV
```kotlin
// 解析 CSV 行
val fields = CsvUtil.parseCsvLine("張三,\"台北市, 信義區\",備註內容")
// 結果: ["張三", "台北市, 信義區", "備註內容"]

// 安全地獲取欄位值
val name = CsvUtil.getFieldSafely(fields, 0)
val address = CsvUtil.getFieldSafely(fields, 1)
val latitude = CsvUtil.parseDoubleSafely(CsvUtil.getFieldSafely(fields, 2))
```

## 測試覆蓋
建立了完整的單元測試 `CsvUtilTest.kt`，包含：
- 基本欄位轉義測試
- 包含逗號的資料處理測試
- 包含雙引號的資料處理測試
- 包含換行符的資料處理測試
- CSV 解析測試
- 安全資料轉換測試
- 完整的匯出/匯入循環測試

## 相容性
- 向後相容：現有的 CSV 檔案仍可正常匯入
- 向前相容：新匯出的 CSV 檔案遵循標準格式，可被其他應用程式正確讀取

## 注意事項
1. 所有 CSV 相關的匯出/匯入功能都已更新使用新的 `CsvUtil`
2. 錯誤處理：使用 `LogUtil.e()` 記錄錯誤訊息
3. 預設值處理：提供安全的預設值避免應用程式崩潰

## 效果
修復後，包含逗號的資料（如地址「台北市, 信義區」）將被正確地用雙引號包圍匯出為 `"台北市, 信義區"`，匯入時也能正確解析回原始內容。
