# 出生資料備注欄位功能說明

## 功能概述

在BirthDataDialogFragment中新增了備注欄位功能，讓使用者可以為出生資料添加額外的備注信息。此功能採用MVVM架構設計，並提供了可重複使用的模組化組件。

## 主要特性

### 1. 資料模型更新
- **BirthData.kt**: 新增 `notes: String` 欄位
- 支援序列化和反序列化 (`@SerializedName("notes")`)
- 完整的Parcelable實現，支援資料傳遞
- 在複製構造函數中包含備注欄位

### 2. UI界面更新
- **fragment_birth_data_dialog.xml**: 新增多行文字輸入欄位
- 支援多行輸入 (`textMultiLine`)
- 可滾動顯示 (`scrollbars="vertical"`)
- 最小2行，最大5行顯示
- 使用Material Design風格

### 3. 邏輯處理更新
- **BirthDataDialogFragment.kt**: 
  - 在 `initData()` 方法中初始化備注欄位
  - 在 `addRecord()` 方法中保存備注內容
  - 使用 `NotesUtil.formatNotes()` 格式化備注內容
  - 添加操作日誌記錄

### 4. 工具類模組
- **NotesUtil.kt**: 提供備注相關的通用功能
  - 備注驗證 (`isValidNotes`)
  - 內容格式化 (`formatNotes`)
  - 摘要生成 (`getNotesPreview`)
  - 統計信息 (`getNotesStats`)
  - 時間戳備注 (`addTimestampedNotes`)
  - 關鍵字搜尋 (`containsKeywords`)
  - 文本導出 (`exportNotesToText`)

### 5. Compose組件
- **NotesCard.kt**: 可重複使用的備注顯示組件
  - `NotesCard`: 完整的備注卡片，支援展開/收起
  - `NotesPreview`: 簡化版備注預覽
  - `EmptyNotesPlaceholder`: 空備注提示組件

## 使用方式

### 1. 在對話框中添加備注
1. 打開出生資料對話框
2. 在備注欄位中輸入相關信息
3. 支援多行文字輸入
4. 點擊保存按鈕儲存資料

### 2. 在其他地方使用備注組件
```kotlin
// 使用NotesCard組件
NotesCard(
    notes = birthData.notes,
    title = "備注",
    expandable = true,
    onNotesClick = { /* 處理點擊事件 */ }
)

// 使用NotesPreview組件
NotesPreview(
    notes = birthData.notes,
    maxLength = 50
)
```

### 3. 使用工具類功能
```kotlin
// 驗證備注
val isValid = NotesUtil.isValidNotes(notes)

// 格式化備注
val formatted = NotesUtil.formatNotes(notes)

// 獲取摘要
val preview = NotesUtil.getNotesPreview(notes, 50)

// 獲取統計信息
val stats = NotesUtil.getNotesStats(notes)
```

## 資料匯入匯出

備注欄位已完整整合到資料匯入匯出功能中：

1. **序列化**: 使用 `@SerializedName("notes")` 註解
2. **Parcelable**: 在 `writeToParcel` 和構造函數中處理
3. **資料庫**: ObjectBox自動處理新欄位
4. **文本導出**: `NotesUtil.exportNotesToText()` 方法

## 技術實現細節

### 資料庫遷移
- ObjectBox會自動處理新欄位的添加
- 現有資料的notes欄位會初始化為空字串

### 性能考量
- 備注內容格式化在保存時進行，避免重複處理
- 使用懶加載和記憶化來優化Compose組件性能
- 統計信息按需計算

### 錯誤處理
- 輸入驗證確保資料完整性
- 格式化處理移除多餘空白字符
- 日誌記錄便於問題追蹤

## 未來擴展

### 可能的增強功能
1. **富文本支援**: 支援基本的文字格式化
2. **標籤系統**: 為備注添加分類標籤
3. **搜尋功能**: 在備注中搜尋關鍵字
4. **版本歷史**: 記錄備注的修改歷史
5. **模板功能**: 提供常用備注模板
6. **語音輸入**: 支援語音轉文字功能

### 整合建議
1. **分享功能**: 在分享出生資料時包含備注
2. **報告生成**: 在占星報告中顯示相關備注
3. **同步功能**: 與雲端服務同步備注資料
4. **備份還原**: 在資料備份中包含備注信息

## 測試建議

### 單元測試
- 測試NotesUtil的各種工具方法
- 驗證資料序列化和反序列化
- 測試邊界條件和異常情況

### UI測試
- 測試備注欄位的輸入和顯示
- 驗證多行文字的正確處理
- 測試展開/收起功能

### 整合測試
- 測試完整的新增/編輯流程
- 驗證資料保存和讀取
- 測試與其他功能的整合

## 維護注意事項

1. **資料遷移**: 未來版本更新時注意備注欄位的相容性
2. **性能監控**: 監控大量備注資料對性能的影響
3. **用戶反饋**: 收集用戶對備注功能的使用反饋
4. **安全考量**: 確保備注資料的隱私和安全
