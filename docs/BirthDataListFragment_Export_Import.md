# BirthDataListFragment 匯出匯入功能實作文件

## 功能概述
為 `BirthDataListFragment` 新增了完整的星盤資料匯出和匯入功能，參考 `SettingFragment` 的實作方式，提供使用者在記錄頁面直接進行資料的匯出和匯入操作。

## 主要功能

### 1. 匯出星盤資料
- **觸發方式**: 點擊右上角匯出按鈕（僅在 DEBUG 模式顯示）
- **匯出格式**: CSV 格式，使用 `CsvUtil` 正確處理特殊字符
- **匯出內容**: 當前列表中的所有星盤資料
- **檔案命名**: `birth_data_yyyyMMdd_HHmmss.csv`
- **分享方式**: 透過系統分享功能，可分享到其他應用程式

### 2. 匯入星盤資料
- **觸發方式**: 點擊右上角匯入按鈕（僅在 DEBUG 模式顯示）
- **支援格式**: CSV 格式檔案
- **匯入來源**: 可從檔案管理器、雲端儲存等選擇檔案
- **錯誤處理**: 顯示成功和失敗的筆數統計
- **自動更新**: 匯入完成後自動重新載入資料列表

## 技術實作

### 新增的 Import
```kotlin
import android.content.Intent
import android.net.Uri
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import com.one.astrology.ui.fragment.navigation.SettingUiState
import com.one.astrology.util.CsvUtil
import com.one.core.util.LogUtil
import com.one.core.view.LoadingDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
```

### 新增的屬性
```kotlin
private val settingViewModel by viewModels<SettingViewModel>()
private lateinit var loadingDialog: LoadingDialog

private val importCsvLauncher: ActivityResultLauncher<String> = registerForActivityResult(
    ActivityResultContracts.GetContent()
) { uri: Uri? ->
    uri?.let {
        loadingDialog.show()
        settingViewModel.importCsvFile(requireContext(), it)
    }
}
```

### 主要方法

#### 1. exportToCsv()
- 檢查資料是否為空
- 使用 `CsvUtil.createCsvHeader()` 建立標題行
- 使用 `CsvUtil.createCsvRow()` 處理每筆資料
- 建立檔案並透過 FileProvider 分享
- 提供錯誤處理和使用者回饋

#### 2. observeViewModel()
- 觀察 `SettingViewModel` 的 `uiState`
- 處理匯出/匯入的成功和失敗狀態
- 顯示適當的 Toast 訊息
- 匯入成功後重新載入資料

#### 3. 選單處理更新
```kotlin
R.id.action_export -> {
    exportToCsv()
    true
}

R.id.action_import -> {
    importCsvLauncher.launch("*/*")
    true
}
```

## CSV 格式

### 匯出欄位
1. 姓名
2. 生日
3. 出生地緯度
4. 出生地經度
5. 出生地
6. 居住地緯度
7. 居住地經度
8. 標籤
9. 是否隱藏

### 特殊字符處理
- 使用 `CsvUtil` 正確處理包含逗號、雙引號、換行符的資料
- 遵循 RFC 4180 CSV 標準
- 確保匯出和匯入的資料完整性

## 使用者體驗

### 匯出流程
1. 使用者點擊匯出按鈕
2. 系統檢查是否有資料可匯出
3. 生成 CSV 檔案
4. 開啟系統分享選單
5. 使用者選擇分享目標（如儲存到檔案、傳送到其他應用程式等）

### 匯入流程
1. 使用者點擊匯入按鈕
2. 開啟檔案選擇器
3. 使用者選擇 CSV 檔案
4. 顯示載入對話框
5. 解析並匯入資料
6. 顯示匯入結果統計
7. 自動重新載入資料列表

## 錯誤處理

### 匯出錯誤
- 無資料可匯出：顯示提示訊息
- 檔案建立失敗：顯示錯誤訊息
- 其他異常：記錄錯誤並顯示通用錯誤訊息

### 匯入錯誤
- 檔案格式錯誤：顯示錯誤訊息
- 資料解析失敗：統計失敗筆數並顯示
- 網路或權限問題：顯示相應錯誤訊息

## 安全性考量

### 權限處理
- 使用 `ActivityResultContracts.GetContent()` 安全地存取檔案
- 透過 FileProvider 安全地分享檔案
- 不直接存取外部儲存空間

### 資料驗證
- 匯入時驗證 CSV 格式
- 檢查欄位數量和資料類型
- 提供安全的預設值處理

## 相容性

### 向後相容
- 可匯入舊版本匯出的 CSV 檔案
- 支援不同格式的日期時間資料

### 跨平台相容
- 匯出的 CSV 檔案可在其他平台（如 Excel、Google Sheets）開啟
- 遵循標準 CSV 格式規範

## 測試建議

### 功能測試
1. 測試空資料列表的匯出行為
2. 測試包含特殊字符的資料匯出匯入
3. 測試大量資料的匯出匯入效能
4. 測試錯誤檔案格式的匯入處理

### 使用者介面測試
1. 確認按鈕在 DEBUG 模式正確顯示/隱藏
2. 測試載入對話框的顯示和隱藏
3. 驗證 Toast 訊息的正確性
4. 測試分享選單的功能

## 未來改進建議

1. **批次操作**: 支援選擇性匯出特定資料
2. **格式支援**: 支援其他格式如 JSON、XML
3. **雲端同步**: 整合雲端儲存服務
4. **資料驗證**: 更嚴格的匯入資料驗證
5. **進度顯示**: 大檔案匯入時顯示進度條
