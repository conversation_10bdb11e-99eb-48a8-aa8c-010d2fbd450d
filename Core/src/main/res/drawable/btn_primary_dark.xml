<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 按壓時 -->
    <item android:state_pressed="true">
        <!--rectangle 長方形 可以畫出直角形，圓角形，弧形等-->
        <shape android:shape="rectangle">
            <!--設定圓角，當設置的圓角半徑很大，就可變成弧形邊了-->
            <corners android:radius="5dp" />
            <!--邊框-->
            <stroke android:width="1dp" android:color="@color/colorPrimaryDark" />
            <!--漸層顏色-->
            <gradient android:angle="0" android:startColor="@color/colorPrimaryDark" android:endColor="@color/colorPrimaryDark"  />
        </shape>
    </item>

    <!-- 預設時 -->
    <item android:color="@color/colorPrimaryDark">
        <shape android:shape="rectangle"  >
            <corners android:radius="5dp" />
            <stroke android:width="1dp" android:color="@color/colorPrimaryDark" />
            <gradient android:angle="0" android:startColor="@color/colorPrimaryDark" android:endColor="@color/colorPrimaryDark"  />
        </shape>
    </item>

</selector>