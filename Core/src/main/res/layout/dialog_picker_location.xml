<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="20dp"
    android:padding="20dp"
    app:cardCornerRadius="5dp"
    app:cardElevation="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingHorizontal="30dp"
        android:paddingVertical="20dp">

        <NumberPicker
            android:id="@+id/numberPicker"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:textColor="@color/primary_text"
            android:textSize="20sp"
            android:theme="@style/DefaultNumberPickerTheme" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_pink" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDone"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="15dp"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="確定"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvMap"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="15dp"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="自訂地點"
                android:visibility="gone"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />
        </androidx.appcompat.widget.LinearLayoutCompat>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
