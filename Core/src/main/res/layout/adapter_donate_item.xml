<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_margin="10dp"
    android:background="@drawable/btn_primary_dark"
    android:gravity="center"
    android:minWidth="300dp"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="18sp"
        tools:text="贊助 - 鼓勵軟體開發與更新" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="18sp"
        tools:text="$30" />
</LinearLayout>