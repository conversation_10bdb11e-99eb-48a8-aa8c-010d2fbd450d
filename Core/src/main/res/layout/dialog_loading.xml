<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rltProgressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        tools:ignore="UselessParent">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="140dp"
            android:layout_height="100dp"
            android:layout_centerInParent="true"
            app:cardBackgroundColor="#e6ffffff"
            app:cardCornerRadius="5dp"
            app:cardElevation="3dp">

            <ProgressBar
                style="@android:style/Widget.Material.ProgressBar.Large"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:layout_margin="20dp"
                android:indeterminateTint="@color/colorPrimaryDark"
                android:indeterminateTintMode="src_atop"
                android:visibility="visible" />
        </com.google.android.material.card.MaterialCardView>

    </RelativeLayout>


</RelativeLayout>