package com.one.core.util

import android.app.Activity
import android.app.SearchManager
import android.content.ActivityNotFoundException
import android.content.Intent

class IntentUtil {
    companion object {
        fun searchWeb(activity: Activity, query: String) {
            val intent = Intent(Intent.ACTION_WEB_SEARCH).apply {
                putExtra(SearchManager.QUERY, query)
            }
            try {
                activity.startActivity(intent)
            } catch (ex: ActivityNotFoundException) {
                ex.message?.let { LogUtil.e(it) }
            }
        }
    }
}