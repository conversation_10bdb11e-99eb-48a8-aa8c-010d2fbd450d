package com.one.core.util;

import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

public class FragmentUtils {


    public static void startTabFragment(FragmentManager manager, Class<? extends Fragment> clazz, int widgetId, Bundle bundle, boolean addFragmentToStack, int popUpInclusive) {
        if (clazz != null && manager != null) {
            int backStackCount = manager.getBackStackEntryCount();
            for (int i = 0; i < backStackCount; i++) {
                int backStackId = manager.getBackStackEntryAt(i).getId();

                manager.popBackStack(backStackId, FragmentManager.POP_BACK_STACK_INCLUSIVE);
            }
            startFragment(manager, clazz, widgetId, bundle, addFragmentToStack, popUpInclusive);
        }
    }

    public static void startTabFragment(FragmentManager manager, Fragment fragment, int widgetId, Bundle bundle, boolean addFragmentToStack, int popUpInclusive) {
        if (fragment != null && manager != null) {
            int backStackCount = manager.getBackStackEntryCount();
            for (int i = 0; i < backStackCount; i++) {
                int backStackId = manager.getBackStackEntryAt(i).getId();

                manager.popBackStack(backStackId, FragmentManager.POP_BACK_STACK_INCLUSIVE);
            }
            startFragment(manager, fragment, widgetId, bundle, addFragmentToStack, popUpInclusive);
        }
    }

    public static void startFragment(FragmentManager manager, Class<? extends Fragment> clazz, int widgetId, Bundle bundle, boolean addFragmentToStack, int popUpInclusive) {
        if (clazz != null && manager != null) {
            Fragment targetFragment = manager.findFragmentByTag(clazz.getName());
            if (popUpInclusive == FragmentManager.POP_BACK_STACK_INCLUSIVE && targetFragment != null) {
                manager.popBackStack(targetFragment.getTag(), FragmentManager.POP_BACK_STACK_INCLUSIVE);
            }

            try {
                targetFragment = clazz.newInstance();
                if (bundle != null) {
                    targetFragment.setArguments(bundle);
                }
                FragmentTransaction transaction = manager.beginTransaction();
                transaction.replace(widgetId, targetFragment, clazz.getName());
                if (addFragmentToStack) {
                    transaction.addToBackStack(clazz.getName());
                }
                transaction.commit();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 新增一個頁面
     */
    public static void startFragment(FragmentManager manager, Class<? extends Fragment> clazz, int widgetId, Bundle bundle, boolean addFragmentToStack) {
        if (clazz != null && manager != null) {
            Fragment targetFragment = manager.findFragmentByTag(clazz.getName());
            if (targetFragment == null) {
                try {
                    targetFragment = clazz.newInstance();
                    if (bundle != null) {
                        targetFragment.setArguments(bundle);
                    }
                    FragmentTransaction transaction = manager.beginTransaction();
                    transaction.replace(widgetId, targetFragment, clazz.getName());
                    if (addFragmentToStack) {
                        transaction.addToBackStack(clazz.getName());
                    }
                    transaction.commit();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                manager.popBackStack(targetFragment.getTag(), 0);
            }
        }
    }

    /**
     * 新增一個頁面
     */
    public static void startFragment(FragmentManager manager, Class<? extends Fragment> clazz, int widgetId, boolean addFragmentToStack) {
        startFragment(manager, clazz, widgetId, null, addFragmentToStack);
    }

    public static void startFragment(FragmentManager manager, Fragment targetFragment, int widgetId, Bundle bundle, boolean addFragmentToStack, int popUpInclusive) {
        if (targetFragment != null && manager != null) {
            if (popUpInclusive == FragmentManager.POP_BACK_STACK_INCLUSIVE) {
                manager.popBackStack(targetFragment.getTag(), FragmentManager.POP_BACK_STACK_INCLUSIVE);
            }

            try {
                if (bundle != null) {
                    targetFragment.setArguments(bundle);
                }
                FragmentTransaction transaction = manager.beginTransaction();
                transaction.replace(widgetId, targetFragment, targetFragment.getClass().getName());
                if (addFragmentToStack) {
                    transaction.addToBackStack(targetFragment.getClass().getName());
                }
                transaction.commit();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void startFragment(FragmentManager manager, Fragment targetFragment, int widgetId, Bundle bundle, boolean addFragmentToStack) {
        if (manager != null) {
            try {
                if (bundle != null) {
                    targetFragment.setArguments(bundle);
                }
                FragmentTransaction transaction = manager.beginTransaction();
                transaction.replace(widgetId, targetFragment, targetFragment.getClass().getName());
                if (addFragmentToStack) {
                    transaction.addToBackStack(targetFragment.getClass().getName());
                }
                transaction.commit();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void startFragment(FragmentManager manager, Fragment targetFragment, int widgetId, boolean addFragmentToStack) {
        if (manager != null) {
            try {
                FragmentTransaction transaction = manager.beginTransaction();
                transaction.replace(widgetId, targetFragment, targetFragment.getClass().getName());
                if (addFragmentToStack) {
                    transaction.addToBackStack(targetFragment.getClass().getName());
                }
                transaction.commit();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void startFragmentAllowStateLoss(FragmentManager manager, Fragment targetFragment, int widgetId, boolean addFragmentToStack) {
        if (manager != null) {
            try {
                FragmentTransaction transaction = manager.beginTransaction();
                transaction.replace(widgetId, targetFragment, targetFragment.getClass().getName());
                if (addFragmentToStack) {
                    transaction.addToBackStack(targetFragment.getClass().getName());
                }
                transaction.commitAllowingStateLoss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void finish(FragmentManager manager) {
        finish(manager, false);
    }

    public static void finish(FragmentManager manager, boolean isImmediate) {
        if (manager == null) {
            return;
        }
        if (isImmediate) {
            manager.popBackStackImmediate();
        } else {
            manager.popBackStack();
        }
    }

}
