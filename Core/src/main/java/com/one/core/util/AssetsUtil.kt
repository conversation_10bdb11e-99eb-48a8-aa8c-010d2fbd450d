package com.one.core.util

import android.content.Context
import com.one.core.util.LogUtil.d
import java.io.IOException
import java.nio.charset.StandardCharsets

object AssetsUtil {
    @JvmStatic
    fun loadJSONFromAsset(context: Context?, fileName: String): String {
        val json: String = try {
            if (context == null) {
                return ""
            }
            val `is` = context.assets.open(fileName)
            val size = `is`.available()
            val buffer = ByteArray(size)
            val nRead = `is`.read(buffer)
            d("" + nRead)
            `is`.close()
            String(buffer, StandardCharsets.UTF_8)
        } catch (ex: IOException) {
            ex.printStackTrace()
            return ""
        }
        return json
    }
}