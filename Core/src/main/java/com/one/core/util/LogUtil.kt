package com.one.core.util

import android.os.Bundle
import android.util.Log
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase

object LogUtil {
    private const val mTag = "[OLog]"
    private var IS_DEBUG = false
    private var IS_MINIFY_ENABLED = false

    fun setDebug(isDebug: Boolean) {
        IS_DEBUG = isDebug
    }

    fun setIsMinifyEnabled(isMinifyEnabled: <PERSON>olean) {
        IS_MINIFY_ENABLED = isMinifyEnabled
    }

    private val className: String
        get() {
            val stackTraceElement = Thread.currentThread().stackTrace[4]
            val callerClazzName = stackTraceElement.className
            val className = callerClazzName.substring(callerClazzName.lastIndexOf(".") + 1)
            return if (IS_MINIFY_ENABLED) {
                "$mTag[$className]"
            } else "$mTag[$className]"
        }

    private val classNameNoTag: String
        get() {
            val stackTraceElement = Thread.currentThread().stackTrace[4]
            val callerClazzName = stackTraceElement.className
            return callerClazzName.substring(callerClazzName.lastIndexOf(".") + 1)
        }

    private val methodName: String
        get() {
            val stackTraceElement = Thread.currentThread().stackTrace[4]
            val methodName = stackTraceElement.methodName
            val lineNumber = stackTraceElement.lineNumber
            return "[$methodName][$lineNumber]"
        }

    fun logEvent() {
        val firebaseAnalytics: FirebaseAnalytics = Firebase.analytics
        val bundle = Bundle()
        bundle.putString("class_name", classNameNoTag.lowercase())
        bundle.putString("method_name", methodName.lowercase())
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }

    fun logEvent(text: String) {
        val firebaseAnalytics: FirebaseAnalytics = Firebase.analytics
        val params = Bundle()
        params.putString("class_name", classNameNoTag.lowercase())
        params.putString("method_name", methodName.lowercase())
        params.putString("text", text)
        firebaseAnalytics.logEvent("log_event", params)
    }

    fun setCurrentScreen(screenName: String, screenClass: String)  {
        val firebaseAnalytics: FirebaseAnalytics = Firebase.analytics
        val bundle = Bundle()
        bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName)
        bundle.putString(FirebaseAnalytics.Param.SCREEN_CLASS, screenClass)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }

    fun i() {
        if (IS_DEBUG) {
            Log.i(className, methodName)
        }
    }

    fun d() {
        if (IS_DEBUG) {
            Log.d(className, methodName)
        }
    }

    fun d(message: String) {
        if (IS_DEBUG) {
            if (message.length < 4000) {
                Log.d(className, methodName + message)
                return
            }
            log(className, methodName + message, object : LogCallBack {
                override fun log(className: String?, message: String?) {
                    Log.d(className, message!!)
                }
            })
        }
    }

    /**
     * 加入tag 避免混淆後看不到  class name 與 method name
     */
    fun e(tag: String, message: String) {
        var message = message
        if (IS_MINIFY_ENABLED) {
            message = "[$tag] $message"
        }
        log(className, methodName + message, object : LogCallBack {
            override fun log(className: String?, message: String?) {
                Log.e(className, message!!)
            }
        })
    }

    /**
     * 加入tag 避免混淆後看不到  class name 與 method name
     */
    fun d(tag: String, message: String) {
        var message = message
        if (IS_MINIFY_ENABLED) {
            message = "[$tag] $message"
        }
        if (IS_DEBUG) {
            log(className, methodName + message, object : LogCallBack {
                override fun log(className: String?, message: String?) {
                    Log.d(className, message!!)
                }
            })
        }
    }

    fun i(message: String) {
        if (message.length < 4000) {
            Log.i(className, methodName + message)
            return
        }
        log(className, methodName + message, object : LogCallBack {
            override fun log(className: String?, message: String?) {
                Log.i(className, message!!)
            }
        })
    }

    /**
     * 加入tag 避免混淆後看不到  class name 與 method name
     */
    fun i(tag: String, message: String) {
        var message = message
        if (IS_MINIFY_ENABLED) {
            message = "[$tag] $message"
        }
        log(className, methodName + message, object : LogCallBack {
            override fun log(className: String?, message: String?) {
                Log.i(className, message!!)
            }
        })
    }

    @JvmStatic
    fun e(message: String) {
        if (message.length < 4000) {
            Log.e(className, methodName + message)
            return
        }
        log(className, methodName + message, object : LogCallBack {
            override fun log(className: String?, message: String?) {
                Log.e(className, message!!)
            }
        })
    }

    private const val MAX_LOG_LENGTH = 3000
    private fun log(className: String, message: String?, callback: LogCallBack) {
        if (message == null) {
            callback.log(className, "")
            return
        }
        if (message.length < 4000) {
            callback.log(className, message)
            return
        }
        for (i in 0..message.length / MAX_LOG_LENGTH) {
            val start = i * MAX_LOG_LENGTH
            var end = (i + 1) * MAX_LOG_LENGTH
            end = if (end > message.length) message.length else end
            callback.log(className, message.substring(start, end))
        }
    }

    private interface LogCallBack {
        fun log(className: String?, message: String?)
    }
}