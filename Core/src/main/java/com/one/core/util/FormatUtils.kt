package com.one.core.util

import android.text.format.DateFormat
import java.text.DecimalFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

fun Long.formatDate(pattern: String): String {
    val calendar = Calendar.getInstance(Locale.getDefault())
    val timeZone = TimeZone.getTimeZone("GMT+8") // ZoneOffset.systemDefault()
    calendar.timeZone = timeZone
    calendar.timeInMillis = this
    return DateFormat.format(pattern, calendar).toString()
}

object FormatUtils {
    fun dateToString(date: Date?, format: String?): String {
        val sdf = SimpleDateFormat(format, Locale.getDefault())
        return sdf.format(date)
    }

    fun stringToTimestamp(dateString: String): Long {
        val dateFormat = SimpleDateFormat("yyyy/MM/dd HH:mm", Locale.getDefault())
        val date: Date = dateFormat.parse(dateString)!!
        return date.time // 回傳毫秒級 timestamp
    }

    fun stringToDate(timeString: String, format: String): Date {
        var date = Date()
        try {
            //"yyyy/MM/dd HH:mm" , Locale.getDefault()
            val dateFormat = SimpleDateFormat(format, Locale.getDefault())
            date = dateFormat.parse(timeString)
            return date
        } catch (ex: ParseException) {
            ex.message?.let { LogUtil.e(it) }
        }
        return date
    }

    fun getInt(s: String): Int {
        return s.replace("[\\D]".toRegex(), "").toInt()
    }

    /**
     * 小數點格式
     */
    fun getDecimalFormat(number: Double): String {
        //運用DecimalFormat制定好金額顯示格式，每三位數顯示逗號
        val mDecimalFormat = DecimalFormat("#,###")
        //在format裡轉型成double
        return mDecimalFormat.format(number)
    }

    /**
     * 轉換時間格式
     */
    fun longToString(time: Long, format: String?): String {
        val calendar = Calendar.getInstance()
//        val timeZone = TimeZone.getTimeZone("GMT+8")
//        calendar.timeZone = timeZone
        calendar.timeInMillis = time
        return DateFormat.format(format, calendar).toString()
    }

    /**
     * Return date in specified format.
     *
     * @param milliSeconds Date in milliseconds
     * @param dateFormat   Date format
     * @return String representing date in specified format
     */
    fun getMilliSecondsToDate(milliSeconds: Long, dateFormat: String?): String {
        // Create a DateFormatter object for displaying date in specified format.
        val formatter = SimpleDateFormat(dateFormat, Locale.getDefault())

        // Create a calendar object that will convert the date and time value in milliseconds to date.
        val calendar = Calendar.getInstance()
        val timeZone = TimeZone.getTimeZone("GMT+8")
        calendar.timeZone = timeZone
        calendar.timeInMillis = milliSeconds
        return formatter.format(calendar.time)
    }
}