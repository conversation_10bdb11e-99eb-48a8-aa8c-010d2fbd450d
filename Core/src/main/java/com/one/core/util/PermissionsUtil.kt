package com.one.core.util

import android.Manifest
import android.Manifest.permission.MANAGE_EXTERNAL_STORAGE
import android.Manifest.permission.READ_EXTERNAL_STORAGE
import android.Manifest.permission.WRITE_EXTERNAL_STORAGE
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION
import androidx.core.app.ActivityCompat.requestPermissions
import androidx.core.content.ContextCompat
import com.one.core.data.Permissions


class PermissionsUtil {
    companion object {
        var permissions: Permissions = Permissions()

        const val LOCATION_PERMISSION_REQUEST_CODE = 1001

        fun checkLocationPermission(activity: Activity, permissionCode: Int): Boolean {
            val fineLocationGranted = ContextCompat.checkSelfPermission(
                activity, Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED

            val coarseLocationGranted = ContextCompat.checkSelfPermission(
                activity, Manifest.permission.ACCESS_COARSE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED

            // 如果兩個都沒給，就請求 fine location 權限
            if (!fineLocationGranted && !coarseLocationGranted) {
                requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                    permissionCode
                )
                return false
            }

            // 只要其中一個有給，就視為 OK
            return true
        }

        fun checkExternalStoragePermissions(context: Context): Boolean {
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.R) {
                val writeStoragePermission = ContextCompat.checkSelfPermission(
                    context,
                    WRITE_EXTERNAL_STORAGE
                )

                val readStoragePermission = ContextCompat.checkSelfPermission(
                    context,
                    READ_EXTERNAL_STORAGE
                )
                return writeStoragePermission == PackageManager.PERMISSION_GRANTED
                        && readStoragePermission == PackageManager.PERMISSION_GRANTED
            }
            return true
        }

        fun requestExternalStoragePermission(activity: Activity, permissionCode: Int) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                if (!Environment.isExternalStorageManager()) {
                    val intent = Intent(ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                    intent.data = Uri.fromParts("package", activity.packageName, null)
                    activity.startActivity(intent)
                }
                requestPermissions(
                    activity,
                    arrayOf(READ_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE, MANAGE_EXTERNAL_STORAGE),
                    permissionCode
                )
            } else {
                requestPermissions(
                    activity,
                    arrayOf(READ_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE),
                    permissionCode
                )
            }
        }
    }
}