package com.one.core.data

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName


@Keep
data class Permissions(
    @SerializedName("login")
    var login: List<String>? = null,
    @SerializedName("no_ad")
    var noAd: List<String>? = null,
    @SerializedName("save_image")
    var saveImage: List<String>? = null,
    @SerializedName("save_pdf")
    var savePdf: List<String>? = null,

    var canLogin: Boolean = false,
    var canNoAd: Boolean = false,
    var canSaveImage: Boolean = false,
    var canSavePdf: Boolean = false,
) {
    fun canLogin(isDev:Boolean): Boolean {
        return isDev || canLogin
    }
    fun canNoAd(isDev:Boolean): Boolean {
        return isDev || canNoAd
    }

    fun canSaveImage(isDev:Boolean): Boolean {
        return isDev || canSaveImage
    }

    fun canSavePdf(isDev:Boolean): Boolean {
        return isDev || canSavePdf
    }
}

