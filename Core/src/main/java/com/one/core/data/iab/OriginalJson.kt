package com.one.core.data.iab

import androidx.annotation.Keep

import com.google.gson.annotations.SerializedName


@Keep
data class OriginalJson(
    @SerializedName("productId")
    var productId: String, // subs.twd_30
    @SerializedName("purchaseToken")
    var purchaseToken: String, // cmppjamcpbhbjilobeojjpoc.AO-J1OxkmWbL3DDgpcQx9l3whzZab48jQ6_zsHVc7y-ldTq7B2vve3-HUSRLHgLBqBv4kky5GOJ0pZZcdDBwqDAqeQJ5_MJCbw
    @SerializedName("purchaseTime")
    var purchaseTime: Long, // *************
    @SerializedName("quantity")
    var quantity: Int, // 1
    @SerializedName("developerPayload")
    var developerPayload: String? = null, // subs.twd_30


    @SerializedName("acknowledged")
    var acknowledged: Boolean? = null, // true
    @SerializedName("autoRenewing")
    var autoRenewing: Boolean? = null, // true
    @SerializedName("obfuscatedAccountId")
    var obfuscatedAccountId: String? = null, // subs.twd_30
    @SerializedName("obfuscatedProfileId")
    var obfuscatedProfileId: String? = null, // subs
    @SerializedName("orderId")
    var orderId: String? = null, // GPA.3317-6146-4938-84715
    @SerializedName("packageName")
    var packageName: String? = null, // com.one.astrology
    @SerializedName("purchaseState")
    var purchaseState: Int? = null, // 0

)