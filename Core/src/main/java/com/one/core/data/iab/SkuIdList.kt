package com.one.core.data.iab

import com.google.gson.annotations.SerializedName

class SkuIdList {
    @SerializedName("inappItemList")
    val inappItemList: List<String> = ArrayList()

    @SerializedName("subsItemList")
    val subsItemList: List<String> = ArrayList()

    @SerializedName("iabDataList")
    val iabDataList: ArrayList<IabData> = ArrayList()
}

data class IabData(
    @SerializedName("productId")
    var productId: String = "",

    @SerializedName("productType")
    var productType: String = "",

    @SerializedName("name")
    var name: String = "",

    @SerializedName("price")
    var price: String = "",

    @SerializedName("isVisible")
    var isVisible: Boolean? = true

)