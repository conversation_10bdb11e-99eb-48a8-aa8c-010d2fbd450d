package com.one.core.dialog

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.ProductDetails
import com.google.gson.Gson
import com.one.core.R
import com.one.core.ad.AdCallback
import com.one.core.ad.GoogleAdHelp
import com.one.core.billing.BillingClientLifecycle
import com.one.core.data.iab.IabData
import com.one.core.data.iab.SkuIdList
import com.one.core.databinding.DialogFragmentDonateBinding
import com.one.core.dialog.adapter.DonateItemAdapter
import com.one.core.firebase.RemoteConfigUtil
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import com.one.core.viewmodel.BillingModelFactory
import com.one.core.viewmodel.BillingViewModel

class DonateDialogFragment : DialogFragment() {

    private lateinit var binding: DialogFragmentDonateBinding
    private lateinit var donateItemAdapter: DonateItemAdapter
    private lateinit var billingClientLifecycle: BillingClientLifecycle
    private lateinit var billingViewModel: BillingViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        binding = DialogFragmentDonateBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!PermissionsUtil.permissions.canNoAd) {
            GoogleAdHelp.addView(
                requireContext(),
                "ca-app-pub-1800606262336792/**********",
                true,
                binding.lltAd
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        billingClientLifecycle = BillingClientLifecycle.getInstance(requireContext())
        lifecycle.addObserver(billingClientLifecycle)

        billingViewModel = ViewModelProvider(
            this,
            BillingModelFactory(requireActivity().application, billingClientLifecycle)
        )[BillingViewModel::class.java]

        fetchConfig()
    }

    private fun addRewardedAd(adId: String) {
        GoogleAdHelp.addRewardedAd(
            requireActivity(),
            adId,
            object : AdCallback {
                override fun onAdLoaded() {

                }

                override fun onCompleted(type: String, amount: Int) {

                }

                override fun onClosed() {

                }
            }
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initRecyclerView(skuIdList: SkuIdList) {
        binding.recyclerView.setHasFixedSize(true)
        binding.recyclerView.layoutManager = GridLayoutManager(requireContext(), 1)

        donateItemAdapter = DonateItemAdapter {
            val productDetails = it.tag as IabData
            when (productDetails.productType) {
                BillingClient.ProductType.INAPP -> {
                    billingViewModel.buy(requireContext(), productDetails.productId)
                }

                BillingClient.ProductType.SUBS -> {
                    billingViewModel.buy(requireContext(), "", productDetails.productId, false)
                }

                "ad" -> {
                    addRewardedAd(productDetails.productId)
                }

                else -> {
                    addRewardedAd(productDetails.productId)
                }
            }
        }

        binding.recyclerView.adapter = donateItemAdapter
        val iabDataList = skuIdList.iabDataList.filter { it.isVisible == true }
        donateItemAdapter.setList(ArrayList(iabDataList))

        val inAppList = iabDataList.filter { it.productType == BillingClient.ProductType.INAPP }
            .map { it.productId }
        if (inAppList.isNotEmpty()) {
            billingClientLifecycle.queryProductDetails(
                inAppList,
                BillingClient.ProductType.INAPP
            )
        }

        val subsList = iabDataList.filter { it.productType == BillingClient.ProductType.SUBS }
            .map { it.productId }
        if (subsList.isNotEmpty()) {
            billingClientLifecycle.queryProductDetails(
                subsList,
                BillingClient.ProductType.SUBS
            )
        }

        BillingClientLifecycle.productsIdLiveData.observe(requireActivity()) { stringList ->
            stringList.forEach { item ->
                PermissionsUtil.permissions.canLogin =
                    PermissionsUtil.permissions.login?.contains(item) == true
                PermissionsUtil.permissions.canNoAd =
                    PermissionsUtil.permissions.noAd?.contains(item) == true
                PermissionsUtil.permissions.canSaveImage =
                    PermissionsUtil.permissions.saveImage?.contains(item) == true
                PermissionsUtil.permissions.canSavePdf =
                    PermissionsUtil.permissions.savePdf?.contains(item) == true
            }
            if (PermissionsUtil.permissions.canNoAd) {
                val itemList = iabDataList.filter { it.productType != "ad" }
                donateItemAdapter.setList(ArrayList(itemList))
                binding.lltAd.visibility = View.GONE
            }
        }

        billingClientLifecycle.purchasesFinished.observe(this) {
            Toast.makeText(
                requireContext(),
                getString(R.string.thanks_for_sponsoring), Toast.LENGTH_SHORT
            ).show()
            billingClientLifecycle.querySubStatus()
        }

        // Launch the billing flow when the user clicks a button to buy something.
        billingViewModel.buyEvent.observe(this) {
            if (it != null) {
                billingClientLifecycle.launchBillingFlow(requireActivity(), it)
            }
        }

        val productList: ArrayList<ProductDetails> = ArrayList()
        val productsInAppWithProductDetails = billingClientLifecycle.productsInAppWithProductDetails
        productsInAppWithProductDetails.observe(this) {
            productList.clear()
            productList.addAll(it.values.toList())
            var hashSet = LinkedHashSet(productList)
            var listWithoutDuplicates = ArrayList(hashSet)
            for (item in iabDataList) {
                val details = listWithoutDuplicates.find { it1 -> it1.productId == item.productId }
                if (details != null) {
                    item.price = details.oneTimePurchaseOfferDetails?.formattedPrice ?: ""
                }
            }

            val productsSubsWithProductDetails =
                billingClientLifecycle.productsSubsWithProductDetails
            productsSubsWithProductDetails.observe(this) { itSubs ->
                productList.addAll(itSubs.values.toList())
                hashSet = LinkedHashSet(productList)
                listWithoutDuplicates = ArrayList(hashSet)
                for (item in iabDataList) {
                    val details =
                        listWithoutDuplicates.find { it1 -> it1.productId == item.productId }
                    if (details != null) {
                        val subscriptionOfferDetails = details.subscriptionOfferDetails
                        subscriptionOfferDetails?.forEach { offerDetail ->
                            if (offerDetail.offerId.isNullOrEmpty()) { // 沒有訂閱產品關聯的優惠 ID
                                val pricingPhase = offerDetail.pricingPhases.pricingPhaseList[0]
                                var price = pricingPhase?.priceAmountMicros
                                if (price != null) {
                                    price /= 1000000
                                }
                                if (pricingPhase != null) {
                                    item.price = pricingPhase.formattedPrice
                                }
                            }
                        }
                    }
                }
                donateItemAdapter.setList(ArrayList(iabDataList))
            }
        }
    }

    private fun fetchConfig() {
        RemoteConfigUtil.fetchConfig(requireActivity()) { task, firebaseRemoteConfig ->
            if (task.isSuccessful) {
                try {
                    val billingItemList = firebaseRemoteConfig.getString("billingItemList")
                    LogUtil.d("billingItemList : $billingItemList")
                    val gson = Gson()
                    val skuIdList = gson.fromJson(billingItemList, SkuIdList::class.java)
                    if (skuIdList == null) {
                        Toast.makeText(requireContext(), "無可購買商品", Toast.LENGTH_SHORT)
                            .show()
                        return@fetchConfig
                    }
                    initRecyclerView(skuIdList)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } else {
                // 無法取得 Remote Config
                LogUtil.e("無法取得 Remote Config")
                Toast.makeText(
                    requireContext(),
                    "無可購買商品",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }
}