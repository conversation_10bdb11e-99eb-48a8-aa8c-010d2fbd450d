package com.one.core.dialog.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.android.billingclient.api.BillingClient
import com.one.core.R
import com.one.core.data.iab.IabData

class DonateItemAdapter(private val onClickListener: View.OnClickListener) :
    RecyclerView.Adapter<DonateItemAdapter.ViewHolder>() {
    private var mList: ArrayList<IabData> = ArrayList()

    @SuppressLint("NotifyDataSetChanged")
    fun setList(list: ArrayList<IabData>) {
        mList = list
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, i: Int): ViewHolder {
        val v = LayoutInflater.from(viewGroup.context)
            .inflate(R.layout.adapter_donate_item, viewGroup, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, i: Int) {
        val productDetails = mList[i]
        viewHolder.tvTitle.text = mList[i].name
        if (mList[i].productType == BillingClient.ProductType.INAPP) {
            viewHolder.tvPrice.text = productDetails.price//oneTimePurchaseOfferDetails?.formattedPrice ?: ""
        } else {
            viewHolder.tvPrice.text = productDetails.price//subscriptionOfferDetails?.get(0)?.pricingPhases?.pricingPhaseList?.get(0)?.formattedPrice ?: ""
        }
        viewHolder.itemView.tag = productDetails
        viewHolder.itemView.setOnClickListener(onClickListener)
    }

    override fun getItemCount(): Int {
        return mList.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var tvTitle: TextView
        var tvPrice: TextView

        init {
            tvTitle = itemView.findViewById(R.id.tvTitle)
            tvPrice = itemView.findViewById(R.id.tvPrice)
        }
    }
}