package com.one.core.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.pm.PackageInfoCompat
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.DialogFragment
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.one.core.R
import com.one.core.databinding.DialogFragmentAboutBinding
import com.one.core.util.LogUtil
import java.io.ByteArrayInputStream
import java.security.cert.CertificateException
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import javax.security.auth.x500.X500Principal

class AboutDialogFragment : DialogFragment() {

    private lateinit var binding: DialogFragmentAboutBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        binding = DialogFragmentAboutBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        try {
            val packageInfo =
                requireActivity().packageManager.getPackageInfo(requireActivity().packageName, 0)
            val verCode = PackageInfoCompat.getLongVersionCode(packageInfo).toInt()
            binding.tvVersionName.text =
                getString(R.string.version, packageInfo.versionName.toString(), verCode)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        setListener()
    }

    private fun setListener() {
        binding.rltVersion.setOnLongClickListener {
            try {
                isDebuggable(requireContext())
                val info = requireActivity().packageManager!!.getPackageInfo(
                    requireActivity().packageName!!,
                    0
                )
                Toast.makeText(
                    requireContext(),
                    getString(R.string.version_no) + info.versionCode + getString(R.string.version_name) + info.versionName
                            + getString(R.string.is_debug) + isDebuggable(requireContext()),
                    Toast.LENGTH_LONG
                ).show()
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
            false
        }
        binding.rltVersion.setOnClickListener {
            checkInAppUpdate()
        }
        binding.rltComment.setOnClickListener { // Open app with Google Play app
            try {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse("market://details?id=${requireActivity().packageName}")
                    )
                )
            } catch (e: Exception) {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse("https://play.google.com/store/apps/details?id=${requireActivity().packageName}")
                    )
                )
            }
        }
        binding.rltIssueResponses.setOnClickListener {
//            val uri = Uri.parse("https://www.facebook.com/Ace-Producer-2150781041902568")
//            val i = Intent(Intent.ACTION_VIEW, uri)
//            startActivity(i)
        }
    }

    // 檢查版本
    private fun checkInAppUpdate() {
        val appUpdateManager = AppUpdateManagerFactory.create(requireContext())
        val appUpdateInfoTask = appUpdateManager.appUpdateInfo

        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            LogUtil.d("updateAvailability : " + appUpdateInfo.updateAvailability().toString())
            when (appUpdateInfo.updateAvailability()) {
                UpdateAvailability.UPDATE_AVAILABLE -> {
                    // 有可用更新
                    // 更新優先權（0-5）
                    val updatePriority = appUpdateInfo.updatePriority()
                    // 商店提供更新後過了幾天
                    val stalenessDays = appUpdateInfo.clientVersionStalenessDays() ?: -1
                    // 可用版本號
                    val availableVersionCode = appUpdateInfo.availableVersionCode()

                    LogUtil.d("更新優先權 $updatePriority")
                    LogUtil.d("商店提供更新後過了幾天 $stalenessDays")
                    LogUtil.d("可用版本號 $availableVersionCode")
                    if (appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
                        showUpdateDialog(appUpdateInfo)
                    }
                }

                UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS -> {
                    LogUtil.d("正在更新")
//                    showDialog("正在更新...", true)
                }

                UpdateAvailability.UPDATE_NOT_AVAILABLE -> {
                    LogUtil.d("無可用更新")
                    if (!isAdded) {
                        return@addOnSuccessListener
                    }
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.no_updates_available),
                        Toast.LENGTH_SHORT
                    ).show()
                }

                UpdateAvailability.UNKNOWN -> {
                    LogUtil.d("未知錯誤")
                }

                else -> {
                    LogUtil.d("else")
                }
            }
        }

        appUpdateInfoTask.addOnFailureListener {
            if (!isAdded) {
                return@addOnFailureListener
            }
            it.message?.let { it1 -> LogUtil.e(it1) }
            Toast.makeText(
                requireContext(),
                it.message,
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun showUpdateDialog(appUpdateInfo: AppUpdateInfo) {
        val dialog = MaterialDialog(requireContext(), MaterialDialog.DEFAULT_BEHAVIOR)
        dialog.title(null, getString(R.string.check_for_version_updates))
        dialog.message(null, getString(R.string.whether_update_new_version_already_exists), null)
        dialog.positiveButton(null, getString(R.string.yes)) {
            val appUpdateManager = AppUpdateManagerFactory.create(requireContext())
            appUpdateManager.startUpdateFlowForResult(
                appUpdateInfo,
                AppUpdateType.IMMEDIATE,
                requireActivity(),
                1000
            )
        }
        dialog.negativeButton(null, getString(R.string.no)) {
            LogUtil.d()
            dismiss()
        }
        dialog.show()
    }

    private fun isDebuggable(ctx: Context): Boolean {
        var debuggable = false
        try {
            @SuppressLint("PackageManagerGetSignatures") val packageInfo =
                ctx.packageManager.getPackageInfo(ctx.packageName, PackageManager.GET_SIGNATURES)
            val signatures = packageInfo.signatures
            val cf = CertificateFactory.getInstance("X.509")
            if (signatures != null) {
                for (signature in signatures) {
                    val stream = ByteArrayInputStream(signature.toByteArray())
                    val cert = cf.generateCertificate(stream) as X509Certificate
                    debuggable = cert.subjectX500Principal == DEBUG_DN
                    if (debuggable) break
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            // debuggable variable will remain false
            e.printStackTrace()
        } catch (e: CertificateException) {
            e.printStackTrace()
        }
        return debuggable
    }

    companion object {
        private val DEBUG_DN = X500Principal("CN=Android Debug,O=Android,C=US")
    }
}