package com.one.core.dialog

import android.app.Activity
import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.google.gson.Gson
import com.one.billing.BillingManager
import com.one.core.R
import com.one.core.ad.AdCallback
import com.one.core.ad.GoogleAdHelp
import com.one.core.billing.BillingClientLifecycle
import com.one.core.data.iab.IabData
import com.one.core.data.iab.SkuIdList
import com.one.core.firebase.RemoteConfigUtil
import com.one.core.util.LogUtil
import com.one.core.util.PermissionsUtil
import com.one.core.viewmodel.BillingModelFactory
import com.one.core.viewmodel.BillingViewModel
import kotlinx.coroutines.launch

@Composable
fun DonateDialog(
    activity: Activity,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    var skuIdList by remember { mutableStateOf<SkuIdList?>(null) }
    var productList by remember { mutableStateOf<List<ProductDetails>>(emptyList()) }

    val billingClientLifecycle = remember { BillingClientLifecycle.getInstance(context) }
    val billingViewModel = remember {
        ViewModelProvider(
            context as androidx.fragment.app.FragmentActivity,
            BillingModelFactory(context.application, billingClientLifecycle)
        )[BillingViewModel::class.java]
    }
    
    // 初始化 BillingManager
    val billingManager = remember { BillingManager(context, activity) }

    LaunchedEffect(Unit) {
        fetchConfig(activity) { result ->
            skuIdList = result
        }
    }

    DonationDialog(onDismiss, skuIdList, billingViewModel, billingManager, context)

    // 觀察購買完成事件
    LaunchedEffect(billingClientLifecycle) {
        billingClientLifecycle.purchasesFinished.observe(context as androidx.fragment.app.FragmentActivity) {
            Toast.makeText(
                context,
                context.getString(R.string.thanks_for_sponsoring),
                Toast.LENGTH_SHORT
            ).show()
            billingClientLifecycle.querySubStatus()
        }
    }

    // 監聽購買結果
    LaunchedEffect(billingManager) {
        billingManager.getPurchases().collect { purchases ->
            purchases.forEach { purchase ->
                if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                    // 處理購買成功
                    Toast.makeText(
                        context,
                        context.getString(R.string.thanks_for_sponsoring),
                        Toast.LENGTH_SHORT
                    ).show()
                    
                    // 確認購買
                    (context as androidx.fragment.app.FragmentActivity).lifecycleScope.launch {
                        billingManager.acknowledgePurchase(purchase.purchaseToken)
                    }
                    
                    // 更新權限
                    val productId = purchase.products.firstOrNull()
                    if (productId != null) {
                        PermissionsUtil.permissions.canLogin =
                            PermissionsUtil.permissions.login?.contains(productId) == true
                        PermissionsUtil.permissions.canNoAd =
                            PermissionsUtil.permissions.noAd?.contains(productId) == true
                        PermissionsUtil.permissions.canSaveImage =
                            PermissionsUtil.permissions.saveImage?.contains(productId) == true
                        PermissionsUtil.permissions.canSavePdf =
                            PermissionsUtil.permissions.savePdf?.contains(productId) == true
                    }
                }
            }
        }
    }
}

@Composable
fun DonationDialog(
    onDismiss: () -> Unit,
    skuIdList: SkuIdList?, // 假設這是你的資料類型
    billingViewModel: BillingViewModel,
    billingManager: BillingManager,
    context: Context
) {
    // 獲取 BillingClientLifecycle 實例
    val billingClientLifecycle = remember { BillingClientLifecycle.getInstance(context) }
    
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            modifier = Modifier.wrapContentSize(),
            color = MaterialTheme.colorScheme.surface,
            shape = MaterialTheme.shapes.large
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.sponsor),
                    fontSize = 20.sp,
                    color = colorResource(id = R.color.colorPrimary),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(5.dp))

                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = "實際支付金額受匯率波動與銀行手續費影響",
                    fontSize = 12.sp,
                    color = colorResource(id = R.color.colorPrimary),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(5.dp))

                LazyColumn {
                    val items =
                        skuIdList?.iabDataList?.filter { it.isVisible == true } ?: emptyList()

                    items(items) { item ->
                        DonateItem(
                            item = item,
                            onItemClick = {
                                when (item.productType) {
                                    BillingClient.ProductType.INAPP -> {
                                        // 使用 BillingManager 處理應用內購買
                                        val productId = item.productId
                                        // 查詢產品詳情
                                        val productDetails = billingClientLifecycle.productsInAppWithProductDetails.value?.get(productId)
                                        if (productDetails != null) {
                                            // 將 ProductDetails 轉換為 BillingProduct
                                            val billingProduct = com.one.billing.model.BillingProduct(
                                                productId = productDetails.productId,
                                                name = productDetails.name,
                                                description = productDetails.description,
                                                price = productDetails.oneTimePurchaseOfferDetails?.formattedPrice ?: "",
                                                priceAmountMicros = productDetails.oneTimePurchaseOfferDetails?.priceAmountMicros ?: 0L,
                                                priceCurrencyCode = productDetails.oneTimePurchaseOfferDetails?.priceCurrencyCode ?: "",
                                                type = productDetails.productType
                                            )
                                            
                                            // 在協程中調用 suspend 函數
                                            (context as androidx.fragment.app.FragmentActivity).lifecycleScope.launch {
                                                billingManager.launchBillingFlow(billingProduct)
                                            }
                                        } else {
                                            Toast.makeText(context, "無法獲取產品信息", Toast.LENGTH_SHORT).show()
                                        }
                                    }

                                    BillingClient.ProductType.SUBS -> {
                                        // 使用 BillingManager 處理訂閱
                                        val productId = item.productId
                                        // 查詢產品詳情
                                        val productDetails = billingClientLifecycle.productsSubsWithProductDetails.value?.get(productId)
                                        if (productDetails != null) {
                                            // 將 ProductDetails 轉換為 BillingProduct
                                            val billingProduct = com.one.billing.model.BillingProduct(
                                                productId = productDetails.productId,
                                                name = productDetails.name,
                                                description = productDetails.description,
                                                price = productDetails.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()?.formattedPrice ?: "",
                                                priceAmountMicros = productDetails.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()?.priceAmountMicros ?: 0L,
                                                priceCurrencyCode = productDetails.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()?.priceCurrencyCode ?: "",
                                                type = productDetails.productType
                                            )
                                            
                                            // 在協程中調用 suspend 函數
                                            (context as androidx.fragment.app.FragmentActivity).lifecycleScope.launch {
                                                billingManager.launchBillingFlow(billingProduct)
                                            }
                                        } else {
                                            Toast.makeText(context, "無法獲取訂閱信息", Toast.LENGTH_SHORT).show()
                                        }
                                    }

                                    "ad" -> {
                                        addRewardedAd(context, item.productId)
                                    }

                                    else -> {
                                        addRewardedAd(context, item.productId)
                                    }
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DonateItem(
    item: IabData,
    onItemClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        onClick = onItemClick
    ) {
        Column(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = item.name,
                style = MaterialTheme.typography.titleMedium,
                color = colorResource(id = R.color.colorPrimary)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = item.price,
                style = MaterialTheme.typography.bodyLarge,
                color = colorResource(id = R.color.colorPrimary),
            )
        }
    }
}

private fun fetchConfig(
    context: Activity,
    onResult: (SkuIdList?) -> Unit
) {
    RemoteConfigUtil.fetchConfig(context) { task, firebaseRemoteConfig ->
        if (task.isSuccessful) {
            try {
                val billingItemList = firebaseRemoteConfig.getString("billingItemList")
                LogUtil.d("billingItemList : $billingItemList")
                val gson = Gson()
                val skuIdList = gson.fromJson(billingItemList, SkuIdList::class.java)
                if (skuIdList == null) {
                    Toast.makeText(context, "無可購買商品", Toast.LENGTH_SHORT).show()
                    onResult(null)
                    return@fetchConfig
                }
                onResult(skuIdList)
            } catch (e: Exception) {
                e.printStackTrace()
                onResult(null)
            }
        } else {
            LogUtil.e("無法取得 Remote Config")
            Toast.makeText(context, "無可購買商品", Toast.LENGTH_SHORT).show()
            onResult(null)
        }
    }
}

private fun addRewardedAd(
    context: Context,
    adId: String
) {
    GoogleAdHelp.addRewardedAd(
        context as androidx.fragment.app.FragmentActivity,
        adId,
        object : AdCallback {
            override fun onAdLoaded() {}
            override fun onCompleted(type: String, amount: Int) {}
            override fun onClosed() {}
        }
    )
} 