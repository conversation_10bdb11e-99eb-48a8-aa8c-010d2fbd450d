package com.one.core.ad


import android.app.Activity
import android.content.Context
import android.widget.LinearLayout
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback
import com.google.android.gms.ads.rewarded.ServerSideVerificationOptions
import com.one.core.data.RemoteConfig
import com.one.core.util.LogUtil


class GoogleAdHelp {
    companion object {

        fun addView(context: Context, adId: String, isOpenAd: Boolean, layout: LinearLayout) {
            if ((!RemoteConfig.isOpenAd && RemoteConfig.adType != 1) || !isOpenAd) {
                return
            }
            val adRequest = AdRequest.Builder().build()
            val adView = AdView(context)
            adView.setAdSize(AdSize.BANNER)
            adView.adUnitId = adId //"ca-app-pub-1800606262336792/2454137591"
            adView.loadAd(adRequest)
            adView.adListener = object : AdListener() {
                override fun onAdClicked() {
                    LogUtil.d("onAdClicked")
                }

                override fun onAdClosed() {
                    LogUtil.d("onAdClicked")
                }

                override fun onAdFailedToLoad(adError: LoadAdError) {
                    // Code to be executed when an ad request fails.
                    LogUtil.e("onAdFailedToLoad : " + adError.message)
                }

                override fun onAdImpression() {
                    LogUtil.d("onAdImpression")
                }

                override fun onAdLoaded() {
                    LogUtil.d("onAdLoaded")
                }

                override fun onAdOpened() {
                    LogUtil.d("onAdOpened")
                }
            }
            layout.addView(adView)
        }

        fun addRewardedAd(context: Activity, adId: String, adCallback: AdCallback) {
//            if (!RemoteConfig.isOpenAd && RemoteConfig.adType != 1) {
//                adCallback.onCompleted("", 0)
//                return
//            }
            var mRewardedAd: RewardedAd?
            val adRequest = AdRequest.Builder().build()//"ca-app-pub-3940256099942544/5224354917"
            RewardedAd.load(context, adId,
                adRequest, object : RewardedAdLoadCallback() {
                    override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                        // Handle the error.
                        LogUtil.d(loadAdError.toString())
                        mRewardedAd = null
                        adCallback.onClosed()
                    }

                    override fun onAdLoaded(rewardedAd: RewardedAd) {
                        mRewardedAd = rewardedAd
                        LogUtil.d("Ad was loaded.")
                        val options = ServerSideVerificationOptions.Builder()
                            .setCustomData("SAMPLE_CUSTOM_DATA_STRING")
                            .build()
                        rewardedAd.setServerSideVerificationOptions(options)

                        fullScreenContentCallback(mRewardedAd!!, adCallback)
                        if (mRewardedAd != null) {
                            mRewardedAd?.show(context) {
                                val rewardAmount = it.amount
                                val rewardType = it.type
                                LogUtil.d("User earned the reward.")
                                adCallback.onCompleted(rewardType, rewardAmount)
                            }
                        } else {
                            LogUtil.d("The rewarded ad wasn't ready yet.")
                        }
                    }
                })

        }

        private fun fullScreenContentCallback(rewardedAd: RewardedAd, adCallback: AdCallback) {
            rewardedAd.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdClicked() {
                    // Called when a click is recorded for an ad.
                    LogUtil.d("Ad was clicked.")
                    adCallback.onClosed()
                }

                override fun onAdDismissedFullScreenContent() {
                    // Called when ad is dismissed.
                    // Set the ad reference to null so you don't show the ad a second time.
                    LogUtil.d("Ad dismissed fullscreen content.")
//                    rewardedAd = null
                    adCallback.onClosed()
                }

//                override fun onAdFailedToShowFullScreenContent(adError: AdError?) {
//                    // Called when ad fails to show.
//                    LogUtil.e("Ad failed to show fullscreen content.")
//                    rewardedAd = null
//                }

                override fun onAdImpression() {
                    // Called when an impression is recorded for an ad.
                    LogUtil.d("Ad recorded an impression.")
                }

                override fun onAdShowedFullScreenContent() {
                    // Called when ad is shown.
                    LogUtil.d("Ad showed fullscreen content.")
                }
            }
        }

    }
}