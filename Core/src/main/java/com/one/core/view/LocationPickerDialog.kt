package com.one.core.view

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import com.one.core.databinding.DialogPickerLocationBinding

class LocationPickerDialog(
    context: Context?,
    private val mArray: Array<String>,
    private val mOnClickListener: View.OnClickListener?,
    private val mOnMapClickListener: View.OnClickListener?
) : Dialog(
    context!!
) {

    private lateinit var binding: DialogPickerLocationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (window != null) {
            window!!.setBackgroundDrawableResource(android.R.color.transparent)
        }

        binding = DialogPickerLocationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.numberPicker.minValue = 0

        if (mArray.isEmpty()) {
            return
        }

        binding.numberPicker.maxValue = mArray.size - 1
        binding.numberPicker.displayedValues = mArray
        binding.numberPicker.value = 0 // 設定預設位置
        binding.numberPicker.wrapSelectorWheel = false // 是否循環顯示
        binding.numberPicker.setFormatter { value -> mArray[value] }
        binding.numberPicker.setOnValueChangedListener { _, _, _ ->
            binding.tvDone.tag = binding.numberPicker.value
        }
        binding.tvDone.tag = binding.numberPicker.value
        binding.tvDone.setOnClickListener {
            mOnClickListener?.onClick(it)
            dismiss()
        }

        binding.tvMap.setOnClickListener {
            mOnMapClickListener?.onClick(it)
            dismiss()
        }
    }
}