/*
 * Copyright 2018 Google LLC. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.one.core.billing

import android.app.Activity
import android.content.Context
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.android.billingclient.api.*
import com.google.gson.Gson
import com.one.core.data.iab.OriginalJson
import com.one.core.util.FormatUtils
import com.one.core.util.LogUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlin.math.pow


class BillingClientLifecycle private constructor(
    private val applicationContext: Context,
    private val externalScope: CoroutineScope =
        CoroutineScope(SupervisorJob() + Dispatchers.Default)
) : DefaultLifecycleObserver, PurchasesUpdatedListener, BillingClientStateListener,
    PurchasesResponseListener {

    companion object {
        val productsIdLiveData = MutableLiveData<List<String>>()
        private const val TAG = "BillingLifecycle"
        private const val MAX_RETRY_ATTEMPT = 3

        @Volatile
        private var INSTANCE: BillingClientLifecycle? = null

        fun getInstance(applicationContext: Context): BillingClientLifecycle =
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: BillingClientLifecycle(applicationContext).also { INSTANCE = it }
            }
    }

    private val _purchases = MutableStateFlow<List<Purchase>>(emptyList())

    /**
     * Purchases are collectable. This list will be updated when the Billing Library
     * detects new or existing purchases.
     */
    val purchases = _purchases.asStateFlow()

    /**
     * ProductDetails for all known products.
     */
    val productsSubsWithProductDetails = MutableLiveData<Map<String, ProductDetails>>()
    val productsInAppWithProductDetails = MutableLiveData<Map<String, ProductDetails>>()
    val purchasesFinished = MutableLiveData<BillingResult>()

    /**
     * Instantiate a new BillingClient instance.
     */
    private lateinit var billingClient: BillingClient

    override fun onCreate(owner: LifecycleOwner) {
        LogUtil.d(TAG, "ON_CREATE")
        // Create a new BillingClient in onCreate().
        // Since the BillingClient can only be used once, we need to create a new instance
        // after ending the previous connection to the Google Play Store in onDestroy().
        billingClient = BillingClient.newBuilder(applicationContext)
            .setListener(this)
            .enablePendingPurchases() // Not used for subscriptions.
            .build()
        if (!billingClient.isReady) {
            LogUtil.d(TAG, "BillingClient: Start connection...")
            billingClient.startConnection(this)
        }
    }

    fun querySubStatus() {
        val gson = Gson().newBuilder().setPrettyPrinting().create()
        val params1 =
            QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.SUBS)
        billingClient.queryPurchasesAsync(params1.build()) { billingResult, purchases ->
            LogUtil.d("responseCode ${billingResult.responseCode}")
            if (BillingClient.BillingResponseCode.OK == billingResult.responseCode) {
                if (purchases.isNotEmpty()) {
                    // 有有效訂閱商品
                    val list = ArrayList<String>()
                    purchases.forEach {
                        val originalJson: OriginalJson =
                            gson.fromJson(it.originalJson, OriginalJson::class.java)
                        LogUtil.d("queryPurchasesAsync originalJson\n${gson.toJson(originalJson)}")
                        val purchaseTime =
                            FormatUtils.longToString(originalJson.purchaseTime, "yyyy/MM/dd HH:mm")
                        LogUtil.d("purchaseTime $purchaseTime")
                        if (Purchase.PurchaseState.PURCHASED == it.purchaseState) {
                            // 此訂閱商品是已支付完成的
                            if (it.isAcknowledged) {
                                // 此訂閱商品已核銷過
                                // 這裡可以判定是在有效期的訂閱
                                LogUtil.d("此訂閱商品已核銷過 判定是在有效期的訂閱")
                                list.add(originalJson.productId)
                            } else {
                                // 此訂閱商品未核銷，需要進行核銷
                                LogUtil.d("此訂閱商品未核銷，需要進行核銷")
                            }
                        }
                    }
                    productsIdLiveData.postValue(list)
                } else {
                    // 沒有有效訂閱商品
                    LogUtil.d("沒有有效訂閱商品")
                }
            }
        }


        val params =
            QueryPurchaseHistoryParams.newBuilder().setProductType(BillingClient.ProductType.SUBS)
        billingClient.queryPurchaseHistoryAsync(params.build()) { p0, p1 ->
            LogUtil.d("responseCode ${p0.responseCode}")
            if (!p1.isNullOrEmpty()) {
                for (it in p1) {
                    val originalJson: OriginalJson =
                        gson.fromJson(it.originalJson, OriginalJson::class.java)
                    LogUtil.d("queryPurchaseHistoryAsync originalJson\n${gson.toJson(originalJson)}")
                    val purchaseTime =
                        FormatUtils.longToString(originalJson.purchaseTime, "yyyy/MM/dd HH:mm")
                    LogUtil.d("purchaseTime $purchaseTime")
                }
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        LogUtil.d(TAG, "ON_DESTROY")
        if (billingClient.isReady) {
            LogUtil.d(TAG, "BillingClient can only be used once -- closing connection")
            // BillingClient can only be used once.
            // After calling endConnection(), we must create a new BillingClient.
            billingClient.endConnection()
        }
    }

    override fun onBillingSetupFinished(billingResult: BillingResult) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        LogUtil.d("onBillingSetupFinished: $responseCode $debugMessage")
        if (responseCode == BillingClient.BillingResponseCode.OK) {
            // The billing client is ready.
            // You can query product details and purchases here.
//            queryProductDetails()
            queryPurchases(BillingClient.ProductType.INAPP)
            queryPurchases(BillingClient.ProductType.SUBS)
            querySubStatus()
        }
    }

    override fun onBillingServiceDisconnected() {
        LogUtil.e("onBillingServiceDisconnected")
    }

    /**
     * In order to make purchases, you need the [ProductDetails] for the item or subscription.
     * This is an asynchronous call that will receive a result in [onProductDetailsResponse].
     *
     * queryProductDetails uses method calls from GPBL 5.0.0. PBL5, released in May 2022,
     * is backwards compatible with previous versions.
     * To learn more about this you can read https://developer.android.com/google/play/billing/compatibility
     */
    fun queryProductDetails(list: List<String>, productType: String) {
        LogUtil.d(TAG, "queryProductDetails")
        val params = QueryProductDetailsParams.newBuilder()

        val productList: MutableList<QueryProductDetailsParams.Product> = arrayListOf()
        for (product in list) {
            productList.add(
                QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(product)
                    .setProductType(productType)
                    .build()
            )
        }

        params.setProductList(productList).let { productDetailsParams ->
            LogUtil.i(TAG, "queryProductDetailsAsync")
            billingClient.queryProductDetailsAsync(
                productDetailsParams.build()
            ) { billingResult: BillingResult,
                productDetailsList: MutableList<ProductDetails> ->
                val response = BillingResponse(billingResult.responseCode)
                val debugMessage = billingResult.debugMessage
                when {
                    response.isOk -> {
                        if (productDetailsList.isEmpty()) {
                            LogUtil.d("productDetailsList.isEmpty()")
                        } else {
                            if (BillingClient.ProductType.INAPP == productType) {
                                productsInAppWithProductDetails.postValue(HashMap<String, ProductDetails>().apply {
                                    for (productDetails in productDetailsList) {
                                        put(productDetails.productId, productDetails)
                                    }
                                }.also {
                                    LogUtil.d(
                                        TAG,
                                        "productsWithProductDetails: $productsSubsWithProductDetails"
                                    )
                                }
                                )
                            } else {
                                productsSubsWithProductDetails.postValue(HashMap<String, ProductDetails>().apply {
                                    for (productDetails in productDetailsList) {
                                        put(productDetails.productId, productDetails)
                                    }
                                }.also {
                                    LogUtil.d(
                                        TAG,
                                        "productsWithProductDetails: $productsSubsWithProductDetails"
                                    )
                                }
                                )
                            }
                        }
                    }

                    response.isTerribleFailure -> {
                        // These response codes are not expected.
                        LogUtil.e(TAG, "onProductDetailsResponse: ${response.code} $debugMessage")
                    }

                    else -> {
                        LogUtil.e(TAG, "onProductDetailsResponse: ${response.code} $debugMessage")
                    }
                }
            }
        }
    }

    /**
     * Query Google Play Billing for existing purchases.
     *
     * New purchases will be provided to the PurchasesUpdatedListener.
     * You still need to check the Google Play Billing API to know when purchase tokens are removed.
     */
    private fun queryPurchases(productType: String) {
        if (!billingClient.isReady) {
            LogUtil.e(TAG, "queryPurchases: BillingClient is not ready")
            billingClient.startConnection(this)
        }
        billingClient.queryPurchasesAsync(
            QueryPurchasesParams.newBuilder()
                .setProductType(productType)
                .build(), this
        )
    }

    override fun onQueryPurchasesResponse(
        billingResult: BillingResult,
        purchases: MutableList<Purchase>
    ) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        LogUtil.d(TAG, "onPurchasesUpdated: $responseCode $debugMessage")
        when (responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                processPurchases(purchases)
            }

            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                LogUtil.i(TAG, "onPurchasesUpdated: The user already owns this item")
                processPurchases(purchases)
            }
        }
    }

    /**
     * Called by the Billing Library when new purchases are detected.
     */
    override fun onPurchasesUpdated(
        billingResult: BillingResult,
        purchases: MutableList<Purchase>?
    ) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        LogUtil.d(TAG, "onPurchasesUpdated: $responseCode $debugMessage")
        when (responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                if (purchases == null) {
                    LogUtil.d(TAG, "onPurchasesUpdated: null purchase list")
                    processPurchases(null)
                } else {
                    processPurchases(purchases)
                }
            }

            BillingClient.BillingResponseCode.USER_CANCELED -> {
                LogUtil.i(TAG, "onPurchasesUpdated: User canceled the purchase")
            }

            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                LogUtil.i(TAG, "onPurchasesUpdated: The user already owns this item")
                processPurchases(purchases)
            }

            BillingClient.BillingResponseCode.DEVELOPER_ERROR -> {
                LogUtil.e(
                    TAG, "onPurchasesUpdated: Developer error means that Google Play " +
                            "does not recognize the configuration. If you are just getting started, " +
                            "make sure you have configured the application correctly in the " +
                            "Google Play Console. The product ID must match and the APK you " +
                            "are using must be signed with release keys."
                )
            }
        }
    }

    /**
     * Send purchase to StateFlow, which will trigger network call to verify the subscriptions
     * on the sever.
     */
    private fun processPurchases(purchasesList: List<Purchase>?) {
        LogUtil.d(TAG, "processPurchases: ${purchasesList?.size} purchase(s)")
        if (purchasesList == null || isUnchangedPurchaseList(purchasesList)) {
            LogUtil.d(TAG, "processPurchases: Purchase list has not changed")
            return
        }
        externalScope.launch {
            _purchases.emit(purchasesList)
        }
        logAcknowledgementStatus(purchasesList)

        for (purchase in purchasesList) {
            externalScope.launch {
                if (purchase.accountIdentifiers?.obfuscatedProfileId == BillingClient.ProductType.SUBS) {
                    acknowledgePurchase(purchase)
                } else {
                    consumeAsync(purchase)
                }
            }
        }
    }

    /**
     * Check whether the purchases have changed before posting changes.
     */
    @Suppress("UNUSED_PARAMETER")
    private fun isUnchangedPurchaseList(purchasesList: List<Purchase>): Boolean {
        // Optimize to avoid updates with identical data.
        return false
    }

    /**
     * Log the number of purchases that are acknowledge and not acknowledged.
     *
     * https://developer.android.com/google/play/billing/billing_library_releases_notes#2_0_acknowledge
     *
     * When the purchase is first received, it will not be acknowledge.
     * This application sends the purchase token to the server for registration. After the
     * purchase token is registered to an account, the Android app acknowledges the purchase token.
     * The next time the purchase list is updated, it will contain acknowledged purchases.
     */
    private fun logAcknowledgementStatus(purchasesList: List<Purchase>) {
        var acknowledgedCounter = 0
        var unacknowledgedCounter = 0
        for (purchase in purchasesList) {
            if (purchase.isAcknowledged) {
                acknowledgedCounter++
            } else {
                unacknowledgedCounter++
            }
        }
        LogUtil.d(
            TAG,
            "logAcknowledgementStatus: acknowledged=$acknowledgedCounter unacknowledged=$unacknowledgedCounter"
        )
    }

    /**
     * Launching the billing flow.
     *
     * Launching the UI to make a purchase requires a reference to the Activity.
     */
    fun launchBillingFlow(activity: Activity, params: BillingFlowParams): Int {
        if (!billingClient.isReady) {
            LogUtil.e(TAG, "launchBillingFlow: BillingClient is not ready")
        }
        val billingResult = billingClient.launchBillingFlow(activity, params)
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        LogUtil.d(TAG, "launchBillingFlow: BillingResponse $responseCode $debugMessage")
        return responseCode
    }

    /**
     * Acknowledge a purchase.
     *
     * https://developer.android.com/google/play/billing/billing_library_releases_notes#2_0_acknowledge
     *
     * Apps should acknowledge the purchase after confirming that the purchase token
     * has been associated with a user. This app only acknowledges purchases after
     * successfully receiving the subscription data back from the server.
     *
     * Developers can choose to acknowledge purchases from a server using the
     * Google Play Developer API. The server has direct access to the user database,
     * so using the Google Play Developer API for acknowledgement might be more reliable.
     * TODO(134506821): Acknowledge purchases on the server.
     * TODO: Remove client side purchase acknowledgement after removing the associated tests.
     * If the purchase token is not acknowledged within 3 days,
     * then Google Play will automatically refund and revoke the purchase.
     * This behavior helps ensure that users are not charged for subscriptions unless the
     * user has successfully received access to the content.
     * This eliminates a category of issues where users complain to developers
     * that they paid for something that the app is not giving to them.
     */
    suspend fun acknowledgePurchase(purchaseToken: String): Boolean {
        val params = AcknowledgePurchaseParams.newBuilder()
            .setPurchaseToken(purchaseToken)
            .build()

        for (trial in 1..MAX_RETRY_ATTEMPT) {
            var response = BillingResponse(500)
            var bResult: BillingResult? = null
            billingClient.acknowledgePurchase(params) { billingResult ->
                response = BillingResponse(billingResult.responseCode)
                bResult = billingResult
            }

            when {
                response.isOk -> {
                    LogUtil.i(TAG, "Acknowledge success - token: $purchaseToken")
                    return true
                }

                response.canFailGracefully -> {
                    // Ignore the error
                    LogUtil.i(TAG, "Token $purchaseToken is already owned.")
                    return true
                }

                response.isRecoverableError -> {
                    // Retry to ack because these errors may be recoverable.
                    val duration = 500L * 2.0.pow(trial).toLong()
                    delay(duration)
                    if (trial < MAX_RETRY_ATTEMPT) {
                        LogUtil.d(
                            TAG,
                            "Retrying($trial) to acknowledge for token $purchaseToken - code: ${bResult!!.responseCode}, message: ${bResult!!.debugMessage}"
                        )
                    }
                }

                response.isNonrecoverableError || response.isTerribleFailure -> {
                    LogUtil.e(
                        TAG,
                        "Failed to acknowledge for token $purchaseToken - code: ${bResult!!.responseCode}, message: ${bResult!!.debugMessage}"
                    )
                    break
                }
            }
        }
        throw Exception("Failed to acknowledge the purchase!")
    }

    private suspend fun consumeAsync(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            val consumeParams =
                ConsumeParams.newBuilder()
                    .setPurchaseToken(purchase.purchaseToken)
                    .build()
            withContext(Dispatchers.IO) {
                billingClient.consumeAsync(
                    consumeParams
                ) { billingResult, _ ->
                    LogUtil.d("onConsumeResponse responseCode : " + billingResult.responseCode)
                    purchasesFinished.postValue(billingResult)
                }
            }
        }
    }

    private suspend fun acknowledgePurchase(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            if (!purchase.isAcknowledged) {
                val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                    .setPurchaseToken(purchase.purchaseToken)
                withContext(Dispatchers.IO) {
                    billingClient.acknowledgePurchase(
                        acknowledgePurchaseParams.build()
                    ) {
                        LogUtil.d("acknowledgePurchase responseCode : " + it.responseCode)
                        purchasesFinished.postValue(it)
                    }
                }
            }
        }
    }


}

@JvmInline
private value class BillingResponse(val code: Int) {
    val isOk: Boolean
        get() = code == BillingClient.BillingResponseCode.OK
    val canFailGracefully: Boolean
        get() = code == BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED
    val isRecoverableError: Boolean
        get() = code in setOf(
            BillingClient.BillingResponseCode.ERROR,
            BillingClient.BillingResponseCode.SERVICE_DISCONNECTED,
        )
    val isNonrecoverableError: Boolean
        get() = code in setOf(
            BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE,
            BillingClient.BillingResponseCode.BILLING_UNAVAILABLE,
            BillingClient.BillingResponseCode.DEVELOPER_ERROR,
        )
    val isTerribleFailure: Boolean
        get() = code in setOf(
            BillingClient.BillingResponseCode.ITEM_UNAVAILABLE,
            BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED,
            BillingClient.BillingResponseCode.ITEM_NOT_OWNED,
            BillingClient.BillingResponseCode.USER_CANCELED,
        )
}