package com.one.core.viewmodel

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.one.core.billing.BillingClientLifecycle


class BillingModelFactory(
    private var mApplication: Application,
    private var billingClientLifecycle: BillingClientLifecycle
) : ViewModelProvider.NewInstanceFactory() {


    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return BillingViewModel(mApplication, billingClientLifecycle) as T
    }

}