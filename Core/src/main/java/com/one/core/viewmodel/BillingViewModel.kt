/*
 * Copyright 2018 Google LLC. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.one.core.viewmodel

import android.app.Application
import android.content.Context
import android.widget.Toast
import androidx.lifecycle.AndroidViewModel
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingFlowParams.SubscriptionUpdateParams.ReplacementMode.WITH_TIME_PRORATION
import com.android.billingclient.api.ProductDetails
import com.one.core.R
import com.one.core.billing.BillingClientLifecycle
import com.one.core.util.LogUtil
import com.one.core.util.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class BillingViewModel(application: Application, billingClientLifecycle: BillingClientLifecycle) :
    AndroidViewModel(application) {

    /**
     * Local billing purchase data.
     */
    private val purchases = billingClientLifecycle.purchases

    /**
     * ProductDetails for all known Products.
     */
    private val productsWithProductDetails =
        billingClientLifecycle.productsSubsWithProductDetails

    private val productsInAppWithProductDetails =
        billingClientLifecycle.productsInAppWithProductDetails
    /**
     * Subscriptions record according to the server.
     */
//    private val subscriptions: StateFlow<List<SubscriptionStatus>> = (application as SubApp).repository.subscriptions

    /**
     * Send an event when the Activity needs to buy something.
     */
    val buyEvent = SingleLiveEvent<BillingFlowParams>()


    /**
     * Open the Play Store subscription center. If the user has exactly one product,
     * then open the deeplink to the specific product.
     */
//    fun openPlayStoreSubscriptions() {
//        val hasBasic = deviceHasGooglePlaySubscription(purchases.value, Constants.BASIC_PRODUCT)
//        val hasPremium = deviceHasGooglePlaySubscription(purchases.value, Constants.BASIC_PRODUCT)
//        LogUtil.d(TAG, "hasBasic: $hasBasic, hasPremium: $hasPremium")
//        when {
//            hasBasic && !hasPremium -> {
//                // If we just have a basic subscription, open the basic Product.
//                openPlayStoreSubscriptionsEvent.postValue(Constants.BASIC_PRODUCT)
//            }
//            !hasBasic && hasPremium -> {
//                // If we just have a premium subscription, open the premium Product.
//                openPlayStoreSubscriptionsEvent.postValue(Constants.PREMIUM_PRODUCT)
//            }
//            else -> {
//                // If we do not have an active subscription,
//                // or if we have multiple subscriptions, open the default subscription center.
//                openPlayStoreSubscriptionsEvent.call()
//            }
//        }
//    }

    /**
     * Open the subscription page on Google Play.
     *
     * Since the purchase tokens will not be returned during account hold or pause,
     * we use the server data to determine the deeplink to Google Play.
     */
//    fun openSubscriptionPageOnGooglePlay() {
//        subscriptions.value.let { subscriptionStatusList ->
//            when {
//                serverHasSubscription(subscriptionStatusList, Constants.PREMIUM_PRODUCT) ->
//                    openProductPlayStoreSubscriptions(Constants.PREMIUM_PRODUCT)
//                serverHasSubscription(subscriptionStatusList, Constants.BASIC_PRODUCT) ->
//                    openProductPlayStoreSubscriptions(Constants.BASIC_PRODUCT)
//            }
//        }
//    }


    /**
     * BillingFlowParams Builder for normal purchases.
     *
     * @param productDetails ProductDetails object returned by the library.
     * @param offerToken the least priced offer's offer id token returned by
     * [leastPricedOfferToken].
     *
     * @return [BillingFlowParams] builder.
     */
    private fun billingFlowParamsBuilder(
        productDetails: ProductDetails,
        offerToken: String
    ): BillingFlowParams {
        return BillingFlowParams.newBuilder().setProductDetailsParamsList(
            listOf(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                    .setProductDetails(productDetails)
                    .setOfferToken(offerToken)
                    .build()
            )
        ).setObfuscatedAccountId(productDetails.productId)
            .setObfuscatedProfileId(productDetails.productType).build()
    }

    /**
     * BillingFlowParams Builder for upgrades and downgrades.
     *
     * @param productDetails ProductDetails object returned by the library.
     * @param offerToken the least priced offer's offer id token returned by
     * [leastPricedOfferToken].
     * @param oldToken the purchase token of the subscription purchase being upgraded or downgraded.
     *
     * @return [BillingFlowParams] builder.
     */
    private fun upDowngradeBillingFlowParamsBuilder(
        productDetails: ProductDetails, offerToken: String, oldToken: String
    ): BillingFlowParams {
        return BillingFlowParams.newBuilder().setProductDetailsParamsList(
            listOf(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                    .setProductDetails(productDetails)
                    .setOfferToken(offerToken)
                    .build()
            )
        ).setSubscriptionUpdateParams(
            BillingFlowParams.SubscriptionUpdateParams.newBuilder()
                .setOldPurchaseToken(oldToken)
                .setSubscriptionReplacementMode(WITH_TIME_PRORATION)
                .build()
        ).setObfuscatedAccountId(productDetails.productId)
            .setObfuscatedProfileId(productDetails.productType).build()
    }


    /**
     * Calculates the lowest priced offer amongst all eligible offers.
     * In this implementation the lowest price of all offers' pricing phases is returned.
     * It's possible the logic can be implemented differently.
     * For example, the lowest average price in terms of month could be returned instead.
     *
     * @param offerDetails List of of eligible offers and base plans.
     *
     * @return the offer id token of the lowest priced offer.
     *
     */
    private fun leastPricedOfferToken(
        offerDetails: List<ProductDetails.SubscriptionOfferDetails>
    ): String {
        var offerToken = String()
        var leastPricedOffer: ProductDetails.SubscriptionOfferDetails
        var lowestPrice = Int.MAX_VALUE

        if (offerDetails.isNotEmpty()) {
            for (offer in offerDetails) {
                for (price in offer.pricingPhases.pricingPhaseList) {
                    if (price.priceAmountMicros < lowestPrice) {
                        lowestPrice = price.priceAmountMicros.toInt()
                        leastPricedOffer = offer
                        offerToken = leastPricedOffer.offerToken
                    }
                }
            }
        }
        return offerToken
    }

    /**
     * Retrieves all eligible base plans and offers using tags from ProductDetails.
     *
     * @param offerDetails offerDetails from a ProductDetails returned by the library.
     * @param tag string representing tags associated with offers and base plans.
     *
     * @return the eligible offers and base plans in a list.
     *
     */
    private fun retrieveEligibleOffers(
        offerDetails: MutableList<ProductDetails.SubscriptionOfferDetails>, tag: String
    ): List<ProductDetails.SubscriptionOfferDetails> {
        val eligibleOffers = emptyList<ProductDetails.SubscriptionOfferDetails>().toMutableList()
        offerDetails.forEach { offerDetail ->
            if (offerDetail.offerTags.contains(tag)) {
                eligibleOffers.add(offerDetail)
            }
        }
        return eligibleOffers
    }

    /**
     * Use the Google Play Billing Library to make a purchase.
     *
     * @param tag String representing tags associated with offers and base plans.
     * @param productId Product being purchased.
     * @param upDowngrade Boolean indicating whether the purchase is an upgrade or downgrade and
     * when converting from one base plan to another.
     *
     */
    fun buy(context: Context, tag: String, productId: String, upDowngrade: Boolean) {
        val productDetails = productsWithProductDetails.value?.get(productId) ?: run {
            LogUtil.e(TAG, "Could not find ProductDetails to make purchase.")
            Toast.makeText(context,
                context.getString(R.string.this_product_cannot_be_found), Toast.LENGTH_SHORT).show()
            return
        }

        val offers = productDetails.subscriptionOfferDetails?.let { offerDetailsList ->
            retrieveEligibleOffers(
                offerDetails = offerDetailsList,
                tag = tag
            )
        }

        //  Get the offer id token of the lowest priced offer.
        val offerToken = offers?.let { leastPricedOfferToken(it) }

        var oldToken = ""
        if (upDowngrade) {
            // The purchase is for an upgrade or downgrade, therefore the existing purchase's
            // token is retrieved.
            for (purchase in purchases.value) {
                oldToken = purchase.purchaseToken
            }

            // Use [upDowngradeBillingFlowParamsBuilder] to build the Params that describe the
            // product to be purchased and the offer to purchase with.
            val billingParams = offerToken?.let { token ->
                upDowngradeBillingFlowParamsBuilder(
                    productDetails = productDetails,
                    offerToken = token,
                    oldToken = oldToken
                )
            }
            buyEvent.postValue(billingParams!!)
        } else {
            // This is a normal purchase for auto-renewing base plans and/or top-up for prepaid
            // base plans.

            // Use [billingFlowParamsBuilder] to build the Params that describe the product to be
            // purchased and the offer to purchase with.
            val billingParams = offerToken?.let { token ->
                billingFlowParamsBuilder(
                    productDetails = productDetails,
                    offerToken = token
                )
            }
            buyEvent.postValue(billingParams!!)
        }
    }

    fun buy(context: Context, productId: String) {
        // Then get the ProductDetails of the product being purchased.
        val productDetails = productsInAppWithProductDetails.value?.get(productId) ?: run {
            LogUtil.e("Could not find ProductDetails to make purchase. productId $productId")
            Toast.makeText(context,
                context.getString(R.string.this_product_cannot_be_found), Toast.LENGTH_SHORT).show()
            return
        }

        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .build()
        )
        val billingFlowParams = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(productDetailsParamsList)
            .setObfuscatedAccountId(productDetails.productId)
            .setObfuscatedProfileId(productDetails.productType)
            .build()
        buyEvent.postValue(billingFlowParams)
    }

    companion object {
        const val TAG = "BillingViewModel"
    }
}
