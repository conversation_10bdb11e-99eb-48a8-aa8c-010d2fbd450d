package com.one.core.firebase;

import android.app.Activity;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;



public class FCMUtil {
    public static void checkGooglePlayServices(Activity activity) {
        // Check for Google Play services
        GoogleApiAvailability googleApiAvailability = GoogleApiAvailability.getInstance();
        int success = googleApiAvailability.isGooglePlayServicesAvailable(activity);
        if (success != ConnectionResult.SUCCESS) {
            googleApiAvailability.makeGooglePlayServicesAvailable(activity);
        }
    }
}
