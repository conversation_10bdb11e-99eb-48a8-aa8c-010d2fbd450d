package com.one.core.firebase;

import android.app.Activity;

import com.google.android.gms.tasks.Task;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;
import com.one.core.R;


public class RemoteConfigUtil {

    public interface CallBack {
        void onComplete(Task<Void> task, FirebaseRemoteConfig firebaseRemoteConfig);
    }

    // 檢查版本
    public static void fetchConfig(Activity activity, CallBack callBack) {
        FirebaseRemoteConfig firebaseRemoteConfig = FirebaseRemoteConfig.getInstance();
        FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(0)
                .setFetchTimeoutInSeconds(0)
                .build();
        firebaseRemoteConfig.setConfigSettingsAsync(configSettings);
        firebaseRemoteConfig.setDefaultsAsync(R.xml.remote_config_defaults);
        // cache expiration in seconds
        long cacheExpiration = 0; //3600 == 1 hour
        firebaseRemoteConfig.fetch(cacheExpiration).addOnCompleteListener(activity, task -> {
            if (task.isSuccessful()) {
                firebaseRemoteConfig.activate();
            }
            callBack.onComplete(task, firebaseRemoteConfig);
        });

    }
}
