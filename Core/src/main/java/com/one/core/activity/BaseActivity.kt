package com.one.core.activity

import android.content.Intent
import android.net.ConnectivityManager
import android.os.Bundle
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.one.core.util.FragmentUtils
import com.one.core.util.LogUtil

abstract class BaseActivity : AppCompatActivity() {
    private var mIsCheckPermission = false

    //    private NetworkCallbackImpl networkCallback;
    private val connectivityManager: ConnectivityManager? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

//        setWindowInsets()

        // 避免小鍵盤跳出
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        val bundle = intent.extras
        initParams(bundle)
    }

//    private fun setWindowInsets() {
//        if (Build.VERSION.SDK_INT >= 35) {
//            ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { v, insets ->
//                val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
//                v.setBackgroundColor(ContextCompat.getColor(this, R.color.colorPrimary))
//                // 如果要保留狀態列高度，可以改成：
//                v.setPadding(0, systemBars.top, 0, 0)
//                insets
//            }
//            val rootView = findViewById<View>(android.R.id.content)
//            ViewCompat.setOnApplyWindowInsetsListener(rootView) { v, insets ->
//                v.setBackgroundColor(ContextCompat.getColor(this, R.color.white))
//                insets
//            }
//        }
//    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // 避免小鍵盤跳出
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        val bundle = intent.extras
        initParams(bundle)
    }


    //    private void registerReceiver() {
    //        networkCallback = new NetworkCallbackImpl();
    //        NetworkRequest.Builder builder = new NetworkRequest.Builder();
    //        NetworkRequest request = builder.build();
    //        connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
    //        connectivityManager.registerNetworkCallback(request, networkCallback);
    //    }
    val appCompatActivity: AppCompatActivity
        get() = this

    override fun onDestroy() {
        super.onDestroy()
        LogUtil.d()
        //        connectivityManager.unregisterNetworkCallback(networkCallback);
    }

    open fun checkPermission(isCheckPermission: Boolean) {
        mIsCheckPermission = isCheckPermission
    }

    public override fun onPause() {
        super.onPause()
        LogUtil.i("onPause")
    }

    /**
     * [初始化參數]
     *
     * @param bundle 參數
     */
    abstract fun initParams(bundle: Bundle?)
    val activity: AppCompatActivity
        get() = this

    /**
     * [頁面跳轉]
     *
     * @param clz Class
     */
    fun startActivity(clz: Class<*>?) {
        startActivity(Intent(this@BaseActivity, clz))
    }

    /**
     * [攜帶數據的頁面跳轉]
     *
     * @param clz    Class
     * @param bundle 參數
     */
    fun startActivity(clz: Class<*>?, bundle: Bundle?) {
        val intent = Intent()
        intent.setClass(this, clz!!)
        if (bundle != null) {
            intent.putExtras(bundle)
        }
        startActivity(intent)
    }

    fun toFragment(
        clazz: Class<out Fragment?>?,
        widgetId: Int,
        bundle: Bundle?,
        isAddToStack: Boolean
    ) {
        FragmentUtils.startFragment(
            supportFragmentManager,
            clazz, widgetId,
            bundle, isAddToStack
        )
    }

    protected open val trackScreenView: Boolean = true

    override fun onResume() {
        super.onResume()

        if (trackScreenView) setCurrentScreen(this.javaClass.simpleName)
    }

    private var firebaseAnalytics: FirebaseAnalytics = Firebase.analytics

    private fun setCurrentScreen(screenName: String) = firebaseAnalytics.run {
        val bundle = Bundle()
        bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName)
        bundle.putString(
            FirebaseAnalytics.Param.SCREEN_CLASS,
            <EMAIL>
        )
        logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }

}