package com.one.core.api

import com.google.gson.GsonBuilder
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

class HeaderInterceptor : Interceptor {
    val key =
        "key=AAAA2ok5zCo:APA91bGAXldFQox-5HM8k4L5u29gmTXCr_6_WxaEYA5vkwnkmWARPrIJf8kw6KTePeR1iGNmt6hCT4D_hD9jurqdxk6Yg_U8I9udJMN7VHotl9w-gr_nvjbsoOj6fRCXHueDsuOnjLSe"

    override fun intercept(chain: Interceptor.Chain): Response {
        val original = chain.request()
        val builder = original.newBuilder()

        builder.header("Content-Type", "application/json")
            .header("Authorization", key)

        val request = builder.method(original.method, original.body).build()
        return chain.proceed(request)
    }

}

object ServiceGenerator {
    private const val TIME_OUT_SHORT = 30
    private const val isRetry = false
    private var BASE_URL: String = ""
    private var retrofit: Retrofit? = null
    fun setBaseUrl(baseUrl: String) {
        BASE_URL = baseUrl
    }

    fun <S> createService(serviceClass: Class<S>): S {
        val httpClientBuilder = OkHttpClient.Builder()
            .retryOnConnectionFailure(isRetry) // 默認重試一次，若需要重試N次，則要實現攔截器。
            .connectTimeout(TIME_OUT_SHORT.toLong(), TimeUnit.SECONDS)
            .readTimeout(TIME_OUT_SHORT.toLong(), TimeUnit.SECONDS)
            .writeTimeout(TIME_OUT_SHORT.toLong(), TimeUnit.SECONDS)

        val headerInterceptor = HeaderInterceptor()
        httpClientBuilder.addInterceptor(headerInterceptor)

        //        OInterceptor loggingInterceptor = new OInterceptor();
//        loggingInterceptor.setLevel(OInterceptor.Level.BODY);
//                httpClientBuilder.addInterceptor(loggingInterceptor);

        val logging = HttpLoggingInterceptor()
        logging.setLevel(HttpLoggingInterceptor.Level.BODY)
        httpClientBuilder.interceptors().add(logging)
        val gson = GsonBuilder()
            .setPrettyPrinting()
            .serializeNulls()
            .create()
        val builder = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .client(httpClientBuilder.build())
        retrofit = builder.client(httpClientBuilder.build()).build()
        return retrofit!!.create(serviceClass)
    }
}