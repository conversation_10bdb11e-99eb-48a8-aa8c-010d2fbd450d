plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    id("kotlin-android")
    id("kotlin-kapt")
    id("kotlin-parcelize")
    id("org.jetbrains.kotlin.plugin.compose")
}
android {

    namespace 'com.one.core'

    defaultConfig {
        compileSdk 35
        minSdkVersion 24
        targetSdkVersion 35

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding true
        compose true
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += "-Xopt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
    }

}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation project(':Billing')

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.compose.runtime:runtime-android:1.7.8'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'


    implementation "com.google.android.material:material:$material_version"
    api 'androidx.recyclerview:recyclerview:1.3.2'
    api 'androidx.cardview:cardview:1.0.0'

    // Google billing
    api 'com.android.billingclient:billing:7.0.0'
    // gson
    api 'com.google.code.gson:gson:2.10.1'

    implementation platform('com.google.firebase:firebase-bom:33.1.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    api("com.google.firebase:firebase-config")
    api("com.google.firebase:firebase-messaging")



//    api 'com.google.firebase:firebase-messaging:24.0.0'
//    api 'com.google.firebase:firebase-auth:23.0.0'
//    api 'com.google.firebase:firebase-firestore:25.0.0'
//    api 'com.google.firebase:firebase-core:21.1.1'
//    api 'com.google.firebase:firebase-config:22.0.0'
//    api 'com.google.firebase:firebase-inappmessaging-display:21.0.0'
//    implementation("com.google.firebase:firebase-crashlytics:19.0.1") {
//        exclude group: 'androidx.datastore', module: 'datastore-preferences'
//    }


    // retrofit
    api 'com.squareup.retrofit2:retrofit:2.9.0'
    api 'com.squareup.retrofit2:converter-gson:2.9.0'
    // logging interceptor
    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0")

    //material-dialogs
    api 'com.afollestad.material-dialogs:core:3.3.0'
    api 'com.afollestad.material-dialogs:input:3.1.1'
    api 'com.afollestad.material-dialogs:files:3.1.1'
    api 'com.afollestad.material-dialogs:color:3.1.1'
    api 'com.afollestad.material-dialogs:datetime:3.1.1'
    api 'com.afollestad.material-dialogs:bottomsheets:3.1.1'
    api 'com.afollestad.material-dialogs:lifecycle:3.1.1'

    // kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.1'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.8.1'

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"

    implementation 'androidx.hilt:hilt-navigation-fragment:1.2.0'

    // Google ads
    api 'com.google.android.gms:play-services-ads:23.1.0'

    // FB ads
    implementation 'androidx.annotation:annotation:1.8.0'
    implementation 'com.facebook.android:audience-network-sdk:6.11.0'

    // 版本更新功能
//    api 'com.google.android.play:core:1.10.3'
    // This dependency is downloaded from the Google’s Maven repository.
    // Make sure you also include that repository in your project's build.gradle file.
    implementation("com.google.android.play:app-update:2.1.0")

    // For Kotlin users, also import the Kotlin extensions library for Play In-App Update:
    implementation("com.google.android.play:app-update-ktx:2.1.0")

    implementation 'androidx.preference:preference-ktx:1.2.1'


    def composeBom = platform('androidx.compose:compose-bom:2024.02.00')
    implementation composeBom
    androidTestImplementation composeBom

    // Material Design 3
    implementation 'androidx.compose.material3:material3'

    // Android Studio Preview support
    implementation 'androidx.compose.ui:ui-tooling-preview'
    debugImplementation 'androidx.compose.ui:ui-tooling'

    // UI Tests
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'

    // Optional - Integration with activities
    implementation 'androidx.activity:activity-compose:1.8.2'
    // Optional - Integration with ViewModels
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'

    implementation("androidx.compose.compiler:compiler:1.5.15")

    implementation 'com.google.dagger:dagger:2.49'
}