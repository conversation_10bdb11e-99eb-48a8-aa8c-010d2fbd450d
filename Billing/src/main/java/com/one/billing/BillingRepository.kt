package com.one.billing

import com.android.billingclient.api.Purchase
import com.one.billing.model.BillingProduct
import kotlinx.coroutines.flow.Flow

interface BillingRepository {
    suspend fun queryProducts(productIds: List<String>): List<BillingProduct>
    suspend fun launchBillingFlow(product: BillingProduct)
    fun getPurchases(): Flow<List<Purchase>>
    suspend fun acknowledgePurchase(purchaseToken: String)
    suspend fun consumePurchase(purchaseToken: String)
    suspend fun restorePurchases()
} 